#!/bin/bash

# 删除指定目录下所有.DS_Store文件的脚本

# 检查是否提供了目录参数
if [ $# -eq 0 ]; then
    echo "使用方法: $0 目录路径"
    echo "示例: $0 ~/Documents"
    exit 1
fi

target_dir="$1"

# 检查目录是否存在
if [ ! -d "$target_dir" ]; then
    echo "错误: 目录 '$target_dir' 不存在"
    exit 1
fi

echo "正在扫描目录 '$target_dir' 中的 .DS_Store 文件..."

# 查找并删除所有.DS_Store文件
find "$target_dir" -type f -name ".DS_Store" -print -delete

echo "操作完成。所有 .DS_Store 文件已被删除。"