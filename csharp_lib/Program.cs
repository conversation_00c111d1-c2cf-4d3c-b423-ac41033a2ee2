using CommandLine;
using Syncfusion.DocIO.DLS;
using Syncfusion.DocIORenderer;
using Syncfusion.Pdf;
using System;
using System.Collections.Generic;
using System.IO;

namespace WordToPdfConverter
{
    // 定义命令行参数选项
    public class Options
    {
        [Option('i', "input", Required = false, HelpText = "输入Word文档路径", Default = "Data/Template.docx")]
        public string InputFile { get; set; } = "Data/Template.docx";

        [Option('o', "output", Required = false, HelpText = "输出PDF文档路径", Default = "Output/Output.pdf")]
        public string OutputFile { get; set; } = "Output/Output.pdf";

        [Option('v', "verbose", Required = false, HelpText = "显示详细输出", Default = false)]
        public bool Verbose { get; set; }
    }

    public class Program
    {
        public static int Main(string[] args)
        {
            // 注册Syncfusion许可证
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("ORg4AjUWIQA/Gnt2XFhhQlJHfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTH5XdEFjXHtccXdRRWlUWkZ/;Mgo+DSMBMAY9C3t2XFhhQlJHfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTH5XdEFjXHtccXdRQ2VYWkZ/");

            return Parser.Default.ParseArguments<Options>(args)
                .MapResult(
                    opts => RunWithOptions(opts),
                    errs => HandleParseErrors(errs)
                );
        }

        private static int RunWithOptions(Options opts)
        {
            try
            {
                if (opts.Verbose)
                    Console.WriteLine($"开始将Word文档 '{opts.InputFile}' 转换为PDF文档 '{opts.OutputFile}'");

                // 打开文件流
                FileStream docStream = new FileStream(Path.GetFullPath(opts.InputFile), FileMode.Open, FileAccess.Read);

                if (opts.Verbose)
                    Console.WriteLine("文件已加载");

                // 加载Word文档
                WordDocument wordDocument = new WordDocument(docStream, Syncfusion.DocIO.FormatType.Automatic);

                if (opts.Verbose)
                    Console.WriteLine("Word文档已成功解析");

                // 创建DocIORenderer实例进行Word到PDF的转换
                DocIORenderer render = new DocIORenderer();
                // render.Settings.EmbedFonts = true;
                // render.Settings.EmbedCompleteFonts = true;
                if (opts.Verbose)
                    Console.WriteLine("开始转换为PDF...");

                // 将Word文档转换为PDF文档
                PdfDocument pdfDocument = render.ConvertToPDF(wordDocument);

                if (opts.Verbose)
                    Console.WriteLine("转换完成");

                // 释放资源
                render.Dispose();
                wordDocument.Dispose();
                docStream.Dispose();

                // 确保输出目录存在
                Directory.CreateDirectory(Path.GetDirectoryName(Path.GetFullPath(opts.OutputFile)) ?? string.Empty);

                // 创建文件流
                using (FileStream outputFileStream = new FileStream(Path.GetFullPath(opts.OutputFile), FileMode.Create, FileAccess.ReadWrite))
                {
                    // 将PDF文档保存到文件流
                    pdfDocument.Save(outputFileStream);
                }

                // 关闭文档
                pdfDocument.Close(true);

                if (opts.Verbose)
                    Console.WriteLine($"PDF文档已成功保存到 '{opts.OutputFile}'");

                return 0; // 成功
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"错误: {ex.Message}");
                return 1; // 失败
            }
        }

        private static int HandleParseErrors(IEnumerable<CommandLine.Error> errs)
        {
            // 解析错误已由CommandLineParser库处理，无需额外操作
            return 1; // 失败
        }
    }
}