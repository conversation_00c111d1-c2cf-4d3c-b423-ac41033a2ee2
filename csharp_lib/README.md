# Word转PDF转换器

一个使用Syncfusion库将Microsoft Word文档转换为PDF格式的命令行工具。

## 环境要求

- .NET 6.0 SDK或更高版本
- Syncfusion库（构建项目时会自动下载所需的NuGet包）

## 构建应用

```bash
# 进入项目目录
cd csharp_lib

# 构建项目
dotnet build

# 发布项目（可选 - 用于分发）
dotnet publish -c Release
```

## 功能特点

- 转换Word文档（.doc, .docx）为PDF
- 保留格式、图像和图表
- 简单直观的命令行界面
- 可配置的转换参数
- 自动创建输出目录（如果不存在）
- 详细的错误处理和日志记录
- 支持中文字体显示

## 注意事项

本应用使用Syncfusion库，商业用途可能需要获取许可证。请访问[Syncfusion官网](https://www.syncfusion.com)了解更多关于其许可的信息。

## 技术实现说明

本应用使用了两种可能的技术实现方式：

### 1. DocIORenderer (当前使用)

- 基于Syncfusion.DocIORenderer.NET包
- 跨平台兼容，支持.NET Core/.NET 6+
- 适用于命令行应用、Web应用等各种场景
- 实现示例：

```csharp
// 加载Word文档
WordDocument wordDocument = new WordDocument(docStream, Syncfusion.DocIO.FormatType.Automatic);

// 创建DocIORenderer实例
DocIORenderer render = new DocIORenderer();

// 将Word文档转换为PDF文档
PdfDocument pdfDocument = render.ConvertToPDF(wordDocument);

// 保存PDF文档
pdfDocument.Save(outputFileStream);
```

### 2. DocToPDFConverter (替代方案，仅适用于Windows平台)

- 基于Syncfusion.DocToPDFConverter.Base包
- 仅支持Windows Forms、WPF、ASP.NET和ASP.NET MVC平台
- 不适用于跨平台.NET Core/.NET 6+控制台应用
- 实现示例：

```csharp
// 创建DocToPDFConverter实例
DocToPDFConverter converter = new DocToPDFConverter();

// 方法1：直接从文件路径转换
PdfDocument pdfDocument = converter.ConvertToPDF("文档路径.docx");

// 方法2：从Word文档对象转换
// WordDocument wordDocument = new WordDocument("文档路径.docx", FormatType.Docx);
// PdfDocument pdfDocument = converter.ConvertToPDF(wordDocument);

// 保存PDF文档
pdfDocument.Save("输出.pdf");
```

相关文档：
- [DocIORenderer文档](https://help.syncfusion.com/document-processing/pdf/word-to-pdf/pdfconversion-with-renderer/)
- [DocToPDFConverter文档](https://help.syncfusion.com/cr/document-processing/Syncfusion.DocToPDFConverter.DocToPDFConverter.html)

## 命令行使用说明

本程序现已支持通过命令行参数进行操作。以下是可用的命令行参数：

```
  -i, --input        输入Word文档路径 (默认: "Data/Template.docx")
  -o, --output       输出PDF文档路径 (默认: "Output/Output.pdf")
  -v, --verbose      显示详细输出
  -f, --font-dir     指定额外的字体目录路径，多个路径用逗号分隔
  -c, --chinese-font 指定默认中文字体，例如：SimSun、Microsoft YaHei
  --help             显示帮助信息
  --version          显示版本信息
```

### 使用示例

1. 使用默认设置转换文档：
```
dotnet run
```

2. 指定输入和输出文件：
```
dotnet run -i "MyDocs/Document.docx" -o "Exports/Result.pdf"
```

3. 启用详细输出：
```
dotnet run -i "Data/Sample.docx" -v
```

4. 指定中文字体和字体目录(Windows示例)：
```
dotnet run -i "含中文文档.docx" -c "SimSun" -f "C:\Windows\Fonts"
```

5. 指定中文字体和字体目录(macOS示例)：
```
dotnet run -i "含中文文档.docx" -c "PingFang SC" -f "/Library/Fonts,/System/Library/Fonts"
```

6. 显示帮助信息：
```
dotnet run --help
```

## 中文字体支持

本应用已添加对中文字体的支持，解决了中文字符在PDF中显示为空白的问题。主要通过以下方式实现：

1. **自动字体替换** - 程序会根据当前操作系统自动选择合适的中文字体：
   - Windows: 默认使用宋体(SimSun)
   - macOS: 默认使用苹方(PingFang SC)
   - Linux: 默认使用Noto Sans CJK SC

2. **手动指定字体** - 可以通过命令行参数指定默认中文字体：
   ```
   dotnet run -c "Microsoft YaHei"
   ```

3. **添加字体目录** - 可以指定额外的字体目录：
   ```
   dotnet run -f "/path/to/fonts/directory"
   ```

4. **字体配置选项** - 程序启用了以下配置以增强中文支持：
   - 完整字体嵌入(非子集)
   - 字体映射
   - 禁用快速渲染以提高精确度

如果PDF中仍然出现中文显示问题，请尝试：
1. 确认系统中是否安装了中文字体
2. 使用`-f`参数指定包含中文字体的目录
3. 使用`-c`参数指定确认可用的中文字体名称
4. 使用`-v`参数启用详细输出，查看字体加载和映射情况 