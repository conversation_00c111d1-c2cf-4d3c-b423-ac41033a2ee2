#![allow(unused)]

mod anki;
mod signals;
use anki::docx_card::convert_docx2html;
use anki::pdf_card::cloze_card::{make_occlusion_card, OcclusionCardConfig};
use anki::pdf_card::common::group_page_annotations;
use anki::pdf_card::qa_card::{make_qa_card_dual_file, make_qa_card_single_file,make_qa_card_full_page, QACardConfig};
use anki::pdf_utils::{
    delete_annotations, export_pdf_to_images, extract_annotations, merge_pdfs, merge_pdfs2,
    reorder_pages,
};
use signals::*;
use rinf::RustSignal;
use std::fs;
use std::path::Path;

/// 创建一个进度回调函数
fn progress_callback(current: f64, total: f64, message: String) {
    ProgressResponse {
        status: "running".to_string(),
        message,
        current,
        total,
    }
    .send_signal_to_dart();
}

#[cfg(test)]
mod tests {
    use super::*;

    // #[tokio::test]
    async fn test_extract_annotations() {
        let pdf_path =
            Path::new("/Users/<USER>/Documents/制卡素材/制卡测试/1.容器运行时与K8s概览_unlocked.pdf");
        let page_range = "1-10";

        let annotations = extract_annotations(pdf_path, page_range, 1, false).await;
        assert!(annotations.is_ok(), "Failed to extract annotations");
    }

    // #[tokio::test]
    async fn test_group_annotations() {
        let pdf_path =
            Path::new("/Users/<USER>/Documents/制卡素材/制卡测试/1.容器运行时与K8s概览_unlocked.pdf");
        let page_range = "1-10";

        let annotations = extract_annotations(pdf_path, page_range, 1, false).await.unwrap();
        let (grouped_annotations, annotations_to_delete, cloze_colors) = group_page_annotations(annotations);
        assert!(
            !grouped_annotations.is_empty(),
            "Failed to group annotations"
        );
    }

    #[tokio::test]
    async fn test_make_occlusion_card() {
        let pdf_path = Path::new(
            // "/Users/<USER>/Documents/制卡素材/制卡测试/PDF制卡/0218U11-2b 背诵版（英盖）_副本.pdf",
            // "/Users/<USER>/Documents/制卡素材/制卡测试/预答辩备案表.pdf",
            // "/Users/<USER>/Downloads/25王道计组基础课文稿_20250323_1113071(1).pdf"
            // "/Users/<USER>/Downloads/25王道计组基础课文稿_20250323_1113071_书签导入.pdf"
            // "/Users/<USER>/Documents/制卡素材/制卡测试/PDF制卡/化学-问题文件_提取页面.pdf"
            // "/Users/<USER>/Downloads/26.2025经综数学396基础精讲26.pdf"
            // "/Users/<USER>/Documents/制卡素材/制卡测试/中学教育知识与能力应试版23下-new.pdf"
            // "/Users/<USER>/Documents/制卡素材/制卡测试/当下日语2（二校版）-去水印版.pdf"
            // "/Users/<USER>/Documents/制卡素材/制卡测试/扫描版错位.pdf"
            // "/Users/<USER>/Downloads/【 一建建筑】-25年王玮案例300问-权限恢复.pdf"
            // "/Users/<USER>/Downloads/03、生理-细胞的基本功能-细胞信号转导_提取页面.pdf"
            // "/Users/<USER>/Documents/制卡素材/制卡测试/1.容器运行时与K8s概览_unlocked.pdf"
            // "/Users/<USER>/Downloads/PDF挖空测试 (1).pdf"
            // "/Users/<USER>/Downloads/bd2025ml徐蓉-第一篇(1).pdf"
            // "/Users/<USER>/Documents/制卡素材/制卡测试/PDF制卡/26张宇基础30讲（高数）_零宽批注.pdf"
            "/Users/<USER>/Downloads/50876400620167202.25考研XL内科学笔记.pdf"

        );
        let output_path = pdf_path.parent().unwrap().join("test.apkg");
        let config = OcclusionCardConfig {
            doc_path: pdf_path.to_string_lossy().to_string(),
            page_range: "".to_string(),
            address: "http://localhost:8765".to_string(),
            deck_name: "test".to_string(),
            model_name: "Kevin Image Cloze v5".to_string(),
            mask_types: vec![
                "Square".to_string(),
                "Highlight".to_string(),
                // "Underline".to_string(),
                // "Strikeout".to_string(),
                // "Squiggly".to_string(),
            ],
            card_type: "free_guess".to_string(),
            one_card_per_cloze: true,
            q_mask_color: "".to_string(),
            a_mask_color: "".to_string(),
            tags: vec!["测试".to_string()],
            dpi: 300,
            is_full_page_mode: false,
            is_create_subdeck: true,
            subdeck_max_level: 0,
            is_show_source: true,
            is_mix_card: false,
            num_cols: 1,
            extra_info: Some("extra info".to_string()),
            item_key: None,
            output_path: output_path.to_string_lossy().to_string(),
        };

        let result = make_occlusion_card(config, progress_callback).await;
        dbg!(&result);
        assert!(result.is_ok(), "Failed to make occlusion card");
        assert!(false);
    }

    // #[tokio::test]
    async fn test_make_qa_card_single_file() {
        let pdf_path = Path::new(
            // "/Users/<USER>/Documents/制卡素材/制卡测试/PDF制卡/11465现代公司管理考前复习资料-问答.pdf",
            // "/Users/<USER>/Documents/制卡素材/制卡测试/PDF制卡/化学-问题文件_提取页面.pdf"
            "/Users/<USER>/Downloads/2025资料分析超大杯讲义(1).pdf"
        );
        let config = QACardConfig {
            doc_path: pdf_path.to_string_lossy().to_string(),
            a_doc_path: None,
            page_range: "".to_string(),
            a_page_range: "".to_string(),
            address: "http://localhost:8765".to_string(),
            deck_name: "test".to_string(),
            model_name: "Kevin Image QA Card v2".to_string(),
            mask_types: vec![
                "Square".to_string(),
                "Highlight".to_string(),
                "Underline".to_string(),
                "Strikeout".to_string(),
                "Squiggly".to_string(),
            ],
            tags: vec!["测试".to_string()],
            dpi: 300,
            is_create_subdeck: true,
            subdeck_max_level: 0,
            is_show_source: true,
            is_mix_card: false,
            is_answer_cloze: true,
            num_cols: 1,
            extra_info: Some("extra info".to_string()),
            item_key: None,
            output_path: "output/test.apkg".to_string(),
        };

        let result = make_qa_card_single_file(config, progress_callback).await;
        dbg!(&result);
        assert!(result.is_ok(), "Failed to make occlusion card");
        assert!(false);
    }

    // #[tokio::test]
    async fn test_make_qa_card_dual_file() {
        let q_pdf_path = Path::new(
            "/Volumes/HDD/中转站/Anki制卡测试(总)/PDF制卡测试/问答题制卡/问题答案分开/第二章 首次公开发行股票-问题_批注删除.pdf"
        );
        let a_pdf_path = Path::new(     
            "/Volumes/HDD/中转站/Anki制卡测试(总)/PDF制卡测试/问答题制卡/问题答案分开/第二章 首次公开发行股票-答案_批注删除.pdf"
        );
        let config = QACardConfig {
            doc_path: q_pdf_path.to_string_lossy().to_string(),
            a_doc_path: Some(a_pdf_path.to_string_lossy().to_string()),
            page_range: "".to_string(),
            a_page_range: "".to_string(),
            address: "http://localhost:8765".to_string(),
            deck_name: "test".to_string(),
            model_name: "Kevin Image QA Card v2".to_string(),
            mask_types: vec![
                "Square".to_string(),
                "Highlight".to_string(),
                "Underline".to_string(),
                "Strikeout".to_string(),
                "Squiggly".to_string(),
            ],
            tags: vec!["测试".to_string()],
            dpi: 300,
            is_create_subdeck: true,
            subdeck_max_level: 0,
            is_show_source: true,
            is_mix_card: false,
            is_answer_cloze: true,
            num_cols: 1,
            extra_info: Some("extra info".to_string()),
            item_key: None,
            output_path: "output/test.apkg".to_string(),
        };
        let result = make_qa_card_dual_file(config, progress_callback).await;
        dbg!(&result);
        assert!(result.is_ok(), "Failed to make occlusion card");
        assert!(false);
    }
    
    // #[tokio::test]
    async fn test_make_qa_card_full_page() {

        let output_path = "output/test.apkg".to_string();
        let result = make_qa_card_full_page(
            "/Users/<USER>/Documents/制卡素材/制卡测试/PDF制卡/第二章 首次公开发行股票-问题.pdf".to_string(),
            "Guru导入".to_string(),
            "".to_string(),
            "dual".to_string(),
            output_path, 
            "http://localhost:8765".to_string(),
            vec!["测试".to_string()],
            progress_callback
        ).await;
        dbg!(&result);
        assert!(result.is_ok(), "Failed to make occlusion card");
        assert!(false);
    }
    
    // #[test]
    fn test_delete_annotations() {
        let pdf_path =
            "/Users/<USER>/Documents/制卡素材/制卡测试/PDF制卡/0218U11-2b 背诵版（英盖）.pdf";
        let parent_dir = Path::new(pdf_path).parent().unwrap();
        let output_path = parent_dir.join("output.pdf").to_string_lossy().to_string();
        let specific_ids = Some(vec![
            "{b5bef2b1-2d63-4b43-856f-cb8589069441}".to_string(),
            "{f8485bc6-691e-433a-a8fc-cbc713eea488}".to_string(),
        ]);
        let result = delete_annotations(pdf_path, "", Some(&output_path), specific_ids);
        dbg!(&result);
        assert!(result.is_ok(), "Failed to delete annotations");
        assert!(false);
    }

    // #[tokio::test]
    async fn test_reorder_pages() {
        let input_path = "/Users/<USER>/Documents/制卡素材/制卡测试/序号14-研究生学位申请书.pdf";
        let page_order = "3,2,1,4-N";
        let output_path = "output/序号14-研究生学位申请书.pdf_reordered.pdf";
        let result = reorder_pages(input_path, page_order, output_path, progress_callback).await;
        assert!(result.is_ok(), "Failed to reorder pages");
    }

    // #[test]
    fn test_merge_pdfs() {
        let input_paths = vec![
            "/Users/<USER>/Documents/制卡素材/制卡测试/预答辩备案表.pdf",
            "/Users/<USER>/Documents/制卡素材/制卡测试/序号14-研究生学位申请书.pdf",
        ];
        let output_path = "output/merge_test.pdf";
        let result = merge_pdfs2(&input_paths, "selection", "asc", false, output_path);
        // let result = merge_pdfs(&input_paths, output_path);
        assert!(result.is_ok(), "Failed to merge PDFs");
    }

    #[test]
    fn test_run_cmd() {
        use std::process::{Command, Stdio};
        use std::io::{BufRead, BufReader};
        use serde_json::Value;
        let cmd_path = "/Users/<USER>/code/anki-guru/anki_guru/native/pylib/cmd.bin";
        let mut child = Command::new(cmd_path)
        .arg("-t")
        .arg("pdf2img")
        .arg("-i")
        .arg("/Users/<USER>/Downloads/26张宇基础30讲（高数）.pdf")
        .arg("-p")
        .arg("1-3")
        .arg("-o")
        .arg("/Users/<USER>/Downloads/456")
        .stdout(Stdio::piped())
        .spawn()
        .expect("Failed to start process");

    let stdout = child.stdout.take().unwrap();
    let reader = BufReader::new(stdout);

    for line in reader.lines() {
        let json_str = line.unwrap();
        dbg!("json_str: {}", &json_str); 
        match serde_json::from_str::<Value>(&json_str) {
            Ok(json) => {
                if let Some(status) = json.get("status").and_then(|v| v.as_str()) {
                    match status {
                        "processing" => {
                            let data = json["data"].as_str().unwrap_or("0%");
                            dbg!("[Rust] 进度更新: {}", data);
                        },
                        "success" => {
                            let message = json["message"].as_str().unwrap_or("完成");
                            
                            // 正确处理data字段，它可能是一个对象而不是字符串
                            let data_str = match &json["data"] {
                                Value::String(s) => s.clone(),
                                Value::Object(obj) => {
                                    // 对象类型，转换为可读的格式
                                    serde_json::to_string_pretty(obj).unwrap_or_else(|_| "{}".to_string())
                                },
                                _ => "".to_string()
                            };
                            
                            dbg!("[Rust] 任务完成 - {}: {}", message, data_str);
                        },
                        _ => {}
                    }
                }
            },
            Err(e) => {
                dbg!("JSON解析失败: {:?}", e);
            }
        }
    }
    child.wait().unwrap();   

    assert!(false);
}
}

#[tokio::main]
async fn main() {
    println!("main");
    // let input_path = "/Users/<USER>/Documents/制卡素材/制卡测试/序号14-研究生学位申请书.pdf";
    // let page_order = "3,2,1,4-N";
    // let output_path = "output/序号14-研究生学位申请书.pdf_reordered.pdf";
    // let result = reorder_pages(input_path, page_order, output_path, progress_callback);
    use lopdf::{xobject::PdfImage, Document, Object, ObjectId};


    // let pdf_path = Path::new("/Users/<USER>/Downloads/50876400620167202.25考研XL内科学笔记-重排.pdf");
    let pdf_path = Path::new("/Users/<USER>/Downloads/50876400620167202.25考研XL内科学笔记.pdf");
    let doc = Document::load(pdf_path).unwrap();
    let total_pages = doc.get_pages().len() as u32;
    println!("total_pages: {}", total_pages);
    // let page_range = "1-10";
    // let annotations = extract_annotations(pdf_path, page_range, 1, false);
    // print!("{:?}", annotations);
}
