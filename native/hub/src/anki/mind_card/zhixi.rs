#![allow(unused)]

use crate::anki::connect::gen_id;
use crate::anki::mind_card::common::{
    mind_node_to_anki_notes, normalize_color, ClozeContext, MindNode, StyleAttribute,
};
use crate::anki::models::{get_kevin_mindmap_card_model, get_mindmap_card_assets, AnkiNote};
use crate::anki::utils::save_to_temp_file;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::default::Default;
use std::fs;
use std::io;
use std::path::Path;
use std::path::PathBuf;
use tempfile::TempDir;
use ulid::Ulid;
use zip::ZipArchive;

/// 解压.zxm文件到临时目录
pub async fn extract_zhixi(zxm_path: impl AsRef<Path>) -> Result<String, io::Error> {
    let temp_dir = crate::anki::utils::get_temp_dir()
        .await
        .map_err(|e| io::Error::new(io::ErrorKind::Other, e.to_string()))?;

    let output_dir = PathBuf::from(temp_dir.clone()).join(Ulid::new().to_string());
    fs::create_dir_all(&output_dir)?;

    let file = fs::File::open(&zxm_path)?;
    let mut archive =
        ZipArchive::new(file).map_err(|e| io::Error::new(io::ErrorKind::InvalidData, e))?;

    for i in 0..archive.len() {
        let mut file = archive
            .by_index(i)
            .map_err(|e| io::Error::new(io::ErrorKind::InvalidData, e))?;

        let outpath = output_dir.join(file.name());

        if file.name().ends_with('/') {
            fs::create_dir_all(&outpath)?;
        } else {
            if let Some(parent) = outpath.parent() {
                fs::create_dir_all(parent)?;
            }
            let mut outfile = fs::File::create(&outpath)?;
            io::copy(&mut file, &mut outfile)?;
        }
    }

    Ok(output_dir.to_string_lossy().to_string())
}

/// 解析知悉导图的JSON结构
pub fn parse_zhixi_json(json: &serde_json::Value) -> Option<serde_json::Value> {
    json.get("root").cloned()
}

#[derive(Debug, Serialize, Deserialize)]
struct RichTextOp {
    insert: String,
    #[serde(default)]
    attributes: HashMap<String, serde_json::Value>,
}

/// 转换知悉的richText格式为HTML
pub fn zhixi_richtext_to_html(
    rich_text: &serde_json::Value,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
) -> String {
    // 获取ops数组
    let ops_value = match rich_text.get("ops") {
        Some(v) => v,
        None => {
            eprintln!("富文本缺少ops字段");
            return String::new();
        }
    };

    // 解析为RichTextOp列表
    let ops = match serde_json::from_value::<Vec<RichTextOp>>(ops_value.clone()) {
        Ok(ops) => ops,
        Err(e) => {
            eprintln!("富文本解析错误: {}", e);
            return String::new();
        }
    };

    let mut html = String::new();
    let mut current_cloze_content = String::new();
    let mut current_cloze_id: Option<usize> = None;
    let mut current_styles: Vec<String> = Vec::new();

    for op in ops {
        let mut span_style = Vec::new();
        let mut should_cloze = false;

        // 检查挖空条件（根据示例中的样式）
        if let Some(styles) = cloze_styles {
            should_cloze = styles.iter().any(|style| match style {
                StyleAttribute::Colors(colors) => op
                    .attributes
                    .get("color")
                    .and_then(|v| v.as_str())
                    .map_or(false, |c| {
                        colors
                            .iter()
                            .any(|color| normalize_color(color) == normalize_color(c))
                    }),
                StyleAttribute::Highlights(colors) => op
                    .attributes
                    .get("background")
                    .and_then(|v| v.as_str())
                    .map_or(false, |bg| {
                        colors
                            .iter()
                            .any(|color| normalize_color(color) == normalize_color(bg))
                    }),
                StyleAttribute::Italic => {
                    op.attributes.get("italic").and_then(|v| v.as_bool()) == Some(true)
                }
                StyleAttribute::Bold => {
                    op.attributes.get("bold").and_then(|v| v.as_bool()) == Some(true)
                }
                StyleAttribute::StrikeThrough => {
                    op.attributes.get("strike").and_then(|v| v.as_bool()) == Some(true)
                }
                StyleAttribute::Underline => {
                    op.attributes.get("underline").and_then(|v| v.as_bool()) == Some(true)
                }
            });
        }
        // 解析样式属性（适配知悉的格式）
        for (attr, value) in &op.attributes {
            match attr.as_str() {
                "bold" if value.as_bool() == Some(true) => {
                    span_style.push("font-weight: bold".to_string())
                }
                "italic" if value.as_bool() == Some(true) => {
                    span_style.push("font-style: italic".to_string())
                }
                "underline" if value.as_bool() == Some(true) => {
                    span_style.push("text-decoration: underline".to_string())
                }
                "strike" if value.as_bool() == Some(true) => {
                    span_style.push("text-decoration: line-through".to_string())
                }
                "color" => {
                    if let Some(color) = value.as_str() {
                        span_style.push(format!("color: {}", normalize_color(color)));
                    }
                }
                "background" => {
                    if let Some(bg) = value.as_str() {
                        span_style.push(format!("background-color: {}", normalize_color(bg)));
                    }
                }
                _ => {}
            }
        }

        // 处理连续挖空合并
        if should_cloze {
            let op_html = if !span_style.is_empty() {
                format!(
                    r#"<span style="{}">{}</span>"#,
                    span_style.join("; "),
                    op.insert
                )
            } else {
                op.insert
            };

            if current_cloze_id.is_none() {
                current_cloze_id = Some(cloze_ctx.next_id());
            }
            current_cloze_content.push_str(&op_html);
        } else {
            // 提交缓存的挖空内容
            if let Some(cloze_id) = current_cloze_id.take() {
                let combined_style = current_styles.join("; ");
                html.push_str(&format!(
                    r#"<span class="cloze activated" style="{}" data-cloze-id="{}" onclick='this.classList.toggle("activated")'>{}</span>"#,
                    combined_style, cloze_id, current_cloze_content
                ));
                current_cloze_content.clear();
            }

            // 处理普通内容
            let text = if !span_style.is_empty() {
                format!(
                    r#"<span style="{}">{}</span>"#,
                    span_style.join("; "),
                    op.insert
                )
            } else {
                op.insert
            };
            html.push_str(&text);
        }
    }

    // 处理最后的挖空内容
    if let Some(cloze_id) = current_cloze_id.take() {
        let combined_style = current_styles.join("; ");
        html.push_str(&format!(
            r#"<span class="cloze activated" style="{}" data-cloze-id="{}" onclick='this.classList.toggle("activated")'>{}</span>"#,
            combined_style, cloze_id, current_cloze_content
        ));
    }

    html
}

/// 解析知悉导图节点
fn parse_zhixi_node(
    node: &serde_json::Value,
    cloze_styles: Option<&[StyleAttribute]>,
    cloze_ctx: &mut ClozeContext,
    images: &mut Vec<String>,
    temp_dir: &str,
) -> Option<MindNode> {
    let data = node.get("data")?;
    let id = format!("zhixi_{}", data.get("id")?.as_str()?.to_string());

    let mut content = String::new();

    // 处理图片（优先使用resourceId）
    if data.get("image").is_some() {
        if let Some(resource_id) = data.get("resourceId").and_then(|v| v.as_str()) {
            // 在resources目录中查找匹配文件（支持无扩展名和常见图片扩展名）
            let resource_dir = PathBuf::from(temp_dir).join("resources");
            let mut img_path = None;

            // 检查可能的文件名格式
            let candidates = [
                resource_dir.join(resource_id), // 无扩展名
                resource_dir.join(format!("{}.png", resource_id)),
                resource_dir.join(format!("{}.jpg", resource_id)),
                resource_dir.join(format!("{}.jpeg", resource_id)),
            ];

            for path in &candidates {
                if path.exists() {
                    img_path = Some(path.file_name().unwrap().to_string_lossy().to_string());
                    break;
                }
            }

            if let Some(img_file) = img_path {
                // 解析图片尺寸
                let mut style = String::new();
                if let Some(size) = data.get("imageSize") {
                    let width = size.get("width").and_then(|v| v.as_u64()).unwrap_or(0);
                    let height = size.get("height").and_then(|v| v.as_u64()).unwrap_or(0);

                    if width > 0 && height > 0 {
                        style = format!("style=\"width:{}px; height:{}px;\"", width, height);
                    }
                }

                content.push_str(&format!(
                    r#"<img src="{}" class="zhixi-image" {}> "#,
                    img_file, style
                ));
                images.push(img_file);
            } else {
                eprintln!("未找到资源文件: {}", resource_id);
            }
        }
        // 回退处理旧版image字段
        else if let Some(image_url) = data.get("image").and_then(|v| v.as_str()) {
            let clean_url = image_url.split(['?', '#']).next().unwrap_or_default();
            let img_name = clean_url.split('/').last().unwrap_or_default();
            content.push_str(&format!(r#"<img src="{}" class="zhixi-image">"#, img_name));
            images.push(img_name.to_string());
        }
    }

    // 处理富文本
    if let Some(rich_text) = data.get("richText") {
        let html = zhixi_richtext_to_html(rich_text, cloze_styles, cloze_ctx);
        content.push_str(&format!(r#"<span class="zhixi-text">{}</span>"#, html));
    }

    // 递归处理子节点
    let mut children = Vec::new();
    if let Some(child_nodes) = node.get("children").and_then(|c| c.as_array()) {
        for child in child_nodes {
            if let Some(child_node) =
                parse_zhixi_node(child, cloze_styles, cloze_ctx, images, temp_dir)
            {
                children.push(child_node);
            }
        }
    }

    Some(MindNode {
        id,
        content,
        children,
    })
}

#[derive(Debug)]
pub struct ZhixiCardConfig {
    pub zxm_path: String,
    pub deck_name: String,
    pub model_name: String,
    pub cloze_styles: Vec<String>,
    pub text_colors: Vec<String>,
    pub highlight_colors: Vec<String>,
    pub use_tags: bool,
    pub tags: Vec<String>,
    pub output_path: String,
}

pub async fn make_zhixi_card(
    config: ZhixiCardConfig,
    progress_callback: impl Fn(f64, f64, String),
) -> Result<(), io::Error> {
    // 解压文件 (0-20%)
    progress_callback(0.0, 100.0, "正在解压知悉导图文件...".to_string());
    let temp_dir = extract_zhixi(&config.zxm_path).await?;
    let path = PathBuf::from(temp_dir.clone()).join("content.json");
    progress_callback(20.0, 100.0, "解压完成".to_string());

    // 解析JSON (20-40%)
    progress_callback(20.0, 100.0, "解析导图结构...".to_string());
    let json = serde_json::from_reader(fs::File::open(&path)?)
        .map_err(|e| io::Error::new(io::ErrorKind::InvalidData, e))?;
    let root = parse_zhixi_json(&json)
        .ok_or_else(|| io::Error::new(io::ErrorKind::Other, "无效的导图格式"))?;

    // 处理样式配置
    let cloze_styles: Vec<StyleAttribute> = config
        .cloze_styles
        .iter()
        .filter_map(|s| match s.as_str() {
            "text_color" => Some(StyleAttribute::Colors(config.text_colors.clone())),
            "text_highlight" => Some(StyleAttribute::Highlights(config.highlight_colors.clone())),
            "italic" => Some(StyleAttribute::Italic),
            "bold" => Some(StyleAttribute::Bold),
            "strikethrough" => Some(StyleAttribute::StrikeThrough),
            "underline" => Some(StyleAttribute::Underline),
            _ => None,
        })
        .collect();

    // 解析节点树 (40-60%)
    progress_callback(40.0, 100.0, "构建节点树...".to_string());
    let mut images = Vec::new();
    let mut cloze_ctx = ClozeContext::new();
    let mind_node = parse_zhixi_node(
        &root,
        Some(&cloze_styles),
        &mut cloze_ctx,
        &mut images,
        &temp_dir,
    )
    .ok_or_else(|| io::Error::new(io::ErrorKind::Other, "解析节点失败"))?;

    // 生成Anki笔记 (60-80%)
    progress_callback(60.0, 100.0, "生成记忆卡片...".to_string());
    let map_id = Ulid::new().to_string();
    let notes = mind_node_to_anki_notes(
        map_id.to_string(),
        &mind_node,
        config.deck_name.clone(),
        config.tags.clone(),
    );

    // 收集媒体文件
    let mut media_files: Vec<String> = images
        .iter()
        .map(|img| {
            PathBuf::from(temp_dir.clone())
                .join("resources")
                .join(img)
                .to_string_lossy()
                .to_string()
        })
        .collect();
    let tree_js_path = PathBuf::from(temp_dir.clone()).join(format!("__{}.js", map_id.to_string()));
    fs::write(&tree_js_path, &mind_node.to_js())?;
    media_files.push(tree_js_path.to_string_lossy().to_string());
    // 生成APKG文件 (80-100%)
    progress_callback(80.0, 100.0, "打包输出文件...".to_string());
    crate::anki::models::gen_apkg(
        notes,
        Some(media_files),
        &config.output_path,
        None,
        false,
        Some(vec!["mindmap_card".to_string()]),
    )
    .await?;
    progress_callback(100.0, 100.0, "导出完成".to_string());
    Ok(())
}

#[cfg(test)]
mod tests {
    use rinf::debug_print;
    use tokio;

    use super::*;
    use std::fs;
    use std::path::PathBuf;
    const TEST_XMIND_PATH: &str = "/Users/<USER>/Downloads/每日工作计划 (1).zxm";

    // #[tokio::test]
    async fn test_make_zhixi_card_apkg() {
        let xmind_path = TEST_XMIND_PATH;
        let parent_dir = Path::new(xmind_path).parent().unwrap();
        let output_path = parent_dir.join("output.apkg");

        let config = ZhixiCardConfig {
            zxm_path: xmind_path.to_string(),
            deck_name: "Test Deck".to_string(),
            model_name: "Kevin Mindmap Card v3".to_string(),
            cloze_styles: vec![
                "bold".to_string(),
                "text_color".to_string(),
                "text_highlight".to_string(),
            ],
            text_colors: vec!["#7a64ff".to_string()],
            highlight_colors: vec!["#72d9ff".to_string()],
            use_tags: false,
            tags: vec!["test".to_string()],
            output_path: output_path.to_string_lossy().to_string(),
        };

        let result = make_zhixi_card(config, |progress, total, msg| {
            println!("Progress: {}/{} - {}", progress, total, msg);
        })
        .await;
        dbg!(&result);
        assert!(result.is_ok());
        assert!(Path::new(&output_path).exists());
    }
}
