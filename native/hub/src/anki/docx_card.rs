#![allow(unused)]

use anyhow::{Context, Result};
use docx2html::DocxDocument;
use regex::Regex;
use rinf::debug_print;
use std::fs::File;
use std::io::Write;

pub fn convert_docx2html(
    docx_path: &str,
    output_path: &str,
    escape_latex_for_js: bool,
) -> Result<String> {
    // 加载DOCX文档
    let doc = DocxDocument::from_file(docx_path, Some(escape_latex_for_js))
        .with_context(|| format!("Failed to open DOCX file: {}", docx_path))?;

    // 转换为HTML字符串
    let html = doc
        .to_html()
        .with_context(|| "Failed to convert DOCX to HTML")?;
    let mut file = File::create(output_path)
        .with_context(|| format!("Failed to create output file: {}", output_path))?;
    file.write_all(html.as_bytes())
        .with_context(|| format!("Failed to write HTML to file: {}", output_path))?;
    Ok(html)
}
