#![allow(unused)]

use crate::anki::models::{gen_apkg, get_model, AnkiNote};
use genanki_rs::{Deck, Note, Package};
use regex::Regex;
use rinf::debug_print;
use std::collections::HashMap;
use std::fs;
use std::path::Path;

pub async fn make_judge_card(
    doc_path: &str,
    parent_deck: &str,
    address: Option<&str>,
    is_create_subdeck: bool,
    subdeck_prefix: &str,
    q_regex: &str,
    correct_regex: &str,
    wrong_regex: &str,
    remark_regex: &str,
    tags: &str,
    output_path: Option<&str>,
    progress_callback: impl Fn(f64, f64, String),
) -> Result<String, Box<dyn std::error::Error>> {
    // 分割文本
    let text_map = split_text(
        doc_path,
        q_regex,
        parent_deck,
        if is_create_subdeck {
            Some(subdeck_prefix)
        } else {
            None
        },
    )?;

    // 准备笔记数据
    let mut notes = Vec::new();
    let total_questions = text_map.values().map(|v| v.len()).sum::<usize>() as f64;
    let mut current_question = 0.0;

    // 解析标签
    let tags: Vec<String> = tags
        .split(',')
        .map(|s| s.trim().to_string())
        .filter(|s| !s.is_empty())
        .collect();

    for (deck_name, texts) in text_map {
        for text in texts {
            // 解析题目内容
            let correct_pattern = Regex::new(correct_regex)?;
            let wrong_pattern = Regex::new(wrong_regex)?;
            let remark_pattern = Regex::new(remark_regex)?;

            // 判断答案
            let is_correct = correct_pattern.is_match(&text);
            let is_wrong = wrong_pattern.is_match(&text);
            let answer = if is_correct {
                "1"
            } else if is_wrong {
                "2"
            } else {
                ""
            };

            // 提取解析
            let remark = remark_pattern
                .captures(&text)
                .and_then(|cap| cap.get(1))
                .map(|m| m.as_str())
                .unwrap_or("");

            // 清理题目文本（从答案或解析的起始位置开始删除所有后续内容）
            let mut question = text.clone();

            // 找到所有模式的最早匹配位置
            let mut earliest_pos = None;

            if let Some(m) = correct_pattern.find(&question) {
                earliest_pos =
                    Some(earliest_pos.map_or(m.start(), |pos: usize| pos.min(m.start())));
            }
            if let Some(m) = wrong_pattern.find(&question) {
                earliest_pos =
                    Some(earliest_pos.map_or(m.start(), |pos: usize| pos.min(m.start())));
            }
            if let Some(m) = remark_pattern.find(&question) {
                earliest_pos =
                    Some(earliest_pos.map_or(m.start(), |pos: usize| pos.min(m.start())));
            }

            // 如果找到任何匹配，从最早的匹配位置开始删除所有后续内容
            if let Some(pos) = earliest_pos {
                question.truncate(pos);
            }

            // 创建笔记数据
            let note = AnkiNote {
                deck_name: deck_name.clone(),
                model_name: "Kevin Choice Card v2".to_string(),
                fields: vec![
                    question.trim().to_string(),
                    "对||错".to_string(),
                    answer.to_string(),
                    remark.to_string(),
                    "".to_string(), // Notes字段留空
                ],
                tags: Some(tags.clone()),
                guid: None,
            };

            notes.push(note);

            current_question += 1.0;
            progress_callback(
                current_question,
                total_questions,
                format!("正在处理第 {} 题", current_question as i32),
            );
        }
    }
    debug_print!("notes: {:?}", notes);
    // 确定输出路径
    let output_path = if let Some(path) = output_path {
        path.to_string()
    } else {
        let parent = Path::new(doc_path)
            .parent()
            .unwrap_or_else(|| Path::new(""));
        parent.join("output.apkg").to_string_lossy().into_owned()
    };

    // 生成apkg文件
    gen_apkg(
        notes,
        None,
        &output_path,
        address,
        false,
        Some(vec!["choice_card".to_string()]),
    )
    .await
    .map_err(|e| e.into())
}

/// 将文本按照规则分割成不同牌组的内容
///
/// # Arguments
/// * `doc_path` - 文档路径
/// * `split_regex` - 分割题目的正则表达式
/// * `parent_deck` - 父牌组名称
/// * `deck_prefix` - 子牌组标识前缀，如 "@@@"
///
/// # Returns
/// * `HashMap<String, Vec<String>>` - key为牌组名，value为该牌组下的题目列表
pub fn split_text(
    doc_path: &str,
    split_regex: &str,
    parent_deck: &str,
    deck_prefix: Option<&str>,
) -> Result<HashMap<String, Vec<String>>, Box<dyn std::error::Error>> {
    // 读取文档内容
    let content = fs::read_to_string(doc_path)?;
    let lines: Vec<&str> = content.lines().collect();

    // 编译正则表达式
    let split_pattern = Regex::new(&regex::escape(split_regex))?;

    let mut result: HashMap<String, Vec<String>> = HashMap::new();
    let mut current_deck = parent_deck.to_string();
    let mut current_text = String::new();

    // 处理子牌组标识
    let deck_prefix = deck_prefix.unwrap_or("");
    let is_using_prefix = !deck_prefix.is_empty();

    let mut current_level_decks: Vec<String> = vec![parent_deck.to_string()];
    let mut last_level = 0;

    for (i, line) in lines.iter().enumerate() {
        if is_using_prefix && line.starts_with(deck_prefix) {
            // 如果有未处理的文本，先保存
            if !current_text.trim().is_empty() {
                result
                    .entry(current_deck.clone())
                    .or_insert_with(Vec::new)
                    .push(current_text.trim().to_string());
                current_text.clear();
            }

            // 计算子牌组层级
            let prefix_char = deck_prefix.chars().next().unwrap();
            let level = line.chars().take_while(|&c| c == prefix_char).count();

            // 提取并清理子牌组名称
            let deck_name = line[level..].trim().to_string();

            // 更新当前层级的牌组
            if level <= last_level {
                // 如果是同级或更浅的层级，从父牌组开始重新构建
                current_level_decks.truncate(level - 2); // 减2是因为要保留parent_deck
            }

            if current_level_decks.is_empty() {
                current_level_decks.push(parent_deck.to_string());
            }

            current_level_decks.push(deck_name);
            last_level = level;
            current_deck = current_level_decks.join("::");

            debug_print!("Level: {}, Deck: {}", level, current_deck);
            continue;
        }

        // 检查是否是新题目的开始
        if split_pattern.is_match(line) && !current_text.trim().is_empty() {
            result
                .entry(current_deck.clone())
                .or_insert_with(Vec::new)
                .push(current_text.trim().to_string());
            current_text.clear();
        }

        current_text.push_str(line);
        current_text.push('\n');

        // 处理最后一行
        if i == lines.len() - 1 && !current_text.trim().is_empty() {
            result
                .entry(current_deck.clone())
                .or_insert_with(Vec::new)
                .push(current_text.trim().to_string());
        }
    }

    Ok(result)
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs::File;
    use std::io::Write;
    use tempfile::tempdir;

    // #[test]
    fn test_split_text() -> Result<(), Box<dyn std::error::Error>> {
        // 创建临时文件
        let dir = tempdir()?;
        let file_path = dir.path().join("test.txt");
        let mut file = File::create(&file_path)?;

        // 写入测试内容，包含各种边缘情况
        let content = r#"@@@ 第一章
@@@@ 第一节
1、以下合同计价模式下，A、固定单价合同方式中承包商所承担的风险最小。
【答案】 正确
【答案解析】 本题考查的是招标采购阶段的管理任务和方法。
@@@ 第二章
@@@@ 第一节
2、根据项目目标要求，要明确项目采购哪些工程。
【答案】 错误
【答案解析】 本题考查的是招标采购阶段的管理任务和方法。
@@@@@ 第一小节
3、开展采购招标工作的主要依据是确定采用何种采购和招标方式。
【答案】 错误"#;

        write!(file, "{}", content)?;

        let result = split_text(
            file_path.to_str().unwrap(),
            r"\d+[.、]",
            "parent_deck",
            Some("@@@"),
        )?;

        // 打印结果用于调试
        dbg!(&result);

        // 验证结果
        assert!(result.contains_key("parent_deck::第一章::第一节"));
        assert!(result.contains_key("parent_deck::第二章::第一节::第一小节"));
        assert!(result.contains_key("parent_deck::第二章::第一节"));

        Ok(())
    }
}
