#![allow(unused)]
// Copyright: Ankitects Pty Ltd and contributors
// License: GNU AGPL, version 3 or later; http://www.gnu.org/licenses/agpl.html
use anki::sync::http_server::{default_ip_header, SimpleServer, SyncServerConfig};
use anyhow::Result;
use local_ip_address::list_afinet_netifas;
use rinf::debug_print;
use std::collections::HashMap;
use std::env;
use std::error::Error;
use std::fs;
use std::net::IpAddr;
use std::path::PathBuf;
use std::process::Command;
use tokio::sync::broadcast::Sender;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;

/// Represents a sync user with credentials
#[derive(Debug, Clone)]
pub struct SyncUser {
    pub username: String,
    pub password: String,
}

impl SyncUser {
    pub fn new(username: String, password: String) -> Self {
        Self { username, password }
    }
}

// 保存全局状态
static mut SHUTDOWN_SENDER: Option<Sender<()>> = None;
static mut SERVER_HANDLE: Option<JoinHandle<anyhow::Result<()>>> = None;

/// Run server with multiple users support
pub async fn run_server_multi_user(
    host: String,
    port: u16,
    data_dir: String,
    users: Vec<SyncUser>,
    max_payload: Option<usize>,
) -> anyhow::Result<()> {
    // 直接尝试关闭服务器
    let _ = stop_server().await;

    // 设置基础环境变量
    std::env::set_var("SYNC_HOST", &host);
    std::env::set_var("SYNC_PORT", port.to_string());
    std::env::set_var("SYNC_BASE", &data_dir);

    // 设置payload限制
    if let Some(payload_size) = max_payload {
        std::env::set_var("MAX_SYNC_PAYLOAD_MEGS", payload_size.to_string());
    }

    // 清除现有用户环境变量
    clear_user_env_vars();

    // 设置多用户环境变量
    for (index, user) in users.iter().enumerate() {
        let user_var = format!("SYNC_USER{}", index + 1);
        let user_value = format!("{}:{}", user.username, user.password);
        std::env::set_var(&user_var, &user_value);
        debug_print!("Set user {}: {}", index + 1, user.username);
    }

    // 创建配置
    let config = SyncServerConfig {
        host: host.parse().map_err(|e| anyhow::anyhow!("Invalid host address: {}", e))?,
        port,
        base_folder: PathBuf::from(data_dir),
        ip_header: default_ip_header(),
    };

    // 启动服务器
    let (addr, server_fut) = SimpleServer::make_server(config).await
        .map_err(|e| anyhow::anyhow!("Failed to create server: {}", e))?;

    // 创建自定义shutdown channel
    let (shutdown_sender, mut shutdown_receiver) = tokio::sync::broadcast::channel::<()>(1);

    // 保存shutdown sender
    unsafe {
        SHUTDOWN_SENDER = Some(shutdown_sender);
    }

    // 在新的任务中运行服务器，支持自定义shutdown
    let handle = tokio::spawn(async move {
        tokio::select! {
            result = server_fut => {
                match result {
                    Ok(_) => Ok(()),
                    Err(e) => Err(anyhow::anyhow!("Server error: {}", e)),
                }
            }
            _ = shutdown_receiver.recv() => {
                debug_print!("Received shutdown signal, stopping server");
                Ok(())
            }
        }
    });

    // 保存服务器句柄
    unsafe {
        SERVER_HANDLE = Some(handle);
    }

    debug_print!("Multi-user server started successfully at {} with {} users", addr, users.len());
    Ok(())
}





/// Helper function to clear user environment variables
fn clear_user_env_vars() {
    // Clear up to 10 user slots (should be more than enough)
    for i in 1..=10 {
        let user_var = format!("SYNC_USER{}", i);
        std::env::remove_var(&user_var);
    }
}

/// Get max payload size from environment variable or default
fn get_max_payload_size() -> usize {
    std::env::var("MAX_SYNC_PAYLOAD_MEGS")
        .ok()
        .and_then(|s| s.parse().ok())
        .unwrap_or(100) // Default to 100MB if not set
}



pub async fn stop_server() -> Result<()> {
    unsafe {
        // 发送关闭信号
        if let Some(sender) = &SHUTDOWN_SENDER {
            sender.send(()).expect("Failed to send shutdown signal");
            debug_print!("Shutdown signal sent");
        }

        // 等待服务器完全关闭
        if let Some(handle) = SERVER_HANDLE.take() {
            // 处理嵌套的 Result
            match handle.await {
                Ok(Ok(())) => Ok(()),
                Ok(Err(e)) => Err(anyhow::anyhow!("Server error: {}", e)),
                Err(e) => Err(anyhow::anyhow!("Join error: {}", e)),
            }?;
        }

        SHUTDOWN_SENDER = None;
    }
    Ok(())
}

pub fn is_running() -> bool {
    let host = env::var("SYNC_HOST").unwrap_or("localhost".to_string());
    let port = env::var("SYNC_PORT").unwrap_or("8080".to_string());
    std::net::TcpStream::connect(format!("{}:{}", host, port)).is_ok()
}

pub fn get_all_ip_addresses() -> Result<Vec<String>> {
    let mut ip_addresses = Vec::new();
    let if_addrs = list_afinet_netifas()?;
    for (_name, ip) in if_addrs {
        if let IpAddr::V4(ipv4) = ip {
            let ip_str = ip.to_string();
            // Filter out APIPA addresses (169.254.x.x)
            if !ip_str.starts_with("169.254.") {
                ip_addresses.push(ip_str);
            }
        }
    }
    Ok(ip_addresses)
}




