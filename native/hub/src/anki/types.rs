#![allow(unused)]
use pdfium_render::prelude::PdfiumError;
use pdfium_render::prelude::*;
use serde::{Deserialize, Serialize};
use std::fmt::format;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum PdfError {
    #[error("Lopdf Error: {0}")]
    Lopdf(#[from] lopdf::Error),
    #[error("PageRange Error: {0}")]
    PageRange(String),
    #[error("IO Error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Other Error: {0}")]
    Other(String),
    #[error("Render Error: {0}")]
    Render(String),
    #[error("Pdfium Error: {0}")]
    Pdfium(#[from] PdfiumError),
}

/// PDF注释类型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub enum AnnotationType {
    Highlight,
    Square,
    Circle,
    Underline,
    StrikeOut,
    Squiggly,
    Text,
    Popup,
    Other(String),
}

/// PDF注释信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Annotation {
    pub page_num: u32,
    pub column: u32,
    pub annot_type: AnnotationType,
    pub rect: [f64; 4],
    pub quad_points: Option<Vec<f64>>,
    pub color: Option<[f32; 3]>,
    pub contents: Option<String>,
    pub creation_date: Option<String>,
    pub mod_date: Option<String>,
    pub id: Option<String>,
}
