#![allow(unused)]

use anyhow::{Context, Result};
use chrono::Local;
use once_cell::sync::OnceCell;
use paddle_ocr_rs::ocr_lite::OcrLite;
use rinf::debug_print;
use serde_json;
use std::fs::File;
use std::io::Write;
use std::path::{Path, PathBuf};
use std::sync::Mutex;

static OCR_INSTANCE: OnceCell<Mutex<OcrLite>> = OnceCell::new();

pub async fn get_ocr() -> Result<&'static Mutex<OcrLite>> {
    // 1. 首先，在 async 函数上下文中执行所有 await 操作
    let temp_dir = crate::anki::utils::get_temp_dir().await?;

    // 2. 然后，在同步的闭包中完成初始化
    //    这个闭包捕获了上面的 temp_dir 变量
    OCR_INSTANCE.get_or_try_init(|| {
        let det_model = Path::new(&temp_dir)
            .join("ocr_models")
            .join("ch_PP-OCRv5_mobile_det.onnx")
            .to_str()
            .unwrap()
            .to_string();
        let cls_model = Path::new(&temp_dir)
            .join("ocr_models")
            .join("ch_ppocr_mobile_v2.0_cls_infer.onnx")
            .to_str()
            .unwrap()
            .to_string();
        let rec_model = Path::new(&temp_dir)
            .join("ocr_models")
            .join("ch_PP-OCRv5_rec_mobile_infer.onnx")
            .to_str()
            .unwrap()
            .to_string();
        let num_thread = 2;

        // 检查文件是否存在并验证文件大小
        if !Path::new(&det_model).exists() {
            debug_print!("[ERROR] Detection model file not found: {}", det_model);
            return Err(anyhow::anyhow!("Detection model file not found"));
        } else {
            // 检查文件大小
            let metadata = std::fs::metadata(&det_model).map_err(|e| {
                debug_print!("[ERROR] Failed to get detection model metadata: {}", e);
                anyhow::anyhow!("Failed to get detection model metadata: {}", e)
            })?;
            if metadata.len() < 100 * 1024 {
                // 小于100KB认为文件无效
                debug_print!(
                    "[ERROR] Detection model file too small: {} bytes",
                    metadata.len()
                );
                return Err(anyhow::anyhow!(
                    "Detection model file too small or corrupted"
                ));
            }
        }

        if !Path::new(&cls_model).exists() {
            debug_print!("[ERROR] Classification model file not found: {}", cls_model);
            return Err(anyhow::anyhow!("Classification model file not found"));
        } else {
            let metadata = std::fs::metadata(&cls_model).map_err(|e| {
                debug_print!("[ERROR] Failed to get classification model metadata: {}", e);
                anyhow::anyhow!("Failed to get classification model metadata: {}", e)
            })?;
            if metadata.len() < 100 * 1024 {
                debug_print!(
                    "[ERROR] Classification model file too small: {} bytes",
                    metadata.len()
                );
                return Err(anyhow::anyhow!(
                    "Classification model file too small or corrupted"
                ));
            }
        }

        if !Path::new(&rec_model).exists() {
            debug_print!("[ERROR] Recognition model file not found: {}", rec_model);
            return Err(anyhow::anyhow!("Recognition model file not found"));
        } else {
            let metadata = std::fs::metadata(&rec_model).map_err(|e| {
                debug_print!("[ERROR] Failed to get recognition model metadata: {}", e);
                anyhow::anyhow!("Failed to get recognition model metadata: {}", e)
            })?;
            if metadata.len() < 100 * 1024 {
                debug_print!(
                    "[ERROR] Recognition model file too small: {} bytes",
                    metadata.len()
                );
                return Err(anyhow::anyhow!(
                    "Recognition model file too small or corrupted"
                ));
            }
        }

        debug_print!("[INFO] det_model: {}", det_model);
        debug_print!("[INFO] cls_model: {}", cls_model);
        debug_print!("[INFO] rec_model: {}", rec_model);
        debug_print!("[INFO] num_thread: {}", num_thread);

        let mut ocr = OcrLite::new();
        debug_print!("[INFO] Initializing OCR models...");
        match ocr.init_models(&det_model, &cls_model, &rec_model, num_thread) {
            Ok(_) => {
                debug_print!("[INFO] OCR models initialized successfully");
                Ok(Mutex::new(ocr))
            }
            Err(e) => {
                debug_print!("[ERROR] Failed to init OCR models: {:?}", e);
                Err(anyhow::anyhow!("Failed to init OCR models: {:?}", e))
            }
        }
    })
}

pub async fn image_ocr(
    image_path_list: &Vec<String>,
    provider: &str,
    model_name: &str,
    api_key: &str,
    base_url: &str,
    system_prompt: &str,
    protocol_type: &str,
    merge_output: bool,
    response_format: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Sync,
) -> Result<Vec<String>> {
    if provider == "paddle-ocr" {
        let padding = 50;
        let max_side_len = 1024;
        let box_score_thresh = 0.5;
        let box_thresh = 0.3;
        let unclip_ratio = 1.6;
        let do_angle = false;
        let most_angle = false;
        let mut results = Vec::new();

        // 报告OCR初始化开始
        progress_callback(0.0, 100.0, "正在初始化OCR引擎...".to_string());

        // 优化OCR初始化处理
        let ocr_result = get_ocr().await;
        if let Err(e) = &ocr_result {
            debug_print!("[ERROR] Failed to initialize OCR: {:?}", e);
            // 对于每个图像路径创建一个错误JSON响应
            if response_format == "json" {
                for image_path in image_path_list {
                    let error_json = serde_json::json!({
                        "image_path": image_path,
                        "error": format!("OCR初始化失败: {}", e)
                    });
                    results.push(serde_json::to_string(&error_json).unwrap_or_default());
                }
                return Ok(results);
            } else {
                return Err(anyhow::anyhow!("Failed to initialize OCR: {}", e));
            }
        }
        let ocr_mutex = ocr_result?;

        // 获取锁，使用match处理锁获取失败的情况
        let ocr_lock_result = ocr_mutex.lock();
        if let Err(e) = &ocr_lock_result {
            debug_print!("[ERROR] Failed to lock OCR mutex: {:?}", e);
            // 对于每个图像路径创建一个错误JSON响应
            if response_format == "json" {
                for image_path in image_path_list {
                    let error_json = serde_json::json!({
                        "image_path": image_path,
                        "error": format!("OCR引擎锁定失败: {}", e)
                    });
                    results.push(serde_json::to_string(&error_json).unwrap_or_default());
                }
                return Ok(results);
            } else {
                return Err(anyhow::anyhow!("Failed to lock OCR mutex: {}", e));
            }
        }
        let mut ocr = ocr_lock_result.unwrap();

        progress_callback(
            10.0,
            100.0,
            "OCR引擎初始化完成，开始处理图片...".to_string(),
        );

        if merge_output {
            if image_path_list.is_empty() {
                return Err(anyhow::anyhow!("No images provided"));
            }
            let mut merged_text = String::new();
            let total_images = image_path_list.len();
            for (index, image_path) in image_path_list.iter().enumerate() {
                // 报告当前处理的图片进度
                let progress = 10.0 + (index as f64 / total_images as f64) * 90.0;
                progress_callback(
                    progress,
                    100.0,
                    format!("正在处理第 {}/{} 张图片", index + 1, total_images),
                );
                if !Path::new(image_path).exists() {
                    debug_print!("[ERROR] Image file not found: {}", image_path);
                    merged_text.push_str(&format!("{}\t{}\n", image_path, "[文件不存在]"));
                    continue;
                }
                let res = ocr.detect_from_path(
                    image_path,
                    padding,
                    max_side_len,
                    box_score_thresh,
                    box_thresh,
                    unclip_ratio,
                    do_angle,
                    most_angle,
                );
                match res {
                    Ok(ocr_result) => {
                        if response_format == "json" {
                            let mut json_data = serde_json::Map::new();
                            json_data.insert(
                                "image_path".to_string(),
                                serde_json::Value::String(image_path.clone()),
                            );

                            let blocks: Vec<serde_json::Value> = ocr_result
                                .text_blocks
                                .iter()
                                .map(|tb| {
                                    let mut block = serde_json::Map::new();
                                    block.insert(
                                        "text".to_string(),
                                        serde_json::Value::String(tb.text.clone()),
                                    );

                                    let box_points: Vec<serde_json::Value> = tb
                                        .box_points
                                        .iter()
                                        .map(|point| {
                                            let mut point_map = serde_json::Map::new();
                                            point_map.insert(
                                                "x".to_string(),
                                                serde_json::Value::Number(
                                                    serde_json::Number::from(point.x),
                                                ),
                                            );
                                            point_map.insert(
                                                "y".to_string(),
                                                serde_json::Value::Number(
                                                    serde_json::Number::from(point.y),
                                                ),
                                            );
                                            serde_json::Value::Object(point_map)
                                        })
                                        .collect();

                                    block.insert(
                                        "box_points".to_string(),
                                        serde_json::Value::Array(box_points),
                                    );
                                    block.insert(
                                        "score".to_string(),
                                        serde_json::Value::Number(
                                            serde_json::Number::from_f64(tb.box_score as f64)
                                                .unwrap_or(serde_json::Number::from(0)),
                                        ),
                                    );

                                    serde_json::Value::Object(block)
                                })
                                .collect();

                            json_data
                                .insert("blocks".to_string(), serde_json::Value::Array(blocks));
                            merged_text.push_str(&format!(
                                "{}\n",
                                serde_json::to_string(&json_data).unwrap_or_default()
                            ));
                        } else {
                            let text = ocr_result
                                .text_blocks
                                .iter()
                                .map(|tb| tb.text.clone())
                                .collect::<Vec<_>>()
                                .join(" ");
                            debug_print!("text: {}", &text);
                            merged_text.push_str(&format!("{}\n", text));
                        }
                    }
                    Err(e) => {
                        debug_print!("[ERROR] OCR detection failed for {}: {:?}", image_path, e);
                        merged_text.push_str(&format!("{}\t[识别失败:{}]\n", image_path, e));
                    }
                }
            }
            results.push(merged_text);
        } else {
            let total_images = image_path_list.len();
            for (index, image_path) in image_path_list.iter().enumerate() {
                // 报告当前处理的图片进度
                let progress = 10.0 + (index as f64 / total_images as f64) * 90.0;
                progress_callback(
                    progress,
                    100.0,
                    format!("正在处理第 {}/{} 张图片", index + 1, total_images),
                );
                if response_format == "json" {
                    if !Path::new(image_path).exists() {
                        debug_print!("[ERROR] Image file not found: {}", image_path);
                        let error_json = serde_json::json!({
                            "image_path": image_path,
                            "error": "文件不存在"
                        });
                        results.push(serde_json::to_string(&error_json).unwrap_or_default());
                        continue;
                    }

                    let res = ocr.detect_from_path(
                        image_path,
                        padding,
                        max_side_len,
                        box_score_thresh,
                        box_thresh,
                        unclip_ratio,
                        do_angle,
                        most_angle,
                    );

                    match res {
                        Ok(ocr_result) => {
                            let mut json_data = serde_json::Map::new();
                            json_data.insert(
                                "image_path".to_string(),
                                serde_json::Value::String(image_path.clone()),
                            );

                            let blocks: Vec<serde_json::Value> = ocr_result
                                .text_blocks
                                .iter()
                                .map(|tb| {
                                    let mut block = serde_json::Map::new();
                                    block.insert(
                                        "text".to_string(),
                                        serde_json::Value::String(tb.text.clone()),
                                    );

                                    let box_points: Vec<serde_json::Value> = tb
                                        .box_points
                                        .iter()
                                        .map(|point| {
                                            let mut point_map = serde_json::Map::new();
                                            point_map.insert(
                                                "x".to_string(),
                                                serde_json::Value::Number(
                                                    serde_json::Number::from(point.x),
                                                ),
                                            );
                                            point_map.insert(
                                                "y".to_string(),
                                                serde_json::Value::Number(
                                                    serde_json::Number::from(point.y),
                                                ),
                                            );
                                            serde_json::Value::Object(point_map)
                                        })
                                        .collect();

                                    block.insert(
                                        "box_points".to_string(),
                                        serde_json::Value::Array(box_points),
                                    );
                                    block.insert(
                                        "score".to_string(),
                                        serde_json::Value::Number(
                                            serde_json::Number::from_f64(tb.box_score as f64)
                                                .unwrap_or(serde_json::Number::from(0)),
                                        ),
                                    );

                                    serde_json::Value::Object(block)
                                })
                                .collect();

                            json_data
                                .insert("blocks".to_string(), serde_json::Value::Array(blocks));
                            results.push(serde_json::to_string(&json_data).unwrap_or_default());
                        }
                        Err(e) => {
                            debug_print!(
                                "[ERROR] OCR detection failed for {}: {:?}",
                                image_path,
                                e
                            );
                            let error_json = serde_json::json!({
                                "image_path": image_path,
                                "error": format!("识别失败: {}", e)
                            });
                            results.push(serde_json::to_string(&error_json).unwrap_or_default());
                        }
                    }
                } else {
                    let mut text = String::new();
                    if !Path::new(image_path).exists() {
                        debug_print!("[ERROR] Image file not found: {}", image_path);
                        text.push_str(&format!("{}\t{}", image_path, "[文件不存在]"));
                        results.push(text);
                        continue;
                    }
                    let res = ocr.detect_from_path(
                        image_path,
                        padding,
                        max_side_len,
                        box_score_thresh,
                        box_thresh,
                        unclip_ratio,
                        do_angle,
                        most_angle,
                    );
                    match res {
                        Ok(ocr_result) => {
                            let ocr_text = ocr_result
                                .text_blocks
                                .iter()
                                .map(|tb| tb.text.clone())
                                .collect::<Vec<_>>()
                                .join(" ");
                            debug_print!("text: {}", &ocr_text);
                            text.push_str(&format!("{}", ocr_text));
                        }
                        Err(e) => {
                            debug_print!(
                                "[ERROR] OCR detection failed for {}: {:?}",
                                image_path,
                                e
                            );
                            text.push_str(&format!("{}\t[识别失败:{}]", image_path, e));
                        }
                    }
                    results.push(text);
                }
            }
        }
        Ok(results)
    } else if provider == "openrouter" {
        // TODO: 实现OpenRouter OCR
        return Err(anyhow::anyhow!("Unsupported provider: {}", provider));
    } else if provider == "siliconflow" {
        // TODO: 实现SiliconFlow OCR
        return Err(anyhow::anyhow!("Unsupported provider: {}", provider));
    } else if provider == "custom" {
        // TODO: 实现自定义OCR
        return Err(anyhow::anyhow!("Unsupported provider: {}", provider));
    } else {
        return Err(anyhow::anyhow!("Unsupported provider: {}", provider));
    }
}
