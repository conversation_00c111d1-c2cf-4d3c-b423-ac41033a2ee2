#![allow(unused)]

use super::models::{gen_apkg, get_choice_card_assets, AnkiNote};
use crate::anki::utils::save_to_temp_file;
use calamine::{open_workbook, Data, Error as XlsxError, Reader, Xlsx};
use regex;
use serde::Serialize;
use std::collections::HashMap;
use std::io;
use std::path::Path;
use std::sync::Arc;

pub type ExcelValidateResult = HashMap<String, Vec<String>>;

pub fn validate_excel(file_path: &str) -> Result<ExcelValidateResult, String> {
    // 打开工作簿
    let mut workbook: Xlsx<_> =
        open_workbook(file_path).map_err(|e| format!("无法打开Excel文件: {}", e))?;

    let mut result = HashMap::new();

    // 遍历所有sheet名称
    let sheet_names = workbook.sheet_names().to_owned();

    // 处理每个sheet
    for sheet_name in sheet_names {
        // worksheet_range 返回 Result，需要正确处理错误
        if let Ok(range) = workbook.worksheet_range(&sheet_name) {
            let mut columns = Vec::new();

            // 获取第一行作为列名
            if let Some(first_row) = range.rows().next() {
                for cell in first_row.iter() {
                    let col_name = cell.to_string();
                    if !col_name.is_empty() {
                        columns.push(col_name);
                    }
                }
            }

            result.insert(sheet_name, columns);
        }
    }

    Ok(result)
}

pub async fn make_excel_card(
    file_path: &str,
    sheet_name: &str,
    address: Option<&str>,
    parent_deck: &str,
    card_model: &str,
    field_mappings: &HashMap<String, Vec<i32>>,
    fields: &[String],
    sub_deck_cols: &[String],
    tag_cols: &[String],
    guid_col: &str,
    tags: &[String],
    output_path: &str,
    op_type: &str,
    answer_position: &str,
    answer_pattern: &str,
    option_position: &str,
    option_pattern: &str,
    judge_answer_position: &str,
    correct_pattern: &str,
    wrong_pattern: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + 'static,
) -> Result<(), std::io::Error> {
    let progress = Arc::new(progress_callback);

    // 打开Excel文件
    let mut workbook: Xlsx<_> = open_workbook(file_path).map_err(|_| {
        std::io::Error::new(
            std::io::ErrorKind::Other,
            format!("无法打开Excel文件: {}", file_path),
        )
    })?;

    // 获取指定的工作表
    let range = workbook.worksheet_range(sheet_name).map_err(|_| {
        std::io::Error::new(
            std::io::ErrorKind::NotFound,
            format!("Sheet not found: {}", sheet_name),
        )
    })?;

    let rows: Vec<_> = range.rows().collect();
    let total_rows = rows.len() as f64;

    // 收集所有笔记
    let mut notes = Vec::new();

    // 跳过标题行，从第二行开始处理
    for (row_idx, row) in rows.iter().enumerate().skip(1) {
        // 更新进度
        progress(
            row_idx as f64,
            total_rows,
            format!("正在处理第 {}/{} 行", row_idx, rows.len()),
        );

        // 处理字段映射时添加错误处理
        let mut fields_values = vec!["".to_string(); fields.len()];
        for (field_idx, field_name) in fields.iter().enumerate() {
            if let Some(col_indices) = field_mappings.get(field_name) {
                let mut field_value = String::new();

                // 添加错误处理
                match process_field_value(
                    field_name,
                    col_indices,
                    row,
                    op_type,
                    answer_position,
                    answer_pattern,
                    option_position,
                    option_pattern,
                    judge_answer_position,
                    correct_pattern,
                    wrong_pattern,
                ) {
                    Ok(value) => field_value = value,
                    Err(e) => {
                        return Err(std::io::Error::new(
                            std::io::ErrorKind::Other,
                            format!("处理第 {} 行 {} 字段时出错: {}", row_idx + 1, field_name, e),
                        ))
                    }
                }

                fields_values[field_idx] = field_value;
            }
        }

        // 构建子牌组名称
        let mut deck_name = parent_deck.to_string();
        if !sub_deck_cols.is_empty() {
            let sub_decks: Vec<String> = sub_deck_cols
                .iter()
                .filter_map(|col_str| {
                    col_str
                        .parse::<usize>()
                        .ok()
                        .and_then(|col| row.get(col))
                        .map(|cell| cell.to_string())
                })
                .collect();
            if !sub_decks.is_empty() {
                deck_name = format!("{}{}{}", deck_name, "::", sub_decks.join("::"));
            }
        }

        // 收集标签
        let mut note_tags = tags.to_vec();
        if !tag_cols.is_empty() {
            let tag_values: Vec<String> = tag_cols
                .iter()
                .filter_map(|col_str| {
                    col_str
                        .parse::<usize>()
                        .ok()
                        .and_then(|col| row.get(col))
                        .map(|cell| cell.to_string())
                        .filter(|s| !s.is_empty()) // 过滤掉空值
                })
                .collect();

            // 只生成一个完整的多级标签
            if !tag_values.is_empty() {
                note_tags.push(tag_values.join("::"));
            }
        }

        // 获取GUID
        let guid = if !guid_col.is_empty() {
            if let Ok(col_idx) = guid_col.parse::<usize>() {
                row.get(col_idx).map(|cell| cell.to_string())
            } else {
                None
            }
        } else {
            None
        };

        // 创建笔记数据
        let note = AnkiNote {
            deck_name,
            model_name: card_model.to_string(),
            fields: fields_values,
            tags: Some(note_tags),
            guid,
        };

        notes.push(note);
    }
    // 生成apkg文件
    gen_apkg(
        notes,
        None,
        output_path,
        address,
        false,
        Some(vec!["choice_card".to_string()]),
    )
    .await?;

    Ok(())
}

// 将字段处理逻辑提取到单独的函数中
fn process_field_value(
    field_name: &str,
    col_indices: &[i32],
    row: &[Data],
    op_type: &str,
    answer_position: &str,
    answer_pattern: &str,
    option_position: &str,
    option_pattern: &str,
    judge_answer_position: &str,
    correct_pattern: &str,
    wrong_pattern: &str,
) -> Result<String, String> {
    let mut field_value = String::new();

    match op_type {
        "choice" => match field_name {
            "Options" => {
                field_value = process_options(col_indices, row, option_position, option_pattern)?;
            }
            "Answers" => {
                field_value = process_answer(col_indices, row, answer_position, answer_pattern)?;
            }
            _ => {
                field_value = process_normal_field(
                    op_type,
                    col_indices,
                    row,
                    answer_position,
                    answer_pattern,
                )?;
            }
        },
        "judge" => {
            match field_name {
                "Options" => {
                    // 判断题固定选项
                    field_value = "对||错".to_string();
                }
                "Answers" => {
                    field_value = process_judge_answer(
                        col_indices,
                        row,
                        judge_answer_position,
                        correct_pattern,
                        wrong_pattern,
                    )?;
                }
                _ => {
                    field_value = process_normal_field(
                        op_type,
                        col_indices,
                        row,
                        judge_answer_position,
                        &format!("({})|({})", correct_pattern, wrong_pattern), // 用correct_pattern替代answer_pattern
                    )?;
                }
            }
        }
        _ => {
            field_value =
                process_normal_field(op_type, col_indices, row, answer_position, answer_pattern)?;
        }
    }

    Ok(field_value)
}

// 处理选项的辅助函数
fn process_options(
    col_indices: &[i32],
    row: &[Data],
    option_position: &str,
    option_pattern: &str,
) -> Result<String, String> {
    match option_position {
        "option_in_column" => {
            // 从多列获取选项并用||拼接
            let options: Vec<String> = col_indices
                .iter()
                .filter_map(|&idx| {
                    if idx >= 0 && (idx as usize) < row.len() {
                        Some(row[idx as usize].to_string())
                    } else {
                        None
                    }
                })
                .filter(|opt| !opt.trim().is_empty())
                .collect();
            Ok(options.join("||"))
        }
        "option_in_text" => {
            // 从单列中提取选项，解析相邻标记间的文本
            if let Some(&first_idx) = col_indices.first() {
                if first_idx >= 0 && (first_idx as usize) < row.len() {
                    let text = row[first_idx as usize].to_string();
                    let options = extract_options_between_markers(&text, option_pattern)?;
                    Ok(options.join("||"))
                } else {
                    Err("选项列索引超出范围".to_string())
                }
            } else {
                Err("未指定选项列".to_string())
            }
        }
        _ => Err(format!("不支持的选项位置类型: {}", option_position)),
    }
}
// 提取文本中相邻选项标记之间的内容
fn extract_options_between_markers(
    text: &str,
    option_pattern: &str,
) -> Result<Vec<String>, String> {
    let re = regex::Regex::new(option_pattern).map_err(|e| format!("选项匹配模式无效: {}", e))?;

    // 找到所有符合"选项标记"的位置
    let matches: Vec<_> = re.find_iter(text).collect();
    if matches.is_empty() {
        // 如果一个标签都没匹配到，可视情况返回空或将整段视为一个选项
        return Ok(vec![]);
    }

    let mut options = Vec::new();
    // 遍历所有标记，从上一个标记的结束到下一个标记的开始，提取文本
    for i in 0..matches.len() {
        // 当前标记的结束位置
        let current_end = matches[i].end();
        // 下一个标记的开始位置，如果没有下一个标记，就到文本末尾
        let next_start = if i + 1 < matches.len() {
            matches[i + 1].start()
        } else {
            text.len()
        };

        // 取出真正的选项文字，并去掉首尾空白
        let chunk = text[current_end..next_start].trim();
        if !chunk.is_empty() {
            options.push(chunk.to_string());
        }
    }

    Ok(options)
}

// 处理答案的辅助函数
fn process_answer(
    col_indices: &[i32],
    row: &[Data],
    answer_position: &str,
    answer_pattern: &str,
) -> Result<String, String> {
    match answer_position {
        "answer_in_column" => {
            if let Some(&idx) = col_indices.first() {
                if idx >= 0 && (idx as usize) < row.len() {
                    let answer = row[idx as usize].to_string();
                    convert_answer_to_numbers(&answer)
                } else {
                    Err("答案列索引超出范围".to_string())
                }
            } else {
                Err("未指定答案列".to_string())
            }
        }
        "answer_in_text" => {
            if let Some(&idx) = col_indices.first() {
                if idx >= 0 && (idx as usize) < row.len() {
                    let text = row[idx as usize].to_string();
                    let re = regex::Regex::new(answer_pattern)
                        .map_err(|e| format!("答案匹配模式无效: {}", e))?;

                    if let Some(caps) = re.captures(&text) {
                        if let Some(answer) = caps.get(1) {
                            return convert_answer_to_numbers(answer.as_str());
                        }
                    }
                    Err("未找到答案".to_string())
                } else {
                    Err("答案列索引超出范围".to_string())
                }
            } else {
                Err("未指定答案列".to_string())
            }
        }
        _ => Err(format!("不支持的答案位置类型: {}", answer_position)),
    }
}

// 处理普通字段的辅助函数
fn process_normal_field(
    op_type: &str,
    col_indices: &[i32],
    row: &[Data],
    answer_position: &str,
    answer_pattern: &str,
) -> Result<String, String> {
    let mut values = Vec::new();

    for &idx in col_indices {
        if idx >= 0 && (idx as usize) < row.len() {
            let mut text = row[idx as usize].to_string();

            // 如果答案在文本中，需要移除答案部分
            if op_type == "choice" && answer_position == "answer_in_text" {
                if let Ok(re) = regex::Regex::new(answer_pattern) {
                    text = re.replace_all(&text, "____").trim().to_string();
                }
            }
            // 如果答案在文本中，需要移除答案部分
            if op_type == "judge" && answer_position == "answer_in_text" {
                if let Ok(re) = regex::Regex::new(answer_pattern) {
                    text = re.replace_all(&text, "    ").trim().to_string();
                }
            }

            // 将换行符替换为<br>标签
            text = replace_newlines_with_br(&text);

            if !text.is_empty() {
                values.push(text);
            }
        }
    }

    if values.is_empty() {
        Ok("".to_string())
    } else {
        Ok(values.join(" "))
    }
}

// 辅助函数：将答案字母转换为数字
fn convert_answer_to_numbers(answer: &str) -> Result<String, String> {
    let numbers: Result<Vec<String>, String> = answer
        .chars()
        .filter(|c| c.is_alphabetic())
        .map(|c| {
            let c = c.to_ascii_uppercase();
            if c >= 'A' && c <= 'Z' {
                Ok(((c as u8 - b'A' + 1) as u32).to_string())
            } else {
                Err(format!("无效的答案字母: {}", c))
            }
        })
        .collect();

    match numbers {
        Ok(nums) => {
            if nums.is_empty() {
                Ok("".to_string())
            } else {
                Ok(nums.join("||"))
            }
        }
        Err(e) => Err(e),
    }
}

// 处理判断题答案的辅助函数
fn process_judge_answer(
    col_indices: &[i32],
    row: &[Data],
    answer_position: &str,
    correct_pattern: &str,
    wrong_pattern: &str,
) -> Result<String, String> {
    if let Some(&idx) = col_indices.first() {
        if idx >= 0 && (idx as usize) < row.len() {
            let text = row[idx as usize].to_string().trim().to_uppercase();

            // 尝试匹配正确答案模式
            let correct_re = regex::Regex::new(correct_pattern)
                .map_err(|e| format!("正确答案匹配模式无效: {}", e))?;

            // 尝试匹配错误答案模式
            let wrong_re = regex::Regex::new(wrong_pattern)
                .map_err(|e| format!("错误答案匹配模式无效: {}", e))?;

            if correct_re.is_match(&text) {
                Ok("1".to_string())
            } else if wrong_re.is_match(&text) {
                Ok("2".to_string())
            } else {
                // 如果都不匹配,尝试直接匹配常见的判断题答案格式
                match text.trim().to_uppercase().as_str() {
                    "T" | "TRUE" | "Y" | "YES" | "√" | "对" | "是" => Ok("1".to_string()),
                    "F" | "FALSE" | "N" | "NO" | "×" | "错" | "否" => Ok("2".to_string()),
                    _ => Err("未找到有效的判断题答案".to_string()),
                }
            }
        } else {
            Err("答案列索引超出范围".to_string())
        }
    } else {
        Err("未指定答案列".to_string())
    }
}

// 辅助函数：将换行符替换为HTML的<br>标签
fn replace_newlines_with_br(text: &str) -> String {
    text.replace("\n", "<br>").replace("\r\n", "<br>")
}

#[cfg(test)]
mod tests {
    use super::*;
    use calamine::{Sheets, Xlsx, XlsxError};
    use std::fs::File;
    use std::io::prelude::*;
    use std::path::PathBuf;
    use std::sync::Arc;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_make_excel_card() {
        // let excel_path = Path::new("/Users/<USER>/Downloads/答案独占一列_选项独占一列.xlsx");
        let excel_path = Path::new("/Users/<USER>/Downloads/答案位于题干_选项独占一列.xlsx");
        let output_path = excel_path.parent().unwrap().join("output.apkg");
        // 进度回调函数
        let progress_callback = move |current: f64, total: f64, message: String| {};

        // 字段映射
        let mut field_mappings = HashMap::new();
        field_mappings.insert("Question".to_string(), vec![0]);
        field_mappings.insert("Options".to_string(), vec![1]);
        field_mappings.insert("Answers".to_string(), vec![0]);

        let result = make_excel_card(
            excel_path.to_str().unwrap(),
            "Sheet1",
            None,
            "测试牌组",
            "Kevin Choice Card v2",
            &field_mappings,
            &[
                "Question".to_string(),
                "Options".to_string(),
                "Answers".to_string(),
                "Remarks".to_string(),
                "Notes".to_string(),
            ],
            &[],                   // 无子牌组
            &[],                   // 无标签列
            "",                    // 无GUID列
            &["测试".to_string()], // 标签
            output_path.to_str().unwrap(),
            "choice",              // 操作类型
            "answer_in_text",      // 答案位置
            "([A-D]+)",            // 答案模式
            "option_in_text",      // 选项位置
            "[A-G]\\s*[.．、：:]", // 选项模式
            "",                    // 判断题答案位置
            "",                    // 正确模式
            "",                    // 错误模式
            progress_callback,
        )
        .await;

        // 预期在没有有效Excel文件时会失败
        assert!(result.is_err());
        assert!(false);
    }
}
