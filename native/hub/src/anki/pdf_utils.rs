#![allow(unused)]

use encoding_rs::{GBK, UTF_8, WINDOWS_1252};
use futures::TryFutureExt;
use html_escape::decode_html_entities;
use lopdf::Dictionary;
use lopdf::{xobject::PdfImage, Document, Object, ObjectId};
use pdfium_render::prelude::PdfiumError;
use pdfium_render::prelude::*;
use rinf::debug_print;
use serde::{Deserialize, Serialize};
use serde_json;
use std::collections::BTreeMap;
use std::collections::HashMap;
use std::collections::HashSet;
use std::fmt::format;
use std::fs;
use std::io::Write;
use std::path::{Path, PathBuf};
use thiserror::Error;

use crate::anki::cmd;
use crate::anki::types::{Annotation, AnnotationType, PdfError};
/// 解析页码范围字符串，返回有序的页码列表
/// 支持格式：
/// - 单页："1"
/// - 连续范围："1-3"
/// - 多个范围："1-3,6-7,9-N"
/// - N 表示文档最后一页
pub fn parse_page_ranges(range_str: &str, total_pages: u32) -> Result<Vec<u32>, String> {
    let mut pages = Vec::new();

    // 处理空字符串
    if range_str.trim().is_empty() {
        return Ok((1..=total_pages).collect());
    }

    // 按逗号分割多个范围
    for range in range_str.split(',') {
        let range = range.trim();

        // 处理单个范围
        if range.contains('-') {
            let parts: Vec<&str> = range.split('-').collect();
            if parts.len() != 2 {
                return Err(format!("无效的页码范围格式: {}", range));
            }

            // 解析起始页码
            let start: u32 = parts[0]
                .parse()
                .map_err(|_| format!("无效的起始页码: {}", parts[0]))?;

            // 解析结束页码，处理 'N' 的情况
            let end: u32 = if parts[1].trim().eq_ignore_ascii_case("n") {
                total_pages
            } else {
                parts[1]
                    .parse()
                    .map_err(|_| format!("无效的结束页码: {}", parts[1]))?
            };

            // 验证页码范围
            if start == 0 || end == 0 {
                return Err("页码必须大于0".to_string());
            }
            if end > total_pages || start > total_pages {
                return Err(format!("页码超出文档范围，文档共 {} 页", total_pages));
            }

            // 支持正序和倒序
            if start <= end {
                pages.extend(start..=end);
            } else {
                pages.extend((end..=start).rev());
            }
        } else {
            // 处理单页
            let page: u32 = if range.trim().eq_ignore_ascii_case("n") {
                total_pages
            } else {
                range
                    .parse()
                    .map_err(|_| format!("无效的结束页码: {}", range))?
            };

            if page == 0 || page > total_pages {
                return Err(format!("页码 {} 超出有效范围 1-{}", page, total_pages));
            }

            pages.push(page);
        }
    }
    Ok(pages)
}

pub fn get_pdfium(libpath: Option<String>) -> Result<Pdfium, PdfError> {
    let path = if let Some(path) = libpath {
        path
    } else {
        #[cfg(target_os = "macos")]
        let path = "/Library/Application Support/PDF Guru Anki2/".to_string();
        #[cfg(target_os = "ios")]
        let path = "./pdfium.xcframework/".to_string();
        #[cfg(not(any(target_os = "macos", target_os = "ios")))]
        let path = "./".to_string();
        path
    };
    #[cfg(any(
        target_os = "windows",
        target_os = "macos",
        target_os = "linux",
        target_os = "android"
    ))]
    {
        let bindings = Pdfium::bind_to_library(Pdfium::pdfium_platform_library_name_at_path(&path))
            .or_else(|_| Pdfium::bind_to_system_library())
            .map_err(PdfError::Pdfium)?;
        Ok(Pdfium::new(bindings))

        // 静态链接库
        // Ok(Pdfium::new(
        //     Pdfium::bind_to_statically_linked_library().unwrap(),
        // ))
    }
    #[cfg(target_os = "ios")]
    {
        // 静态链接
        Ok(Pdfium::new(
            Pdfium::bind_to_statically_linked_library().unwrap(),
        ))
    }
}

/// 获取PDF文件的页面大小
/// 返回一个Vec，每个元素是一个[f32; 4]，表示页面的左、底、右、上边界
pub fn get_pdf_page_size(path: &str) -> Result<Vec<[f32; 4]>, PdfError> {
    let pdfium = get_pdfium(None).map_err(|e| PdfError::Other(e.to_string()))?;
    let document = pdfium
        .load_pdf_from_file(path, None)
        .map_err(|e| PdfError::Other(e.to_string()))?;
    let pages = document.pages();
    let page_sizes = pages
        .iter()
        .map(|page| {
            let rect = page.page_size();
            [
                rect.left().value,
                rect.bottom().value,
                rect.right().value,
                rect.top().value,
            ]
        })
        .collect();
    Ok(page_sizes)
}

/// 将PDF文件导出为图片
///
/// # 参数
/// * `path` - PDF文件路径
/// * `output_path` - 输出目录路径
/// * `output_format` - 输出图片格式 ("jpg" 或 "png")
/// * `page_range` - 页面范围字符串 (如 "1-3,5,7-9")
/// * `libpath` - pdfium 库路径
/// * `is_gray` - 是否输出灰度图像
/// * `progress_callback` - 进度回调函数
///
/// # 返回值
/// 返回一个 HashMap，其中：
/// - key: 页码 (u32)
/// - value: 该页对应的图片绝对路径 (String)
pub async fn export_pdf_to_images(
    path: &str,
    page_range: &str,
    output_path: &str,
    dpi: u32,
    is_gray: bool,
    libpath: Option<String>,
    output_format: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<HashMap<u32, String>, PdfError> {
    let res = if cfg!(any(
        target_os = "windows",
        target_os = "macos",
        target_os = "linux"
    )) {
        cmd::py_pdf2img(
            path,
            page_range,
            output_path,
            dpi,
            is_gray,
            output_format,
            progress_callback,
        )
        .await
    } else {
        export_pdf_to_images_by_pdfium(
            path,
            output_path,
            output_format,
            page_range,
            libpath,
            is_gray,
            progress_callback,
        )
    };
    res
}

/// 将PDF文件导出为图片
///
/// # 参数
/// * `path` - PDF文件路径
/// * `output_path` - 输出目录路径
/// * `output_format` - 输出图片格式 ("jpg" 或 "png")
/// * `page_range` - 页面范围字符串 (如 "1-3,5,7-9")
/// * `libpath` - pdfium 库路径
/// * `is_gray` - 是否输出灰度图像
/// * `progress_callback` - 进度回调函数
///
/// # 返回值
/// 返回一个 HashMap，其中：
/// - key: 页码 (u32)
/// - value: 该页对应的图片绝对路径 (String)
pub fn export_pdf_to_images_by_pdfium(
    path: &str,
    output_path: &str,
    output_format: &str,
    page_range: &str,
    libpath: Option<String>,
    is_gray: bool,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<HashMap<u32, String>, PdfError> {
    // Ensure the output directory exists
    std::fs::create_dir_all(output_path)?;

    let pdfium = get_pdfium(libpath).map_err(|e| PdfError::Other(e.to_string()))?;
    let document = pdfium
        .load_pdf_from_file(path, None)
        .map_err(|e| PdfError::Other(e.to_string()))?;
    // Parse page range
    let pages = parse_page_ranges(page_range, document.pages().len() as u32)
        .map_err(|e| PdfError::PageRange(e))?;

    let total_pages = pages.len() as f64;
    let mut output_files = HashMap::new();

    // Only render pages in the valid range
    for (i, page_num) in pages.iter().enumerate() {
        progress_callback(i as f64, total_pages, format!("正在处理第 {} 页", page_num));

        // Convert u32 to u16 for page index
        let page_index = u16::try_from(page_num - 1)
            .map_err(|_| PdfError::Other("Page number too large".to_string()))?;

        if let Ok(page) = document.pages().get(page_index) {
            let width = page.width().value as f64;
            let height = page.height().value as f64;
            let aspect_ratio = width / height;
            let target_width = 2000.0;
            let target_height = target_width / aspect_ratio;
            let render_config = PdfRenderConfig::new()
                .set_target_width(target_width as i32)
                .set_maximum_height(target_height as i32);

            dbg!("Rendering page {}", page_num);
            let output_file = if output_format == "jpg" {
                PathBuf::from(output_path).join(format!("page-{}.jpg", page_num))
            } else {
                PathBuf::from(output_path).join(format!("page-{}.png", page_num))
            };
            // for (annotation_index, annotation) in page.annotations().iter().enumerate() {
            //     println!(
            //         "Annotation {} is of type {:?} with bounds {:?}",
            //         annotation_index,
            //         annotation.annotation_type(),
            //         annotation.bounds()
            //     );
            // }
            if is_gray {
                page.render_with_config(&render_config)
                    .map_err(|e| PdfError::Render(e.to_string()))?
                    .as_image()
                    .into_luma8()
                    .save_with_format(
                        &output_file,
                        if output_format == "jpg" {
                            image::ImageFormat::Jpeg
                        } else {
                            image::ImageFormat::Png
                        },
                    )
                    .map_err(|_| PdfError::Other("Failed to save image".to_string()))?;
            } else {
                page.render_with_config(&render_config)
                    .map_err(|e| PdfError::Render(e.to_string()))?
                    .as_image()
                    .into_rgb8()
                    .save_with_format(
                        &output_file,
                        if output_format == "jpg" {
                            image::ImageFormat::Jpeg
                        } else {
                            image::ImageFormat::Png
                        },
                    )
                    .map_err(|_| PdfError::Other("Failed to save image".to_string()))?;
            }

            // 将生成的图片绝对路径转换为字符串并添加到 HashMap 中
            let abs_path = fs::canonicalize(&output_file)?;
            output_files.insert(*page_num, abs_path.to_string_lossy().into_owned());
        }
    }

    // 完成时的回调
    progress_callback(total_pages, total_pages, "处理完成".to_string());

    Ok(output_files)
}

/// 旋转PDF指定页面
pub fn rotate_pages<P: AsRef<Path>>(
    input_path: P,
    page_range: &str,
    angle: i64,
    output_path: Option<P>,
) -> Result<(), PdfError> {
    let mut doc = Document::load(input_path.as_ref())?;
    let total_pages = doc.get_pages().len() as u32;

    // 解析页码范围
    let pages = parse_page_ranges(page_range, total_pages).map_err(PdfError::PageRange)?;

    // 处理每个页面
    for page_num in pages {
        if let Some(&page_id) = doc.get_pages().get(&page_num) {
            if let Ok(page) = doc.get_dictionary_mut(page_id) {
                let current_rotate = page.get(b"Rotate").and_then(|r| r.as_i64()).unwrap_or(0);
                let new_rotate = (current_rotate + angle) % 360;
                page.set("Rotate", Object::Integer(new_rotate));
            }
        }
    }

    let output_path = match output_path {
        Some(path) => path.as_ref().to_owned(),
        None => input_path.as_ref().to_owned(),
    };
    doc.save(output_path)?;

    Ok(())
}

/// 删除PDF指定页面
pub fn delete_pages<P: AsRef<Path>>(
    input_path: P,
    page_range: &str,
    output_path: Option<P>,
) -> Result<(), PdfError> {
    // 加载原始文档
    let mut doc = Document::load(input_path.as_ref())?;
    let total_pages = doc.get_pages().len() as u32;

    // 解析页码范围
    let pages_to_delete =
        parse_page_ranges(page_range, total_pages).map_err(PdfError::PageRange)?;

    // 删除不需要的页面
    doc.delete_pages(&pages_to_delete);

    // 清理和优化文档
    doc.prune_objects();
    doc.renumber_objects();
    doc.compress();

    // 保存文档
    let output_path = match output_path {
        Some(path) => path.as_ref().to_owned(),
        None => input_path.as_ref().to_owned(),
    };
    doc.save(output_path)?;

    Ok(())
}

/// 提取PDF指定页面到新文件
pub fn extract_pages<P: AsRef<Path>>(
    input_path: P,
    page_range: &str,
    output_path: Option<P>,
) -> Result<(), PdfError> {
    // 加载原始文档
    let mut doc = Document::load(input_path.as_ref())?;
    let total_pages = doc.get_pages().len() as u32;

    // 解析页码范围
    let pages_to_keep = parse_page_ranges(page_range, total_pages).map_err(PdfError::PageRange)?;

    // 创建要删除的页码列表（所有不在保留列表中的页码）
    let pages_to_delete: Vec<u32> = (1..=total_pages)
        .filter(|page_num| !pages_to_keep.contains(page_num))
        .collect();

    // 删除不需要的页面
    doc.delete_pages(&pages_to_delete);

    // 清理和优化文档
    doc.prune_objects();
    doc.renumber_objects();
    doc.compress();

    // 保存文档
    let output_path = match output_path {
        Some(path) => path.as_ref().to_owned(),
        None => input_path.as_ref().to_owned(),
    };
    doc.save(output_path)?;

    Ok(())
}

/// 重排PDF页面顺序（带进度回调）
/// 优化后的页面重排序实现（最小化拆分次数）
pub async fn reorder_pages(
    input_path: &str,
    page_order: &str,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<(), PdfError> {
    // 创建临时目录
    let temp_dir = crate::anki::utils::get_temp_dir()
        .await
        .map_err(|e| PdfError::Other(e.to_string()))?;

    // 解析原始文档信息
    let doc = Document::load(input_path)?;
    let total_pages = doc.get_pages().len() as u32;
    let ordered_pages =
        parse_page_ranges(page_order, total_pages).map_err(|e| PdfError::PageRange(e))?;
    dbg!("ordered_pages: {:?}", ordered_pages.clone());
    // 将有序页码转换为连续范围（智能分组）
    let ranges = group_consecutive_pages(&ordered_pages);
    dbg!("ranges: {:?}", ranges.clone());
    // 生成临时文件列表（按实际需要拆分）
    let mut temp_files = Vec::new();
    for (i, range) in ranges.iter().enumerate() {
        let temp_path = PathBuf::from(temp_dir.clone()).join(format!("part_{}.pdf", i));
        let range_str = if range.0 == range.1 {
            range.0.to_string()
        } else {
            format!("{}-{}", range.0, range.1)
        };

        // 提取连续页面范围
        extract_pages(
            input_path,
            &range_str,
            Some(temp_path.to_str().unwrap()), // 转换为&str
        )?;
        temp_files.push(temp_path);
        progress_callback(
            i as f64,
            ranges.len() as f64,
            format!("处理第 {} 页", range_str),
        );
    }

    // 合并所有临时文件
    let str_paths: Vec<&str> = temp_files.iter().map(|p| p.to_str().unwrap()).collect();
    dbg!(&str_paths);
    merge_pdfs(&str_paths, output_path).map_err(|e| PdfError::Other(e.to_string()))?; // 错误类型转换

    // 进度回调
    progress_callback(ranges.len() as f64, ranges.len() as f64, "完成".to_string());

    Ok(())
}

/// 智能分组连续页码（核心算法）
fn group_consecutive_pages(pages: &[u32]) -> Vec<(u32, u32)> {
    let mut ranges = Vec::new();
    let mut current_start = pages.first().copied().unwrap_or(0);
    let mut current_end = current_start;

    for &page in pages.iter().skip(1) {
        if page == current_end + 1 {
            current_end = page;
        } else {
            ranges.push((current_start, current_end));
            current_start = page;
            current_end = page;
        }
    }
    if !pages.is_empty() {
        ranges.push((current_start, current_end));
    }
    ranges
}

/// 提取PDF指定页面的注释
pub async fn extract_annotations<P: AsRef<Path>>(
    input_path: P,
    page_range: &str,
    num_cols: u32, // 新增: 列数参数
    is_full_page_mode: bool,
) -> Result<Vec<Annotation>, PdfError> {
    // 根据环境选择不同的实现
    #[cfg(test)]
    {
        // 测试环境下使用 lopdf 实现
        extract_annotations_from_lopdf(input_path, page_range, num_cols, is_full_page_mode)
    }
    #[cfg(not(test))]
    {
        // 根据平台选择不同的实现方式
        // if cfg!(any(target_os = "ios", target_os = "android")) {
        // 移动端使用 dart 接口导出 xfdf
        if let Ok(exported_path) = crate::anki::utils::export_pdf_annotations(
            input_path.as_ref().to_string_lossy().as_ref(),
        )
        .await
        {
            debug_print!("exported_path: {:?}", &exported_path);
            if !exported_path.is_empty() {
                let xfdf_file = std::path::Path::new(&exported_path);
                // 导出成功后，从 xfdf 提取
                extract_annotations_from_xfdf(
                    xfdf_file,
                    input_path.as_ref(),
                    page_range,
                    num_cols,
                    is_full_page_mode,
                )
            } else {
                // 导出失败，使用 lopdf 实现
                extract_annotations_from_lopdf(input_path, page_range, num_cols, is_full_page_mode)
            }
        } else {
            // 导出异常，使用 lopdf 实现
            extract_annotations_from_lopdf(input_path, page_range, num_cols, is_full_page_mode)
        }
        // } else {
        //     // 电脑端(Windows/macOS/Linux)直接使用 PyMuPDF 实现
        //     match crate::anki::cmd::py_extract_annotations(
        //         input_path.as_ref().to_string_lossy().as_ref(),
        //         page_range,
        //         num_cols,
        //         is_full_page_mode,
        //         |current: f64, total: f64, message: String| {
        //             debug_print!("Progress: {}/{} - {}", current, total, message);
        //         },
        //     )
        //     .await
        //     {
        //         Ok(annotations) => Ok(annotations),
        //         Err(e) => {
        //             debug_print!(
        //                 "PyMuPDF annotation extraction failed: {:?}, falling back to lopdf",
        //                 e
        //             );
        //             extract_annotations_from_lopdf(
        //                 input_path,
        //                 page_range,
        //                 num_cols,
        //                 is_full_page_mode,
        //             )
        //         }
        //     }
        // }
    }
}

/// 使用lopdf提取PDF指定页面的注释
pub fn extract_annotations_from_lopdf<P: AsRef<Path>>(
    input_path: P,
    page_range: &str,
    num_cols: u32, // 新增: 列数参数
    is_full_page_mode: bool,
) -> Result<Vec<Annotation>, PdfError> {
    print!("input_path: {:?}", input_path.as_ref());
    let doc = Document::load(input_path.as_ref())?;

    let total_pages = doc.get_pages().len() as u32;
    let pdfium = get_pdfium(None).map_err(|e| PdfError::Other(e.to_string()))?;
    let document = pdfium
        .load_pdf_from_file(input_path.as_ref(), None)
        .map_err(|e| PdfError::Other(e.to_string()))?;
    let pdfium_pages = document.pages();
    let page_sizes = pdfium_pages
        .iter()
        .map(|page| {
            let rect = page.page_size();
            [
                rect.left().value,
                rect.bottom().value,
                rect.right().value,
                rect.top().value,
            ]
        })
        .collect::<Vec<[f32; 4]>>(); // 解析页码范围
    let pages = parse_page_ranges(page_range, total_pages).map_err(PdfError::PageRange)?;
    let mut annotations = Vec::new();
    print!("pages: {:?}", pages);
    // 处理每个页面
    for page_num in pages {
        print!("page: {:?}", &page_num);
        if let Some(&page_id) = doc.get_pages().get(&page_num) {
            let page_width = page_sizes[(page_num - 1) as usize][2];
            let page_height = page_sizes[(page_num - 1) as usize][3];
            if let Ok(annots) = doc.get_page_annotations(page_id) {
                let mut is_hit = false;
                for annot in annots {
                    // dbg!("annot: {:?}", annot);
                    let dict = &annot;
                    // 提取注释类型
                    let annot_type = dict
                        .get(b"Subtype")
                        .and_then(|s| s.as_name())
                        .map(|s| match s {
                            b"Highlight" => {
                                is_hit = true;
                                AnnotationType::Highlight
                            }
                            b"Square" => {
                                is_hit = true;
                                AnnotationType::Square
                            }
                            b"Circle" => {
                                is_hit = true;
                                AnnotationType::Circle
                            }
                            b"Underline" => {
                                is_hit = true;
                                AnnotationType::Underline
                            }
                            b"StrikeOut" => {
                                is_hit = true;
                                AnnotationType::StrikeOut
                            }
                            b"Squiggly" => {
                                is_hit = true;
                                AnnotationType::Squiggly
                            }
                            b"Text" => {
                                is_hit = true;
                                AnnotationType::Text
                            }
                            b"Popup" => {
                                is_hit = true;
                                AnnotationType::Popup
                            }
                            other => {
                                AnnotationType::Other(String::from_utf8_lossy(other).into_owned())
                            }
                        })
                        .unwrap_or(AnnotationType::Other("Unknown".to_string()));

                    // 提取矩形坐标
                    let rect = dict
                        .get(b"Rect")
                        .and_then(|r| r.as_array())
                        .map(|arr| {
                            let coords: Vec<f64> = arr
                                .iter()
                                .filter_map(|v| match v {
                                    Object::Real(n) => Some(*n as f64),
                                    Object::Integer(n) => Some(*n as f64),
                                    _ => None,
                                })
                                .collect();
                            if coords.len() == 4 {
                                [coords[0], coords[1], coords[2], coords[3]]
                            } else {
                                [0.0, 0.0, 0.0, 0.0]
                            }
                        })
                        .unwrap_or([0.0, 0.0, 0.0, 0.0]);

                    let quad_points = dict
                        .get(b"QuadPoints")
                        .and_then(|q| q.as_array())
                        .map(|arr| {
                            arr.iter()
                                .filter_map(|v| match v {
                                    Object::Real(n) => Some(*n as f64),
                                    Object::Integer(n) => Some(*n as f64),
                                    _ => None,
                                })
                                .collect()
                        })
                        .ok();

                    // 提取颜色
                    let color = dict
                        .get(b"C")
                        .and_then(|c| c.as_array())
                        .map(|arr| {
                            let colors: Vec<f32> = arr
                                .iter()
                                .filter_map(|v| match v {
                                    Object::Real(n) => Some(*n as f32),
                                    Object::Integer(n) => Some(*n as f32),
                                    _ => None,
                                })
                                .collect();
                            if colors.len() == 3 {
                                [colors[0], colors[1], colors[2]]
                            } else {
                                [0.0, 0.0, 0.0]
                            }
                        })
                        .ok();

                    // 尝试多种编码方式解码文本内容
                    let contents = dict.get(b"Contents").and_then(|c| c.as_str()).map(|s| {
                        // s 已经是 &[u8] 类型，不需要 as_bytes()
                        let bytes = s;

                        // 尝试 UTF-8
                        if let Ok(text) = String::from_utf8(bytes.to_vec()) {
                            return decode_html_entities(&text).into_owned();
                        }

                        // 尝试 GBK
                        let (cow, _encoding_used, had_errors) = GBK.decode(bytes);
                        if !had_errors {
                            return decode_html_entities(&cow).into_owned();
                        }

                        // 尝试 Windows-1252
                        let (cow, _encoding_used, _) = WINDOWS_1252.decode(bytes);
                        decode_html_entities(&cow).into_owned()
                    });

                    // 提取创建时间
                    let creation_date = dict
                        .get(b"CreationDate")
                        .and_then(|d| d.as_str())
                        .map(|s| String::from_utf8_lossy(s).into_owned())
                        .ok();

                    // 提取修改时间
                    let mod_date = dict
                        .get(b"M")
                        .and_then(|d| d.as_str())
                        .map(|s| String::from_utf8_lossy(s).into_owned())
                        .ok();

                    // 提取注释ID
                    let mut id = dict
                        .get(b"NM")
                        .and_then(|n| n.as_str())
                        .map(|s| String::from_utf8_lossy(s).into_owned())
                        .ok();
                    // 如果id为空，使用creation_date或生成新的Ulid
                    if id.is_none() {
                        id = if let Some(date) = &creation_date {
                            Some(date.clone())
                        } else {
                            Some(format!("{:?}-{:?}", &page_num, &rect))
                        };
                    }

                    // 在创建 Annotation 之前，计算列号
                    let center_x = ((rect[0] + rect[2]) / 2.0) as f32; // 计算注释的中心 x 坐标
                                                                       // 计算列宽
                    let column_width = page_width / num_cols as f32;

                    // 确定注释属于哪一列（列号从1开始）
                    let column = (center_x / column_width)
                        .ceil()
                        .min(num_cols as f32)
                        .max(1.0) as u32;
                    // 判断是否为零宽批注
                    // 计算批注宽度
                    let width = (rect[2] - rect[0]).abs();
                    let height = (rect[3] - rect[1]).abs();
                    if width <= 1.0 || height <= 1.0 {
                        continue; // 跳过零宽批注
                    }
                    annotations.push(Annotation {
                        page_num,
                        column,
                        annot_type,
                        rect,
                        quad_points,
                        color,
                        contents: contents.ok(),
                        creation_date,
                        mod_date,
                        id,
                    });
                }
                if is_full_page_mode && is_hit {
                    // 获取文件名（不含路径和扩展名）
                    let file_name = input_path
                        .as_ref()
                        .file_name()
                        .and_then(|name| name.to_str())
                        .and_then(|name| name.rsplit_once('.').map(|(name, _)| name))
                        .unwrap_or("unknown");

                    let size_obj = if page_num <= page_sizes.len() as u32 {
                        let index = page_num as usize - 1;
                        page_sizes[index]
                    } else {
                        // 如果找不到对应页面，使用标准 A4 尺寸
                        [0.0, 0.0, 595.276, 841.89] // 标准 A4 尺寸 (210mm x 297mm)
                    };
                    annotations.push(Annotation {
                        page_num,
                        column: 1,
                        annot_type: AnnotationType::Square,
                        rect: [
                            size_obj[0] as f64,
                            size_obj[1] as f64,
                            size_obj[2] as f64,
                            size_obj[3] as f64,
                        ],
                        quad_points: None,
                        color: None,
                        contents: None,
                        creation_date: None,
                        mod_date: None,
                        id: Some(format!("{}_page{}_fullpage", file_name, page_num)),
                    });
                }
            }
        }
    }
    // dbg!(&annotations);
    Ok(annotations)
}

/// 从 XFDF 文件中提取 PDF 注释
///
/// # 参数
/// * `xfdf_path` - XFDF 文件路径
/// * `doc_path` - PDF 文件路径
/// * `page_range` - 页面范围字符串
/// * `num_cols` - 页面分列数
/// * `is_full_page_mode` - 是否启用全页模式
///
/// # 返回值
/// 成功时返回注释列表，失败时返回错误
pub fn extract_annotations_from_xfdf<P: AsRef<Path>>(
    xfdf_path: P,
    doc_path: P,
    page_range: &str,
    num_cols: u32,
    is_full_page_mode: bool,
) -> Result<Vec<Annotation>, PdfError> {
    // 读取 XFDF 文件内容
    let xfdf_content = fs::read_to_string(xfdf_path)
        .map_err(|e| PdfError::Other(format!("读取 XFDF 文件失败: {}", e)))?;

    // 使用 pdfium 获取 PDF 信息
    let pdfium = get_pdfium(None).map_err(|e| PdfError::Other(e.to_string()))?;
    let document = pdfium
        .load_pdf_from_file(doc_path.as_ref(), None)
        .map_err(|e| PdfError::Other(e.to_string()))?;

    // 获取总页数和页面尺寸
    let total_pages = document.pages().len() as u32;
    let pdfium_pages = document.pages();
    let page_sizes = pdfium_pages
        .iter()
        .map(|page| {
            let rect = page.page_size();
            [
                rect.left().value,
                rect.bottom().value,
                rect.right().value,
                rect.top().value,
            ]
        })
        .collect::<Vec<[f32; 4]>>();

    // 使用 quick-xml 解析 XML
    let mut reader = quick_xml::Reader::from_str(&xfdf_content);
    // 针对 quick-xml 0.37.2 版本
    reader.config_mut().trim_text(true);

    let mut annotations = Vec::new();
    let mut buf = Vec::new();
    let mut in_annots = false; // Track if we are inside the <annots> tag
    let mut current_annotation: Option<Annotation> = None; // Hold the annotation being processed (for Start/End tags)
    let mut in_contents = false; // Track if we are inside the <contents> tag

    // 解析页码范围
    let pages = parse_page_ranges(page_range, total_pages).map_err(PdfError::PageRange)?;
    // dbg!("pages: {:?}", &pages);

    // 处理 XML 元素
    loop {
        match reader.read_event_into(&mut buf) {
            Ok(quick_xml::events::Event::Start(e)) => {
                match e.name().as_ref() {
                    b"annots" => in_annots = true,
                    b"contents" => in_contents = true,
                    b"highlight" | b"square" | b"circle" | b"underline" | b"strikeout"
                    | b"text" => {
                        if !in_annots {
                            continue;
                        }

                        // Reset state for a new annotation started with <Start>
                        current_annotation = None;

                        let annot_type = match e.name().as_ref() {
                            b"highlight" => AnnotationType::Highlight,
                            b"square" => AnnotationType::Square,
                            b"circle" => AnnotationType::Circle,
                            b"underline" => AnnotationType::Underline,
                            b"strikeout" => AnnotationType::StrikeOut,
                            b"text" => AnnotationType::Text,
                            _ => AnnotationType::Other(
                                String::from_utf8_lossy(e.name().as_ref()).into_owned(),
                            ),
                        };

                        // Temporary variables to parse attributes
                        let mut page = 0;
                        let mut rect = [0.0, 0.0, 0.0, 0.0];
                        let mut color = None;
                        let mut creation_date = None;
                        let mut mod_date = None;
                        let mut id = None;
                        let mut quad_points = None;

                        // Parse attributes
                        for attr_result in e.attributes() {
                            if let Ok(attr) = attr_result {
                                match attr.key.as_ref() {
                                    b"page" => {
                                        if let Ok(p) =
                                            attr.unescape_value().unwrap_or_default().parse::<u32>()
                                        {
                                            page = p + 1; // XFDF page is 0-based
                                        }
                                    }
                                    b"rect" => {
                                        let rect_str = attr.unescape_value().unwrap_or_default();
                                        let coords: Vec<f64> = rect_str
                                            .split(',')
                                            .filter_map(|s| s.trim().parse::<f64>().ok())
                                            .collect();
                                        if coords.len() == 4 {
                                            rect = [coords[0], coords[1], coords[2], coords[3]];
                                        }
                                    }
                                    b"color" => {
                                        let color_str = attr.unescape_value().unwrap_or_default();
                                        if color_str.starts_with('#') && color_str.len() == 7 {
                                            if let (Ok(r), Ok(g), Ok(b)) = (
                                                u8::from_str_radix(&color_str[1..3], 16),
                                                u8::from_str_radix(&color_str[3..5], 16),
                                                u8::from_str_radix(&color_str[5..7], 16),
                                            ) {
                                                color = Some([
                                                    r as f32 / 255.0,
                                                    g as f32 / 255.0,
                                                    b as f32 / 255.0,
                                                ]);
                                            }
                                        }
                                    }
                                    b"creationdate" => {
                                        creation_date = Some(
                                            attr.unescape_value().unwrap_or_default().to_string(),
                                        )
                                    }
                                    b"date" => {
                                        mod_date = Some(
                                            attr.unescape_value().unwrap_or_default().to_string(),
                                        )
                                    }
                                    b"name" => {
                                        id = Some(
                                            attr.unescape_value().unwrap_or_default().to_string(),
                                        )
                                    }
                                    b"coords" => {
                                        let coords_str = attr.unescape_value().unwrap_or_default();
                                        quad_points = Some(
                                            coords_str
                                                .split(',')
                                                .filter_map(|s| s.trim().parse::<f64>().ok())
                                                .collect(),
                                        );
                                    }
                                    _ => {}
                                }
                            }
                        }

                        // If page is valid, create the annotation object and store it
                        if page > 0 {
                            let page_width = if page <= page_sizes.len() as u32 {
                                page_sizes[(page - 1) as usize][2]
                            } else {
                                595.276
                            };
                            let center_x = ((rect[0] + rect[2]) / 2.0) as f32;
                            let column_width = page_width / num_cols as f32;
                            let column = (center_x / column_width)
                                .ceil()
                                .min(num_cols as f32)
                                .max(1.0) as u32;

                            current_annotation = Some(Annotation {
                                page_num: page,
                                column,
                                annot_type,
                                rect,
                                quad_points,
                                color,
                                contents: None, // Initialize contents as None, will be filled by Text event if available
                                creation_date,
                                mod_date,
                                id,
                            });
                        }
                    }
                    // Ignore other start tags like <popup> for now, or handle if needed
                    _ => {}
                }
            }
            Ok(quick_xml::events::Event::Empty(e)) => {
                match e.name().as_ref() {
                    b"highlight" | b"square" | b"circle" | b"underline" | b"strikeout"
                    | b"text" => {
                        if !in_annots {
                            continue;
                        }
                        // Reset state before processing Empty tag
                        let annot_type = match e.name().as_ref() {
                            b"highlight" => AnnotationType::Highlight,
                            b"square" => AnnotationType::Square,
                            b"circle" => AnnotationType::Circle,
                            b"underline" => AnnotationType::Underline,
                            b"strikeout" => AnnotationType::StrikeOut,
                            b"text" => AnnotationType::Text, // Text can also be empty? Handle defensively.
                            _ => AnnotationType::Other(
                                String::from_utf8_lossy(e.name().as_ref()).into_owned(),
                            ),
                        };
                        // Temporary variables
                        let mut page = 0;
                        let mut rect = [0.0, 0.0, 0.0, 0.0];
                        let mut color = None;
                        let mut creation_date = None;
                        let mut mod_date = None;
                        let mut id = None;
                        let mut quad_points = None;
                        // Note: Empty tags usually don't have separate <contents>, but handle just in case xfdf is weird
                        let mut contents_attr: Option<String> = None;

                        // Parse attributes
                        for attr_result in e.attributes() {
                            if let Ok(attr) = attr_result {
                                match attr.key.as_ref() {
                                    b"page" => {
                                        if let Ok(p) =
                                            attr.unescape_value().unwrap_or_default().parse::<u32>()
                                        {
                                            page = p + 1;
                                        }
                                    }
                                    b"rect" => {
                                        let rect_str = attr.unescape_value().unwrap_or_default();
                                        let coords: Vec<f64> = rect_str
                                            .split(',')
                                            .filter_map(|s| s.trim().parse::<f64>().ok())
                                            .collect();
                                        if coords.len() == 4 {
                                            rect = [coords[0], coords[1], coords[2], coords[3]];
                                        }
                                    }
                                    b"color" => {
                                        let color_str = attr.unescape_value().unwrap_or_default();
                                        if color_str.starts_with('#') && color_str.len() == 7 {
                                            if let (Ok(r), Ok(g), Ok(b)) = (
                                                u8::from_str_radix(&color_str[1..3], 16),
                                                u8::from_str_radix(&color_str[3..5], 16),
                                                u8::from_str_radix(&color_str[5..7], 16),
                                            ) {
                                                color = Some([
                                                    r as f32 / 255.0,
                                                    g as f32 / 255.0,
                                                    b as f32 / 255.0,
                                                ]);
                                            }
                                        }
                                    }
                                    b"creationdate" => {
                                        creation_date = Some(
                                            attr.unescape_value().unwrap_or_default().to_string(),
                                        )
                                    }
                                    b"date" => {
                                        mod_date = Some(
                                            attr.unescape_value().unwrap_or_default().to_string(),
                                        )
                                    }
                                    b"name" => {
                                        id = Some(
                                            attr.unescape_value().unwrap_or_default().to_string(),
                                        )
                                    }
                                    b"coords" => {
                                        let coords_str = attr.unescape_value().unwrap_or_default();
                                        quad_points = Some(
                                            coords_str
                                                .split(',')
                                                .filter_map(|s| s.trim().parse::<f64>().ok())
                                                .collect(),
                                        );
                                    }
                                    // Check for contents directly as attribute (less common in XFDF but possible)
                                    b"contents" => {
                                        contents_attr = Some(
                                            attr.unescape_value().unwrap_or_default().to_string(),
                                        )
                                    }
                                    _ => {}
                                }
                            }
                        }

                        // If page is valid and in range, create and add annotation directly
                        let page_in_range = pages.contains(&page);
                        if page > 0 && page_in_range {
                            let page_width = if page <= page_sizes.len() as u32 {
                                page_sizes[(page - 1) as usize][2]
                            } else {
                                595.276
                            };
                            let center_x = ((rect[0] + rect[2]) / 2.0) as f32;
                            let column_width = page_width / num_cols as f32;
                            let column = (center_x / column_width)
                                .ceil()
                                .min(num_cols as f32)
                                .max(1.0) as u32;
                            let width = (rect[2] - rect[0]).abs();
                            let height = (rect[3] - rect[1]).abs();
                            let valid_dimensions = width > 1.0 && height > 1.0;
                            if valid_dimensions {
                                annotations.push(Annotation {
                                    page_num: page,
                                    column,
                                    annot_type,
                                    rect,
                                    quad_points,
                                    color,
                                    contents: contents_attr, // Use attribute content if found
                                    creation_date,
                                    mod_date,
                                    id,
                                });
                            }
                        }
                    }
                    // Handle other empty tags like <f/>
                    _ => {}
                }
            }
            Ok(quick_xml::events::Event::Text(e)) => {
                // If we are inside <contents>...</contents> and have an active annotation object
                if in_contents {
                    if let Some(ref mut annot) = current_annotation {
                        if let Ok(text) = e.unescape() {
                            let trimmed_text = text.trim();
                            if !trimmed_text.is_empty() {
                                // Append or set contents. If multiple text nodes, might need appending logic.
                                // For simplicity, just set/overwrite.
                                annot.contents = Some(trimmed_text.to_string());
                            }
                        }
                    }
                }
            }
            Ok(quick_xml::events::Event::End(e)) => {
                match e.name().as_ref() {
                    b"annots" => in_annots = false,
                    b"contents" => in_contents = false,
                    b"highlight" | b"square" | b"circle" | b"underline" | b"strikeout"
                    | b"text" => {
                        // If we have an annotation held from a <Start> event, finalize and add it
                        if let Some(annot) = current_annotation.take() {
                            // take() consumes the Option
                            if pages.contains(&annot.page_num) {
                                let width = (annot.rect[2] - annot.rect[0]).abs();
                                let height = (annot.rect[3] - annot.rect[1]).abs();
                                // Add check for dimensions again, in case rect was invalid
                                if width > 1.0 && height > 1.0 {
                                    annotations.push(annot);
                                }
                            }
                        }
                    }
                    // Handle end of other tags if needed
                    _ => {}
                }
            }
            Ok(quick_xml::events::Event::Eof) => break,
            Err(e) => return Err(PdfError::Other(format!("解析 XFDF 失败: {}", e))),
            _ => (), // Ignore other events like Comment, CData, etc.
        }
        buf.clear();
    }

    // 全页模式逻辑 - 保持不变
    if is_full_page_mode && !annotations.is_empty() {
        // 获取文件名（不含路径和扩展名）
        let file_name = doc_path
            .as_ref()
            .file_name()
            .and_then(|name| name.to_str())
            .and_then(|name| name.rsplit_once('.').map(|(name, _)| name))
            .unwrap_or("unknown");

        // 收集已经注释的页面
        let mut annotated_pages = HashSet::new();
        for annotation in &annotations {
            annotated_pages.insert(annotation.page_num);
        }

        // 为每个有注释的页面添加一个全页注释
        for &page_num in &annotated_pages {
            if pages.contains(&page_num) {
                // 使用 PDF 中读取的实际页面尺寸
                let size_obj = if page_num <= page_sizes.len() as u32 {
                    let index = page_num as usize - 1;
                    page_sizes[index]
                } else {
                    // 如果找不到对应页面，使用标准 A4 尺寸
                    [0.0, 0.0, 595.276, 841.89] // 标准 A4 尺寸 (210mm x 297mm)
                };

                annotations.push(Annotation {
                    page_num,
                    column: 1,
                    annot_type: AnnotationType::Square,
                    rect: [
                        size_obj[0] as f64,
                        size_obj[1] as f64,
                        size_obj[2] as f64,
                        size_obj[3] as f64,
                    ],
                    quad_points: None,
                    color: None,
                    contents: None,
                    creation_date: None,
                    mod_date: None,
                    id: Some(format!("{}_page{}_fullpage", file_name, page_num)),
                });
            }
        }
    }
    Ok(annotations)
}

/// 删除PDF指定页面的注释
pub fn delete_annotations<P: AsRef<Path>>(
    input_path: P,
    page_range: &str,
    output_path: Option<P>,
    annotation_ids: Option<Vec<String>>,
) -> Result<(), PdfError> {
    // 加载PDF文档
    let mut doc = Document::load(input_path.as_ref())?;
    let total_pages = doc.get_pages().len() as u32;

    // 解析页码范围
    let pages = parse_page_ranges(page_range, total_pages).map_err(PdfError::PageRange)?;

    // 处理每一页
    for page_num in pages {
        if let Some(&page_id) = doc.get_pages().get(&page_num) {
            if let Some(ref ids) = annotation_ids {
                // 首先收集页面上所有注释的信息
                let mut annotations_to_keep = Vec::new();

                if let Ok(page_dict) = doc.get_dictionary(page_id) {
                    if let Ok(Object::Array(ref annots_array)) = page_dict.get(b"Annots") {
                        for (idx, annot) in annots_array.iter().enumerate() {
                            let should_keep = if let Ok(ref_id) = annot.as_reference() {
                                if let Ok(dict) = doc.get_dictionary(ref_id) {
                                    let mut id_str = dict
                                        .get(b"NM")
                                        .and_then(|n| n.as_str())
                                        .map(|s| String::from_utf8_lossy(s).into_owned())
                                        .ok();
                                    if id_str.is_none() {
                                        let creation_date = dict
                                            .get(b"CreationDate")
                                            .and_then(|d| d.as_str())
                                            .map(|s| String::from_utf8_lossy(s).into_owned())
                                            .ok();
                                        id_str = if let Some(date) = &creation_date {
                                            Some(date.clone())
                                        } else {
                                            let rect = dict
                                                .get(b"Rect")
                                                .and_then(|r| r.as_array())
                                                .map(|arr| {
                                                    let coords: Vec<f64> = arr
                                                        .iter()
                                                        .filter_map(|v| match v {
                                                            Object::Real(n) => Some(*n as f64),
                                                            Object::Integer(n) => Some(*n as f64),
                                                            _ => None,
                                                        })
                                                        .collect();
                                                    if coords.len() == 4 {
                                                        [coords[0], coords[1], coords[2], coords[3]]
                                                    } else {
                                                        [0.0, 0.0, 0.0, 0.0]
                                                    }
                                                })
                                                .unwrap_or([0.0, 0.0, 0.0, 0.0]);
                                            Some(format!("{:?}-{:?}", &page_num, &rect))
                                        };
                                    }
                                    if let Some(ref id_str) = id_str {
                                        !ids.contains(id_str) // 使用 ref 来获取 Option 内部值的引用
                                    } else {
                                        true
                                    }
                                } else {
                                    true
                                }
                            } else {
                                true
                            };

                            if should_keep {
                                annotations_to_keep.push(annot.clone());
                            }
                        }
                    }
                }

                // 然后更新页面的注释数组
                if let Ok(page_dict) = doc.get_dictionary_mut(page_id) {
                    if !annotations_to_keep.is_empty() {
                        page_dict.set("Annots", Object::Array(annotations_to_keep));
                    } else {
                        page_dict.remove(b"Annots");
                    }
                }
            } else {
                // 如果没有提供注释ID，删除所有注释
                if let Ok(page_dict) = doc.get_dictionary_mut(page_id) {
                    page_dict.remove(b"Annots");
                }
            }
        }
    }

    // 清理和优化文档
    doc.prune_objects();
    doc.renumber_objects();
    doc.compress();

    let save_path = match output_path {
        Some(path) => path.as_ref().to_owned(),
        None => input_path.as_ref().to_owned(),
    };
    doc.save(save_path)?;
    Ok(())
}

/// 从PDF提取文本
pub fn extract_text_from_pdf<P: AsRef<Path>>(
    input_path: P,
    page_range: &str,
    output_path: Option<P>,
) -> Result<String, PdfError> {
    // 加载PDF文档
    let doc = Document::load(input_path.as_ref())?;
    let total_pages = doc.get_pages().len() as u32;

    // 解析页码范围
    let pages = parse_page_ranges(page_range, total_pages).map_err(PdfError::PageRange)?;

    // 提取文本
    let text = doc.extract_text(&pages)?;

    // 如果指定了输出路径，则将文本保存到文件
    if let Some(path) = output_path {
        std::fs::write(path, &text)?;
    }

    Ok(text)
}

/// 从PDF提取图片
pub fn extract_images<P: AsRef<Path>>(
    input_path: P,
    page_range: &str,
    output_dir: P,
) -> Result<Vec<PathBuf>, PdfError> {
    // 加载PDF文档
    let doc = Document::load(input_path.as_ref())?;
    let total_pages = doc.get_pages().len() as u32;

    // 解析页码范围
    let pages = parse_page_ranges(page_range, total_pages).map_err(PdfError::PageRange)?;

    // 创建输出目录
    fs::create_dir_all(output_dir.as_ref())?;

    let mut extracted_files = Vec::new();

    // 处理每一页
    for page_num in pages {
        if let Some(&page_id) = doc.get_pages().get(&page_num) {
            // 获取页面中的所有图片
            let images = doc.get_page_images(page_id)?;

            // 保存每个图片
            for (idx, image) in images.iter().enumerate() {
                // 构建输出文件路径
                let file_name = format!(
                    "page_{}_img_{}.{}",
                    page_num,
                    idx + 1,
                    guess_image_extension(image)
                );
                let output_path = output_dir.as_ref().join(&file_name);

                // 写入图片数据
                fs::write(&output_path, image.content)?;
                extracted_files.push(output_path);
            }
        }
    }

    Ok(extracted_files)
}

/// 根据图片信息猜测文件扩展名
fn guess_image_extension(image: &PdfImage) -> &'static str {
    if let Some(filters) = &image.filters {
        for filter in filters {
            match filter.as_str() {
                "DCTDecode" => return "jpg",
                "JBIG2Decode" => return "jbig2",
                "JPXDecode" => return "jp2",
                _ => {}
            }
        }
    }

    // 默认为原始图像数据
    "raw"
}

// 定义排序方式枚举
#[derive(Debug)]
pub enum SortBy {
    Selection,    // 添加顺序
    Name,         // 文件名字母
    NumberPrefix, // 文件名开头编号
    NumberSuffix, // 文件名结尾编号
    Size,         // 文件大小
    Created,      // 创建时间
    Modified,     // 修改时间
}

impl From<&str> for SortBy {
    fn from(s: &str) -> Self {
        match s {
            "name" => SortBy::Name,
            "number_prefix" => SortBy::NumberPrefix,
            "number_suffix" => SortBy::NumberSuffix,
            "size" => SortBy::Size,
            "created" => SortBy::Created,
            "modified" => SortBy::Modified,
            _ => SortBy::Selection,
        }
    }
}

pub fn merge_pdfs(
    input_paths: &[&str],
    output_path: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut documents = Vec::new();
    let mut max_id = 1;

    // 加载所有PDF文档并重新编号对象ID
    for path in input_paths {
        let mut doc = Document::load(*path)?;
        doc.renumber_objects_with(max_id);
        max_id = doc.max_id + 1;
        documents.push(doc);
    }

    let mut merged_doc = Document::with_version("1.5");
    let mut documents_pages = BTreeMap::new();
    let mut documents_objects = BTreeMap::new();

    // 收集所有页面和对象
    for doc in documents {
        // 收集页面对象
        for (_, object_id) in doc.get_pages() {
            if let Ok(page_object) = doc.get_object(object_id) {
                documents_pages.insert(object_id, page_object.clone());
            }
        }
        // 收集其他对象
        documents_objects.extend(doc.objects);
    }

    // 处理Catalog和Pages对象
    let mut catalog_object: Option<(ObjectId, Object)> = None;
    let mut pages_object: Option<(ObjectId, Object)> = None;

    for (object_id, object) in documents_objects {
        match object.type_name().unwrap_or(b"") {
            b"Catalog" => {
                catalog_object = Some((object_id, object));
            }
            b"Pages" => {
                if let Ok(dictionary) = object.as_dict() {
                    let mut dict = dictionary.clone();
                    if let Some((_, ref existing)) = pages_object {
                        if let Ok(old_dict) = existing.as_dict() {
                            dict.extend(old_dict);
                        }
                    }
                    pages_object = Some((object_id, Object::Dictionary(dict)));
                }
            }
            _ => {
                merged_doc.objects.insert(object_id, object);
            }
        }
    }

    // 更新页面引用
    if let (Some((pages_id, pages_obj)), Some((catalog_id, catalog_obj))) =
        (pages_object, catalog_object)
    {
        // 更新Pages对象
        if let Ok(dict) = pages_obj.as_dict() {
            let mut owned_dict = dict.clone();
            owned_dict.set("Count", documents_pages.len() as u32);
            owned_dict.set(
                "Kids",
                documents_pages
                    .keys()
                    .map(|&id| Object::Reference(id))
                    .collect::<Vec<_>>(),
            );
            merged_doc
                .objects
                .insert(pages_id, Object::Dictionary(owned_dict));
        }

        // 更新每个页面的Parent引用（使用正确的ObjectId元组）
        for (object_id, object) in documents_pages {
            if let Ok(dict) = object.as_dict() {
                let mut owned_dict = dict.clone();
                owned_dict.set("Parent", pages_id);
                merged_doc
                    .objects
                    .insert(object_id, Object::Dictionary(owned_dict));
            }
        }

        // 更新Catalog对象
        if let Ok(dict) = catalog_obj.as_dict() {
            let mut owned_dict = dict.clone();
            owned_dict.set("Pages", pages_id);
            owned_dict.remove(b"Outlines");
            merged_doc
                .objects
                .insert(catalog_id, Object::Dictionary(owned_dict));
            merged_doc.trailer.set("Root", catalog_id);
        }
    }

    // 优化并保存PDF
    merged_doc.renumber_objects();
    merged_doc.compress();
    merged_doc.save(output_path)?;

    Ok(())
}

/// 合并PDF文件
pub fn merge_pdfs2<P: AsRef<Path>>(
    input_paths: &[P],
    sort_by: &str,
    sort_direction: &str,
    interleave: bool,
    output_path: P,
) -> Result<(), PdfError> {
    // 根据排序规则获取排序后的文件列表
    let mut file_infos: Vec<(PathBuf, fs::Metadata)> = input_paths
        .iter()
        .filter_map(|path| {
            let metadata = fs::metadata(path).ok()?;
            Some((path.as_ref().to_path_buf(), metadata))
        })
        .collect();

    // 应用排序规则
    apply_sort(&mut file_infos, sort_by, sort_direction == "asc");

    // 加载所有文档
    let mut documents: Vec<Document> = Vec::new();
    let mut max_id = 1;
    for (path, _) in &file_infos {
        let mut document = Document::load(path)?;
        document.renumber_objects_with(max_id);
        max_id = document.max_id + 1;
        documents.push(document);
    }

    // 创建新文档
    let mut merged_doc = Document::with_version("1.5");
    let mut documents_pages = BTreeMap::new();
    let mut documents_objects = BTreeMap::new();

    // 收集所有页面
    if interleave {
        // 交叉合并模式
        let max_pages = documents
            .iter()
            .map(|doc| doc.get_pages().len())
            .max()
            .unwrap_or(0);

        for page_idx in 0..max_pages {
            for doc in &documents {
                if let Some((_, &object_id)) = doc.get_pages().iter().nth(page_idx) {
                    if let Ok(page_object) = doc.get_object(object_id) {
                        documents_pages.insert(object_id, page_object.clone());
                    }
                }
            }
        }
    } else {
        // 顺序合并模式
        for doc in &documents {
            for (_, object_id) in doc.get_pages() {
                if let Ok(page_object) = doc.get_object(object_id) {
                    documents_pages.insert(object_id, page_object.clone());
                }
            }
        }
    }

    // 收集所有其他对象
    for doc in documents {
        documents_objects.extend(doc.objects);
    }

    // 处理 Catalog 和 Pages 对象
    let mut catalog_object: Option<(ObjectId, Object)> = None;
    let mut pages_object: Option<(ObjectId, Object)> = None;

    for (object_id, object) in documents_objects {
        match object.type_name().unwrap_or(b"") {
            b"Catalog" => {
                catalog_object = Some((object_id, object));
            }
            b"Pages" => {
                if let Ok(dictionary) = object.as_dict() {
                    let mut dict = dictionary.clone();
                    if let Some((_, ref existing)) = pages_object {
                        if let Ok(old_dict) = existing.as_dict() {
                            dict.extend(old_dict);
                        }
                    }
                    pages_object = Some((object_id, Object::Dictionary(dict)));
                }
            }
            _ => {
                merged_doc.objects.insert(object_id, object);
            }
        }
    }

    // 更新页面引用和目录
    if let (Some((pages_id, pages_obj)), Some((catalog_id, catalog_obj))) =
        (pages_object, catalog_object)
    {
        // 更新 Pages 对象
        if let Ok(dict) = pages_obj.as_dict() {
            let mut owned_dict = dict.clone();
            owned_dict.set("Count", documents_pages.len() as u32);
            owned_dict.set(
                "Kids",
                documents_pages
                    .keys()
                    .map(|&id| Object::Reference(id))
                    .collect::<Vec<_>>(),
            );
            merged_doc
                .objects
                .insert(pages_id, Object::Dictionary(owned_dict));
        }

        // 更新每个页面的 Parent 引用
        for (object_id, object) in documents_pages {
            if let Ok(dict) = object.as_dict() {
                let mut owned_dict = dict.clone();
                owned_dict.set("Parent", pages_id);
                merged_doc
                    .objects
                    .insert(object_id, Object::Dictionary(owned_dict));
            }
        }

        // 更新 Catalog 对象
        if let Ok(dict) = catalog_obj.as_dict() {
            let mut owned_dict = dict.clone();
            owned_dict.set("Pages", pages_id);
            owned_dict.remove(b"Outlines");
            merged_doc
                .objects
                .insert(catalog_id, Object::Dictionary(owned_dict));
            merged_doc.trailer.set("Root", catalog_id);
        }
    }

    // 优化和保存
    merged_doc.renumber_objects();
    merged_doc.compress();
    merged_doc.save(output_path)?;

    Ok(())
}

// 辅助函数：应用排序规则
fn apply_sort(file_infos: &mut Vec<(PathBuf, fs::Metadata)>, sort_by: &str, ascending: bool) {
    match SortBy::from(sort_by) {
        SortBy::Selection => {} // 保持原有顺序
        SortBy::Name => {
            file_infos.sort_by(|a, b| {
                let ord = a.0.file_name().cmp(&b.0.file_name());
                if ascending {
                    ord
                } else {
                    ord.reverse()
                }
            });
        }
        SortBy::NumberPrefix => {
            file_infos.sort_by(|a, b| {
                let ord = extract_number_prefix(&a.0).cmp(&extract_number_prefix(&b.0));
                if ascending {
                    ord
                } else {
                    ord.reverse()
                }
            });
        }
        SortBy::NumberSuffix => {
            file_infos.sort_by(|a, b| {
                let ord = extract_number_suffix(&a.0).cmp(&extract_number_suffix(&b.0));
                if ascending {
                    ord
                } else {
                    ord.reverse()
                }
            });
        }
        SortBy::Size => {
            file_infos.sort_by(|a, b| {
                let ord = a.1.len().cmp(&b.1.len());
                if ascending {
                    ord
                } else {
                    ord.reverse()
                }
            });
        }
        SortBy::Created => {
            file_infos.sort_by(|a, b| {
                let ord = a.1.created().unwrap().cmp(&b.1.created().unwrap());
                if ascending {
                    ord
                } else {
                    ord.reverse()
                }
            });
        }
        SortBy::Modified => {
            file_infos.sort_by(|a, b| {
                let ord = a.1.modified().unwrap().cmp(&b.1.modified().unwrap());
                if ascending {
                    ord
                } else {
                    ord.reverse()
                }
            });
        }
    }
}

// 辅助函数：提取文件名中的前缀数字
fn extract_number_prefix(path: &Path) -> i32 {
    path.file_name()
        .and_then(|name| name.to_str())
        .and_then(|name| {
            let number_str: String = name.chars().take_while(|c| c.is_digit(10)).collect();
            number_str.parse().ok()
        })
        .unwrap_or(0)
}

// 辅助函数：提取文件名中的后缀数字
fn extract_number_suffix(path: &Path) -> i32 {
    path.file_stem()
        .and_then(|name| name.to_str())
        .and_then(|name| {
            let number_str: String = name
                .chars()
                .rev()
                .take_while(|c| c.is_digit(10))
                .collect::<String>()
                .chars()
                .rev()
                .collect();
            number_str.parse().ok()
        })
        .unwrap_or(0)
}

/// PDF转图片的配置选项
#[derive(Debug)]
pub struct PdfToImageOptions {
    pub dpi: u16,        // 输出图片的DPI
    pub grayscale: bool, // 是否转换为灰度图
    pub format: String,  // 输出格式：jpg, png
    pub quality: u8,     // 图片质量(1-100)，仅用于jpg
}

impl Default for PdfToImageOptions {
    fn default() -> Self {
        Self {
            dpi: 300,
            grayscale: false,
            format: "jpg".to_string(),
            quality: 90,
        }
    }
}

/// PDF书签(目录)条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BookmarkItem {
    pub level: i32,
    pub title: String,
    pub page: Option<u32>,
}

/// 提取PDF书签(目录)
pub fn extract_bookmarks<P: AsRef<Path>>(
    doc_path: P,
    export_format: &str,
    output_path: Option<P>,
) -> Result<(), PdfError> {
    // 加载PDF文档
    let doc = Document::load(doc_path.as_ref())?;

    // 获取目录
    let toc = doc.get_toc().map_err(|e| PdfError::Other(e.to_string()))?;

    // 如果指定了输出路径
    if let Some(path) = output_path {
        match export_format.to_lowercase().as_str() {
            "json" => {
                let mut data = Vec::new();
                for item in toc.toc {
                    data.push(BookmarkItem {
                        level: item.level as i32,
                        title: item.title,
                        page: Some(item.page as u32),
                    });
                }
                let json = serde_json::to_string_pretty(&data)
                    .map_err(|e| PdfError::Other(e.to_string()))?;
                fs::write(path, json)?;
            }
            "txt" => {
                // 导出为TXT格式，带缩进
                let mut file = fs::File::create(path)?;
                for item in toc.toc {
                    let line = format!(
                        "{}{} ... {}\n",
                        "\t".repeat(item.level - 1),
                        item.title,
                        item.page
                    );
                    file.write_all(line.as_bytes())?;
                }
            }
            _ => return Err(PdfError::Other("不支持的导出格式".to_string())),
        }
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::tempdir;

    #[test]
    fn test_extract_annotations_from_xfdf() {
        // let xfdf_path = PathBuf::from(
        //     "/Users/<USER>/Downloads/执医易错考点（挖空版）_批注导出.xfdf",
        // );
        // let doc_path =
        //     PathBuf::from("/Users/<USER>/Downloads/执医易错考点（挖空版）.pdf");

        let xfdf_path = PathBuf::from(
            "/Users/<USER>/Downloads/50876400620167202.25考研XL内科学笔记-无书签_提取页面_批注导出.xfdf",
        );
        let doc_path =
            PathBuf::from("/Users/<USER>/Downloads/50876400620167202.25考研XL内科学笔记.pdf");

        let annotations = extract_annotations_from_xfdf(xfdf_path, doc_path, "", 1, false).unwrap();
        println!("{:?}", annotations);
        assert!(false);
    }
}
