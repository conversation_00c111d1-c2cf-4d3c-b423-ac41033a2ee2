use crate::anki::types::{Annotation, AnnotationType, PdfError};
use futures_util::StreamExt;
use reqwest::header::CONTENT_TYPE;
use reqwest::multipart::{Form, Part};
use reqwest::Client;
use reqwest_eventsource::{Event, EventSource};
use rinf::debug_print;
use serde_json;
use serde_json::Value;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::sync::mpsc;

// API服务器地址
pub fn get_api_url() -> String {
    "http://127.0.0.1:52026".to_string()
}

/// 通用HTTP任务执行函数，处理各种命令的共同逻辑
/// * `args` - API参数，使用JSON对象表示
/// * `progress_callback` - 进度回调函数
/// * `process_result` - 结果处理函数
async fn execute_http_task<T, F>(
    operation: &str,
    params: Value,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
    process_result: F,
) -> Result<T, PdfError>
where
    F: FnOnce(&Value) -> Result<T, PdfError>,
{
    let client = Client::new();
    let api_url = get_api_url();

    // 创建任务请求
    let request_body = serde_json::json!({
        "operation": operation,
        "params": params
    });

    // 发送任务请求
    debug_print!("Sending request to: {}/task", api_url);
    debug_print!("Request body: {}", serde_json::to_string_pretty(&request_body).unwrap_or_default());

    let response = client
        .post(format!("{}/task", api_url))
        .header(CONTENT_TYPE, "application/json")
        .json(&request_body)
        .send()
        .await
        .map_err(|e| {
            debug_print!("HTTP request failed: {}", e);
            PdfError::Other(format!("Failed to send request: {}", e))
        })?;

    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        debug_print!("HTTP request failed with status: {}, body: {}", status, error_text);
        return Err(PdfError::Other(format!(
            "API request failed: {}",
            error_text
        )));
    }

    // 解析任务ID
    let task_result = response
        .json::<serde_json::Value>()
        .await
        .map_err(|e| PdfError::Other(format!("Failed to parse response: {}", e)))?;

    let task_id = task_result["task_id"]
        .as_str()
        .ok_or_else(|| PdfError::Other("No task ID in response".to_string()))?;

    // 监听SSE事件流
    let mut es = EventSource::get(format!("{}/task/{}/events", api_url, task_id));

    let result = Arc::new(Mutex::new(None));
    let result_clone = Arc::clone(&result);

    // 处理事件流
    while let Some(event) = es.next().await {
        match event {
            Ok(Event::Open) => {
                // Connection opened - we can log this or ignore
                debug_print!("EventSource connection opened");
            }
            Ok(Event::Message(message)) => {
                if let Ok(data) = serde_json::from_str::<serde_json::Value>(&message.data) {
                    let event_data = data.get("data").ok_or_else(|| {
                        PdfError::Other(
                            "Invalid SSE event format: missing 'data' field".to_string(),
                        )
                    })?;

                    let status = event_data["status"].as_str().unwrap_or("unknown");

                    match status {
                        "processing" => {
                            let message = event_data["message"].as_str().unwrap_or("");
                            let default_progress = serde_json::json!({"current": 0, "total": 100});
                            let progress_data = event_data.get("data").unwrap_or(&default_progress);
                            let current = progress_data["current"].as_f64().unwrap_or(0.0);
                            let total = progress_data["total"].as_f64().unwrap_or(100.0);
                            progress_callback(current, total, message.to_string());
                        }
                        "completed" => {
                            let message = event_data["message"].as_str().unwrap_or("完成");
                            progress_callback(100.0, 100.0, message.to_string());

                            // 保存结果
                            if let Some(result_data) = event_data.get("result") {
                                *result_clone.lock().unwrap() = Some(result_data.clone());
                            }

                            break;
                        }
                        "error" => {
                            let message = event_data["message"].as_str().unwrap_or("错误");
                            let error = event_data["error"].as_str().unwrap_or("未知错误");
                            progress_callback(100.0, 100.0, message.to_string());
                            return Err(PdfError::Other(error.to_string()));
                        }
                        _ => {}
                    }
                }
            }
            Err(e) => {
                debug_print!("SSE Error: {}", e);
                return Err(PdfError::Other(format!("SSE Error: {}", e)));
            }
        }
    }

    // 处理结果
    // Clone the data from the Arc<Mutex<Option<Value>>> instead of trying to unwrap it
    let result_value = {
        let lock = result
            .lock()
            .map_err(|_| PdfError::Other("Failed to acquire mutex lock".to_string()))?;
        match &*lock {
            Some(json) => json.clone(),
            None => return Err(PdfError::Other("No result received".to_string())),
        }
    };

    // Process the cloned value
    process_result(&result_value)
}

/// 处理PDF转图片的结果，返回HashMap<u32, String>
fn process_pdf2img_result(json: &Value) -> Result<HashMap<u32, String>, PdfError> {
    if json.is_null() {
        return Ok(HashMap::new());
    }

    let mut result_map = HashMap::new();
    if let Value::Object(obj) = json {
        for (key, value) in obj {
            if let Ok(page_num) = key.parse::<u32>() {
                if let Some(path) = value.as_str() {
                    result_map.insert(page_num, path.to_string());
                }
            }
        }
    }

    Ok(result_map)
}

/// 处理注释提取的结果，返回Vec<Annotation>
fn process_annotations_result(json: &Value) -> Result<Vec<Annotation>, PdfError> {
    if json.is_null() {
        debug_print!("Received null JSON result");
        return Ok(Vec::new());
    }

    debug_print!("Received JSON result: {:?}", json);

    // 尝试从不同的格式中提取注释数组
    let annotations_arr = if let Some(arr) = json.get("annotations").and_then(|v| v.as_array()) {
        // 标准格式：{"annotations": [...]}
        arr
    } else if json.is_array() {
        // 直接返回数组：[...]
        json.as_array().unwrap()
    } else {
        // 其他格式，尝试从顶级对象中提取
        debug_print!("JSON format not recognized: {:?}", json);
        return Err(PdfError::Other("返回的JSON格式无法识别".to_string()));
    };

    let mut annotations = Vec::new();
    debug_print!("Found {} annotations", annotations_arr.len());

    for (idx, item) in annotations_arr.iter().enumerate() {
        // 获取必须字段
        let page_num = item["page_num"].as_u64().unwrap_or(0) as u32;
        let column = item["column"].as_u64().unwrap_or(1) as u32;

        // 获取注释类型并转换为枚举
        let annot_type_str = item["annot_type"].as_str().unwrap_or("Other");
        let annot_type = match annot_type_str {
            "Highlight" => AnnotationType::Highlight,
            "Square" => AnnotationType::Square,
            "Circle" => AnnotationType::Circle,
            "Underline" => AnnotationType::Underline,
            "StrikeOut" => AnnotationType::StrikeOut,
            "Squiggly" => AnnotationType::Squiggly,
            "Text" => AnnotationType::Text,
            "Popup" => AnnotationType::Popup,
            _ => AnnotationType::Other(annot_type_str.to_string()),
        };

        // 获取矩形坐标
        let rect = if let Some(rect_arr) = item["rect"].as_array() {
            let coords: Vec<f64> = rect_arr.iter().filter_map(|v| v.as_f64()).collect();

            if coords.len() == 4 {
                [coords[0], coords[1], coords[2], coords[3]]
            } else {
                debug_print!("Invalid rect for annotation {}: {:?}", idx, rect_arr);
                [0.0, 0.0, 0.0, 0.0]
            }
        } else {
            debug_print!("Missing rect for annotation {}", idx);
            [0.0, 0.0, 0.0, 0.0]
        };

        // 获取QuadPoints（可选）
        let quad_points = item["quad_points"]
            .as_array()
            .map(|arr| arr.iter().filter_map(|v| v.as_f64()).collect::<Vec<f64>>());

        // 获取颜色（可选）
        let color = item["color"].as_array().map(|arr| {
            let colors: Vec<f32> = arr
                .iter()
                .filter_map(|v| v.as_f64().map(|f| f as f32))
                .collect();

            if colors.len() == 3 {
                [colors[0], colors[1], colors[2]]
            } else {
                [0.0, 0.0, 0.0]
            }
        });

        // 获取其他可选字段
        let contents = item["contents"].as_str().map(|s| s.to_string());
        let creation_date = item["creation_date"].as_str().map(|s| s.to_string());
        let mod_date = item["mod_date"].as_str().map(|s| s.to_string());
        let id = item["id"].as_str().map(|s| s.to_string());

        // 创建Annotation结构体并添加到结果向量
        annotations.push(Annotation {
            page_num,
            column,
            annot_type,
            rect,
            quad_points,
            color,
            contents,
            creation_date,
            mod_date,
            id,
        });
    }

    debug_print!("Successfully processed {} annotations", annotations.len());
    Ok(annotations)
}

/// 处理通用的字符串结果
fn process_string_result(json: &Value) -> Result<String, PdfError> {
    if json.is_null() {
        return Ok("".to_string());
    }

    match json {
        Value::String(s) => Ok(s.clone()),
        _ => Ok(json.to_string()),
    }
}

pub async fn py_merge_pdf(
    doc_path_list: &Vec<String>,
    merge_type: &str,
    doc_path: &str,
    page_range: &str,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path_list": doc_path_list,
        "type": merge_type,
        "doc_path": doc_path,
        "page_range": page_range,
        "output_path": output_path,
    });

    execute_http_task("pdf_merge", args, progress_callback, process_string_result).await
}

pub async fn py_pdf2img(
    doc_path: &str,
    page_range: &str,
    output_dir: &str,
    dpi: u32,
    is_gray: bool,
    output_format: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<HashMap<u32, String>, PdfError> {
    py_pdf2img_with_scaling(
        doc_path,
        page_range,
        output_dir,
        dpi,
        is_gray,
        output_format,
        3000,  // Default max_width_pixels
        4000,  // Default max_height_pixels
        true,  // Default auto_scale_to_a4
        progress_callback,
    ).await
}

pub async fn py_pdf2img_with_scaling(
    doc_path: &str,
    page_range: &str,
    output_dir: &str,
    dpi: u32,
    is_gray: bool,
    output_format: &str,
    max_width_pixels: u32,
    max_height_pixels: u32,
    auto_scale_to_a4: bool,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<HashMap<u32, String>, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_dir": output_dir,
        "page_range": page_range,
        "format": output_format,
        "dpi": dpi,
        "is_gray": is_gray,
        "max_width_pixels": max_width_pixels,
        "max_height_pixels": max_height_pixels,
        "auto_scale_to_a4": auto_scale_to_a4,
    });

    execute_http_task("pdf2img", args, progress_callback, process_pdf2img_result).await
}

pub async fn py_epub2pdf(
    doc_path: &str,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path
    });

    execute_http_task("epub2pdf", args, progress_callback, process_string_result).await
}

pub async fn py_mobi2pdf(
    doc_path: &str,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path
    });

    execute_http_task("mobi2pdf", args, progress_callback, process_string_result).await
}

pub async fn py_ofd2pdf(
    doc_path: &str,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path
    });

    execute_http_task("ofd2pdf", args, progress_callback, process_string_result).await
}

pub async fn py_docx2pdf(
    doc_path: &str,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path
    });

    execute_http_task("docx2pdf", args, progress_callback, process_string_result).await
}

pub async fn py_pdf2docx(
    doc_path: &str,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path
    });

    execute_http_task("pdf2docx", args, progress_callback, process_string_result).await
}

pub async fn py_pdf_recover_permission(
    doc_path: &str,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path
    });

    execute_http_task(
        "pdf_recover_permission",
        args,
        progress_callback,
        process_string_result,
    )
    .await
}

pub async fn py_pdf_scale(
    doc_path: &str,
    output_path: &str,
    scale_type: &str,
    scale_ratio: f64,
    width: f64,
    height: f64,
    unit: &str,
    paper_size: &str,
    page_range: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "type": scale_type,
        "ratio": scale_ratio,
        "width": width,
        "height": height,
        "unit": unit,
        "paper_size": paper_size,
        "page_range": page_range
    });
    execute_http_task("pdf_scale", args, progress_callback, process_string_result).await
}

pub async fn py_pdf_crop(
    doc_path: &str,
    output_path: &str,
    crop_type: &str,
    expand_mode: bool,
    top: f64,
    bottom: f64,
    left: f64,
    right: f64,
    unit: &str,
    keep_annotation: bool,
    keep_page_size: bool,
    output_format: &str,
    dpi: u64,
    page_range: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "type": crop_type,
        "top": top,
        "bottom": bottom,
        "left": left,
        "right": right,
        "unit": unit,
        "dpi": dpi,
        "expand_mode": expand_mode,
        "keep_page_size": keep_page_size,
        "keep_annotation": keep_annotation,
        "page_range": page_range,
        "format": output_format
    });
    execute_http_task("pdf_crop", args, progress_callback, process_string_result).await
}

pub async fn py_pdf_insert(
    doc_path: &str,
    output_path: &str,
    insert_mode: &str,
    insert_pos_type: &str,
    insert_pos: u64,
    count: u64,
    orientation: &str,
    paper_size: &str,
    page_range: &str,
    doc_path2: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "insert_mode": insert_mode,
        "pos_type": insert_pos_type,
        "insert_pos": insert_pos,
        "count": count,
        "orientation": orientation,
        "paper_size": paper_size,
        "page_range": page_range,
        "doc_path2": doc_path2
    });
    execute_http_task("pdf_insert", args, progress_callback, process_string_result).await
}

pub async fn py_pdf_expand(
    doc_path: &str,
    output_path: &str,
    expand_type: &str,
    top: f64,
    bottom: f64,
    left: f64,
    right: f64,
    unit: &str,
    bg_doc_path: &str,
    direction: &str,
    page_range: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "type": expand_type,
        "top": top,
        "bottom": bottom,
        "left": left,
        "right": right,
        "unit": unit,
        "bg_doc_path": bg_doc_path,
        "direction": direction,
        "page_range": page_range
    });
    execute_http_task("pdf_expand", args, progress_callback, process_string_result).await
}

pub async fn py_pdf_split(
    doc_path: &str,
    output_path: &str,
    split_type: &str,
    chunk_size: u64,
    level: u64,
    page_range: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "type": split_type,
        "chunk_size": chunk_size,
        "level": level,
        "page_range": page_range
    });
    execute_http_task("pdf_split", args, progress_callback, process_string_result).await
}

pub async fn py_pdf_cut(
    doc_path: &str,
    output_path: &str,
    cut_type: &str,
    n_row: u64,
    n_col: u64,
    paper_size: &str,
    orientation: &str,
    top: f64,
    bottom: f64,
    left: f64,
    right: f64,
    h_breakpoints: &Vec<serde_json::Value>,
    v_breakpoints: &Vec<serde_json::Value>,
    page_range: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "type": cut_type,
        "n_row": n_row,
        "n_col": n_col,
        "paper_size": paper_size,
        "orientation": orientation,
        "top": top,
        "bottom": bottom,
        "left": left,
        "right": right,
        "h_breakpoints": h_breakpoints,
        "v_breakpoints": v_breakpoints,
        "page_range": page_range
    });

    execute_http_task("pdf_cut", args, progress_callback, process_string_result).await
}

pub async fn py_pdf_combine(
    doc_path: &str,
    output_path: &str,
    n_row: u64,
    n_col: u64,
    layout_order: &str,
    page_range: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "n_row": n_row,
        "n_col": n_col,
        "layout_order": layout_order,
        "page_range": page_range
    });

    execute_http_task(
        "pdf_combine",
        args,
        progress_callback,
        process_string_result,
    )
    .await
}

pub async fn py_pdf_background(
    doc_path: &str,
    background_path: &str,
    output_path: &str,
    page_range: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "bg_doc_path": background_path,
        "page_range": page_range
    });

    execute_http_task(
        "pdf_background",
        args,
        progress_callback,
        process_string_result,
    )
    .await
}

pub async fn py_pdf_reorder(
    doc_path: &str,
    output_path: &str,
    page_range: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "page_range": page_range
    });
    execute_http_task(
        "pdf_reorder",
        args,
        progress_callback,
        process_string_result,
    )
    .await
}

pub async fn py_pdf_meta(
    doc_path: &str,
    output_path: &str,
    title: &str,
    author: &str,
    subject: &str,
    keywords: &str,
    creator: &str,
    producer: &str,
    creation_date: &str,
    mod_date: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "title": title,
        "author": author,
        "subject": subject,
        "keywords": keywords,
        "creator": creator,
        "producer": producer,
        "creation_date": creation_date,
        "mod_date": mod_date
    });
    execute_http_task("pdf_meta", args, progress_callback, process_string_result).await
}

/// 提取PDF文件中的注释
pub async fn py_extract_annotations(
    doc_path: &str,
    page_range: &str,
    num_cols: u32,
    is_full_page_mode: bool,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<Vec<Annotation>, PdfError> {
    let args = serde_json::json!({
        "input_path": doc_path,
        "page_range": page_range,
        "num_cols": num_cols,
        "is_full_page_mode": is_full_page_mode
    });

    execute_http_task(
        "extract_annotations",
        args,
        progress_callback,
        process_annotations_result,
    )
    .await
}

/// 文本转语音
pub async fn py_text_to_speech(
    text: &str,
    voice_name: &str,
    rate: &str,
    volume: &str,
    pitch: &str,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "text": text,
        "voice_name": voice_name,
        "rate": rate,
        "volume": volume,
        "pitch": pitch,
        "output_path": output_path
    });

    rinf::debug_print!("准备调用execute_http_task");
    execute_http_task(
        "text_to_speech",
        args,
        progress_callback,
        process_string_result,
    )
    .await
}

/// PDF图片提取
pub async fn py_pdf_extract_images(
    doc_path: &str,
    output_path: &str,
    page_range: &str,
    image_format: &str,
    min_width: i32,
    min_height: i32,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "page_range": page_range,
        "image_format": image_format,
        "min_width": min_width,
        "min_height": min_height
    });

    execute_http_task(
        "pdf_extract_images",
        args,
        progress_callback,
        process_string_result,
    )
    .await
}

/// PDF页面转图片
pub async fn py_pdf_extract_pages_as_images(
    doc_path: &str,
    output_path: &str,
    page_range: &str,
    dpi: i32,
    image_format: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    let args = serde_json::json!({
        "doc_path": doc_path,
        "output_path": output_path,
        "page_range": page_range,
        "dpi": dpi,
        "image_format": image_format
    });

    execute_http_task(
        "pdf_extract_pages_as_images",
        args,
        progress_callback,
        process_string_result,
    )
    .await
}


/// 统一的水印检测函数
pub async fn py_detect_watermark(
    watermark_type: &str,
    doc_path: &str,
    page_range: &str,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    // Parse page_range to get the first page number for detection
    let wm_page_number = if page_range.is_empty() {
        0 // Default to first page (0-indexed)
    } else {
        // Parse the first page from the range (convert from 1-indexed to 0-indexed)
        page_range.split(',').next()
            .and_then(|s| s.trim().parse::<i32>().ok())
            .map(|p| p - 1)
            .unwrap_or(0)
            .max(0)
    };

    // Determine the Python endpoint and build arguments based on watermark type
    let (endpoint, args) = match watermark_type {
        "type" => (
            "detect_watermark_by_type",
            serde_json::json!({
                "doc_path": doc_path,
                "wm_page_number": wm_page_number,
                "output_path": output_path
            })
        ),
        "content" => (
            "detect_watermark_by_content",
            serde_json::json!({
                "doc_path": doc_path,
                "wm_page_number": wm_page_number,
                "output_path": output_path
            })
        ),
        "image" => (
            "detect_watermark_by_image",
            serde_json::json!({
                "doc_path": doc_path,
                "wm_page_number": wm_page_number,
                "output_path": output_path
            })
        ),
        "path" => (
            "detect_watermark_by_path",
            serde_json::json!({
                "doc_path": doc_path,
                "wm_page_number": wm_page_number,
                "output_path": output_path
            })
        ),
        _ => {
            return Err(PdfError::Other(format!("Unsupported watermark detection type: {}", watermark_type)));
        }
    };

    execute_http_task(
        endpoint,
        args,
        progress_callback,
        process_string_result,
    )
    .await
}

/// 统一的水印去除函数
pub async fn py_remove_watermark(
    watermark_type: &str,
    doc_path: &str,
    page_range: &str,
    wm_index: &Vec<i32>,
    wm_index_str: Option<&str>,
    wm_text: Option<&str>,
    threshold_below: Option<i32>,
    threshold_high: Option<i32>,
    threshold_target: Option<&str>,
    is_limit_region: Option<bool>,
    dpi: Option<i32>,
    output_path: &str,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<String, PdfError> {
    // Determine the Python endpoint and build arguments based on watermark type
    let (endpoint, args) = match watermark_type {
        "type" => (
            "remove_watermark_by_type",
            serde_json::json!({
                "doc_path": doc_path,
                "page_range": page_range,
                "wm_index": wm_index,
                "output_path": output_path
            })
        ),
        "content" => (
            "remove_watermark_by_content",
            serde_json::json!({
                "doc_path": doc_path,
                "page_range": page_range,
                "wm_index": wm_index,
                "output_path": output_path
            })
        ),
        "image" => (
            "remove_watermark_by_image",
            serde_json::json!({
                "doc_path": doc_path,
                "page_range": page_range,
                "wm_index_list": wm_index,
                "output_path": output_path
            })
        ),
        "text" => (
            "remove_watermark_by_text",
            serde_json::json!({
                "doc_path": doc_path,
                "page_range": page_range,
                "wm_text": wm_text.unwrap_or(""),
                "output_path": output_path
            })
        ),
        "path" => (
            "remove_watermark_by_path",
            serde_json::json!({
                "doc_path": doc_path,
                "page_range": page_range,
                "wm_index_str": wm_index_str.unwrap_or(""),
                "output_path": output_path
            })
        ),
        "pixel" => (
            "remove_watermark_by_pixel",
            serde_json::json!({
                "doc_path": doc_path,
                "page_range": page_range,
                "threshold_below": threshold_below.unwrap_or(128),
                "threshold_high": threshold_high.unwrap_or(255),
                "threshold_target": threshold_target.unwrap_or("#ffffff"),
                "is_limit_region": is_limit_region.unwrap_or(false),
                "dpi": dpi.unwrap_or(300),
                "output_path": output_path
            })
        ),
        _ => {
            return Err(PdfError::Other(format!("Unsupported watermark removal type: {}", watermark_type)));
        }
    };

    execute_http_task(
        endpoint,
        args,
        progress_callback,
        process_string_result,
    )
    .await
}



