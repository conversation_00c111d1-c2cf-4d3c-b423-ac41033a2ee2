#![allow(unused)]

use crate::anki::models::{gen_apkg, AnkiNote};
use crate::anki::pdf_card::common::{
    clean_page_annotations, get_page_subdeck_map, get_subdeck_by_bookmark, group_page_annotations,
    process_card_image_and_masks,
};
use crate::anki::pdf_utils::{
    delete_annotations, export_pdf_to_images, extract_annotations, get_pdfium, parse_page_ranges,
};
use crate::anki::types::{Annotation, AnnotationType, PdfError};

use genanki_rs::{Deck, Note, Package};
use image::ImageFormat;
use indexmap::indexmap;
use pdfium_render::prelude::*;
use rand::Rng;
use rinf::debug_print;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::collections::HashSet;
use std::path::{Path, PathBuf};
use thiserror::Error;
use urlencoding;

#[derive(Debug)]
pub struct OcclusionCardConfig {
    pub doc_path: String,
    pub page_range: String,
    pub address: String,
    pub deck_name: String,
    pub model_name: String,
    pub mask_types: Vec<String>,
    pub card_type: String,
    pub one_card_per_cloze: bool,
    pub q_mask_color: String,
    pub a_mask_color: String,
    pub tags: Vec<String>,
    pub dpi: u32,
    pub is_full_page_mode: bool,
    pub is_create_subdeck: bool,
    pub subdeck_max_level: u32,
    pub is_mix_card: bool,
    pub is_show_source: bool,
    pub num_cols: u32,
    pub extra_info: Option<String>,
    pub item_key: Option<String>,
    pub output_path: String,
}

pub async fn make_occlusion_card(
    config: OcclusionCardConfig,
    progress_callback: impl Fn(f64, f64, String) + Send + Clone + 'static,
) -> Result<(), PdfError> {
    let doc_path = config.doc_path.clone();
    let mut item_key = config.item_key.clone().unwrap_or_else(|| "".to_string());
    progress_callback(5.0, 100.0, "正在提取批注...".to_string());
    let annotations = extract_annotations(
        doc_path.clone(),
        &config.page_range,
        config.num_cols,
        config.is_full_page_mode,
    )
    .await?;
    debug_print!("annotations: {:?}", &annotations);
    progress_callback(10.0, 100.0, "正在整理批注...".to_string());
    // 先按页码分组
    let mut page_annotations: HashMap<u32, Vec<Annotation>> = HashMap::new();
    for annotation in annotations {
        page_annotations
            .entry(annotation.page_num)
            .or_default()
            .push(annotation);
    }
    // 对每页进行分组，然后合并所有结果
    let mut all_groups: Vec<Vec<Annotation>> = Vec::new();
    let mut extra_annotations_to_delete = Vec::new();
    let mut all_cloze_colors = Vec::new();
    for page_num in page_annotations.keys() {
        if let Some(page_annots) = page_annotations.get(page_num) {
            let (page_groups, annotations_to_delete, cloze_colors) =
                group_page_annotations(page_annots.clone());
            all_groups.extend(page_groups);
            extra_annotations_to_delete.extend(annotations_to_delete);
            all_cloze_colors.extend(cloze_colors);
        }
    }
    // 按page、column、rect上坐标、rect左坐标排序
    let mut sorted_groups = all_groups;
    if config.is_mix_card {
        // 对 all_cloze_colors 去重
        let mut unique_colors: HashSet<String> = HashSet::new();
        for color in all_cloze_colors.iter() {
            unique_colors.insert(color.clone());
        }

        // 过滤 sorted_groups，只保留第一个元素的颜色在 unique_colors 中的组
        sorted_groups.retain(|group| {
            if let Some(first_annot) = group.first() {
                if let Some(color) = &first_annot.color {
                    return unique_colors.contains(&format!("{:?}", color));
                }
            }
            false
        });
    }
    sorted_groups.sort_by(|a, b| {
        // 获取每组的第一个元素
        let first_a = &a[0];
        let first_b = &b[0];

        // 先按page_num排序
        let page_cmp = first_a.page_num.cmp(&first_b.page_num);
        if page_cmp != std::cmp::Ordering::Equal {
            return page_cmp;
        }

        // 再按column排序
        let column_cmp = first_a.column.cmp(&first_b.column);
        if column_cmp != std::cmp::Ordering::Equal {
            return column_cmp;
        }

        // 再按rect上坐标(y坐标)排序
        let y_cmp = first_b.rect[3]
            .partial_cmp(&first_a.rect[3])
            .unwrap_or(std::cmp::Ordering::Equal);
        if y_cmp != std::cmp::Ordering::Equal {
            return y_cmp;
        }

        // 最后按rect左坐标(x坐标)排序
        first_a.rect[0]
            .partial_cmp(&first_b.rect[0])
            .unwrap_or(std::cmp::Ordering::Equal)
    });
    progress_callback(15.0, 100.0, "正在渲染图片...".to_string());
    let image_map = clean_page_annotations(
        &Path::new(&doc_path),
        sorted_groups.clone(),
        extra_annotations_to_delete,
        "png",
        config.is_full_page_mode,
    )
    .await?;
    debug_print!("image_map: {:?}", &image_map);
    let temp_dir = crate::anki::utils::get_temp_dir()
        .await
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))?;
    // 合并组
    let mut merged_groups = Vec::new();
    let mut current_group = Vec::new();
    for group in sorted_groups {
        // 获取当前组的第一个元素的contents
        let first_annot = &group[0];
        let contents = first_annot
            .contents
            .as_ref()
            .map(|s| s.as_str())
            .unwrap_or("");

        if contents.starts_with("^") && !contents.starts_with("^^") {
            // 遇到新组标记，保存旧组并开始新组
            if !current_group.is_empty() {
                merged_groups.push(current_group);
                current_group = Vec::new();
            }
            current_group.push(group);
        } else if contents.starts_with("^^") {
            // 合并到当前组
            if !current_group.is_empty() {
                current_group.push(group);
            } else {
                // 如果没有前导组，作为独立组
                merged_groups.push(vec![group]);
            }
        } else {
            // 不以^开头，作为新组
            if !current_group.is_empty() {
                merged_groups.push(current_group);
                current_group = Vec::new();
            }
            merged_groups.push(vec![group]);
        }
    }

    // 处理最后一个组
    if !current_group.is_empty() {
        merged_groups.push(current_group);
    }

    let mut final_notes = Vec::new();
    let mut final_image_paths = Vec::new();
    let mut total_cards = merged_groups.len() as f64;
    let mut current_card = 0.0;
    debug_print!("generate page_map...");
    let page_map = get_page_subdeck_map(Path::new(&doc_path))
        .await
        .map_err(|e| PdfError::Other(e.to_string()))?;
    // dbg!("page_map: {:?}", &page_map);
    // dbg!("merged_groups: {:?}", &merged_groups);
    for card in merged_groups {
        current_card += 1.0;
        progress_callback(
            30.0 + (current_card / total_cards) * 60.0,
            100.0,
            "正在生成卡片...".to_string(),
        );

        let (final_image_path, final_masks) = process_card_image_and_masks(
            &card,
            &config.mask_types,
            &doc_path,
            &temp_dir,
            &image_map,
        )
        .await?;

        final_image_paths.push(final_image_path.clone());

        let masks_str = serde_json::to_string(&final_masks).unwrap_or_default();
        let page_num = card[0][0].page_num.clone();
        let mut cid = card[0][0].id.clone();
        if cid.is_none() {
            let rect = &card[0][0].rect;
            cid = Some(format!(
                "{}-[{:.3}, {:.3}, {:.3}, {:.3}]",
                card[0][0].page_num, rect[0], rect[1], rect[2], rect[3]
            ));
        }

        let card_contents = card[0][0].contents.clone();
        let (mut header, mut notes) = if let Some(contents) = card_contents {
            // Remove leading ^ characters
            let contents = contents.trim_start_matches('^');

            // Split by "---" if it exists
            if let Some((h, n)) = contents.split_once("---") {
                (h.trim().to_string(), n.trim().to_string())
            } else {
                // No separator found, treat all as notes
                ("".to_string(), contents.trim().to_string())
            }
        } else {
            ("".to_string(), "".to_string())
        };
        // 从notes中提取标签
        let mut tags = config.tags.clone();
        if !notes.is_empty() {
            // 使用正则表达式匹配#开头的标签
            let re = regex::Regex::new(r"#([^\s#]+)").unwrap();
            for cap in re.captures_iter(&notes) {
                if let Some(tag) = cap.get(1) {
                    tags.push(tag.as_str().to_string());
                }
            }
            // 删除notes中的标签
            notes = re.replace_all(&notes, "").to_string();
        }
        let re_reverse = regex::Regex::new(r"(?s)^(.*)\r?\n[～~]\r?\n((?s:.*))$").unwrap();
        let mut is_reverse = false;
        if re_reverse.is_match(&notes) {
            is_reverse = true;
            notes = re_reverse.replace(&notes, "${1}${2}").trim().to_string();
            if header.is_empty() {
                header = notes.clone();
                notes = "".to_string();
            }
        }
        // 替换notes中的换行为<br>
        let notes = notes.replace('\n', "<br>");
        let file_name = final_image_path
            .file_name()
            .ok_or_else(|| PdfError::Other("Invalid image path".to_string()))?
            .to_str()
            .ok_or_else(|| PdfError::Other("Invalid file name".to_string()))?;
        // println!("page_map: {:?}", page_map);
        let deck_name = get_subdeck_by_bookmark(
            &page_map,
            config.deck_name.clone(),
            config.is_create_subdeck,
            config.subdeck_max_level,
            page_num,
        )?;

        let mut source = if config.is_show_source {
            let mut label = get_subdeck_by_bookmark(
                &page_map,
                config.deck_name.clone(),
                true,
                config.subdeck_max_level,
                page_num,
            )?;

            if let Some(cid_value) = cid.as_ref() {
                let cid_lower = cid_value.to_lowercase();
                if cid_lower.starts_with("zotero") {
                    // 处理以zotero开头的cid
                    let link = format!(
                        "zotero://open-pdf/library/items/{}?page={}",
                        &item_key, page_num
                    );
                    format!("<div>From: <a href=\"{}\">P{}</a></div>", link, page_num)
                } else {
                    let link = format!(
                        "guru2://seek?category=pdf&path={}&page={}",
                        urlencoding::encode(&doc_path.to_string()),
                        page_num
                    );
                    // 以::切割，从第二个元素开始用"->"拼接，包含第二个元素
                    let parts: Vec<&str> = label.split("::").collect();
                    if parts.len() >= 2 {
                        let first = parts[0];
                        let rest = parts[1..].join("->");
                        label = format!("{}", rest);
                        format!(
                            "<div>From: <a href=\"{}\">{}(P{})</a></div>",
                            link, label, page_num
                        )
                    } else {
                        format!("<div>From: <a href=\"{}\">P{}</a></div>", link, page_num)
                    }
                }
            } else {
                "".to_string() // Handle the case where cid is None
            }
        } else {
            "".to_string()
        };
        let extra_info = config.extra_info.clone();
        if let Some(info) = extra_info {
            source = format!("{}<div style=\"font-size:12px;color:#C0C0C0;text-align:left;font-family:Arial\">{}</div>", source, info);
        }
        if config.one_card_per_cloze
            && (config.card_type == "mask_one_guess_one"
                || config.card_type == "mask_all_guess_one")
        {
            let masks_len = final_masks.len();
            for i in 0..masks_len {
                let fields = indexmap! {
                    "ID" => format!(
                        "{}__{:06x}",
                        cid.clone().unwrap(),
                        rand::thread_rng().gen::<u32>() & 0xFFFFFF
                    ),
                    "Header" => header.clone(),
                    "Image" => format!("<img src=\"{file_name}\" />"),
                    "Text" => "".to_string(),
                    "Masks" => masks_str.clone(),
                    "Source" => source.clone(),
                    "Notes" => notes.clone(),
                    "Mode" => format!("{}_multi", config.card_type),
                    "Index" => format!("c{}", i + 1),
                    "Colors" => format!("{},{}", config.q_mask_color, config.a_mask_color),
                    "Reversed" => if is_reverse { "true".to_string() } else { "".to_string() },
                };
                final_notes.push(AnkiNote {
                    deck_name: deck_name.clone(),
                    model_name: config.model_name.clone(),
                    fields: fields
                        .into_iter()
                        .map(|(key, value)| value.to_string())
                        .collect(),
                    tags: Some(tags.clone()),
                    guid: Some(format!(
                        "{}_{}_{}",
                        cid.clone().unwrap(),
                        config.card_type,
                        i + 1
                    )),
                });
            }
        } else {
            let fields = indexmap! {
                "ID" => format!(
                    "{}__{:06x}",
                    cid.clone().unwrap(),
                    rand::thread_rng().gen::<u32>() & 0xFFFFFF
                ),
                "Header" => header.clone(),
                "Image" => format!("<img src=\"{file_name}\" />"),
                "Text" => "".to_string(),
                "Masks" => masks_str.clone(),
                "Source" => source.clone(),
                "Notes" => notes.clone(),
                "Mode" => config.card_type.clone(),
                "Index" => "".to_string(),
                "Colors" => format!("{},{}", config.q_mask_color, config.a_mask_color),
                "Reversed" => if is_reverse { "true".to_string() } else { "".to_string() },
            };
            final_notes.push(AnkiNote {
                deck_name: deck_name.clone(),
                model_name: config.model_name.clone(),
                fields: fields
                    .into_iter()
                    .map(|(key, value)| value.to_string())
                    .collect(),
                tags: Some(tags.clone()),
                guid: Some(format!(
                    "{}_{}_{}",
                    cid.clone().unwrap(),
                    config.card_type,
                    ""
                )),
            });
        }
    }
    progress_callback(90.0, 100.0, "开始导出卡片...".to_string());

    // 将 PathBuf 转换为 String
    let image_paths: Vec<String> = final_image_paths
        .into_iter()
        .map(|path| path.to_string_lossy().into_owned())
        .collect();
    // dbg!("final_notes: {:?}", &final_notes);
    // 检查 final_notes 是否为空
    if final_notes.is_empty() {
        return Err(PdfError::Other(
            "没有发现卡片。请检查是否已添加批注及批注规范性！".to_string(),
        ));
    }
    // 使用 gen_apkg 生成文件
    gen_apkg(
        final_notes,
        Some(image_paths),
        &config.output_path,
        Some(&config.address),
        true, // 生成后删除临时媒体文件
        Some(vec!["image_cloze_card".to_string()]),
    )
    .await
    .map_err(|e| PdfError::Other(e.to_string()))?;
    progress_callback(100.0, 100.0, "已完成".to_string());
    Ok(())
}
