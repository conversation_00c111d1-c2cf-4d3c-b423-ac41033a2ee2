// Copyright: Ankitects Pty Ltd and contributors
// License: GNU AGPL, version 3 or later; http://www.gnu.org/licenses/agpl.html

use anki::card::CardId;
use anki::collection::Collection;
use anki::collection::CollectionBuilder;
use anki::decks::DeckId;
use anki::import_export::package::ImportAnkiPackageOptions;
use anki::import_export::package::UpdateCondition;
use anki::notetype::render::RenderCardOutput;
use anki::prelude::*;
use anki::search::{SearchNode, SortMode};
use anki::template::RenderedNode;

use anyhow::{anyhow, Result};
use chrono::Local;
use rinf::debug_print;
use sanitize_filename;
use serde::Serialize;
use std::collections::HashMap;
use std::fs;
use std::fs::File;
use std::io::Write;
use std::path::{Path, PathBuf};
use tempfile::tempdir;
use zip::ZipArchive;

/// Structure to hold rendered card HTML content
#[derive(Debug, Serialize)]
pub struct CardHtml {
    pub card_id: i64,
    pub front: String,
    pub back: String,
    pub css: String,
    pub deck_name: String,
    pub note_id: i64,
    pub card_ord: u32,
    pub tags: Vec<String>,
}

/// Structure to hold exported deck information
#[derive(Debug, Serialize)]
pub struct ExportedDeck {
    pub name: String,
    pub cards: Vec<CardHtml>,
}

/// Convert card nodes to HTML text
fn nodes_to_html(nodes: &[RenderedNode]) -> String {
    let mut result = String::new();
    for node in nodes {
        match node {
            RenderedNode::Text { text } => result.push_str(text),
            RenderedNode::Replacement { current_text, .. } => result.push_str(current_text),
        }
    }
    result
}

/// Exports all cards from an APKG file as HTML
pub fn export_html_from_apkg(apkg_path: &str, output_dir: &str) -> Result<Vec<ExportedDeck>> {
    // Create a temporary directory for the extraction
    let temp_dir = tempdir().map_err(|e| anyhow!("Failed to create temporary directory: {}", e))?;
    let temp_path = temp_dir.path();

    // Create a new temporary collection to import the apkg file
    let col_path = temp_path.join("collection.anki2");
    let mut col = CollectionBuilder::new(col_path).build()?;

    // Import the APKG file with default options
    let options = ImportAnkiPackageOptions::default();

    debug_print!("Importing APKG file: {}", apkg_path);
    col.import_apkg(apkg_path, options)?;

    // Extract media
    let media_dir = Path::new(output_dir).join("media");
    fs::create_dir_all(&media_dir)?;
    extract_media_files(apkg_path, &media_dir)?;

    // Use search to find all decks
    let search = SearchNode::WholeCollection;
    let all_notes = col.search_notes(search.clone(), SortMode::NoOrder)?;

    // Collect all unique deck names
    let mut deck_names = HashMap::new();

    for note_id in &all_notes {
        // Find cards for this note
        let card_ids = col.get_note_cards(*note_id)?;

        for &card_id in &card_ids {
            // Get card info
            let info = col.get_card_info(card_id)?;

            // Extract deck name
            let deck_name = info.deck_name;
            if !deck_name.is_empty() {
                let deck_id = info.deck_id;
                deck_names.insert(deck_id, deck_name);
            }
        }
    }

    let mut exported_decks = Vec::new();

    // Process each deck
    for (deck_id, deck_name) in deck_names {
        debug_print!("Processing deck: {}", deck_name);

        // Search for all cards in this deck
        let search = SearchNode::Deck(deck_name.clone());
        let card_ids = col.search_cards(search, SortMode::NoOrder)?;

        if card_ids.is_empty() {
            debug_print!("No cards found in deck: {}", deck_name);
            continue;
        }

        // Create a directory for the deck if it doesn't exist
        let deck_dir = Path::new(output_dir).join(sanitize_filename::sanitize(&deck_name));
        fs::create_dir_all(&deck_dir)?;

        let mut cards = Vec::new();

        // Process each card
        for &card_id in &card_ids {
            match export_card_html(&mut col, card_id, &deck_name, &deck_dir)? {
                Some(card_html) => cards.push(card_html),
                None => continue,
            }
        }

        // Create an index.html file
        create_index_html(&deck_dir, &deck_name, &cards)?;

        exported_decks.push(ExportedDeck {
            name: deck_name,
            cards,
        });
    }

    // Create a main index.html file in the output directory
    create_main_index_html(output_dir, &exported_decks)?;

    debug_print!("HTML export completed to: {}", output_dir);
    Ok(exported_decks)
}

/// Extract a single card's HTML content
fn export_card_html(
    col: &mut Collection,
    card_id: CardId,
    deck_name: &str,
    output_dir: &Path,
) -> Result<Option<CardHtml>> {
    // Get card info to access public fields
    let card_info = col.get_card_info(card_id)?;

    // Render the card to plain text and HTML
    let card_output = col.render_existing_card(card_id, false, false)?;

    // Extract HTML content using question() and answer() methods
    let front = card_output.question().to_string();
    let back = card_output.answer().to_string();

    // Skip empty cards
    if front.trim().is_empty() && back.trim().is_empty() {
        return Ok(None);
    }

    // Get the CSS
    let css = card_output.css;

    // Create card HTML file
    let card_filename = format!("card_{}.html", card_id.0);
    let card_path = output_dir.join(&card_filename);

    // Create the HTML content with front and back
    let html_content = format!(
        r#"<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card {}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}
        .card-section {{
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 4px;
        }}
        .front {{
            background-color: #e7f5fe;
            border-left: 4px solid #2196F3;
        }}
        .back {{
            background-color: #f1f8e9;
            border-left: 4px solid #8bc34a;
        }}
        h1 {{
            color: #333;
            font-size: 24px;
            margin-bottom: 20px;
        }}
        h2 {{
            color: #555;
            font-size: 18px;
            margin-bottom: 10px;
        }}
        .navigation {{
            margin-top: 20px;
            text-align: center;
        }}
        .navigation a {{
            display: inline-block;
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 0 5px;
        }}
        .navigation a:hover {{
            background-color: #45a049;
        }}
        /* Anki CSS */
        {}
    </style>
</head>
<body>
    <div class="container">
        <h1>Card {} - {}</h1>
        
        <h2>Front</h2>
        <div class="card-section front">
            {}
        </div>
        
        <h2>Back</h2>
        <div class="card-section back">
            {}
        </div>
        
        <div class="navigation">
            <a href="index.html">Back to Deck</a>
        </div>
    </div>
</body>
</html>"#,
        card_id.0, css, card_id.0, deck_name, front, back
    );

    // Write the HTML file
    fs::write(&card_path, html_content)?;

    // Get the tags
    let tags = col.get_note_tags(card_info.note_id)?;

    Ok(Some(CardHtml {
        card_id: card_id.0,
        front,
        back,
        css,
        deck_name: deck_name.to_string(),
        note_id: card_info.note_id.0,
        card_ord: card_info.ord as u32,
        tags,
    }))
}

/// Create an index.html file for a deck
fn create_index_html(deck_dir: &Path, deck_name: &str, cards: &[CardHtml]) -> Result<()> {
    let index_path = deck_dir.join("index.html");

    let mut card_list_html = String::new();
    for (i, card) in cards.iter().enumerate() {
        card_list_html.push_str(&format!(
            r#"<tr>
                <td>{}</td>
                <td><a href="card_{}.html">Card {}</a></td>
                <td>{}</td>
            </tr>"#,
            i + 1,
            card.card_id,
            card.card_id,
            format!(
                "{:.40}...",
                card.front.replace("<", "&lt;").replace(">", "&gt;")
            )
        ));
    }

    let html_content = format!(
        r#"<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{} - Anki Deck</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}
        h1 {{
            color: #333;
            margin-bottom: 20px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        th, td {{
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        .navigation {{
            margin-top: 20px;
            text-align: center;
        }}
        .navigation a {{
            display: inline-block;
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 0 5px;
        }}
        .navigation a:hover {{
            background-color: #45a049;
        }}
        .stats {{
            margin: 20px 0;
            padding: 15px;
            background-color: #e7f5fe;
            border-radius: 4px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{} - Anki Deck</h1>
        
        <div class="stats">
            <p><strong>Total cards:</strong> {}</p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>Card ID</th>
                    <th>Front (Preview)</th>
                </tr>
            </thead>
            <tbody>
                {}
            </tbody>
        </table>
        
        <div class="navigation">
            <a href="../index.html">Back to All Decks</a>
        </div>
    </div>
</body>
</html>"#,
        deck_name,
        deck_name,
        cards.len(),
        card_list_html
    );

    fs::write(index_path, html_content)?;
    Ok(())
}

/// Create a main index.html file for all decks
fn create_main_index_html(output_dir: &str, decks: &[ExportedDeck]) -> Result<()> {
    let index_path = Path::new(output_dir).join("index.html");

    let mut deck_list_html = String::new();
    for deck in decks {
        deck_list_html.push_str(&format!(
            r#"<tr>
                <td><a href="{}/index.html">{}</a></td>
                <td>{}</td>
            </tr>"#,
            sanitize_filename::sanitize(&deck.name),
            deck.name,
            deck.cards.len()
        ));
    }

    let html_content = format!(
        r#"<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anki Decks</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}
        h1 {{
            color: #333;
            margin-bottom: 20px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        th, td {{
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        a {{
            color: #2196F3;
            text-decoration: none;
        }}
        a:hover {{
            text-decoration: underline;
        }}
        .stats {{
            margin: 20px 0;
            padding: 15px;
            background-color: #e7f5fe;
            border-radius: 4px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Anki Decks</h1>
        
        <div class="stats">
            <p><strong>Total decks:</strong> {}</p>
            <p><strong>Total cards:</strong> {}</p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Deck Name</th>
                    <th>Cards</th>
                </tr>
            </thead>
            <tbody>
                {}
            </tbody>
        </table>
    </div>
</body>
</html>"#,
        decks.len(),
        decks.iter().map(|d| d.cards.len()).sum::<usize>(),
        deck_list_html
    );

    fs::write(index_path, html_content)?;
    Ok(())
}

/// Extract media files from the APKG file
fn extract_media_files(apkg_path: &str, media_dir: &Path) -> Result<()> {
    // Open the APKG file (which is a ZIP file)
    let file = File::open(apkg_path)?;
    let mut archive = ZipArchive::new(file)?;

    // Extract media.json to get media file mappings
    let media_map = if let Ok(mut media_json) = archive.by_name("media") {
        let mut json_content = String::new();
        std::io::Read::read_to_string(&mut media_json, &mut json_content)?;
        serde_json::from_str::<HashMap<String, String>>(&json_content)?
    } else {
        HashMap::new()
    };

    // Extract all media files
    for i in 0..archive.len() {
        let mut file = archive.by_index(i)?;
        let filename = file.name().to_string();

        // Check if this is a media file
        if let Some(file_id) = filename.strip_prefix("media/") {
            let file_id = file_id.to_string();
            let real_name = media_map.get(&file_id).unwrap_or(&file_id).clone();

            // Create the output file
            let output_path = media_dir.join(&real_name);
            let mut output_file = File::create(output_path)?;

            // Copy the file content
            std::io::copy(&mut file, &mut output_file)?;
        }
    }

    Ok(())
}

/// 从HTML内容中提取卡片的前面和背面内容
fn extract_card_content(html: &str) -> (String, String) {
    // 查找问题部分
    let question = match html.find("<div class=\"card-section question\">") {
        Some(start_idx) => {
            let content_start = start_idx + "<div class=\"card-section question\">".len();
            match html[content_start..].find("</div>") {
                Some(end_idx) => html[content_start..(content_start + end_idx)]
                    .trim()
                    .to_string(),
                None => "".to_string(),
            }
        }
        None => "".to_string(),
    };

    // 查找答案部分
    let answer = match html.find("<div class=\"card-section answer\">") {
        Some(start_idx) => {
            let content_start = start_idx + "<div class=\"card-section answer\">".len();
            match html[content_start..].find("</div>") {
                Some(end_idx) => html[content_start..(content_start + end_idx)]
                    .trim()
                    .to_string(),
                None => "".to_string(),
            }
        }
        None => "".to_string(),
    };

    (question, answer)
}
