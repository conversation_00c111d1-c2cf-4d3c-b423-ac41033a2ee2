use crate::edgetts_rs::websocket::{RobustWebSocket, WebSocketConfig, WebSocketError};
use chrono::{DateTime, Utc};
use futures_util::{SinkExt, StreamExt};
use serde::{Deserialize, Serialize};
use serde_json::json;
use sha2::{Digest, Sha256};
use std::fs::File;
use std::io::Write;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::Duration;
use thiserror::Error;
use tokio::time::timeout;
use tokio_tungstenite::tungstenite::protocol::Message;
use uuid::Uuid;

// Constants
pub const BASE_URL: &str = "speech.platform.bing.com/consumer/speech/synthesize/readaloud";
pub const TRUSTED_CLIENT_TOKEN: &str = "6A5AA1D4EAFF4E9FB37E23D68491D6F4";
pub const WSS_URL: &str = concat!(
    "wss://",
    "speech.platform.bing.com/consumer/speech/synthesize/readaloud",
    "/edge/v1?TrustedClientToken=",
    "6A5AA1D4EAFF4E9FB37E23D68491D6F4"
);
pub const VOICE_LIST: &str = concat!(
    "https://",
    "speech.platform.bing.com/consumer/speech/synthesize/readaloud",
    "/voices/list?trustedclienttoken=",
    "6A5AA1D4EAFF4E9FB37E23D68491D6F4"
);

pub const DEFAULT_VOICE: &str = "en-US-EmmaMultilingualNeural";
pub const CHROMIUM_FULL_VERSION: &str = "130.0.2849.68";
pub const CHROMIUM_MAJOR_VERSION: &str = "130";
pub const SEC_MS_GEC_VERSION: &str = "1-130.0.2849.68";

// Constants for time calculations
const WIN_EPOCH: f64 = 11644473600.0;
const S_TO_NS: f64 = 1e9;

#[derive(Error, Debug)]
pub enum DRMError {
    #[error("Unknown response from server")]
    UnknownResponse,

    #[error("Unexpected response from server")]
    UnexpectedResponse,

    #[error("No audio received from server")]
    NoAudioReceived,

    #[error("WebSocket error: {0}")]
    WebSocketError(String),

    #[error("Clock skew adjustment error: {0}")]
    SkewAdjustmentError(String),
}

pub struct DRM {
    clock_skew_seconds: AtomicU64,
}

impl Default for DRM {
    fn default() -> Self {
        Self::new()
    }
}

impl DRM {
    pub fn new() -> Self {
        Self {
            clock_skew_seconds: AtomicU64::new(f64::to_bits(0.0)),
        }
    }

    pub fn adj_clock_skew_seconds(&self, skew_seconds: f64) {
        let current = f64::from_bits(self.clock_skew_seconds.load(Ordering::SeqCst));
        let new = current + skew_seconds;
        self.clock_skew_seconds
            .store(f64::to_bits(new), Ordering::SeqCst);
    }

    pub fn get_unix_timestamp(&self) -> f64 {
        let skew = f64::from_bits(self.clock_skew_seconds.load(Ordering::SeqCst));
        Utc::now().timestamp() as f64 + skew
    }

    pub fn parse_rfc2616_date(&self, date: &str) -> Option<f64> {
        // Parse RFC 2616 date format: "Wed, 21 Oct 2015 07:28:00 GMT"
        DateTime::parse_from_rfc2822(date)
            .ok()
            .map(|dt| dt.timestamp() as f64)
    }

    pub fn handle_client_response_error(
        &self,
        headers: Option<&reqwest::header::HeaderMap>,
    ) -> Result<(), DRMError> {
        let headers = headers
            .ok_or_else(|| DRMError::SkewAdjustmentError("No headers in response".to_string()))?;

        let server_date = headers
            .get("date")
            .and_then(|h| h.to_str().ok())
            .ok_or_else(|| {
                DRMError::SkewAdjustmentError("No server date in headers".to_string())
            })?;

        let server_date_parsed = self.parse_rfc2616_date(server_date).ok_or_else(|| {
            DRMError::SkewAdjustmentError(format!("Failed to parse server date: {}", server_date))
        })?;

        let client_date = self.get_unix_timestamp();
        self.adj_clock_skew_seconds(server_date_parsed - client_date);

        Ok(())
    }

    pub fn generate_sec_ms_gec(&self) -> String {
        // Get the current timestamp with clock skew correction
        let mut ticks = self.get_unix_timestamp();

        // Switch to Windows file time epoch
        ticks += WIN_EPOCH;

        // Round down to nearest 5 minutes (300 seconds)
        ticks -= ticks % 300.0;

        // Convert to Windows file time format (100-nanosecond intervals)
        ticks *= S_TO_NS / 100.0;

        // Create string to hash
        let str_to_hash = format!("{:.0}{}", ticks, TRUSTED_CLIENT_TOKEN);

        // Compute SHA256 hash
        let mut hasher = Sha256::new();
        hasher.update(str_to_hash.as_bytes());
        format!("{:X}", hasher.finalize())
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct SubLine {
    part: String,
    start: i64,
    end: i64,
}

pub struct EdgeTTS {
    voice: String,
    lang: String,
    output_format: String,
    save_subtitles: bool,
    rate: String,
    pitch: String,
    volume: String,
    timeout: Duration,
}

impl Default for EdgeTTS {
    fn default() -> Self {
        Self::new()
    }
}

impl EdgeTTS {
    pub fn new() -> Self {
        Self {
            voice: "zh-CN-XiaoyiNeural".to_string(),
            lang: "zh-CN".to_string(),
            output_format: "audio-24khz-48kbitrate-mono-mp3".to_string(),
            save_subtitles: false,
            rate: "default".to_string(),
            pitch: "default".to_string(),
            volume: "default".to_string(),
            timeout: Duration::from_secs(10),
        }
    }

    pub fn with_config(
        voice: Option<String>,
        lang: Option<String>,
        output_format: Option<String>,
        save_subtitles: Option<bool>,
        rate: Option<String>,
        pitch: Option<String>,
        volume: Option<String>,
        timeout_secs: Option<u64>,
    ) -> Self {
        Self {
            voice: voice.unwrap_or_else(|| "zh-CN-XiaoyiNeural".to_string()),
            lang: lang.unwrap_or_else(|| "zh-CN".to_string()),
            output_format: output_format
                .unwrap_or_else(|| "audio-24khz-48kbitrate-mono-mp3".to_string()),
            save_subtitles: save_subtitles.unwrap_or(false),
            rate: rate.unwrap_or_else(|| "default".to_string()),
            pitch: pitch.unwrap_or_else(|| "default".to_string()),
            volume: volume.unwrap_or_else(|| "default".to_string()),
            timeout: Duration::from_secs(timeout_secs.unwrap_or(10)),
        }
    }

    async fn connect_websocket(
        &self,
    ) -> Result<RobustWebSocket, Box<dyn std::error::Error + Send + Sync>> {
        let drm = DRM::new();
        let sec_ms_gec = drm.generate_sec_ms_gec();
        println!("Generated sec_ms_gec: {}", sec_ms_gec);

        let ws_url = format!(
            "wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken={}&Sec-MS-GEC={}&Sec-MS-GEC-Version=1-{}",
            TRUSTED_CLIENT_TOKEN,
            sec_ms_gec,
            CHROMIUM_FULL_VERSION
        );
        println!("WebSocket URL: {}", ws_url);

        let config = WebSocketConfig {
            url: ws_url,
            headers: vec![
                ("Host".to_string(), "speech.platform.bing.com".to_string()),
                (
                    "Origin".to_string(),
                    "chrome-extension://jdiccldimpdaibmpdkjnbmckianbfold".to_string(),
                ),
                (
                    "User-Agent".to_string(),
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0".to_string(),
                ),
            ],
            ..Default::default()
        };

        let ws = RobustWebSocket::new(config);
        ws.connect()
            .await
            .map_err(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>)?;
        println!("WebSocket connected successfully");

        let config_msg = format!(
            "Content-Type:application/json; charset=utf-8\r\nPath:speech.config\r\n\r\n{}",
            serde_json::to_string(&json!({
                "context": {
                    "synthesis": {
                        "audio": {
                            "metadataoptions": {
                                "sentenceBoundaryEnabled": "false",
                                "wordBoundaryEnabled": "true"
                            },
                            "outputFormat": self.output_format
                        }
                    }
                }
            }))?
        );
        println!("Sending config message: {}", config_msg);
        ws.send(Message::Text(config_msg))
            .await
            .map_err(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>)?;
        println!("Config message sent successfully");

        Ok(ws)
    }

    fn save_sub_file(
        &self,
        sub_file: Vec<SubLine>,
        text: &str,
        sub_path: &str,
    ) -> Result<(), std::io::Error> {
        let sub_chars: Vec<char> = text.chars().collect();
        let mut sub_char_index = 0;

        let processed_subs = sub_file
            .iter()
            .enumerate()
            .map(|(index, cue)| {
                let mut full_part = String::new();
                let cue_chars: Vec<char> = cue.part.chars().collect();
                let mut step_index = 0;

                for sci in sub_char_index..sub_chars.len() {
                    if step_index < cue_chars.len() && sub_chars[sci] == cue_chars[step_index] {
                        full_part.push(sub_chars[sci]);
                        step_index += 1;
                    } else if index + 1 < sub_file.len()
                        && !sub_file[index + 1].part.is_empty()
                        && sub_chars[sci] == sub_file[index + 1].part.chars().next().unwrap()
                    {
                        sub_char_index = sci;
                        break;
                    } else {
                        full_part.push(sub_chars[sci]);
                    }
                }

                SubLine {
                    part: full_part,
                    start: cue.start,
                    end: cue.end,
                }
            })
            .collect::<Vec<SubLine>>();

        let json = serde_json::to_string_pretty(&processed_subs)?;
        std::fs::write(sub_path, json)?;
        Ok(())
    }

    pub async fn tts_promise(
        &self,
        text: &str,
        audio_path: &str,
        sub_path: Option<&str>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let ws = self.connect_websocket().await?;
        let mut file = File::create(audio_path)?;
        let sub_file: Vec<SubLine> = Vec::new();
        let mut received_audio = false;

        let request_id = Uuid::new_v4().to_string().replace("-", "");
        let ssml = format!(
            "X-RequestId:{}\r\nContent-Type:application/ssml+xml\r\nPath:ssml\r\n\r\n<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xmlns:mstts='https://www.w3.org/2001/mstts' xml:lang='{}'><voice name='{}'><prosody rate='{}' pitch='{}' volume='{}'>{}</prosody></voice></speak>",
            request_id, self.lang, self.voice, self.rate, self.pitch, self.volume, text
        );

        println!("Sending SSML message: {}", ssml);
        ws.send(Message::Text(ssml))
            .await
            .map_err(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>)?;
        println!("SSML message sent successfully");

        let process_messages = async {
            while let Ok(Some(message)) = ws.receive().await {
                match message {
                    Message::Binary(data) => {
                        if let Some(pos) = data
                            .windows(12)
                            .position(|window| window == b"Path:audio\r\n")
                        {
                            let audio_data = &data[pos + 12..];
                            file.write_all(audio_data).map_err(|e| {
                                Box::new(e) as Box<dyn std::error::Error + Send + Sync>
                            })?;
                            received_audio = true;
                        } else {
                            file.write_all(&data).map_err(|e| {
                                Box::new(e) as Box<dyn std::error::Error + Send + Sync>
                            })?;
                            received_audio = true;
                        }
                    }
                    Message::Text(message) => {
                        if message.contains("Path:turn.end") {
                            if self.save_subtitles {
                                if let Some(sub_path) = sub_path {
                                    self.save_sub_file(sub_file.clone(), text, sub_path)
                                        .map_err(|e| {
                                            Box::new(e) as Box<dyn std::error::Error + Send + Sync>
                                        })?;
                                }
                            }
                            break;
                        }
                    }
                    _ => {
                        // Handle other message types (Close, Ping, Pong, Frame)
                        println!("Received other message type");
                    }
                }
            }

            if !received_audio {
                return Err(Box::new(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    "No audio data was received!",
                ))
                    as Box<dyn std::error::Error + Send + Sync>);
            }

            Ok(())
        };

        timeout(self.timeout, process_messages).await??;
        println!("Process completed");
        Ok(())
    }
}

pub async fn gen_tts(
    lang: &str,
    voice: &str,
    rate: &str,
    pitch: &str,
    volume: &str,
    output_format: &str,
    timeout: u64,
    text: &str,
    output_path: &str,
    sub_path: Option<&str>,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let tts = EdgeTTS::with_config(
        Some(voice.to_string()),
        Some(lang.to_string()),
        Some(output_format.to_string()),
        Some(false),
        Some(rate.to_string()),
        Some(pitch.to_string()),
        Some(volume.to_string()),
        Some(timeout),
    );
    if text.is_empty() {
        return Err(Box::new(std::io::Error::new(
            std::io::ErrorKind::Other,
            "Text is empty",
        )) as Box<dyn std::error::Error + Send + Sync>);
    }
    match tts.tts_promise(text, output_path, sub_path).await {
        Ok(_) => Ok(()),
        Err(e) => Err(e),
    }
}
