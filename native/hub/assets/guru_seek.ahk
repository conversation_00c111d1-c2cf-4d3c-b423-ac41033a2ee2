#Requires AutoHotkey v2.0

; 获取命令行参数
if (A_Args.Length < 1) {
    MsgBox "Usage: guru_seek.ahk <URI>"
    ExitApp 1
}

uri := A_Args[1]

; 如果URI是"paste"，则发送粘贴快捷键
if (uri = "paste") {
    SendPaste()
} else {
    ; 否则，发送URI到guru_jump接口
    PostRequest("http://localhost:52025/guru_jump", '{"uri":"' . uri . '"}')
}

; 发送粘贴快捷键的函数
SendPaste() {
    Send "^v"  ; 发送Ctrl+V组合键
}

GetRequest(url) {
    WHR := ComObject("WinHttp.WinHttpRequest.5.1")
    WHR.Open("GET", url, true)
    WHR.Send()
    WHR.WaitForResponse()
    ResponseText := WHR.ResponseText
    ; MsgBox ResponseText
}

PostRequest(url, body) {
    ; Body := '{"key": "value"}'
    WHR := ComObject("WinHttp.WinHttpRequest.5.1")
    WHR.Open("POST", url, true)
    WHR.SetRequestHeader("Content-Type", "application/json")
    WHR.Send(Body)
    ; Using 'true' above and the call below allows the script to remain responsive.
    WHR.WaitForResponse()
    ResponseText := WHR.ResponseText
    ; MsgBox ResponseText
}