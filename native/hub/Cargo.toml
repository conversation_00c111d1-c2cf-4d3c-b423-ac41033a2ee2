[package]
# Do not change the name of this crate.
name = "hub"
version = "0.1.0"
edition = "2021"
# build = "build.rs"

[lib]
# `lib` is required for non-library targets,
# such as tests and benchmarks.
# `cdylib` is for Linux, Android, Windows, and web.
# `staticlib` is for iOS and macOS.
crate-type = ["lib", "cdylib", "staticlib"]


# [[bin]]
# name = "main"
# path = "src/main.rs"

[target.'cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))'.dependencies]
pdfium-render = { git = "https://github.com/ajrcarey/pdfium-render.git" }
killport = { git = "https://github.com/jkfran/killport.git" }

[target.'cfg(any(target_os = "android"))'.dependencies]
openssl = { version = "*", features = ["vendored"] }
pdfium-render = { git = "https://github.com/ajrcarey/pdfium-render.git" }

[target.'cfg(any(target_os = "ios"))'.dependencies]
openssl = { version = "*", features = ["vendored"] }
pdfium-render = { git = "https://github.com/ajrcarey/pdfium-render.git", features = [
    "image",
    "thread_safe",
    "pdfium_latest",
    "static",
    "libc++",
    "core_graphics",
] }

[dependencies]
# Add genai for multi-provider LLM interactions
docx2html = { path = "../docx2html_rs" }
genanki_rs = { path = "../genanki_rs" }
anki = { path = "../../../anki-main/rslib" }
llm = { path = "../llm-main", features = [
    "openai",
    "anthropic",
    "ollama",
    "deepseek",
    "google",
] }
paddle-ocr-rs = { path = "../paddle-ocr-rs" }
rinf = "8.7.1"
lopdf = "0.36.0"
image_025 = { package = "image", version = "0.25", optional = true }
anyhow = "1.0.95"
local-ip-address = "0.6.3"
once_cell = "1.20.2"
tokio = { version = "1", features = ["rt-multi-thread", "macros", "sync"] }
tokio-util = "0.7"
thiserror = "2.0.9"
serde = { version = "1.0", features = ["derive"] }
reqwest = { version = "0.12.9", features = ["json", "multipart"] }
tokio-tungstenite = { version = "0.21.0", features = [
    "native-tls",
    "tokio-native-tls",
] }
md5 = "0.7.0"
log = "0.4.22"
hex = "0.4.3"
regex = "1.11.1"
serde_json = "1.0.133"
ed25519-dalek = "2"
chrono = "0.4.39"

open = "5.3.1"
html-escape = "0.2.13"
encoding_rs = "0.8.35"
image = "0.25.5"
mozjpeg = "0.10"
ulid = "1.1.3"
rand = "0.8.5"
# pyo3 = { version = "0.23.3", features = ["auto-initialize", "extension-module"] }
# anki = { git = "https://gitee.com/Kevin234/anki_rs.git", branch = "main"}
# anki = { path = "/Users/<USER>/Downloads/anki-main/"}
lazy_static = "1.5.0"
tempfile = "3.15.0"
zip = "^2.5"
scraper = "0.22.0"
uuid = { version = "1.12.0", features = ["v4", "fast-rng"] }
futures-util = "0.3.31"
reqwest-websocket = "0.4.4"
sha2 = "0.10.8"
calamine = "0.26.1"
csv = "1.3.0"
# markup5ever = "0.14.0"
urlencoding = "2.1.3"
rayon = "1.10.0"
filetime = "0.2.25"
roxmltree = "0.20.0"
quick-xml = "0.37.2"
dirs = "6.0.0"
rust_search = "2.1.0"
indexmap = "2.7.1"
futures = "0.3.31"
base64 = "0.13"
hashbrown = "0.15.2"
rust_xlsxwriter = "0.86.1"
notify-rust = "4.11.7"
reqwest-eventsource = "0.5.0"
eventsource-stream = "0.2.3"
repair_json = "0.1.0"
chardetng = "0.1.17"
text-splitter = "0.26.0"
html2text = "0.6.0"

# Uncomment below to target the web.
# tokio_with_wasm = { version = "0.7.2", features = ["rt", "macros"] }
# wasm-bindgen = "0.2.95"

[dev-dependencies]
# Testing dependencies
tokio-test = "0.4"
mockall = "0.12"
serial_test = "3.0"
wiremock = "0.6"
tempfile = "3.15.0"

[build-dependencies]
# Bindgen 0.70.0 and later cause build failures when compiling to WASM. For more details, see:
# https://github.com/ajrcarey/pdfium-render/issues/156
bindgen = { version = "<=0.69.4", optional = true }
