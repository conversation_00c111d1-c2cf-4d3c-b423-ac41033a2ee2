// Import required modules from the LLM library for DeepSeek integration
use llm::{
    builder::{LLMBackend, LLMBuilder}, // Builder pattern components
    chat::{ChatMessage, StructuredOutputFormat}, // Chat-related structures
};
use repair_json;

fn test_repair_json() {
    let extracted_json = r#"
[
    {
        "fields": {
            "Front": "什么是电路交换？它的连网方式有什么特点？",
            "Hint": "考虑连接建立的步骤和面向连接的特性",
            "Back": "电路交换必须经过"建立连接→通信→释放连接"三个步骤的连网方式，属于面向连接的连网方式。在电路交换中，整个报文的比特流连续地从源点直达终点，好像在一个管道中传送。"
        },
        "tags": [
            "计算机网络",
            "数据交换技术",
            "电路交换",
            "面向连接",
            "通信方式"
        ],
        "card_type": "qa"
    }
]
"#;
    let fixed_json =
        repair_json::repair(extracted_json).map_err(|e| println!("修复JSON失败: {:?}", &e));
    print!("fixed_json: {:?}", &fixed_json);
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Define a simple JSON schema for structured output
    let schema_str = r#"{
        "name": "anki_card",
        "schema": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "field": {
                "type": "object",
                "description": "卡片的字段，根据卡片类型不同而有所差异",
                "additionalProperties": true
              },
              "tag": {
                "type": "array",
                "items": {
                  "type": "string"
                },
                "description": "包含3-5个与该卡片知识点直接相关的关键词标签"
              },
              "card_type": {
                "type": "string",
                "enum": ["qa", "cloze", "choice", "multi_choice", "judge"],
                "description": "卡片类型"
              }
            },
            "required": ["fields", "tags", "card_type"]
          }
        }
      }
    "#;
    let schema: StructuredOutputFormat = serde_json::from_str(schema_str)?;
    test_repair_json();
    // OpenRouter
    // let api_key = std::env::var("DEEPSEEK_API_KEY").unwrap_or(
    //     "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".into(),
    // );
    // let llm = LLMBuilder::new()
    //     .backend(LLMBackend::OpenAI) // Use DeepSeek as the LLM provider
    //     .base_url("https://openrouter.ai/api/v1/")
    //     .system("You are a helpful assistant designed to output JSON.")
    //     .api_key(api_key) // Set the API key
    //     .model("deepseek/deepseek-chat-v3-0324:free") // Use DeepSeek Chat model
    //     .timeout_seconds(1200)
    //     .temperature(0.7) // Control response randomness (0.0-1.0)
    //     .stream(false) // Disable streaming responses
    //     // .schema(schema) // Set JSON schema for structured output
    //     .build()
    //     .expect("Failed to build LLM (DeepSeek)");

    // ## CloseAI
    let api_key = std::env::var("DEEPSEEK_API_KEY")
        .unwrap_or("sk-SxdKiihG0TK2SsGih5lhbAjCJn1KBZmZ8Aadw794ZxfL08qa".into());
    // ### CloseAI/OpenAI
    // let llm = LLMBuilder::new()
    //     .backend(LLMBackend::OpenAI) // Use DeepSeek as the LLM provider
    //     .base_url("https://api.openai-proxy.org/v1/")
    //     .system("You are a helpful assistant designed to output JSON.")
    //     .api_key(api_key) // Set the API key
    //     .model("deepseek-chat") // Use DeepSeek Chat model
    //     .timeout_seconds(1200)
    //     .temperature(0.7) // Control response randomness (0.0-1.0)
    //     .stream(false) // Disable streaming responses
    //     .build()
    //     .expect("Failed to build LLM (DeepSeek)");

    // ### CloseAI/Google
    let llm = LLMBuilder::new()
        .backend(LLMBackend::Google) // Use Google as the LLM provider
        .base_url("https://api.openai-proxy.org/google/")
        .system("You are a helpful assistant designed to output JSON.")
        .api_key(api_key) // Set the API key
        // .model("gemini-2.0-flash") // Use Gemini model
        .model("gemini-2.5-pro-preview-03-25") // Use Gemini model
        .timeout_seconds(1200)
        .temperature(0.7) // Control response randomness (0.0-1.0)
        .stream(false) // Disable streaming responses
        .build()
        .expect("Failed to build LLM (Google)");

    // ### CloseAI/Anthropic
    // let llm = LLMBuilder::new()
    //     .backend(LLMBackend::Anthropic) // Use Anthropic as the LLM provider
    //     .base_url("https://api.openai-proxy.org/anthropic/")
    //     .system("You are a helpful assistant designed to output JSON.")
    //     .api_key(api_key) // Set the API key
    //     .model("claude-3-7-sonnet-latest") // Use Claude model
    //     .timeout_seconds(1200)
    //     .temperature(0.7) // Control response randomness (0.0-1.0)
    //     .stream(false) // Disable streaming responses
    //     .build()
    //     .expect("Failed to build LLM (Claude)");

    // // Prepare conversation history with example messages
    // let messages = vec![
    //     ChatMessage::user()
    //         .content("2020 年世界奥运会乒乓球男子和女子单打冠军分别是谁? Please respond in the format {\"男子冠军\": ..., \"女子冠军\": ...}")
    //         .build(),
    // ];

    // // Send chat request and handle the response
    // match llm.chat(&messages).await {
    //     Ok(text) => {
    //         println!("Chat response:\n{:?}", text.text().unwrap_or_default());

    //     }
    //     Err(e) => eprintln!("Chat error: {}", e),
    // }

    Ok(())
}
