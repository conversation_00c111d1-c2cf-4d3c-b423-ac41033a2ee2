[package]
name = "docx2html"
version = "0.1.0"
edition = "2021"
authors = ["Your Name"]
description = "A library for converting DOCX to HTML"
license = "MIT"

[[bin]]
name = "docx2html"
path = "src/main.rs"

[lib]
name = "docx2html"
path = "src/lib.rs"

[dependencies]
zip = "0.6"        # 用于解压 docx 文件
quick-xml = "0.30" # 用于解析 XML
thiserror = "1.0"  # 错误处理
base64 = "0.21"    # 用于处理图片编码
regex = "1.9"      # 用于辅助转换公式
html-escape = "0.2"
once_cell = "1.18"
image = { version = "0.24", optional = true }     # 图像处理库
resvg = { version = "0.35", optional = true }     # SVG和一些矢量格式处理
tiny-skia = { version = "0.10", optional = true } # 2D图形渲染
[features]
default = []
image-conversion = ["image", "resvg", "tiny-skia"]
emf-svg-conversion = [] # EMF到SVG转换功能（使用外部工具）