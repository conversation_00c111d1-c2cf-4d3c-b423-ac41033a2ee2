use crate::DocxError;

/// 图像转换器，用于将各种格式转换为PNG
pub struct ImageConverter;

impl ImageConverter {
    /// 尝试将EMF/WMF数据转换为PNG
    pub fn convert_to_png(data: &[u8], content_type: &str) -> Result<Vec<u8>, DocxError> {
        match content_type {
            "image/emf" | "image/wmf" => {
                // EMF/WMF是Windows专有的矢量格式，直接转换比较困难
                // 目前我们尝试一些基本方法，如果失败则返回错误
                Self::try_emf_conversion(data)
            }
            _ => {
                // 对于其他格式，尝试使用image crate进行转换
                Self::convert_generic_to_png(data)
            }
        }
    }

    /// 通用图像格式转换
    #[cfg(feature = "image-conversion")]
    fn convert_generic_to_png(data: &[u8]) -> Result<Vec<u8>, DocxError> {
        use image::{ImageFormat, ImageOutputFormat};
        use std::io::Cursor;

        // 尝试检测和转换图像格式
        let img = image::load_from_memory(data)
            .map_err(|e| DocxError::Custom(format!("Failed to load image: {}", e)))?;

        let mut png_data = Vec::new();
        let mut cursor = Cursor::new(&mut png_data);

        img.write_to(&mut cursor, ImageOutputFormat::Png)
            .map_err(|e| DocxError::Custom(format!("Failed to convert to PNG: {}", e)))?;

        Ok(png_data)
    }

    /// 不支持image-conversion feature时的fallback
    #[cfg(not(feature = "image-conversion"))]
    fn convert_generic_to_png(_data: &[u8]) -> Result<Vec<u8>, DocxError> {
        Err(DocxError::Custom(
            "Image conversion feature not enabled".to_string(),
        ))
    }

    /// 尝试EMF/WMF转换
    fn try_emf_conversion(_data: &[u8]) -> Result<Vec<u8>, DocxError> {
        // EMF/WMF转换目前不支持，返回错误
        Err(DocxError::Custom(
            "EMF/WMF conversion not supported".to_string(),
        ))
    }

    /// 检查是否支持转换特定格式
    pub fn is_conversion_supported(content_type: &str) -> bool {
        #[cfg(feature = "image-conversion")]
        {
            match content_type {
                "image/bmp" | "image/jpeg" | "image/gif" | "image/tiff" | "image/webp" => true,
                "image/emf" | "image/wmf" => false, // PNG转换暂时不支持
                _ => false,
            }
        }
        #[cfg(not(feature = "image-conversion"))]
        {
            false
        }
    }
}

/// 简单的EMF头部检查
pub fn is_emf_data(data: &[u8]) -> bool {
    if data.len() < 44 {
        return false;
    }

    // EMF文件通常以特定的签名开始
    // EMF记录类型 1 (EMR_HEADER)
    let record_type = u32::from_le_bytes([data[0], data[1], data[2], data[3]]);
    record_type == 1
}

/// 简单的WMF头部检查  
pub fn is_wmf_data(data: &[u8]) -> bool {
    if data.len() < 6 {
        return false;
    }

    // WMF文件的魔数检查
    let magic = u16::from_le_bytes([data[0], data[1]]);
    magic == 0x9AC6 || magic == 0xCDD7
}
