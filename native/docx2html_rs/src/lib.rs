#![allow(unused)]

use std::collections::HashMap;
use std::io::Read;
use std::path::Path;
use thiserror::Error;
pub mod converter;
pub mod formula;
pub mod image_converter;
pub mod parser;
pub use converter::HtmlConverter;
use parser::DocxParser;

#[derive(Error, Debug)]
pub enum DocxError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Zip error: {0}")]
    Zip(#[from] zip::result::ZipError),
    #[error("XML error: {0}")]
    Xml(#[from] quick_xml::Error),
    #[error("Invalid document structure: {0}")]
    Structure(String),
    #[error("Custom error: {0}")]
    Custom(String),
}

#[derive(Debug, Clone)]
pub struct ListLevel {
    pub level: u8,
    pub format: String, // Standard format string ("decimal", "bullet", etc.)
    pub format_ext: Option<String>, // Extended format information (special character code)
    pub start_at: u32,
    pub text: String, // Format like "%1." or "%1.%2."
    pub indent: u32,
}

// REFACTOR: This struct now holds a reference to an abstract template
// and a map of any local overrides for its levels.
#[derive(Debug, Clone)]
pub struct Num {
    pub id: String,
    pub abstract_id: String,
    pub level_overrides: HashMap<u8, ListLevel>,
}

#[derive(Debug, Clone)]
pub struct ListStyle {
    pub id: String,
    pub levels: Vec<ListLevel>,
}

impl ListStyle {
    pub fn get_format(&self, level: u8) -> String {
        self.levels
            .iter()
            .find(|l| l.level == level)
            .map(|l| l.format.clone())
            .unwrap_or_else(|| "decimal".to_string())
    }
}

// ... rest of lib.rs is unchanged ...
pub struct DocxDocument {
    pub content: String,
    pub styles: Vec<Style>,
    pub images: Vec<Image>,
    pub lists: Vec<ListStyle>,
}

pub struct Style {
    pub id: String,
    pub properties: StyleProperties,
}

#[derive(Debug, Clone, PartialEq)]
pub enum VerticalAlign {
    Superscript,
    Subscript,
}

#[derive(Debug, Clone)]
pub struct StyleProperties {
    pub font_family: Option<String>,
    pub font_size: Option<f32>,
    pub bold: bool,
    pub italic: bool,
    pub color: Option<String>,
    pub highlight: Option<String>,
    pub underline: bool,
    pub strike_through: bool,
    pub vertical_align: Option<VerticalAlign>,
    pub paragraph_type: ParagraphType,
    pub indent_left: Option<u32>,
    pub indent_right: Option<u32>,
    pub indent_first_line: Option<i32>,
}

impl Default for StyleProperties {
    fn default() -> Self {
        Self {
            font_family: None,
            font_size: None,
            bold: false,
            italic: false,
            color: None,
            highlight: None,
            underline: false,
            strike_through: false,
            vertical_align: None,
            paragraph_type: ParagraphType::Normal,
            indent_left: None,
            indent_right: None,
            indent_first_line: None,
        }
    }
}

pub struct Image {
    pub id: String,
    pub data: Vec<u8>,
    pub content_type: String,
}

#[derive(Debug, Clone)]
pub enum ParagraphType {
    Normal,
    Heading(u8),
    ListItem { list_id: String, level: u8 },
}

#[derive(Debug, Clone)]
pub struct TextRun {
    pub text: String,
    pub properties: StyleProperties,
}

impl TextRun {
    pub fn new(text: String, properties: StyleProperties) -> Self {
        Self { text, properties }
    }

    pub fn plain(text: String) -> Self {
        Self {
            text,
            properties: StyleProperties::default(),
        }
    }
}

#[derive(Debug)]
pub struct DocxContent {
    pub document: String,                // document.xml 内容
    pub styles: String,                  // styles.xml 内容
    pub numbering: String,               // numbering.xml 内容
    pub relationships: String,           // document.xml.rels 内容
    pub media: HashMap<String, Vec<u8>>, // 图片等媒体文件
}

impl DocxDocument {
    /// 从 .docx 文件加载文档
    pub fn from_file<P: AsRef<Path>>(
        path: P,
        escape_latex_for_js: Option<bool>,
    ) -> Result<Self, DocxError> {
        let file = std::fs::File::open(path)?;
        Self::from_reader(file, escape_latex_for_js)
    }

    /// 从解压后的DOCX目录加载文档
    pub fn from_directory<P: AsRef<Path>>(
        dir_path: P,
        escape_latex_for_js: Option<bool>,
    ) -> Result<Self, DocxError> {
        // 读取文档内容
        let document_path = dir_path.as_ref().join("word").join("document.xml");
        let mut document_file = std::fs::File::open(document_path)?;
        let mut document_content = String::new();
        document_file.read_to_string(&mut document_content)?;

        let mut content = DocxContent {
            document: document_content,
            styles: String::new(),
            numbering: String::new(),
            relationships: String::new(),
            media: HashMap::new(),
        };

        // 尝试读取样式文件
        let styles_path = dir_path.as_ref().join("word").join("styles.xml");
        if let Ok(mut styles_file) = std::fs::File::open(&styles_path) {
            let mut styles_content = String::new();
            if styles_file.read_to_string(&mut styles_content).is_ok() {
                content.styles = styles_content;
            }
        }

        // 尝试读取编号文件
        let numbering_path = dir_path.as_ref().join("word").join("numbering.xml");
        if let Ok(mut numbering_file) = std::fs::File::open(&numbering_path) {
            let mut numbering_content = String::new();
            if numbering_file
                .read_to_string(&mut numbering_content)
                .is_ok()
            {
                content.numbering = numbering_content;
            }
        }

        // 尝试读取关系文件
        let rels_path = dir_path
            .as_ref()
            .join("word")
            .join("_rels")
            .join("document.xml.rels");
        if let Ok(mut rels_file) = std::fs::File::open(&rels_path) {
            let mut rels_content = String::new();
            if rels_file.read_to_string(&mut rels_content).is_ok() {
                content.relationships = rels_content;
            }
        }

        // 尝试加载图片
        let media_path = dir_path.as_ref().join("word").join("media");
        if media_path.exists() && media_path.is_dir() {
            if let Ok(entries) = std::fs::read_dir(&media_path) {
                for entry in entries {
                    if let Ok(entry) = entry {
                        let path = entry.path();
                        if path.is_file() {
                            if let Some(filename) = path.file_name() {
                                if let Some(filename_str) = filename.to_str() {
                                    if let Ok(image_data) = std::fs::read(&path) {
                                        content.media.insert(filename_str.to_string(), image_data);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 使用加载的内容创建解析器和文档
        let mut parser = DocxParser::new(content, escape_latex_for_js);

        // 返回解析后的文档
        parser.parse()
    }

    /// 从字节流读取器加载文档
    pub fn from_reader<R: Read + std::io::Seek>(
        reader: R,
        escape_latex_for_js: Option<bool>,
    ) -> Result<Self, DocxError> {
        let mut archive = zip::ZipArchive::new(reader)?;

        // 处理document.xml
        let document = if let Ok(mut file) = archive.by_name("word/document.xml") {
            let mut content = String::new();
            file.read_to_string(&mut content)?;
            content
        } else {
            return Err(DocxError::Structure("Missing document.xml".to_string()));
        };

        // 处理styles.xml
        let styles = if let Ok(mut file) = archive.by_name("word/styles.xml") {
            let mut content = String::new();
            file.read_to_string(&mut content)?;
            content
        } else {
            String::new()
        };

        // 处理numbering.xml
        let numbering = if let Ok(mut file) = archive.by_name("word/numbering.xml") {
            let mut content = String::new();
            file.read_to_string(&mut content)?;
            content
        } else {
            String::new()
        };

        // 处理relationships
        let relationships = if let Ok(mut file) = archive.by_name("word/_rels/document.xml.rels") {
            let mut content = String::new();
            file.read_to_string(&mut content)?;
            content
        } else {
            String::new()
        };

        // 处理图片
        let mut media = HashMap::new();
        for i in 0..archive.len() {
            if let Ok(mut file) = archive.by_index(i) {
                let name = file.name().to_string();
                if name.starts_with("word/media/") {
                    if let Some(filename) = name.split('/').last() {
                        let mut image_data = Vec::new();
                        file.read_to_end(&mut image_data)?;
                        media.insert(filename.to_string(), image_data);
                    }
                }
            }
        }

        let content = DocxContent {
            document,
            styles,
            numbering,
            relationships,
            media,
        };

        let mut parser = DocxParser::new(content, escape_latex_for_js);

        // 返回解析后的文档
        parser.parse()
    }

    /// 将文档转换为HTML
    pub fn to_html(&self) -> Result<String, DocxError> {
        let converter = HtmlConverter::new(self);
        Ok(converter.convert())
    }
}

impl DocxContent {
    fn from_file(file: std::fs::File) -> Result<Self, DocxError> {
        let mut archive = zip::ZipArchive::new(file)?;

        // 提取必要的 XML 文件
        let document = Self::extract_xml(&mut archive, "word/document.xml")?;
        let styles = Self::extract_xml(&mut archive, "word/styles.xml")?;
        let numbering = Self::extract_xml(&mut archive, "word/numbering.xml").unwrap_or_default();
        let relationships = Self::extract_xml(&mut archive, "word/_rels/document.xml.rels")?;

        // 提取媒体文件
        let media = Self::extract_media(&mut archive)?;

        Ok(Self {
            document,
            styles,
            numbering,
            relationships,
            media,
        })
    }

    fn extract_xml(
        archive: &mut zip::ZipArchive<std::fs::File>,
        name: &str,
    ) -> Result<String, DocxError> {
        let mut file = archive.by_name(name)?;
        let mut content = String::new();
        file.read_to_string(&mut content)?;
        Ok(content)
    }

    fn extract_media(
        archive: &mut zip::ZipArchive<std::fs::File>,
    ) -> Result<HashMap<String, Vec<u8>>, DocxError> {
        let mut media = HashMap::new();

        for i in 0..archive.len() {
            let mut file = archive.by_index(i)?;
            let name = file.name().to_string();

            if name.starts_with("word/media/") {
                let mut content = Vec::new();
                file.read_to_end(&mut content)?;
                media.insert(name, content);
            }
        }

        Ok(media)
    }
}
