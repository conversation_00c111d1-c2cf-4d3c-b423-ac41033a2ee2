#![allow(unused)]

use crate::{DocxDocument, ListStyle, Style, StyleProperties};

pub struct HtmlConverter<'a> {
    document: &'a DocxDocument,
}

impl<'a> HtmlConverter<'a> {
    pub fn new(document: &'a DocxDocument) -> Self {
        Self { document }
    }

    pub fn convert(&self) -> String {
        let mut html = String::from(
            r#"<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <title>DOCX-to-HTML Conversion</title>
            <!-- KaTeX for LaTeX Rendering -->
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css" integrity="sha384-5TcZemv2l/9On385z///+d7MSYlvIEw9FuZTIdZ14vJLqWphw7e7ZPuOiCHJcFCP" crossorigin="anonymous">

            <!-- The loading of KaTeX is deferred to speed up page rendering -->
            <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.js" integrity="sha384-cMkvdD8LoxVzGF/RPUKAcvmm49FQ0oxwDF3BGKtDXcEc+T1b2N+teh/OJfpU0jr6" crossorigin="anonymous"></script>

            <!-- To automatically render math in text elements, include the auto-render extension: -->
            <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/contrib/auto-render.min.js" integrity="sha384-hCXGrW6PitJEwbkoStFjeJxv+fSOOQKOPbJxSfM6G5sWZjAyWhXiTIIAmQqnlLlh" crossorigin="anonymous" onload="renderMathInElement(document.body);"></script>            
        <style>
                body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "Arial", sans-serif; line-height: 1.6; max-width: 800px; margin: 2em auto; padding: 0 1em; }
                .list-marker { 
                    margin-right: 0.5em; 
                    display: inline-block;
                    min-width: 1.5em;
                    text-align: right;
                    vertical-align: top;
                }
                .list-item {
                    display: flex;
                    align-items: flex-start;
                    margin-bottom: 0.2em;
                }
                .list-content {
                    flex: 1;
                    display: inline-block;
                }
                p { margin: 0.5em 0; }
                table { border-collapse: collapse; margin: 1em 0; width: 100%; }
                td, th { border: 1px solid #ccc; padding: 4px 8px; text-align: left; vertical-align: top; }
            </style>
        </head>
        <body>
        "#,
        );

        html.push_str(&self.convert_paragraph(&self.document.content));

        html.push_str(
            r#"
        </body>
        </html>
        "#,
        );

        html
    }

    fn convert_paragraph(&self, text: &str) -> String {
        // The text is already processed with inline styling from the parser
        // Just return it as-is since styling is now handled at the run level
        text.to_string()
    }

    fn find_style_for_text(&self, _text: &str) -> Option<&Style> {
        // 暂时返回第一个找到的样式，后续完善样式匹配逻辑
        self.document.styles.first()
    }

    pub fn get_style_attributes(&self, properties: &StyleProperties) -> String {
        let mut style_attrs = String::from(" style=\"");

        // 添加粗体
        if properties.bold {
            style_attrs.push_str("font-weight: bold; ");
        }

        // 添加斜体
        if properties.italic {
            style_attrs.push_str("font-style: italic; ");
        }

        // 添加下划线
        if properties.underline {
            style_attrs.push_str("text-decoration: underline; ");
        }

        // 添加字体
        if let Some(font) = &properties.font_family {
            style_attrs.push_str(&format!("font-family: '{}'; ", font));
        }

        // 添加字号
        if let Some(size) = properties.font_size {
            style_attrs.push_str(&format!("font-size: {}pt; ", size));
        }

        // 添加颜色（跳过纯黑色，因为这是默认颜色）
        if let Some(color) = &properties.color {
            if !Self::is_black_color(color) {
                style_attrs.push_str(&format!("color: #{};", color));
            }
        }

        style_attrs.push('"');
        style_attrs
    }

    // Helper function to check if a color is pure black (#000000)
    fn is_black_color(color: &str) -> bool {
        // Check for hex format without # prefix
        if color.len() == 6 && color.chars().all(|c| c.is_ascii_hexdigit()) {
            return color.to_lowercase() == "000000";
        }
        // Check for hex format with # prefix
        if color.len() == 7 && color.starts_with('#') {
            return color.to_lowercase() == "#000000";
        }
        // Check for named colors
        color.to_lowercase() == "black"
    }
}
