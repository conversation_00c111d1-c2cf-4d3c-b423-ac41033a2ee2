[package]
name = "paddle-ocr-rs"
version = "0.5.1"
edition = "2021"
readme = "README.md"
keywords = ["paddle", "ocr", "onnx"]
license = "Apache-2.0"
description = "Use Rust to call Paddle OCR models via ONNX Runtime for image text recognition."
repository = "https://github.com/mg-chao/paddle-ocr-rs"
authors = ["mg-chao <<EMAIL>>"]
build = "build.rs"

[dependencies]
serde_json = "^1.0"
serde = { version = "^1.0", features = ["derive"] }
imageproc = "0.25"
ndarray = "0.16"
ort = { version = "=2.0.0-rc.9", default-features = false, features = [
    "ndarray"
] }
ort-sys = { version = "=2.0.0-rc.9", default-features = false }
geo-types = "0.7.15"
geo-clipper = "0.9.0"
clipper-sys = "0.8.0"
thiserror = "2.0.12"
image = "0.25.6"
clap = { version = "4.5.1", features = ["derive"] }
reqwest = { version = "0.11", features = ["blocking"] }
indicatif = "0.17.8"
sha2 = "0.10"
hex = "0.4"

[features]
default = ["download-binaries", "copy-dylibs", "ort/default"]
download-binaries = ["ort/download-binaries"]
copy-dylibs = ["ort/copy-dylibs"]

[[bin]]
name = "paddle-ocr"
path = "src/main.rs"
