use clap::{Parser, ValueEnum};
use hex::encode;
use indicatif::{ProgressBar, ProgressStyle};
use paddle_ocr_rs::{ocr_error::OcrError, ocr_lite::OcrLite};
use reqwest::blocking::Client;
use sha2::{Digest, Sha256};
use std::{
    fs::{self, File},
    io::{self, Write},
    path::{Path, PathBuf},
};

// source: https://github.com/RapidAI/RapidOCR/blob/main/python/rapidocr/default_models.yaml
const MODEL_URLS: &[(
    &str, // 模型文件名
    &str, // 主要下载URL (ModelScope)
    &str, // 备用下载URL (GitHub)
    &str, // 文件哈希值
)] = &[
    (
        "ch_PP-OCRv5_mobile_det.onnx",
        "https://www.modelscope.cn/models/RapidAI/RapidOCR/resolve/v3.1.0/onnx/PP-OCRv5/det/ch_PP-OCRv5_mobile_det.onnx",
        "https://github.com/RapidAI/RapidOCR/releases/download/v1.3.0/ch_PP-OCRv5_mobile_det.onnx",
        "4d97c44a20d30a81aad087d6a396b08f786c4635742afc391f6621f5c6ae78ae",
    ),
    (
        "ch_ppocr_mobile_v2.0_cls_infer.onnx",
        "https://www.modelscope.cn/models/RapidAI/RapidOCR/resolve/v3.1.0/onnx/PP-OCRv4/cls/ch_ppocr_mobile_v2.0_cls_infer.onnx",
        "https://github.com/RapidAI/RapidOCR/releases/download/v1.0.0/ch_ppocr_mobile_v2.0_cls_infer.onnx",
        "e47acedf663230f8863ff1ab0e64dd2d82b838fceb5957146dab185a89d6215c",
    ),
    (
        "ch_PP-OCRv5_rec_mobile_infer.onnx",
        "https://www.modelscope.cn/models/RapidAI/RapidOCR/resolve/v3.1.0/onnx/PP-OCRv5/rec/ch_PP-OCRv5_rec_mobile_infer.onnx",
        "https://github.com/RapidAI/RapidOCR/releases/download/v1.3.0/ch_PP-OCRv5_rec_mobile_infer.onnx",
        "5825fc7ebf84ae7a412be049820b4d86d77620f204a041697b0494669b1742c5",
    ),
];

#[derive(Parser)]
#[clap(
    name = "paddle-ocr-rs",
    about = "A command line tool for PaddleOCR text detection",
    version
)]
struct Cli {
    /// Path to the input image
    #[clap(short, long)]
    image: String,

    /// Path to detection model
    #[clap(
        short = 'd',
        long,
        default_value = "./models/ch_PP-OCRv5_mobile_det.onnx"
    )]
    det_model: String,

    /// Path to classification model
    #[clap(
        short = 'c',
        long,
        default_value = "./models/ch_ppocr_mobile_v2.0_cls_infer.onnx"
    )]
    cls_model: String,

    /// Path to recognition model
    #[clap(
        short = 'r',
        long,
        default_value = "./models/ch_PP-OCRv5_rec_mobile_infer.onnx"
    )]
    rec_model: String,

    /// Padding size
    #[clap(short, long, default_value = "50")]
    padding: u32,

    /// Max side length for image resizing
    #[clap(short = 's', long, default_value = "1024")]
    max_side_len: u32,

    /// Box score threshold
    #[clap(short = 'b', long, default_value = "0.5")]
    box_score_thresh: f32,

    /// Text detection threshold
    #[clap(short = 't', long, default_value = "0.3")]
    box_thresh: f32,

    /// Unclip ratio
    #[clap(short = 'u', long, default_value = "1.6")]
    unclip_ratio: f32,

    /// Enable angle detection
    #[clap(short = 'a', long)]
    do_angle: bool,

    /// Use most angle
    #[clap(short = 'm', long)]
    most_angle: bool,

    /// Enable angle rollback and set threshold
    #[clap(short = 'k', long)]
    angle_rollback_threshold: Option<f32>,

    /// Number of threads for inference
    #[clap(short = 'j', long, default_value = "2")]
    num_thread: usize,

    /// Output format
    #[clap(short = 'f', long, value_enum, default_value = "text")]
    format: OutputFormat,

    /// Force model download even if files exist
    #[clap(short = 'D', long)]
    download_models: bool,
}

#[derive(Copy, Clone, PartialEq, Eq, ValueEnum)]
enum OutputFormat {
    /// Plain text output
    Text,
    /// JSON output
    Json,
    /// Debug output with scores
    Debug,
}

fn download_file(url: &str, path: &Path) -> Result<Result<(), OcrError>, OcrError> {
    println!("Downloading: {}", url);

    // Create parent directories if they don't exist
    if let Some(parent) = path.parent() {
        fs::create_dir_all(parent)?;
    }

    let client = Client::builder()
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        .build()?;

    let resp = match client
        .get(url)
        .header(
            "Accept",
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
        )
        .header("Accept-Language", "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7")
        .header("Referer", "https://www.modelscope.cn/")
        .header(
            "sec-ch-ua",
            "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\"",
        )
        .header("sec-ch-ua-platform", "\"Windows\"")
        .send()
    {
        Ok(r) => r,
        Err(e) => {
            eprintln!("Error downloading from {}: {}", url, e);
            return Ok(Err(OcrError::Network(e)));
        }
    };

    if !resp.status().is_success() {
        let error = OcrError::Io(io::Error::new(
            io::ErrorKind::Other,
            format!("Failed to download file: HTTP {}", resp.status()),
        ));
        eprintln!("Error: {}", error);
        return Ok(Err(error));
    }

    let total_size = resp.content_length().unwrap_or(0);

    let pb = ProgressBar::new(total_size);
    pb.set_style(ProgressStyle::default_bar()
        .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {bytes}/{total_bytes} ({eta})")
        .unwrap()
        .progress_chars("#>-"));

    let mut file = File::create(path)?;

    match resp.bytes() {
        Ok(bytes) => {
            if let Err(e) = file.write_all(&bytes) {
                return Ok(Err(OcrError::Io(e)));
            }
        }
        Err(e) => {
            return Ok(Err(OcrError::Network(e)));
        }
    }

    pb.finish_with_message("Download complete");
    Ok(Ok(()))
}

fn verify_file(path: &Path, expected_hash: &str) -> Result<bool, OcrError> {
    let mut file = File::open(path)?;
    let mut hasher = Sha256::new();

    let mut buffer = Vec::new();
    io::copy(&mut file, &mut buffer)?;
    hasher.update(&buffer);

    let hash = encode(hasher.finalize());
    Ok(hash == expected_hash)
}

fn ensure_model_files(cli: &Cli) -> Result<(), OcrError> {
    let model_paths = [&cli.det_model, &cli.cls_model, &cli.rec_model];

    for (i, path_str) in model_paths.iter().enumerate() {
        let path = Path::new(path_str);

        if cli.download_models || !path.exists() {
            let (filename, primary_url, backup_url, hash) = MODEL_URLS[i];

            println!("Model file '{}' not found or download forced.", path_str);

            // If the path is using the default naming convention, download to that path
            // Otherwise download to the models directory with default name
            let download_path = if path_str.contains(filename) {
                PathBuf::from(path_str)
            } else {
                let models_dir = Path::new("models");
                if !models_dir.exists() {
                    fs::create_dir_all(models_dir)?;
                }
                models_dir.join(filename)
            };

            // Try primary URL first, then fallback to backup URL if needed
            println!("Trying primary download source...");

            let download_result = match download_file(primary_url, &download_path)? {
                Ok(_) => {
                    println!("Download successful from primary source.");
                    Ok(())
                }
                Err(_) => {
                    println!("Primary source failed, trying backup source...");
                    match download_file(backup_url, &download_path)? {
                        Ok(_) => {
                            println!("Download successful from backup source.");
                            Ok(())
                        }
                        Err(e) => {
                            Err(OcrError::Io(io::Error::new(
                                io::ErrorKind::Other,
                                format!("Failed to download model from both sources: {}", e),
                            )))
                        }
                    }
                }
            };

            // Check if download was successful
            if let Err(e) = download_result {
                return Err(e);
            }

            // Verify the downloaded file
            if verify_file(&download_path, hash)? {
                println!("Verified file: {}", download_path.display());

                // If we downloaded to a different path than requested, copy the file
                if download_path != path {
                    println!("Copying model to requested path: {}", path_str);
                    if let Some(parent) = path.parent() {
                        fs::create_dir_all(parent)?;
                    }
                    fs::copy(&download_path, path)?;
                }
            } else {
                return Err(OcrError::Io(io::Error::new(
                    io::ErrorKind::Other,
                    format!(
                        "Downloaded file has incorrect hash: {}",
                        download_path.display()
                    ),
                )));
            }
        }
    }

    Ok(())
}

fn main() -> Result<(), OcrError> {
    let cli = Cli::parse();

    // Check and download model files if needed
    ensure_model_files(&cli)?;

    // Initialize OCR
    let mut ocr = OcrLite::new();
    ocr.init_models(
        &cli.det_model,
        &cli.cls_model,
        &cli.rec_model,
        cli.num_thread,
    )?;

    // Check if image exists
    if !Path::new(&cli.image).exists() {
        eprintln!("Error: Image file '{}' does not exist", cli.image);
        std::process::exit(1);
    }

    // Process image
    let result = if let Some(threshold) = cli.angle_rollback_threshold {
        // Use angle rollback
        ocr.detect_angle_rollback(
            &image::open(&cli.image)?.to_rgb8(),
            cli.padding,
            cli.max_side_len,
            cli.box_score_thresh,
            cli.box_thresh,
            cli.unclip_ratio,
            cli.do_angle,
            cli.most_angle,
            threshold,
        )?
    } else {
        // Regular detection
        ocr.detect_from_path(
            &cli.image,
            cli.padding,
            cli.max_side_len,
            cli.box_score_thresh,
            cli.box_thresh,
            cli.unclip_ratio,
            cli.do_angle,
            cli.most_angle,
        )?
    };

    // Output results based on format
    match cli.format {
        OutputFormat::Text => {
            // Simple text output
            for block in &result.text_blocks {
                println!("{}", block.text);
            }
        }
        OutputFormat::Json => {
            // JSON output
            println!(
                "{}",
                serde_json::to_string_pretty(&result).map_err(OcrError::Json)?
            );
        }
        OutputFormat::Debug => {
            // Debug output with scores
            for (i, block) in result.text_blocks.iter().enumerate() {
                println!(
                    "[{}] Text: '{}'\n    Score: {:.4}\n    Box Score: {:.4}\n    Angle: {} ({:.4})",
                    i + 1,
                    block.text,
                    block.text_score,
                    block.box_score,
                    block.angle_index,
                    block.angle_score
                );
            }
        }
    }

    Ok(())
}
