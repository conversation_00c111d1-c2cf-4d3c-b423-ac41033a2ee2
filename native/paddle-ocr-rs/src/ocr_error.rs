use thiserror::Error;

#[derive(E<PERSON><PERSON>, Debug)]
pub enum OcrError {
    #[error("Ort error")]
    Ort(#[from] ort::Error),
    #[error("Io error")]
    Io(#[from] std::io::Error),
    #[error("Session not initialized")]
    ImageError(#[from] image::ImageError),
    #[error("Image error")]
    SessionNotInitialized,
    #[error("Json error")]
    Json(#[from] serde_json::Error),
    #[error("Network error")]
    Network(#[from] reqwest::Error),
}
