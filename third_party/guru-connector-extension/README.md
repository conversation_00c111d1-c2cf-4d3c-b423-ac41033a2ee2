# Anki Guru 浏览器连接器

这是一个浏览器扩展，用于连接Anki Guru应用与各种视频网站，支持视频笔记功能。

## 功能

- 🎬 **视频识别**：自动检测页面中的视频元素
- 📷 **视频截图**：一键截取当前视频画面
- 🔗 **时间戳链接**：生成带时间戳的视频链接
- 🎮 **播放控制**：播放/暂停、调整播放速度、快进/快退
- 🔄 **WebSocket通信**：与Flutter应用实时通信
- 🎛️ **悬浮控制面板**：页面内视频控制界面

## 支持的网站

- YouTube
- Bilibili
- Coursera
- Udemy
- Vimeo
- Teachable
- Netflix
- 其他包含HTML5视频的网站

## 安装

### 开发模式

1. 克隆仓库
2. 安装依赖
   ```
   pnpm install
   ```
3. 运行开发服务器
   ```
   pnpm dev
   ```
   
### 手动安装

1. 运行 `pnpm build` 构建扩展
2. 打开Chrome扩展页面 (chrome://extensions/)
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `.output/chrome-mv3/` 文件夹

## 使用方法

1. 安装并启用扩展
2. 打开任何包含视频的网站
3. 点击扩展图标查看控制面板
4. 确保WebSocket端口与Anki Guru应用中配置的端口一致（默认52025）
5. 开启"服务器连接"开关连接到Anki Guru应用
6. 使用悬浮控制面板或快捷键控制视频

## 与Flutter应用通信

扩展通过WebSocket与Anki Guru Flutter应用通信，默认连接地址：`ws://localhost:52025/connect`

### 主要消息类型

```json
// 截取视频截图
{
  "type": "take_screenshot"
}

// 获取视频信息
{
  "type": "get_video_info"
}

// 播放控制
{
  "type": "play"
}
{
  "type": "pause"
}
{
  "type": "toggle_play"
}
{
  "type": "seek",
  "time": 120
}
{
  "type": "seek_relative",
  "seconds": 10
}
{
  "type": "set_playback_rate",
  "rate": 1.5
}
```

## 开发

本项目使用 [WXT](https://wxt.dev/) 框架开发。

### 构建命令

```bash
# 安装依赖
pnpm install

# 开发模式 (Chrome)
pnpm dev

# 开发模式 (Firefox)
pnpm dev:firefox

# 构建MV3版本（适用于最新版Chrome）
pnpm build:mv3
pnpm zip:mv3

# 构建MV2版本（适用于较旧版本浏览器）
pnpm build:mv2
pnpm zip:mv2

# 同时构建两个版本
pnpm build:all
```

## 版本历史

### v1.0.8
- 统一了两个扩展项目的代码
- 修复了WebSocket连接问题
- 改进了用户界面和连接状态显示
- 增强了错误处理和用户反馈
- 修正了默认端口配置（52025）

## 许可证

MIT License
