#!/usr/bin/env node

/**
 * Verification script for the unified Guru Connector extension
 * Checks that all required files exist and configurations are correct
 */

import fs from 'fs';
import path from 'path';

const requiredFiles = [
  'package.json',
  'wxt.config.ts',
  'tsconfig.json',
  'entrypoints/background.ts',
  'entrypoints/content.ts',
  'entrypoints/popup/App.vue',
  'entrypoints/popup/index.html',
  'entrypoints/popup/main.ts',
  'store/index.ts',
  'store/videoStore.ts',
  'manifests/chrome.json',
  'scripts/build-all.sh'
];

const requiredDirectories = [
  'entrypoints',
  'entrypoints/popup',
  'store',
  'manifests',
  'scripts',
  'public',
  'public/icon'
];

console.log('🔍 Verifying Guru Connector Extension Setup...\n');

// Check directories
console.log('📁 Checking directories:');
let directoriesOk = true;
for (const dir of requiredDirectories) {
  if (fs.existsSync(dir)) {
    console.log(`  ✅ ${dir}`);
  } else {
    console.log(`  ❌ ${dir} - MISSING`);
    directoriesOk = false;
  }
}

// Check files
console.log('\n📄 Checking required files:');
let filesOk = true;
for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING`);
    filesOk = false;
  }
}

// Check package.json configuration
console.log('\n📦 Checking package.json configuration:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  const requiredScripts = ['dev', 'build', 'build:mv2', 'build:mv3', 'zip:mv2', 'zip:mv3', 'build:all'];
  const requiredDeps = ['vue', 'webextension-polyfill', 'pinia'];
  const requiredDevDeps = ['wxt', '@wxt-dev/module-vue', 'typescript'];
  
  console.log('  Scripts:');
  for (const script of requiredScripts) {
    if (packageJson.scripts && packageJson.scripts[script]) {
      console.log(`    ✅ ${script}`);
    } else {
      console.log(`    ❌ ${script} - MISSING`);
      filesOk = false;
    }
  }
  
  console.log('  Dependencies:');
  for (const dep of requiredDeps) {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`    ✅ ${dep}`);
    } else {
      console.log(`    ❌ ${dep} - MISSING`);
      filesOk = false;
    }
  }
  
  console.log('  Dev Dependencies:');
  for (const dep of requiredDevDeps) {
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`    ✅ ${dep}`);
    } else {
      console.log(`    ❌ ${dep} - MISSING`);
      filesOk = false;
    }
  }
} catch (error) {
  console.log('  ❌ Error reading package.json:', error.message);
  filesOk = false;
}

// Check WebSocket configuration
console.log('\n🔌 Checking WebSocket configuration:');
try {
  const backgroundContent = fs.readFileSync('entrypoints/background.ts', 'utf8');
  const popupContent = fs.readFileSync('entrypoints/popup/App.vue', 'utf8');
  
  if (backgroundContent.includes('52025')) {
    console.log('  ✅ Background script uses correct port (52025)');
  } else {
    console.log('  ❌ Background script port configuration incorrect');
    filesOk = false;
  }
  
  if (popupContent.includes('52025') && popupContent.includes('/connect')) {
    console.log('  ✅ Popup uses correct WebSocket URL (ws://localhost:52025/connect)');
  } else {
    console.log('  ❌ Popup WebSocket URL configuration incorrect');
    filesOk = false;
  }
} catch (error) {
  console.log('  ❌ Error checking WebSocket configuration:', error.message);
  filesOk = false;
}

// Check manifest configuration
console.log('\n📋 Checking manifest configuration:');
try {
  const manifest = JSON.parse(fs.readFileSync('manifests/chrome.json', 'utf8'));
  
  const requiredPermissions = ['storage', 'tabs', 'activeTab', 'notifications'];
  console.log('  Permissions:');
  for (const permission of requiredPermissions) {
    if (manifest.permissions && manifest.permissions.includes(permission)) {
      console.log(`    ✅ ${permission}`);
    } else {
      console.log(`    ❌ ${permission} - MISSING`);
      filesOk = false;
    }
  }
  
  if (manifest.host_permissions && manifest.host_permissions.includes('*://*/*')) {
    console.log('  ✅ Host permissions configured');
  } else {
    console.log('  ❌ Host permissions missing');
    filesOk = false;
  }
  
  if (manifest.action && manifest.action.default_popup === 'popup.html') {
    console.log('  ✅ Popup action configured');
  } else {
    console.log('  ❌ Popup action configuration incorrect');
    filesOk = false;
  }
} catch (error) {
  console.log('  ❌ Error reading manifest:', error.message);
  filesOk = false;
}

// Final result
console.log('\n' + '='.repeat(50));
if (directoriesOk && filesOk) {
  console.log('🎉 All checks passed! Extension setup is complete.');
  console.log('\n📋 Next steps:');
  console.log('  1. Run: pnpm install');
  console.log('  2. Run: pnpm dev (for development)');
  console.log('  3. Run: pnpm build:mv3 (for production)');
  console.log('  4. Load extension from .output/chrome-mv3/ in Chrome');
} else {
  console.log('❌ Some checks failed. Please review the issues above.');
  process.exit(1);
}
