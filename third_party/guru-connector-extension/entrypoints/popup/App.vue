<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
// @ts-ignore 忽略类型检查问题
import browser from 'webextension-polyfill';

// 定义设置接口
interface Settings {
    serverEnabled: boolean;
    serverUrl: string;
}

// 响应式状态
const settings = reactive<Settings>({
    serverEnabled: false,
    serverUrl: 'ws://localhost:52025/connect'
});

const statusMessage = ref('');
const statusType = ref('info');
const isConnecting = ref(false);
const connected = ref(false);

// 计算属性
const statusClass = computed(() => ({
    'status-success': statusType.value === 'success',
    'status-error': statusType.value === 'error'
}));

// 加载设置
async function loadSettings(): Promise<void> {
    try {
        const result = await browser.storage.local.get({
            serverEnabled: false,
            serverUrl: 'ws://localhost:52025/connect'
        });

        Object.assign(settings, result);
        console.log('设置已加载:', settings);

        // 检查连接状态
        await checkConnectionStatus();
    } catch (error) {
        console.error('加载设置失败:', error);
        showStatus('加载设置失败，使用默认设置', 'error');
    }
}

// 保存设置
async function saveSettings(): Promise<void> {
    try {
        await browser.storage.local.set({
            serverEnabled: settings.serverEnabled,
            serverUrl: settings.serverUrl
        });
        console.log('设置已保存:', settings);
    } catch (error) {
        console.error('保存设置失败:', error);
        showStatus('保存设置失败', 'error');
    }
}

// 显示状态消息
function showStatus(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    statusMessage.value = message;
    statusType.value = type;
    
    // 3秒后清除状态消息
    setTimeout(() => {
        statusMessage.value = '';
    }, 3000);
}

// 切换服务器连接状态
async function toggleServerConnection() {
    console.log('切换服务器连接状态:', settings.serverEnabled);
    
    if (isConnecting.value) {
        console.log('正在连接中，忽略切换请求');
        return;
    }
    
    await saveSettings();

    if (settings.serverEnabled) {
        // 启用服务器连接 - 自动连接到默认服务器
        console.log('启用服务器连接，尝试连接到:', settings.serverUrl);
        isConnecting.value = true;
        
        try {
            // 向后台脚本发送连接请求
            const response = await browser.runtime.sendMessage({
                type: 'reconnect_websocket',
                port: 52025  // 使用正确的端口
            });
            
            console.log('连接响应:', response);
            
            if (response && response.success) {
                // 等待连接状态更新
                setTimeout(async () => {
                    await checkConnectionStatus();
                    if (connected.value) {
                        // 连接成功，状态会通过 setupMessageListener 处理
                        // 这里不需要重复设置状态消息
                        console.log('连接检查完成，状态已通过消息监听器更新');
                    } else {
                        // 连接超时，重置状态
                        isConnecting.value = false;
                        settings.serverEnabled = false;
                        showStatus('连接超时，请检查PDF Guru Anki应用是否运行', 'error');
                        await saveSettings();
                    }
                }, 2000);
            } else {
                showStatus(response?.error || '连接服务器失败', 'error');
                settings.serverEnabled = false;
                await saveSettings();
                isConnecting.value = false;
            }
        } catch (error) {
            console.error('连接请求失败:', error);
            showStatus('连接请求失败', 'error');
            settings.serverEnabled = false;
            await saveSettings();
            isConnecting.value = false;
        }
    } else {
        // 断开服务器连接
        console.log('断开服务器连接');
        try {
            await browser.runtime.sendMessage({ type: 'disconnect_websocket' });
            connected.value = false;
        } catch (error) {
            console.error('断开连接失败:', error);
            showStatus('断开连接失败', 'error');
        }
    }
}

// 检查连接状态
async function checkConnectionStatus() {
    try {
        const response = await browser.runtime.sendMessage({ type: 'get_connection_status' });
        const newConnectionStatus = response && response.connected;

        console.log('连接状态检查结果:', newConnectionStatus);
        connected.value = newConnectionStatus;

        // 同步设置状态，但不干扰正在进行的连接过程
        if (!newConnectionStatus && settings.serverEnabled && !isConnecting.value) {
            console.log('检测到连接断开，更新设置状态');
            settings.serverEnabled = false;
            await saveSettings();
        } else if (newConnectionStatus && !settings.serverEnabled && !isConnecting.value) {
            console.log('检测到连接建立，更新设置状态');
            settings.serverEnabled = true;
            await saveSettings();
        }
    } catch (error) {
        console.error('检查连接状态失败:', error);
        connected.value = false;
    }
}

// 监听后台消息
function setupMessageListener() {
    browser.runtime.onMessage.addListener((message: any) => {
        console.log('Popup收到后台消息:', message);
        
        if (message.type === 'connection_status') {
            const oldStatus = connected.value;
            const newStatus = message.connected;
            
            console.log('连接状态更新:', oldStatus, '->', newStatus);
            connected.value = newStatus;
            
            // 同步设置状态
            if (newStatus !== settings.serverEnabled) {
                settings.serverEnabled = newStatus;
                saveSettings();
            }
            
            // 显示连接状态变化通知
            if (oldStatus !== newStatus) {
                if (newStatus) {
                    // 连接成功时，重置连接中状态（不显示冗余消息）
                    isConnecting.value = false;
                } else {
                    // 连接断开时，重置连接中状态（不显示冗余消息）
                    isConnecting.value = false;
                }
            }
        }
        
        // 处理连接错误消息
        if (message.type === 'connection_error') {
            console.log('连接错误:', message.error);
            // 连接错误时，重置所有状态
            isConnecting.value = false;
            connected.value = false;
            settings.serverEnabled = false;
            showStatus(message.error || '连接到PDF Guru Anki时发生错误', 'error');
            saveSettings();
        }
    });
}

// 组件挂载时加载设置
onMounted(async () => {
    await loadSettings();
    setupMessageListener();
    
    // 定期检查连接状态
    setInterval(checkConnectionStatus, 5000);
});
</script>

<template>
    <div class="popup-container">
        <div class="header">
            <h1>Guru Connector</h1>
        </div>

        <div class="content">
            <div class="control-item">
                <span class="label">服务器连接</span>
                <label class="switch">
                    <input type="checkbox"
                           v-model="settings.serverEnabled"
                           @change="toggleServerConnection"
                           :disabled="isConnecting">
                    <span class="slider" :class="{ 'connecting': isConnecting }"></span>
                </label>
                <div class="connection-status">
                    <span v-if="isConnecting" class="status-connecting">连接中...</span>
                    <span v-else-if="connected" class="status-connected">已连接</span>
                    <span v-else class="status-disconnected">未连接</span>
                </div>
            </div>

            <div class="status-message" v-if="statusMessage && statusType === 'error'" :class="statusClass">
                {{ statusMessage }}
            </div>
        </div>
    </div>
</template>

<style scoped>
.popup-container {
    width: 280px;
    background: #ffffff;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.header {
    background: #667eea;
    color: white;
    padding: 12px 16px;
    text-align: center;
}

.header h1 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.content {
    padding: 16px;
}

.control-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    margin-bottom: 16px;
}

.label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.slider.connecting {
    background-color: #ff9800;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

.connection-status {
    position: absolute;
    right: 54px;
    font-size: 12px;
}

.status-connected {
    color: #4caf50;
    font-weight: 500;
}

.status-disconnected {
    color: #f44336;
}

.status-connecting {
    color: #ff9800;
    font-weight: 500;
}

.status-message {
    margin-top: 12px;
    padding: 10px;
    border-radius: 4px;
    font-size: 13px;
    text-align: center;
    background: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.status-message.status-success {
    background: #e8f5e8;
    color: #2e7d32;
    border-color: #c8e6c9;
}

.status-message.status-error {
    background: #ffebee;
    color: #c62828;
    border-color: #ffcdd2;
}
</style>
