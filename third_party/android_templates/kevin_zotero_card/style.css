
:root {
    --yellow: #ffeba2;
    --blue: #ff5656;
}

.card {
    font-family: 'Arial', sans-serif;
    font-size: 20px;
    padding: 8px;
    text-align: left;
    color: #333;
    background-color: white;
    border-radius: 4px;
}

.original-text {
    font-size: 1.2em;
    color: #333;
    background-color: #fbfbfb;
    border: 2px solid #eee;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    white-space: pre-wrap;
}

.source {
    /* line-height: 1.6; */
    margin-bottom: 4px;
    font-size: small;
    font-family: Microsoft YaHei;
    color: #929292;
}

.remark{
    font-size: 16px;
    font-family: Microsoft YaHei;
    color: #929292;
    font-size: small;
    margin-bottom: 4px;
    white-space: pre-wrap;
}

.notes1{
    font-family: Arial, Helvetica, sans-serif;
    margin-bottom: 4px;
    white-space: pre-wrap;
}

.notes2{
    font-family: Arial, Helvetica, sans-serif;
    white-space: pre-wrap;
}

.cloze-hide {
    font-weight: bold;
    color: var(--blue);
    border-bottom: 1px solid var(--blue);
    cursor: pointer;
    /* filter: blur(0.25em); */
}

.cloze-show {
    font-weight: bold;
    color: var(--blue);
    border-bottom: 1px solid var(--blue);
    cursor: pointer;
}

.cloze-hide2 {
    font-weight: bold;
    color: var(--yellow);
    border-bottom: 1px solid var(--yellow);
    cursor: pointer;
    /* filter: blur(0.25em); */
}

.cloze-show2 {
    font-weight: bold;
    color: var(--yellow);
    border-bottom: 1px solid var(--yellow);
    cursor: pointer;
}

* {
    margin: 0;
}