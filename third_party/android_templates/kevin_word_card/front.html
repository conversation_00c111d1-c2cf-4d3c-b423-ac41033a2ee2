<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON><PERSON>mplates -->

<script src="__anki-persistence.js"></script>

<div class="card">
    <div class="image-container">
        {{Image}}
        <div class="mask-overlay"></div>
    </div>
    {{#Source}}
    <div>{{Source}}</div>
    {{/Source}}
    <div id="nav" style="display: none; text-align: center">
        <button id="prev" onclick="go_prev()">
            < </button>
                <span id="progress"></span>
                <button id="next" onclick="go_next()">></button>
    </div>
    <div id="debug"></div>
    {{^Image}}
    <h1 class="word">
        {{word}}
    </h1>
    {{/Image}}
</div>

<script>
    var idx = 0;
    var cloze_ids = new Array();
    var flags = new Array();
    var mode = "{{Mode}}";

    function free_guess_init() {
        const Masks = JSON.parse("{{Masks}}");
        const container = document.querySelector(".image-container");
        const img = container.querySelector("img");
        const maskOverlay = document.querySelector(".mask-overlay");
        maskOverlay.innerHTML = "";
        const imgWidth = img.width;
        const imgHeight = img.height;
        for (const [index, group] of Masks.entries()) {
            let cloze_id = `c${index + 1}`;
            cloze_ids.push(cloze_id);
            for (let i = 0; i < group.length; i++) {
                let mask = group[i];
                const maskArea = document.createElement("a");
                maskArea.style.position = "absolute";
                maskArea.style.left = mask[0] * imgWidth + "px";
                maskArea.style.top = mask[1] * imgHeight + "px";
                maskArea.style.width = mask[2] * imgWidth + "px";
                maskArea.style.height = mask[3] * imgHeight + "px";
                maskArea.style.pointerEvents = "auto";
                maskArea.style.opacity = 1;
                maskArea.className = "overlay";
                maskArea.setAttribute("cloze_id", cloze_id);
                maskArea.onclick = function () {
                    [].forEach.call(
                        maskOverlay.querySelectorAll(`a[cloze_id=${cloze_id}]`),
                        (ele) => {
                            ele.style.opacity = ele.style.opacity >= 0.5 ? 0.1 : 1;
                        }
                    );
                };
                maskOverlay.appendChild(maskArea);
            }
        }
    }

    function mask_one_guess_one_multi_init() {
        const Masks = JSON.parse("{{Masks}}");
        const container = document.querySelector(".image-container");
        const img = container.querySelector("img");
        const maskOverlay = document.querySelector(".mask-overlay");
        maskOverlay.innerHTML = "";
        const imgWidth = img.width;
        const imgHeight = img.height;
        const target_index = "{{Index}}";
        for (const [index, group] of Masks.entries()) {
            let cloze_id = `c${index + 1}`;
            cloze_ids.push(cloze_id);
            if (cloze_id === target_index) {
                for (let i = 0; i < group.length; i++) {
                    let mask = group[i];
                    const maskArea = document.createElement("a");
                    maskArea.style.position = "absolute";
                    maskArea.style.left = mask[0] * imgWidth + "px";
                    maskArea.style.top = mask[1] * imgHeight + "px";
                    maskArea.style.width = mask[2] * imgWidth + "px";
                    maskArea.style.height = mask[3] * imgHeight + "px";
                    maskArea.style.pointerEvents = "auto";
                    maskArea.style.opacity = 1;
                    maskArea.className = "overlay";
                    maskArea.setAttribute("cloze_id", cloze_id);
                    maskArea.onclick = function () {
                        [].forEach.call(
                            maskOverlay.querySelectorAll(`a[cloze_id=${cloze_id}]`),
                            (ele) => {
                                ele.style.opacity = ele.style.opacity >= 0.5 ? 0.1 : 1;
                            }
                        );
                    };
                    maskOverlay.appendChild(maskArea);
                }
            }
        }
    }

    function mask_all_guess_one_multi_init() {
        const Masks = JSON.parse("{{Masks}}");
        const container = document.querySelector(".image-container");
        const img = container.querySelector("img");
        const maskOverlay = document.querySelector(".mask-overlay");
        maskOverlay.innerHTML = "";
        const imgWidth = img.width;
        const imgHeight = img.height;
        const target_index = "{{Index}}";
        for (const [index, group] of Masks.entries()) {
            let cloze_id = `c${index + 1}`;
            cloze_ids.push(cloze_id);
            if (cloze_id === target_index) {
                for (let i = 0; i < group.length; i++) {
                    let mask = group[i];
                    const maskArea = document.createElement("a");
                    maskArea.style.position = "absolute";
                    maskArea.style.left = mask[0] * imgWidth + "px";
                    maskArea.style.top = mask[1] * imgHeight + "px";
                    maskArea.style.width = mask[2] * imgWidth + "px";
                    maskArea.style.height = mask[3] * imgHeight + "px";
                    maskArea.style.pointerEvents = "auto";
                    maskArea.style.opacity = 1;
                    maskArea.setAttribute("cloze_id", cloze_id);
                    maskArea.className = "overlay";
                    maskArea.onclick = function () {
                        [].forEach.call(
                            maskOverlay.querySelectorAll(`a[cloze_id=${cloze_id}]`),
                            (ele) => {
                                ele.style.opacity = ele.style.opacity >= 0.5 ? 0.1 : 1;
                            }
                        );
                    };
                    maskOverlay.appendChild(maskArea);
                }
            } else {
                for (let i = 0; i < group.length; i++) {
                    let mask = group[i];
                    const maskArea = document.createElement("a");
                    maskArea.style.position = "absolute";
                    maskArea.style.left = mask[0] * imgWidth + "px";
                    maskArea.style.top = mask[1] * imgHeight + "px";
                    maskArea.style.width = mask[2] * imgWidth + "px";
                    maskArea.style.height = mask[3] * imgHeight + "px";
                    maskArea.style.pointerEvents = "auto";
                    maskArea.style.opacity = 1;
                    maskArea.className = "overlay-second";
                    maskArea.setAttribute("cloze_id", cloze_id);
                    maskArea.onclick = function () {
                        [].forEach.call(
                            maskOverlay.querySelectorAll(`a[cloze_id=${cloze_id}]`),
                            (ele) => {
                                ele.style.opacity = ele.style.opacity >= 0.5 ? 0.1 : 1;
                            }
                        );
                    };
                    maskOverlay.appendChild(maskArea);
                }
            }
        }
    }

    function mask_all_guess_one_init() {
        const Masks = JSON.parse("{{Masks}}");
        const container = document.querySelector(".image-container");
        const img = container.querySelector("img");
        const maskOverlay = document.querySelector(".mask-overlay");
        maskOverlay.innerHTML = "";
        const imgWidth = img.width;
        const imgHeight = img.height;
        const target_index = "{{Index}}";
        for (const [index, group] of Masks.entries()) {
            let cloze_id = `c${index + 1}`;
            cloze_ids.push(cloze_id);
            if (index === 0) {
                for (let i = 0; i < group.length; i++) {
                    let mask = group[i];
                    const maskArea = document.createElement("a");
                    maskArea.style.position = "absolute";
                    maskArea.style.left = mask[0] * imgWidth + "px";
                    maskArea.style.top = mask[1] * imgHeight + "px";
                    maskArea.style.width = mask[2] * imgWidth + "px";
                    maskArea.style.height = mask[3] * imgHeight + "px";
                    maskArea.style.pointerEvents = "auto";
                    maskArea.style.opacity = 1;
                    maskArea.className = "overlay";
                    maskArea.setAttribute("cloze_id", cloze_id);
                    maskArea.onclick = function () {
                        [].forEach.call(
                            maskOverlay.querySelectorAll(`a[cloze_id=${cloze_id}]`),
                            (ele) => {
                                ele.style.opacity = ele.style.opacity >= 0.5 ? 0.1 : 1;
                            }
                        );
                    };
                    maskOverlay.appendChild(maskArea);
                }
            } else {
                for (let i = 0; i < group.length; i++) {
                    let mask = group[i];
                    const maskArea = document.createElement("a");
                    maskArea.style.position = "absolute";
                    maskArea.style.left = mask[0] * imgWidth + "px";
                    maskArea.style.top = mask[1] * imgHeight + "px";
                    maskArea.style.width = mask[2] * imgWidth + "px";
                    maskArea.style.height = mask[3] * imgHeight + "px";
                    maskArea.style.pointerEvents = "auto";
                    maskArea.style.opacity = 1;
                    maskArea.className = "overlay-second";
                    maskArea.setAttribute("cloze_id", cloze_id);
                    maskArea.onclick = function () {
                        [].forEach.call(
                            maskOverlay.querySelectorAll(`a[cloze_id=${cloze_id}]`),
                            (ele) => {
                                ele.style.opacity = ele.style.opacity >= 0.5 ? 0.1 : 1;
                            }
                        );
                    };
                    maskOverlay.appendChild(maskArea);
                }
            }
        }
        // 显示导航
        let nav = document.getElementById("nav");
        nav.style.display = "block";
        let progress = document.getElementById("progress");
        progress.innerHTML = `1/${Masks.length}`;
    }

    function mask_all_guess_all_init() {
        const Masks = JSON.parse("{{Masks}}");
        const container = document.querySelector(".image-container");
        const img = container.querySelector("img");
        const maskOverlay = document.querySelector(".mask-overlay");
        maskOverlay.innerHTML = "";
        const imgWidth = img.width;
        const imgHeight = img.height;
        const target_index = "{{Index}}";
        for (const [index, group] of Masks.entries()) {
            let cloze_id = `c1`;
            cloze_ids.push(cloze_id);
            for (let i = 0; i < group.length; i++) {
                let mask = group[i];
                const maskArea = document.createElement("a");
                maskArea.style.position = "absolute";
                maskArea.style.left = mask[0] * imgWidth + "px";
                maskArea.style.top = mask[1] * imgHeight + "px";
                maskArea.style.width = mask[2] * imgWidth + "px";
                maskArea.style.height = mask[3] * imgHeight + "px";
                maskArea.style.pointerEvents = "auto";
                maskArea.style.opacity = 1;
                maskArea.className = "overlay";
                maskArea.setAttribute("cloze_id", cloze_id);
                maskArea.onclick = function () {
                    [].forEach.call(
                        maskOverlay.querySelectorAll(`a[cloze_id=${cloze_id}]`),
                        (ele) => {
                            ele.style.opacity = ele.style.opacity >= 0.5 ? 0.1 : 1;
                        }
                    );
                };
                maskOverlay.appendChild(maskArea);
            }
        }
    }

    function mask_one_guess_one_init() {
        const Masks = JSON.parse("{{Masks}}");
        const container = document.querySelector(".image-container");
        const img = container.querySelector("img");
        const maskOverlay = document.querySelector(".mask-overlay");
        maskOverlay.innerHTML = "";
        const imgWidth = img.width;
        const imgHeight = img.height;
        const target_index = "{{Index}}";
        for (const [index, group] of Masks.entries()) {
            let cloze_id = `c${index + 1}`;
            cloze_ids.push(cloze_id);
            if (index === 0) {
                for (let i = 0; i < group.length; i++) {
                    let mask = group[i];
                    const maskArea = document.createElement("a");
                    maskArea.style.position = "absolute";
                    maskArea.style.left = mask[0] * imgWidth + "px";
                    maskArea.style.top = mask[1] * imgHeight + "px";
                    maskArea.style.width = mask[2] * imgWidth + "px";
                    maskArea.style.height = mask[3] * imgHeight + "px";
                    maskArea.style.pointerEvents = "auto";
                    maskArea.style.opacity = 1;
                    maskArea.className = "overlay";
                    maskArea.setAttribute("cloze_id", cloze_id);
                    maskArea.onclick = function () {
                        [].forEach.call(
                            maskOverlay.querySelectorAll(`a[cloze_id=${cloze_id}]`),
                            (ele) => {
                                ele.style.opacity = ele.style.opacity >= 0.5 ? 0.1 : 1;
                            }
                        );
                    };
                    maskOverlay.appendChild(maskArea);
                }
            } else {
                for (let i = 0; i < group.length; i++) {
                    let mask = group[i];
                    const maskArea = document.createElement("a");
                    maskArea.style.position = "absolute";
                    maskArea.style.left = mask[0] * imgWidth + "px";
                    maskArea.style.top = mask[1] * imgHeight + "px";
                    maskArea.style.width = mask[2] * imgWidth + "px";
                    maskArea.style.height = mask[3] * imgHeight + "px";
                    maskArea.style.pointerEvents = "auto";
                    maskArea.style.opacity = 0.1;
                    maskArea.className = "overlay";
                    maskArea.setAttribute("cloze_id", cloze_id);
                    maskArea.onclick = function () {
                        [].forEach.call(
                            maskOverlay.querySelectorAll(`a[cloze_id=${cloze_id}]`),
                            (ele) => {
                                ele.style.opacity = ele.style.opacity >= 0.5 ? 0.1 : 1;
                            }
                        );
                    };
                    maskOverlay.appendChild(maskArea);
                }
            }
        }
        // 显示导航
        let nav = document.getElementById("nav");
        nav.style.display = "block";
        let progress = document.getElementById("progress");
        progress.innerHTML = `1/${Masks.length}`;
    }

    function switch_card() {
        let cloze_id = cloze_ids[idx];
        const maskOverlay = document.querySelector(".mask-overlay");
        if (mode === "free_guess") {
            [].forEach.call(maskOverlay.querySelectorAll("a"), (ele) => {
                let cur_cloze_id = ele.getAttribute("cloze_id");
                if (cur_cloze_id) {
                    let k = parseInt(cur_cloze_id.slice(1)) - 1;
                    console.log({ k });
                    if (flags[k]) {
                        ele.style.opacity = 0.1;
                    } else {
                        ele.style.opacity = 1;
                    }
                }
            });
        } else if (mode === "mask_one_guess_one") {
            [].forEach.call(maskOverlay.querySelectorAll("a"), (ele) => {
                let cur_cloze_id = ele.getAttribute("cloze_id");
                if (cur_cloze_id) {
                    if (cur_cloze_id === cloze_id) {
                        if (flags[idx]) {
                            ele.style.opacity = 0.1;
                        } else {
                            ele.style.opacity = 1;
                        }
                    } else {
                        ele.style.opacity = 0.1;
                    }
                }
            });
        } else if (mode === "mask_all_guess_one") {
            [].forEach.call(maskOverlay.querySelectorAll("a"), (ele) => {
                let cur_cloze_id = ele.getAttribute("cloze_id");
                if (cur_cloze_id) {
                    if (cur_cloze_id === cloze_id) {
                        ele.className = "overlay";
                        if (flags[idx]) {
                            ele.style.opacity = 0.1;
                        } else {
                            ele.style.opacity = 1;
                        }
                    } else {
                        ele.style.opacity = 1;
                        ele.className = "overlay-second";
                    }
                }
            });
        } else if (mode === "mask_half_guess_one") {
            [].forEach.call(maskOverlay.querySelectorAll("a"), (ele) => {
                let cur_cloze_id = ele.getAttribute("cloze_id");
                if (cur_cloze_id) {
                    let k = parseInt(cur_cloze_id.slice(1)) - 1;
                    if (k === idx) {
                        ele.className = "overlay";
                        if (flags[idx]) {
                            ele.style.opacity = 0.1;
                        } else {
                            ele.style.opacity = 1;
                        }
                    } else if (k > idx) {
                        ele.style.opacity = 1;
                        ele.className = "overlay-second";
                    } else {
                        ele.style.opacity = 0.1;
                    }
                }
            });
        }
    }

    function go_next() {
        const n = cloze_ids.length;
        if (mode !== 'free_guess') {
            if (!flags[idx]) {
                flags[idx] = !flags[idx];
                switch_card();
                return;
            }
            flags[idx] = true;
            idx = (idx + 1) % n;
            flags[idx] = false;
            let progress = document.getElementById("progress");
            progress.innerHTML = `${idx + 1}/${n}`;
            switch_card();
        } else {
            const maskOverlay = document.querySelector(".mask-overlay");
            let a_tags = maskOverlay.querySelectorAll("a");
            flags = new Array();
            let k = -1;
            for (const [index, a_tag] of a_tags.entries()) {
                if (a_tag.style.opacity >= 0.5) {
                    flags.push(false);
                    if (k < 0) {
                        k = index;
                    }
                } else {
                    flags.push(true);
                }
            }
            if (k >= 0) {
                flags[k] = true;
            }
            switch_card();
        }
        if (Persistence.isAvailable()) {
            Persistence.setItem("idx", idx);
            Persistence.setItem("flags", flags);
        }
    }

    function go_prev() {
        const n = cloze_ids.length;
        if (mode !== "free_guess") {
            if (!flags[idx]) {
                console.log("hit");
                flags[idx] = !flags[idx];
                switch_card();
                return;
            }
            flags[idx] = true;
            idx = (idx - 1 + n) % n;
            flags[idx] = false;
            let progress = document.getElementById("progress");
            progress.innerHTML = `${idx + 1}/${n}`;
            switch_card();
        } else {
            const maskOverlay = document.querySelector(".mask-overlay");
            let a_tags = maskOverlay.querySelectorAll("a");
            flags = new Array();
            let k = cloze_ids.length - 1;
            let FLAG = false;
            for (const [index, a_tag] of a_tags.entries()) {
                if (a_tag.style.opacity >= 0.5) {
                    flags.push(false);
                    FLAG = true;
                } else {
                    flags.push(true);
                    if (!FLAG) {
                        k = index;
                    }
                }
            }
            flags[k] = false;
            switch_card();

        }
        if (Persistence.isAvailable()) {
            Persistence.setItem("idx", idx);
            Persistence.setItem("flags", flags);
        }
    }
    function go_all() {
        if (flags.includes(false)) {
            flags.fill(true);
        } else {
            flags.fill(false);
        }
        switch_card();
    }

    function init() {
        cloze_ids = new Array();
        switch (mode) {
            case "free_guess": {
                free_guess_init();
                flags = new Array(cloze_ids.length).fill(false);
                break;
            }
            case "mask_one_guess_one": {
                mask_one_guess_one_init();
                flags = new Array(cloze_ids.length).fill(true);
                flags[0] = false;
                break;
            }
            case "mask_all_guess_one": {
                mask_all_guess_one_init();
                flags = new Array(cloze_ids.length).fill(true);
                flags[0] = false;
                break;
            }
            case "mask_all_guess_all": {
                mask_all_guess_all_init();
                break;
            }
            case "mask_half_guess_one": {
                mask_all_guess_one_init();
                break;
            }
            case "mask_one_guess_one_multi": {
                mask_one_guess_one_multi_init();
                flags = new Array(cloze_ids.length).fill(true);
                flags[0] = false;
                break;
            }
            case "mask_all_guess_one_multi": {
                mask_all_guess_one_multi_init();
                flags = new Array(cloze_ids.length).fill(true);
                flags[0] = false;
                break;
            }
        }
    }
    function handleResize() {
        init();
        switch_card();
    }
    handleResize();
    // 绑定快捷键
    function keyboardHandler(e) {
        console.log("e.key:", e.key);
        if (e.key == "j") {
            go_prev();
        }else if( e.key == "k"){
            go_next();
        }else if( e.key == "u"){
            go_all();
        }
        document.addEventListener("keyup", keyboardHandler, {once: true});
    }
    document.addEventListener("keyup", keyboardHandler, {once: true});


    if (Persistence.isAvailable()) {
        Persistence.setItem("cloze_ids", cloze_ids);
        Persistence.setItem("idx", idx);
        Persistence.setItem("flags", flags);
    }
    window.addEventListener("resize", handleResize);
</script>