<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON><PERSON>-<PERSON>mplates -->
<script src="__anki-persistence.js"></script>

<div class="card">
    {{#Header}}
    <div class="header">
        {{Header}}
    </div>
    {{/Header}}
    <div>{{Front}}</div>
</div>
<hr >
<div class="card">
    <div class="image-container">
        {{Back}}
        <div class="mask-overlay"></div>
    </div>
    <div>{{Notes}}</div>
    {{#Masks}}
    <div style="text-align:center">
        <button onclick="go_all()">显示全部</button>
    </div>
    {{/Masks}}
</div>
<script>
    var idx = 0;
    var cloze_ids = new Array();
    var flags = new Array();
    function free_guess_init() {
        const Masks = JSON.parse("{{Masks}}");
        console.log({ Masks });
        const container = document.querySelector(".image-container");
        const img = container.querySelector("img");
        const maskOverlay = document.querySelector(".mask-overlay");
        maskOverlay.innerHTML = "";
        const imgWidth = img.width;
        const imgHeight = img.height;
        console.log({ imgWidth, imgHeight });
        for (const [index, group] of Masks.entries()) {
            let cloze_id = `c${index + 1}`;
            cloze_ids.push(cloze_id);
            for (let i = 0; i < group.length; i++) {
                let mask = group[i];
                const maskArea = document.createElement("a");
                maskArea.style.position = "absolute";
                maskArea.style.left = mask[0] * imgWidth + "px";
                maskArea.style.top = mask[1] * imgHeight + "px";
                maskArea.style.width = mask[2] * imgWidth + "px";
                maskArea.style.height = mask[3] * imgHeight + "px";
                maskArea.style.pointerEvents = "auto";
                maskArea.style.opacity = 1;
                maskArea.className = "overlay";
                maskArea.setAttribute("cloze_id", cloze_id);
                maskArea.onclick = function () {
                    [].forEach.call(
                        maskOverlay.querySelectorAll(`a[cloze_id=${cloze_id}]`),
                        (ele) => {
                            ele.style.opacity = ele.style.opacity >= 0.5 ? 0.1 : 1;
                        }
                    );
                };
                maskOverlay.appendChild(maskArea);
            }
        }
    }
    function switch_card() {
        let cloze_id = cloze_ids[idx];
        const maskOverlay = document.querySelector(".mask-overlay");
        [].forEach.call(maskOverlay.querySelectorAll("a"), (ele) => {
            let cur_cloze_id = ele.getAttribute("cloze_id");
            if (cur_cloze_id) {
                let k = parseInt(cur_cloze_id.slice(1)) - 1;
                console.log({ k });
                if (flags[k]) {
                    ele.style.opacity = 0.1;
                } else {
                    ele.style.opacity = 1;
                }
            }
        });
    }
    function go_next() {
        const n = cloze_ids.length;
        const maskOverlay = document.querySelector(".mask-overlay");
        let a_tags = maskOverlay.querySelectorAll("a");
        flags = new Array();
        let k = -1;
        for (const [index, a_tag] of a_tags.entries()) {
            if (a_tag.style.opacity >= 0.5) {
                flags.push(false);
                if (k < 0) {
                    k = index;
                }
            } else {
                flags.push(true);
            }
        }
        if (k >= 0) {
            flags[k] = true;
        }
        switch_card();
    }
    function go_prev() {
        const n = cloze_ids.length;
        const maskOverlay = document.querySelector(".mask-overlay");
        let a_tags = maskOverlay.querySelectorAll("a");
        flags = new Array();
        let k = cloze_ids.length - 1;
        let FLAG = false;
        for (const [index, a_tag] of a_tags.entries()) {
            if (a_tag.style.opacity >= 0.5) {
                flags.push(false);
                FLAG = true;
            } else {
                flags.push(true);
                if (!FLAG) {
                    k = index;
                }
            }
        }
        flags[k] = false;
        switch_card();
    }
    function go_all() {
        console.log("go_all");
        if (flags.includes(false)) {
            flags.fill(true);
        } else {
            flags.fill(false);
        }
        switch_card();
    }
    function init() {
        cloze_ids = new Array();
        free_guess_init();
        flags = new Array(cloze_ids.length).fill(false);
    }
    function handleResize() {
        init();
        switch_card();
    }
    init();
    // 绑定快捷键
    function keyboardHandler(e) {
        console.log("e.key:", e.key);
        if (e.key == "j") {
            go_prev();
        }else if( e.key == "k"){
            go_next();
        }else if( e.key == "u"){
            go_all();
        }
        document.addEventListener("keyup", keyboardHandler, {once: true});
    }
    document.addEventListener("keyup", keyboardHandler, {once: true});

    window.addEventListener("resize", handleResize);
</script>
