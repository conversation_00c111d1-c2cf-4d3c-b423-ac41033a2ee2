<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON><PERSON>mplates -->
<script>
    // v1.1.8 - https://github.com/SimonLammer/anki-persistence/blob/584396fea9dea0921011671a47a0fdda19265e62/script.js
    if(void 0===window.Persistence){var e="github.com/SimonLammer/anki-persistence/",t="_default";if(window.Persistence_sessionStorage=function(){var i=!1;try{"object"==typeof window.sessionStorage&&(i=!0,this.clear=function(){for(var t=0;t<sessionStorage.length;t++){var i=sessionStorage.key(t);0==i.indexOf(e)&&(sessionStorage.removeItem(i),t--)}},this.setItem=function(i,n){void 0==n&&(n=i,i=t),sessionStorage.setItem(e+i,JSON.stringify(n))},this.getItem=function(i){return void 0==i&&(i=t),JSON.parse(sessionStorage.getItem(e+i))},this.removeItem=function(i){void 0==i&&(i=t),sessionStorage.removeItem(e+i)},this.getAllKeys=function(){for(var t=[],i=Object.keys(sessionStorage),n=0;n<i.length;n++){var s=i[n];0==s.indexOf(e)&&t.push(s.substring(e.length,s.length))}return t.sort()})}catch(n){}this.isAvailable=function(){return i}},window.Persistence_windowKey=function(i){var n=window[i],s=!1;"object"==typeof n&&(s=!0,this.clear=function(){n[e]={}},this.setItem=function(i,s){void 0==s&&(s=i,i=t),n[e][i]=s},this.getItem=function(i){return void 0==i&&(i=t),void 0==n[e][i]?null:n[e][i]},this.removeItem=function(i){void 0==i&&(i=t),delete n[e][i]},this.getAllKeys=function(){return Object.keys(n[e])},void 0==n[e]&&this.clear()),this.isAvailable=function(){return s}},window.Persistence=new Persistence_sessionStorage,Persistence.isAvailable()||(window.Persistence=new Persistence_windowKey("py")),!Persistence.isAvailable()){var i=window.location.toString().indexOf("title"),n=window.location.toString().indexOf("main",i);i>0&&n>0&&n-i<10&&(window.Persistence=new Persistence_windowKey("qt"))}}
    </script>
    <div class="detail-body">
      <div class="question">
        <div class="q-header">
          <span>
            <img src="__bookmark.svg" />
            <b id="q_type"></b>
          </span>
          <a class="submit-btn" onclick="openPopup()">
            <img src="__gear.svg"  style="width:1.5em"/>
          </a>
        </div>
        <hr />
        <div class="q-body">{{Question}}</div>
        <div id="q-clozes"></div>
        <div class="q-answers" id="q-options"></div>
        <div id="show_deck"></div>
        <div id="show_tag"></div>
      </div>
    </div>
    <div id="settingsModal" class="settings-container" style="display: none">
      <div class="modal-content">
        <span class="close-popup" onclick="closePopup()">&times;</span>
        <div class="settings-block">
          <div class="single-setting">
            <div class="setting-label">显示题型</div>
            <div class="setting-switch">
              <input
                id="show-type"
                class="mui-switch mui-switch-anim"
                type="checkbox"
              />
            </div>
          </div>
          <div class="single-setting">
            <div class="setting-label">显示牌组</div>
            <div class="setting-switch">
              <input
                id="show-deck"
                class="mui-switch mui-switch-anim"
                type="checkbox"
              />
            </div>
          </div>
          <div class="single-setting">
            <div class="setting-label">显示标签</div>
            <div class="setting-switch">
              <input
                id="show-tag"
                class="mui-switch mui-switch-anim"
                type="checkbox"
              />
            </div>
          </div>
          <div class="single-setting">
            <div class="setting-label">随机选项</div>
            <div class="setting-switch">
              <input
                id="random-option"
                class="mui-switch mui-switch-anim"
                type="checkbox"
              />
            </div>
          </div>
          <div class="single-setting">
            <div class="setting-label">自动翻转</div>
            <div class="setting-switch">
              <input
                id="auto-flip"
                class="mui-switch mui-switch-anim"
                type="checkbox"
              />
            </div>
          </div>
          <div class="single-setting">
            <div class="setting-label">做题统计</div>
            <div class="setting-switch">
              <input
                id="statistics"
                class="mui-switch mui-switch-anim"
                type="checkbox"
              />
            </div>
          </div>
          <div class="single-setting">
            <div class="setting-label">播放音效</div>
            <div class="setting-switch">
              <input
                id="play-sound"
                class="mui-switch mui-switch-anim"
                type="checkbox"
              />
            </div>
          </div>
          <div class="single-setting">
            <div class="setting-label">填空模式</div>
            <div class="setting-switch">
              <input
                id="cloze-mode"
                class="mui-switch mui-switch-anim"
                type="checkbox"
              />
            </div>
          </div>
          <div class="single-setting">
            <div class="setting-label">夜间模式</div>
            <div class="setting-switch">
              <input
                id="night-mode"
                class="mui-switch mui-switch-anim"
                type="checkbox"
              />
            </div>
          </div>
      </div>
      </div>
    </div>
    <div id="options_data" style="display:none">{{Options}}</div>
    <div id="debug"></div>
    
    <script>
      // 打开弹窗的函数
      function openPopup() {
        document.getElementById("settingsModal").style.display = "block";
      }
    
      // 关闭弹窗的函数
      function closePopup() {
        document.getElementById("settingsModal").style.display = "none";
      }
    
      function autoFlip() {
        document.getElementById("qa").style.display = "none";
        if (window.pycmd) {
          pycmd("ans");
        } else if (window.showAnswer) {
          showAnswer();
        }
        document.getElementById("qa").style.display = "block";
      }
      //  功能配置
      var config_map = {
        "show_type"    : true,    // 显示题型
        "show_deck"    : false,   // 显示牌组
        "show_tag"     : true,    // 显示标签
        "random_option": false,   // 随机选项
        "auto_flip"    : true,    // 自动翻转
        "statistics"   : true,    // 显示统计信息
        "play_sound"   : true,    // 播放音效
        "cloze_mode"   : false,   // 填空模式
        "night_mode"   : false,   // 夜间模式
      };
      var show_type     = document.getElementById("show-type");
      var show_deck     = document.getElementById("show-deck");
      var show_tag      = document.getElementById("show-tag");
      var random_option = document.getElementById("random-option");
      var auto_flip     = document.getElementById("auto-flip");
      var statistics    = document.getElementById("statistics");
      var play_sound    = document.getElementById("play-sound");
      var cloze_mode    = document.getElementById("cloze-mode");
      var night_mode    = document.getElementById("night-mode");
    
      if (Persistence.isAvailable()) {
        for(const key in config_map) {
          if (Persistence.getItem(key) == undefined || Persistence.getItem(key) == null) {
            Persistence.setItem(key, config_map[key]);
          }
        }
        show_type.checked     = Persistence.getItem("show_type");
        show_deck.checked     = Persistence.getItem("show_deck");
        show_tag.checked      = Persistence.getItem("show_tag");
        random_option.checked = Persistence.getItem("random_option");
        auto_flip.checked     = Persistence.getItem("auto_flip");
        statistics.checked    = Persistence.getItem("statistics");
        play_sound.checked    = Persistence.getItem("play_sound");
        cloze_mode.checked    = Persistence.getItem("cloze_mode");
        night_mode.checked    = Persistence.getItem("night_mode");
      }
    
      show_type.onchange = function () {
        if (Persistence.isAvailable()) {
          Persistence.setItem("show_type", show_type.checked);
          update_type();
        }
      };
      show_deck.onchange = function () {
        if (Persistence.isAvailable()) {
            Persistence.setItem("show_deck", show_deck.checked);
            update_deck();
        }
      };
      show_tag.onchange = function () {
        if (Persistence.isAvailable()) {
            Persistence.setItem("show_tag", show_tag.checked);
            update_tag();
        }
      };
      random_option.onchange = function () {
        if (Persistence.isAvailable()) {
          Persistence.setItem("random_option", random_option.checked);
          updateOptions();
          update_nightmode();
        }
      };
      auto_flip.onchange = function () {
        if (Persistence.isAvailable()) {
          Persistence.setItem("auto_flip", auto_flip.checked);
        }
      };
      statistics.onchange = function () {
        if (Persistence.isAvailable()) {
          Persistence.setItem("statistics", statistics.checked);
        }
      };
      play_sound.onchange = function () {
        if (Persistence.isAvailable()) {
          Persistence.setItem("play_sound", play_sound.checked);
        }
      }
      cloze_mode.onchange = function () {
        if (Persistence.isAvailable()) {
          Persistence.setItem("cloze_mode", cloze_mode.checked);
          if(cloze_mode.checked) {
            updateClozes();
          }else{
            var q_cloze_div = document.getElementById("q-clozes");
            q_cloze_div.style.display = "none";
            updateOptions();
          }
          update_type();
        }
      }
      night_mode.onchange = function () {
        if (Persistence.isAvailable()) {
          Persistence.setItem("night_mode", night_mode.checked);
        }
        update_nightmode();
      }
      function update_nightmode(){
        var roi_selectors = ["#show_deck",  "#show_tag", "#q_type", "#q-clozes", ".q-header", ".detail-body", ".question", ".q-body", ".q-answer", ".q-answers", ".answer", ".q-deck", ".q-tag", ".explain", "table", "thead tr"];
        for(const name of roi_selectors){
          try{
            [].forEach.call(document.querySelectorAll(name), (ele)=>{
              if(night_mode.checked){
                if(!ele.classList.contains("nightMode")){
                  ele.classList.toggle("nightMode");
                }
              }else{
                if(ele.classList.contains("nightMode")){
                  ele.classList.toggle("nightMode");
                }
              }
            });
          }catch(err){
            console.log(err)
          }
        }
      }
      function update_type() {
        var q_type = document.getElementById("q_type");
        var answers = `{{Answers}}`.split("||");
        if (Persistence.isAvailable()) {
          var show_type = Persistence.getItem("show_type");
          if(!Persistence.getItem("cloze_mode")){
            if (show_type) {
              if (answers.length > 1) {
                q_type.innerText = "多选题";
              } else {
                q_type.innerText = "单选题";
              }
            } else {
              q_type.innerText = "选择题";
            }
          }else{
            q_type.innerText = "填空题";
          }
        }
      }
      function update_tag() {
        if (Persistence.isAvailable()) {
          var show_tag_div = document.getElementById("show_tag");
          if(Persistence.getItem("show_tag")) {
            show_tag_div.style.display = "block";
            if(`{{Tags}}`.trim() == "") {
              show_tag_div.innerHTML = "<div style='color:#707070'><b>标签:</b> 无</div>";
            }else{
              var tags = `{{Tags}}`.split(" ");
              tag_html = "<b style='color:#707070'>标签:</b>"
              for(var i = 0; i < tags.length; i++) {
                let span = document.createElement("span");
                span.className = "q-tag";
                span.innerText = tags[i];
                tag_html += span.outerHTML;
              }
              show_tag_div.innerHTML = tag_html;
            }
          }else{
            show_tag_div.innerHTML = "";
            show_tag_div.style.display = "none";
          }
          update_nightmode();
        }
      };
      function update_deck() {
          if (Persistence.isAvailable()) {
            var show_deck_div = document.getElementById("show_deck");
            if(Persistence.getItem("show_deck")) {
              show_deck_div.style.display = "block";
              show_deck_div.innerHTML = `<div style='color:#707070'><b>牌组:</b><span class="q-deck">{{Deck}}</span>`;
            }else{
              show_deck_div.innerHTML = "";
              show_deck_div.style.display = "none";
            }
            update_nightmode();
          }
      };
      function shuffleArray(array) {
        var currentIndex = array.length,
          temporaryValue,
          randomIndex;
        while (0 !== currentIndex) {
          randomIndex = Math.floor(Math.random() * currentIndex);
          currentIndex -= 1;
          temporaryValue = array[currentIndex];
          array[currentIndex] = array[randomIndex];
          array[randomIndex] = temporaryValue;
        }
        return array;
      }
    
      function updateOptions() {
        var options = document.getElementById("options_data").innerHTML.split("||");
        var answers = `{{Answers}}`.split("||");
        var q_options_div = document.getElementById("q-options");
        q_options_div.style.display = "block";
        q_options_div.innerHTML = "";
        var seqs = [...Array(options.length).keys()].map((x) => x);
        console.log("before:", seqs);
        if (Persistence.isAvailable()) {
          var is_random = Persistence.getItem("random_option");
          if (is_random) {
            seqs = shuffleArray(seqs);
          }
          console.log("after:", seqs);
        }
        if (Persistence.isAvailable()) {
          Persistence.setItem("seqs", seqs);
        }
        for (const [i, val] of seqs.entries()) {
          console.log(i, val);
          var option_div = document.createElement("div");
          option_div.className = "q-answer choosable normal";
          option_div.setAttribute("seq", i + 1);
          option_div.setAttribute("val", val + 1);
          // option_div.innerHTML = String.fromCharCode(65 + i) + ". " + options[val];
          option_div.innerHTML = `<span>${String.fromCharCode(65 + i)}. ${options[val]}</span>`;
          option_div.onclick = function () {
            if (answers.length <= 1) {
              // 清除其他选中
              for (let j = 0; j < q_options_div.children.length; j++) {
                let child = q_options_div.children[j];
                if (child.classList.contains("chosen")) {
                  child.classList.remove("chosen");
                  child.classList.add("normal");
                }
              }
            }
            if (this.classList.contains("choosable")) {
              this.classList.toggle("chosen");
              if (this.classList.contains("chosen")) {
                this.classList.remove("normal");
              } else {
                this.classList.add("normal");
              }
              let selectedOptions = [];
              for (let j = 0; j < q_options_div.children.length; j++) {
                if (q_options_div.children[j].classList.contains("chosen")) {
                  selectedOptions.push(
                    q_options_div.children[j].getAttribute("val")
                  );
                }
              }
              if (Persistence.isAvailable()) {
                Persistence.setItem("selectedOptions", selectedOptions);
              }
            }
            if (answers.length > 1) {
              return;
            }
            if (Persistence.isAvailable()) {
              var is_auto_flip = Persistence.getItem("auto_flip");
              if (is_auto_flip) {
                autoFlip();
              }
            }
          };
          q_options_div.appendChild(option_div);
        }
      }
    
      function updateClozes() {
        var mask = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
        var correctAnswers = `{{Answers}}`.split("||");
        var q_cloze_div = document.getElementById("q-clozes");
        q_cloze_div.style.display = "block";
        var q_options_div = document.getElementById("q-options");
        q_options_div.style.display = "none";
        var options = `{{Options}}`.split("||");
        let final_html_content = "";
        for(let i=0; i<correctAnswers.length; i++){
          let option_content = options[parseInt(correctAnswers[i])-1];
          let span = document.createElement("span");
          span.setAttribute("cloze_id", `c${i}`);
          span.setAttribute("cloze_text", `${option_content}`);
          span.innerHTML = mask;
          span.className = "cloze-hide";
          final_html_content += `${span.outerHTML}`;
          if(i!= correctAnswers.length-1) final_html_content += "，";
        }
        q_cloze_div.innerHTML = final_html_content;
        document.querySelectorAll(".cloze-hide, .cloze-show").forEach(ele => {
            let cloze_id = ele.getAttribute("cloze_id");
            ele.onclick = () => {
                [].forEach.call(q_cloze_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                    if (ele.className === 'cloze-hide') {
                        ele.innerHTML = ele.getAttribute("cloze_text");
                        ele.className = "cloze-show";
                    } else {
                        ele.innerHTML = mask;
                        ele.className = "cloze-hide";
                    }
                })
            }
        });
      }
    
      update_type();
      update_tag();
      update_deck();
      if (Persistence.isAvailable()) {
        // 非填空模式
        if(!Persistence.getItem("cloze_mode")){
          var q_cloze_div = document.getElementById("q-clozes");
          q_cloze_div.style.display = "none";
          updateOptions();
          Persistence.setItem("selectedOptions", []);
        }else{
          // 填空模式
          updateClozes();
        }
      }
      update_nightmode();
    
      // 绑定键盘事件
      function keyboardHandler(e) {
        console.log("e.key:", e.key);
        if (e.key >= "1" && e.key <= "7") {
          let id = parseInt(e.key - '1');
          document.getElementById("q-options")?.children[id]?.click();
        }
        document.addEventListener("keyup", keyboardHandler, {once: true});
      }
      var userJs1 = () => {
        console.log("e.key:", e.key);
        document.getElementById("q-options")?.children[0]?.click();
        document.addEventListener("keyup", keyboardHandler, {once: true});
      };
      var userJs2 = () => {
        console.log("e.key:", e.key);
        document.getElementById("q-options")?.children[1]?.click();
        document.addEventListener("keyup", keyboardHandler, {once: true});
      };
      var userJs3 = () => {
        console.log("e.key:", e.key);
        document.getElementById("q-options")?.children[2]?.click();
        document.addEventListener("keyup", keyboardHandler, {once: true});
      };
      var userJs4 = () => {
        console.log("e.key:", e.key);
        document.getElementById("q-options")?.children[3]?.click();
        document.addEventListener("keyup", keyboardHandler, {once: true});
      };
      var userJs5 = () => {
        console.log("e.key:", e.key);
        document.getElementById("q-options")?.children[4]?.click();
        document.addEventListener("keyup", keyboardHandler, {once: true});
      };
      document.addEventListener("keyup", keyboardHandler, {once: true});
    </script>
    