<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON><PERSON>mplates -->
<script>
    // v1.1.8 - https://github.com/SimonLammer/anki-persistence/blob/584396fea9dea0921011671a47a0fdda19265e62/script.js
    if (void 0 === window.Persistence) { var e = "github.com/SimonLammer/anki-persistence/", t = "_default"; if (window.Persistence_sessionStorage = function () { var i = !1; try { "object" == typeof window.sessionStorage && (i = !0, this.clear = function () { for (var t = 0; t < sessionStorage.length; t++) { var i = sessionStorage.key(t); 0 == i.indexOf(e) && (sessionStorage.removeItem(i), t--) } }, this.setItem = function (i, n) { void 0 == n && (n = i, i = t), sessionStorage.setItem(e + i, JSON.stringify(n)) }, this.getItem = function (i) { return void 0 == i && (i = t), JSON.parse(sessionStorage.getItem(e + i)) }, this.removeItem = function (i) { void 0 == i && (i = t), sessionStorage.removeItem(e + i) }, this.getAllKeys = function () { for (var t = [], i = Object.keys(sessionStorage), n = 0; n < i.length; n++) { var s = i[n]; 0 == s.indexOf(e) && t.push(s.substring(e.length, s.length)) } return t.sort() }) } catch (n) { } this.isAvailable = function () { return i } }, window.Persistence_windowKey = function (i) { var n = window[i], s = !1; "object" == typeof n && (s = !0, this.clear = function () { n[e] = {} }, this.setItem = function (i, s) { void 0 == s && (s = i, i = t), n[e][i] = s }, this.getItem = function (i) { return void 0 == i && (i = t), void 0 == n[e][i] ? null : n[e][i] }, this.removeItem = function (i) { void 0 == i && (i = t), delete n[e][i] }, this.getAllKeys = function () { return Object.keys(n[e]) }, void 0 == n[e] && this.clear()), this.isAvailable = function () { return s } }, window.Persistence = new Persistence_sessionStorage, Persistence.isAvailable() || (window.Persistence = new Persistence_windowKey("py")), !Persistence.isAvailable()) { var i = window.location.toString().indexOf("title"), n = window.location.toString().indexOf("main", i); i > 0 && n > 0 && n - i < 10 && (window.Persistence = new Persistence_windowKey("qt")) } }
</script>
<div style="display: flex;flex-direction: column; height: 100%;">
    <div style="flex: 1;">
        <div id='q_div'>{{Front}}</div>
    </div>
    <div id="nav">
        <div>
            <a id="prev" class="nav_btn" onclick="go_prev()">上一个</a>
        </div>
        <div>
            <a id="next" class="nav_btn" onclick="go_next()">下一个</a>
        </div>
    </div>
</div>
<script type="text/javascript">
    var cloze_regex = /\[\[c(\d+)::(.*?)\]\]/gm;
    var idx = 0;
    var cloze_ids = new Array();
    var flags = new Array();
    if (Persistence.isAvailable()) {
        Persistence.setItem("idx", idx);
        Persistence.setItem("cloze_ids", cloze_ids);
    }
    var mode = "{{Mode}}";
    var final_html_content = "";
    function free_guess_init() {
        let matches;
        let last_pos = 0;
        final_html_content = "";
        var q_div = document.getElementById("q_div");
        const text = q_div.innerHTML;
        while ((matches = cloze_regex.exec(text)) !== null) {
            let index = matches[1];
            let content = matches[2];
            let start = matches.index;
            let end = start + matches[0].length;
            if (start >= last_pos) {
                let pos = Math.max(0, last_pos - 1);
                final_html_content += `${text.slice(pos, start)}`
            }
            let cloze_id = `c${index}`;
            let span = document.createElement("span");
            span.setAttribute("cloze_id", cloze_id);
            span.innerHTML = content;
            span.className = "cloze activated";
            final_html_content += span.outerHTML;
            last_pos = end + 1;
        }
        if (text.length >= last_pos) {
            let pos = Math.max(0, last_pos - 1);
            final_html_content += `${text.slice(pos, text.length)}`
        }
        q_div.innerHTML = final_html_content;
        document.querySelectorAll(".cloze").forEach(ele => {
            let cloze_id = ele.getAttribute("cloze_id");
            ele.onclick = () => {
                [].forEach.call(q_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                    ele.classList.toggle("activated");
                })
            }
        });
    }
    function mask_one_guess_one_multi_init() {
        let matches;
        let last_pos = 0;
        final_html_content = "";
        var q_div = document.getElementById("q_div");
        const text = q_div.innerHTML;
        const target_index = "{{Index}}";
        while ((matches = cloze_regex.exec(text)) !== null) {
            let index = matches[1];
            let content = matches[2];
            let start = matches.index;
            let end = start + matches[0].length;
            if (start >= last_pos) {
                let pos = Math.max(0, last_pos - 1);
                final_html_content += `${text.slice(pos, start)}`
            }
            let cloze_id = `c${index}`;
            let span = document.createElement("span");
            span.setAttribute("cloze_id", cloze_id);
            if (cloze_id === target_index) {
                span.className = "cloze activated";
            }
            span.innerHTML = content;
            final_html_content += span.outerHTML;
            last_pos = end + 1;
        }
        if (text.length >= last_pos) {
            let pos = Math.max(0, last_pos - 1);
            final_html_content += `${text.slice(pos, text.length)}`
        }
        q_div.innerHTML = final_html_content;
        document.querySelectorAll(".cloze").forEach(ele => {
            let cloze_id = ele.getAttribute("cloze_id");
            ele.onclick = () => {
                [].forEach.call(q_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                    ele.classList.toggle("activated");
                })
            }
        });
    }
    function mask_all_guess_one_multi_init() {
        let matches;
        let last_pos = 0;
        final_html_content = "";
        var q_div = document.getElementById("q_div");
        const text = q_div.innerHTML;
        const target_index = "{{Index}}";
        while ((matches = cloze_regex.exec(text)) !== null) {
            let index = matches[1];
            let content = matches[2];
            let start = matches.index;
            let end = start + matches[0].length;
            if (start >= last_pos) {
                let pos = Math.max(0, last_pos - 1);
                final_html_content += `${text.slice(pos, start)}`
            }
            let cloze_id = `c${index}`;
            let span = document.createElement("span");
            span.setAttribute("cloze_id", cloze_id);
            if (cloze_id === target_index) {
                span.className = "cloze activated";
            } else {
                span.className = "cloze2 activated";
            }
            span.innerHTML = content;
            final_html_content += span.outerHTML;
            last_pos = end + 1;
        }
        if (text.length >= last_pos) {
            let pos = Math.max(0, last_pos - 1);
            final_html_content += `${text.slice(pos, text.length)}`
        }
        q_div.innerHTML = final_html_content;
        document.querySelectorAll(".cloze, .cloze2").forEach(ele => {
            let cloze_id = ele.getAttribute("cloze_id");
            ele.onclick = () => {
                [].forEach.call(q_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                    ele.classList.toggle("activated");
                })
            }
        });
    }
    function mask_all_guess_all_init() {
        let matches;
        let last_pos = 0;
        final_html_content = "";
        var q_div = document.getElementById("q_div");
        const text = q_div.innerHTML;
        const target_index = "{{Index}}";
        while ((matches = cloze_regex.exec(text)) !== null) {
            let index = matches[1];
            let content = matches[2];
            let start = matches.index;
            let end = start + matches[0].length;
            if (start >= last_pos) {
                let pos = Math.max(0, last_pos - 1);
                final_html_content += `${text.slice(pos, start)}`
            }
            let span = document.createElement("span");
            span.setAttribute("cloze_id", "c1");
            span.className = "cloze activated";
            span.innerHTML = content;
            final_html_content += span.outerHTML;
            last_pos = end + 1;
        }
        if (text.length >= last_pos) {
            let pos = Math.max(0, last_pos - 1);
            final_html_content += `${text.slice(pos, text.length)}`
        }
        q_div.innerHTML = final_html_content;
        document.querySelectorAll(".cloze").forEach(ele => {
            let cloze_id = ele.getAttribute("cloze_id");
            ele.onclick = () => {
                [].forEach.call(q_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                    ele.classList.toggle("activated");
                })
            }
        });
    }
    function mask_one_guess_one_init() {
        let matches;
        let last_pos = 0;
        final_html_content = "";
        var q_div = document.getElementById("q_div");
        const text = q_div.innerHTML;
        while ((matches = cloze_regex.exec(text)) !== null) {
            let index = matches[1];
            let content = matches[2];
            let start = matches.index;
            let end = start + matches[0].length;
            if (start >= last_pos) {
                let pos = Math.max(0, last_pos - 1);
                final_html_content += `${text.slice(pos, start)}`
            }
            let span = document.createElement("span");
            let cloze_id = `c${index}`;
            span.setAttribute("cloze_id", cloze_id);
            if (!cloze_ids.includes(cloze_id)) {
                cloze_ids.push(cloze_id)
            }
            span.className = "cloze";
            span.innerHTML = content;
            final_html_content += span.outerHTML;
            last_pos = end + 1;
        }
        if (text.length >= last_pos) {
            let pos = Math.max(0, last_pos - 1);
            final_html_content += `${text.slice(pos, text.length)}`
        }
        q_div.innerHTML = final_html_content;
        document.querySelectorAll(".cloze").forEach(ele => {
            let cloze_id = ele.getAttribute("cloze_id");
            ele.onclick = () => {
                [].forEach.call(q_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                    ele.classList.toggle("activated");
                })
            }
            if (cloze_ids.length > 0 && cloze_id == cloze_ids[0]) {
                ele.className = "cloze activated";
                if (Persistence.isAvailable()) {
                    Persistence.setItem("cur_cloze_id", cloze_ids[0]);
                }
            }
        });
    }
    function mask_all_guess_one_init() {
        let matches;
        let last_pos = 0;
        final_html_content = "";
        var q_div = document.getElementById("q_div");
        const text = q_div.innerHTML;
        const target_index = "{{Index}}";
        while ((matches = cloze_regex.exec(text)) !== null) {
            let index = matches[1];
            let content = matches[2];
            let start = matches.index;
            let end = start + matches[0].length;
            if (start >= last_pos) {
                let pos = Math.max(0, last_pos - 1);
                final_html_content += `${text.slice(pos, start)}`
            }
            let cloze_id = `c${index}`;
            let span = document.createElement("span");
            span.setAttribute("cloze_id", cloze_id);
            if (!cloze_ids.includes(cloze_id)) {
                cloze_ids.push(cloze_id);
            }
            span.className = "cloze";
            span.innerHTML = content;
            final_html_content += span.outerHTML;
            last_pos = end + 1;
        }
        if (text.length >= last_pos) {
            let pos = Math.max(0, last_pos - 1);
            final_html_content += `${text.slice(pos, text.length)}`
        }
        q_div.innerHTML = final_html_content;
        document.querySelectorAll(".cloze, .cloze2").forEach(ele => {
            let cloze_id = ele.getAttribute("cloze_id");
            ele.onclick = () => {
                [].forEach.call(q_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                    ele.classList.toggle("activated");
                })
            }
            if (cloze_ids.length > 0 && cloze_id == cloze_ids[0]) {
                ele.className = "cloze activated";
                if (cloze_ids.length > 0 && cloze_id == cloze_ids[0]) {
                    ele.className = "cloze activated";
                    if (Persistence.isAvailable()) {
                        Persistence.setItem("cur_cloze_id", cloze_ids[0]);
                    }
                }
            } else {
                ele.className = "cloze2 activated";
            }
        });
    }

    function go_next() {
        if (mode === "free_guess" || mode === "mask_all_guess_all") {
            for (const ele of document.querySelectorAll(".cloze")) {
                if (ele.classList.contains("activated")) {
                    ele.classList.toggle("activated");
                    break;
                }
            }
        } else if (mode === "mask_all_guess_one") {
            let ele = document.querySelector(".cloze");
            if (ele.classList.contains("activated")) {
                [].forEach.call(q_div.querySelectorAll(`.cloze`), (ele) => {
                    ele.classList.toggle("activated");
                })
            } else {
                let cur_cloze_id = ele.getAttribute("cloze_id");
                let idx = cloze_ids.indexOf(cur_cloze_id);
                let next_cloze_id = cloze_ids[(idx + 1) % cloze_ids.length];
                if (Persistence.isAvailable()) {
                    Persistence.setItem("cur_cloze_id", next_cloze_id);
                }
                [].forEach.call(q_div.querySelectorAll(`span[cloze_id]`), (ele) => {
                    let cloze_id = ele.getAttribute("cloze_id");
                    if (cloze_id === next_cloze_id) {
                        ele.className = "cloze activated";
                    } else {
                        ele.className = "cloze2 activated";
                    }
                })
            }
        } else if (mode === "mask_one_guess_one") {
            let cur_cloze_id = "";
            if (Persistence.isAvailable()) {
                cur_cloze_id = Persistence.getItem("cur_cloze_id");
            }
            let ele = document.querySelector(`.cloze[cloze_id=${cur_cloze_id}]`);
            if (ele == null || ele == undefined) {
                return;
            }
            if (ele.classList.contains("activated")) {
                [].forEach.call(q_div.querySelectorAll(`.cloze[cloze_id=${cur_cloze_id}]`), (ele) => {
                    ele.classList.toggle("activated");
                })
            } else {
                let idx = cloze_ids.indexOf(cur_cloze_id);
                let next_cloze_id = cloze_ids[(idx + 1) % cloze_ids.length];
                if (Persistence.isAvailable()) {
                    Persistence.setItem("cur_cloze_id", next_cloze_id);
                }
                [].forEach.call(q_div.querySelectorAll(`span[cloze_id]`), (ele) => {
                    let cloze_id = ele.getAttribute("cloze_id");
                    if (cloze_id === next_cloze_id) {
                        ele.className = "cloze activated";
                    } else {
                        ele.className = "cloze";
                    }
                })
            }
        }
    }
    function go_prev() {
        if (mode === "free_guess" || mode === "mask_all_guess_all") {
            for (const ele of Array.from(document.querySelectorAll(".cloze")).reverse()) {
                if (!ele.classList.contains("activated")) {
                    ele.classList.toggle("activated");
                    break;
                }
            }
        } else if (mode === "mask_all_guess_one") {
            let ele = document.querySelector(".cloze");
            if (ele.classList.contains("activated")) {
                [].forEach.call(q_div.querySelectorAll(`.cloze`), (ele) => {
                    ele.classList.toggle("activated");
                })
            } else {
                let cur_cloze_id = ele.getAttribute("cloze_id");
                let idx = cloze_ids.indexOf(cur_cloze_id);
                let prev_cloze_id = cloze_ids[(idx - 1 + cloze_ids.length) % cloze_ids.length];
                if (Persistence.isAvailable()) {
                    Persistence.setItem("cur_cloze_id", prev_cloze_id);
                }
                [].forEach.call(q_div.querySelectorAll(`span[cloze_id]`), (ele) => {
                    let cloze_id = ele.getAttribute("cloze_id");
                    if (cloze_id === prev_cloze_id) {
                        ele.className = "cloze activated";
                    } else {
                        ele.className = "cloze2 activated";
                    }
                })
            }
        } else if (mode === "mask_one_guess_one") {
            let cur_cloze_id = "";
            if (Persistence.isAvailable()) {
                cur_cloze_id = Persistence.getItem("cur_cloze_id");
            }
            let ele = document.querySelector(`.cloze[cloze_id=${cur_cloze_id}]`);
            if (ele.classList.contains("activated")) {
                [].forEach.call(q_div.querySelectorAll(`.cloze[cloze_id=${cur_cloze_id}]`), (ele) => {
                    ele.classList.toggle("activated");
                })
            } else {
                let idx = cloze_ids.indexOf(cur_cloze_id);
                let prev_cloze_id = cloze_ids[(idx - 1 + cloze_ids.length) % cloze_ids.length];
                if (Persistence.isAvailable()) {
                    Persistence.setItem("cur_cloze_id", prev_cloze_id);
                }
                [].forEach.call(q_div.querySelectorAll(`span[cloze_id]`), (ele) => {
                    let cloze_id = ele.getAttribute("cloze_id");
                    if (cloze_id === prev_cloze_id) {
                        ele.className = "cloze activated";
                    } else {
                        ele.className = "cloze";
                    }
                })
            }
        }
    }
    // 绑定快捷键
    function keyboardHandler(e) {
        console.log("e.key:", e.key);
        if (e.key == "j") {
            go_prev();
        } else if (e.key == "k") {
            go_next();
        }
        document.addEventListener("keyup", keyboardHandler, { once: true });
    }
    document.addEventListener("keyup", keyboardHandler, { once: true });

    switch (mode) {
        case "free_guess": {
            free_guess_init();
            break;
        }
        case "mask_one_guess_one": {
            mask_one_guess_one_init();
            break;
        }
        case "mask_all_guess_one": {
            mask_all_guess_one_init();
            break;
        }
        case "mask_all_guess_all": {
            mask_all_guess_all_init();
            break;
        }
        case "mask_one_guess_one_multi": {
            mask_one_guess_one_multi_init();
            break;
        }
        case "mask_all_guess_one_multi": {
            mask_all_guess_one_multi_init();
            break;
        }
    }
    if (Persistence.isAvailable()) {
        Persistence.setItem("cloze_ids", cloze_ids);
    }
</script>