<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON><PERSON>mplates -->

<script>
    // v1.1.8 - https://github.com/SimonLammer/anki-persistence/blob/584396fea9dea0921011671a47a0fdda19265e62/script.js
    if (void 0 === window.Persistence) { var e = "github.com/SimonLammer/anki-persistence/", t = "_default"; if (window.Persistence_sessionStorage = function () { var i = !1; try { "object" == typeof window.sessionStorage && (i = !0, this.clear = function () { for (var t = 0; t < sessionStorage.length; t++) { var i = sessionStorage.key(t); 0 == i.indexOf(e) && (sessionStorage.removeItem(i), t--) } }, this.setItem = function (i, n) { void 0 == n && (n = i, i = t), sessionStorage.setItem(e + i, JSON.stringify(n)) }, this.getItem = function (i) { return void 0 == i && (i = t), JSON.parse(sessionStorage.getItem(e + i)) }, this.removeItem = function (i) { void 0 == i && (i = t), sessionStorage.removeItem(e + i) }, this.getAllKeys = function () { for (var t = [], i = Object.keys(sessionStorage), n = 0; n < i.length; n++) { var s = i[n]; 0 == s.indexOf(e) && t.push(s.substring(e.length, s.length)) } return t.sort() }) } catch (n) { } this.isAvailable = function () { return i } }, window.Persistence_windowKey = function (i) { var n = window[i], s = !1; "object" == typeof n && (s = !0, this.clear = function () { n[e] = {} }, this.setItem = function (i, s) { void 0 == s && (s = i, i = t), n[e][i] = s }, this.getItem = function (i) { return void 0 == i && (i = t), void 0 == n[e][i] ? null : n[e][i] }, this.removeItem = function (i) { void 0 == i && (i = t), delete n[e][i] }, this.getAllKeys = function () { return Object.keys(n[e]) }, void 0 == n[e] && this.clear()), this.isAvailable = function () { return s } }, window.Persistence = new Persistence_sessionStorage, Persistence.isAvailable() || (window.Persistence = new Persistence_windowKey("py")), !Persistence.isAvailable()) { var i = window.location.toString().indexOf("title"), n = window.location.toString().indexOf("main", i); i > 0 && n > 0 && n - i < 10 && (window.Persistence = new Persistence_windowKey("qt")) } }
</script>

<div style="display: flex;flex-direction: column; height: 100%;">
    <div>
        <div id='q_div'>{{Front}}</div>
    </div>
    <hr>
    <div>
        {{#Back}}
        <div id='a_div'>
            {{Back}}
            {{/Back}}
        </div>
    </div>

    <script type="text/javascript">
        var cloze_regex = /\[\[c(\d+)::(.*?)\]\]/gm;
        var mask = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
        var idx = 0;
        var cur_cloze_id = 0;
        var cloze_ids = new Array();
        if (Persistence.isAvailable()) {
            idx = Persistence.getItem("idx");
            cloze_ids = Persistence.getItem("cloze_ids");
            cur_cloze_id = Persistence.getItem("cur_cloze_id");
        }
        var mode = "{{Mode}}";
        var final_html_content = "";
        function init_front() {
            switch (mode) {
                case "mask_all_guess_one_multi": {
                    let matches;
                    let last_pos = 0;
                    final_html_content = "";
                    var q_div = document.getElementById("q_div");
                    const text = q_div.innerHTML;
                    const target_index = "{{Index}}";
                    while ((matches = cloze_regex.exec(text)) !== null) {
                        let index = matches[1];
                        let content = matches[2];
                        let start = matches.index;
                        let end = start + matches[0].length;
                        if (start >= last_pos) {
                            let pos = Math.max(0, last_pos - 1);
                            final_html_content += `${text.slice(pos, start)}`
                        }
                        let cloze_id = `c${index}`;
                        let span = document.createElement("span");
                        span.setAttribute("cloze_id", cloze_id);
                        if (cloze_id === target_index) {
                            span.className = "cloze";
                        } else {
                            span.className = "cloze2 activated";
                        }
                        span.innerHTML = content;
                        final_html_content += span.outerHTML;
                        last_pos = end + 1;
                    }
                    if (text.length >= last_pos) {
                        let pos = Math.max(0, last_pos - 1);
                        final_html_content += `${text.slice(pos, text.length)}`
                    }
                    q_div.innerHTML = final_html_content;
                    document.querySelectorAll(".cloze").forEach(ele => {
                        let cloze_id = ele.getAttribute("cloze_id");
                        ele.onclick = () => {
                            [].forEach.call(q_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                                ele.classList.toggle("activated");
                            })
                        }
                    });
                    break;
                }
                case "mask_one_guess_one_multi": {
                    let matches;
                    let last_pos = 0;
                    final_html_content = "";
                    var q_div = document.getElementById("q_div");
                    const text = q_div.innerHTML;
                    const target_index = "{{Index}}";
                    while ((matches = cloze_regex.exec(text)) !== null) {
                        let index = matches[1];
                        let content = matches[2];
                        let start = matches.index;
                        let end = start + matches[0].length;
                        if (start >= last_pos) {
                            let pos = Math.max(0, last_pos - 1);
                            final_html_content += `${text.slice(pos, start)}`
                        }
                        let cloze_id = `c${index}`;
                        let span = document.createElement("span");
                        span.setAttribute("cloze_id", cloze_id);
                        if (cloze_id === target_index) {
                            span.className = "cloze";
                            span.innerHTML = content;
                            span.onclick = () => {
                                [].forEach.call(q_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                                    ele.classList.toggle("activated");
                                })
                            }
                        } span.innerHTML = content;
                        final_html_content += span.outerHTML;
                        last_pos = end + 1;
                    }
                    if (text.length >= last_pos) {
                        let pos = Math.max(0, last_pos - 1);
                        final_html_content += `${text.slice(pos, text.length)}`
                    }
                    q_div.innerHTML = final_html_content;
                    document.querySelectorAll(".cloze").forEach(ele => {
                        let cloze_id = ele.getAttribute("cloze_id");
                        ele.onclick = () => {
                            [].forEach.call(q_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                                ele.classList.toggle("activated");
                            })
                        }
                    });
                    break;
                }
                case "mask_all_guess_one": {
                    let matches;
                    let last_pos = 0;
                    final_html_content = "";
                    var q_div = document.getElementById("q_div");
                    const text = q_div.innerHTML;
                    let target_index = cur_cloze_id;
                    while ((matches = cloze_regex.exec(text)) !== null) {
                        let index = matches[1];
                        let content = matches[2];
                        let start = matches.index;
                        let end = start + matches[0].length;
                        if (start >= last_pos) {
                            let pos = Math.max(0, last_pos - 1);
                            final_html_content += `${text.slice(pos, start)}`
                        }
                        let cloze_id = `c${index}`;
                        let span = document.createElement("span");
                        span.setAttribute("cloze_id", cloze_id);
                        if (cloze_id === target_index) {
                            span.className = "cloze";
                        } else {
                            span.className = "cloze2 activated";
                        }
                        span.innerHTML = content;
                        final_html_content += span.outerHTML;
                        last_pos = end + 1;
                    }
                    if (text.length >= last_pos) {
                        let pos = Math.max(0, last_pos - 1);
                        final_html_content += `${text.slice(pos, text.length)}`
                    }
                    q_div.innerHTML = final_html_content;
                    document.querySelectorAll(".cloze").forEach(ele => {
                        let cloze_id = ele.getAttribute("cloze_id");
                        ele.onclick = () => {
                            [].forEach.call(q_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                                ele.classList.toggle("activated");
                            })
                        }
                    });
                    break;
                }
                default: {
                    let matches;
                    let last_pos = 0;
                    final_html_content = "";
                    var q_div = document.getElementById("q_div");
                    const text = q_div.innerHTML;
                    while ((matches = cloze_regex.exec(text)) !== null) {
                        let index = matches[1];
                        let content = matches[2];
                        let start = matches.index;
                        let end = start + matches[0].length;
                        if (start >= last_pos) {
                            let pos = Math.max(0, last_pos - 1);
                            final_html_content += `${text.slice(pos, start)}`
                        }
                        let span = document.createElement("span");
                        span.className = "cloze";
                        span.setAttribute("cloze_id", `c${index}`);
                        span.innerHTML = content;
                        final_html_content += span.outerHTML;
                        last_pos = end + 1;
                    }
                    if (text.length >= last_pos) {
                        let pos = Math.max(0, last_pos - 1);
                        final_html_content += `${text.slice(pos, text.length)}`
                    }
                    q_div.innerHTML = final_html_content;
                    document.querySelectorAll(".cloze").forEach(ele => {
                        let cloze_id = ele.getAttribute("cloze_id");
                        ele.onclick = () => {
                            [].forEach.call(q_div.querySelectorAll(`span[cloze_id]`), (ele) => {
                                ele.classList.toggle("activated");
                            })
                        }
                    });
                    break;
                }
            }
        }
        // 背面挖空
        function init_back() {
            let matches;
            let last_pos = 0;
            final_html_content = "";
            var a_div = document.getElementById("a_div");
            if (a_div == null || a_div == undefined) {
                return;
            }
            const text = a_div.innerHTML;
            while ((matches = cloze_regex.exec(text)) !== null) {
                let index = matches[1];
                let content = matches[2];
                let start = matches.index;
                let end = start + matches[0].length;
                if (start >= last_pos) {
                    let pos = Math.max(0, last_pos - 1);
                    final_html_content += `${text.slice(pos, start)}`
                }
                let span = document.createElement("span");
                span.className = "cloze";
                span.setAttribute("cloze_id", `c${index}`);
                span.innerHTML = content;
                final_html_content += span.outerHTML;
                last_pos = end + 1;
            }
            if (text.length >= last_pos) {
                let pos = Math.max(0, last_pos - 1);
                final_html_content += `${text.slice(pos, text.length)}`
            }
            a_div.innerHTML = final_html_content;
            a_div.querySelectorAll("span[cloze_id]").forEach(ele => {
                let cloze_id = ele.getAttribute("cloze_id");
                ele.onclick = () => {
                    [].forEach.call(a_div.querySelectorAll(`span[cloze_id=${cloze_id}]`), (ele) => {
                        ele.classList.toggle("activated");
                    })
                }
            });
        };
        init_front();
        init_back();
    </script>