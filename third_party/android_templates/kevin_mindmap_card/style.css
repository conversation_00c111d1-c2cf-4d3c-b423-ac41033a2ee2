.card {
    font-family: arial;
    font-size: 20px;
    text-align: auto;
    color: black;
    background-color: white;
}

html,
body,
#content,
#qa,
.container {
    height: 100%;
    margin: 0;
    padding: 0;
}

.container {
    display: flex;
    flex-direction: column;
}

.source {
    background-color: #eaeaea;
    text-align: left;
    font-size: small;
    font-family: Microsoft YaHei;
    color: #929292;
    display: flex;
    padding: 0.4em;
}

.source2 {
    background-color: #eaeaea;
    text-align: left;
    font-size: small;
    font-family: Microsoft YaHei;
    color: #929292;
    padding: 0.4em;
    display: flex;
}

.mode {
    cursor: pointer;
}

.upper {
    flex: 1;
    background-color: #eaeaea;
}

.lower {
    background-color: #eaeaea;
    color: white;
    display: flex;
    justify-content: space-around;
    padding: 0 10px;
}

.lower a {
    padding: 4px;
    font-size: 16px;
    min-width: 18%;
    color: #fff;
    background-color: #007bff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    margin: 0 5px;
}

.lower a:hover {
    background-color: #0056b3;
}

span.keywords {
    cursor: pointer;
    background: rgba(255, 0, 0, 0.2);
}

span.cloze {
    cursor: pointer;
    position: relative;
    background: rgba(255, 0, 0, 0.1);
}

span.cloze.activated::before {
    cursor: pointer;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: #ec625d;
    z-index: 1;
}


#q_div,
#a_div,
#notes {
    font-size: 1.2em;
    color: #333;
    background-color: #fbfbfb;
    border: 2px solid #ddd;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 0.4em;
}

* {
    margin: 0;
}

#outline {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    margin: 0;
    padding: 0;
}

#mindmap {
    display: flex;
    flex-direction: column;
    height: 100%;
    margin: 0;
    padding: 0;
}

.q-header {
    height: 24px;
    line-height: 24px;
    color: black;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.4em 0 0 0.4em;
}

details summary {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    list-style: none;
}

details summary::before {
    content: "▸";
    margin-right: 6px;
    transition: transform 0.2s;
    align-items: center;
}

details[open]>summary::before {
    transform: rotate(90deg);
}


.node {
    margin: 0 4px 4px 0;
    border: 1px solid #e5e5e5;
    border-radius: 0.2em;
    padding: 0.4em;
}

#nav {
    display: flex;
    justify-content: center;
}

.nav_btn {
    padding: 4px;
    font-size: 16px;
    min-width: 18%;
    color: #fff;
    background-color: #007bff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    margin: 0.4em;
}

.nav_btn:hover {
    background-color: #0056b3;
}

/* mubu css */
.text-color-dark {
    color: #51565d;
}

.text-color-red {
    color: #dc2d1e;
}

.text-color-yellow {
    color: #ffaf38;
}

.text-color-green {
    color: #75c940;
}

.text-color-blue {
    color: #3da8f5;
}

.text-color-purple {
    color: #797ec9;
}

.bold {
    font-weight: bold;
}

.underline {
    text-decoration: underline;
}

.strikethrough {
    text-decoration: line-through;
}

.italic {
    font-style: italic;
}

.highlight-yellow {
    background-color: #f9f989;
}

.highlight-red {
    background-color: #f5bdbc;
}

.highlight-blue {
    background-color: #c5edfd;
}

.highlight-grey {
    background-color: #d1d3d8;
}

.highlight-olive {
    background-color: #e4fec1;
}

.highlight-pink {
    background-color: #f1ccfc;
}

.highlight-cyan {
    background-color: #cffdde;
}

.kevin-cloze {
    background-color: #f5f5f5;
    border: 1px solid #dcdcdc;
    opacity: 0;
}

.no-kevin-cloze {
    background-color: #fff;
}