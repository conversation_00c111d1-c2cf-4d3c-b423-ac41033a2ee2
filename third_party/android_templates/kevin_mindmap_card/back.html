<!-- Author: <PERSON><PERSON><PERSON> -->
<!-- Github: https://github.com/kevin2li/<PERSON>-<PERSON><PERSON>mplates -->
<script>
    // v1.1.8 - https://github.com/SimonLammer/anki-persistence/blob/584396fea9dea0921011671a47a0fdda19265e62/script.js
    if (void 0 === window.Persistence) { var e = "github.com/SimonLammer/anki-persistence/", t = "_default"; if (window.Persistence_sessionStorage = function () { var i = !1; try { "object" == typeof window.sessionStorage && (i = !0, this.clear = function () { for (var t = 0; t < sessionStorage.length; t++) { var i = sessionStorage.key(t); 0 == i.indexOf(e) && (sessionStorage.removeItem(i), t--) } }, this.setItem = function (i, n) { void 0 == n && (n = i, i = t), sessionStorage.setItem(e + i, JSON.stringify(n)) }, this.getItem = function (i) { return void 0 == i && (i = t), JSON.parse(sessionStorage.getItem(e + i)) }, this.removeItem = function (i) { void 0 == i && (i = t), sessionStorage.removeItem(e + i) }, this.getAllKeys = function () { for (var t = [], i = Object.keys(sessionStorage), n = 0; n < i.length; n++) { var s = i[n]; 0 == s.indexOf(e) && t.push(s.substring(e.length, s.length)) } return t.sort() }) } catch (n) { } this.isAvailable = function () { return i } }, window.Persistence_windowKey = function (i) { var n = window[i], s = !1; "object" == typeof n && (s = !0, this.clear = function () { n[e] = {} }, this.setItem = function (i, s) { void 0 == s && (s = i, i = t), n[e][i] = s }, this.getItem = function (i) { return void 0 == i && (i = t), void 0 == n[e][i] ? null : n[e][i] }, this.removeItem = function (i) { void 0 == i && (i = t), delete n[e][i] }, this.getAllKeys = function () { return Object.keys(n[e]) }, void 0 == n[e] && this.clear()), this.isAvailable = function () { return s } }, window.Persistence = new Persistence_sessionStorage, Persistence.isAvailable() || (window.Persistence = new Persistence_windowKey("py")), !Persistence.isAvailable()) { var i = window.location.toString().indexOf("title"), n = window.location.toString().indexOf("main", i); i > 0 && n > 0 && n - i < 10 && (window.Persistence = new Persistence_windowKey("qt")) } }
</script>

<script src="__d3.js" type="text/javascript"></script>
<script src="__markmap_lib.js" type="text/javascript"></script>
<script src="__markmap_view.js" type="text/javascript"></script>
<script src="__{{MapID}}.js" type="text/javascript"></script>

<div id="mindmap">
    {{#Source}}
    <div class="source">
        <div style="flex: 1">来源：{{Source}}</div>
        <div><a onclick="switch_mode()" class="mode">切换模式</a></div>
    </div>
    <hr>
    {{/Source}}
    <div class="upper">
        <svg id="markmap" style="width: 100%;height: 100%"></svg>
    </div>
    <div class="lower">
        <a onclick="expand_toggle()">
            <!-- 展开/折叠全部节点 -->
            <svg t="1723425251651" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                p-id="22914" width="2em" height="2em">
                <path
                    d="M785.066667 716.8c-64.853333 0-116.053333 44.373333-133.12 102.4H409.6c-58.026667 0-102.4-44.373333-102.4-102.4v-170.666667h344.746667c13.653333 58.026667 68.266667 102.4 133.12 102.4 75.093333 0 136.533333-61.44 136.533333-136.533333s-61.44-136.533333-136.533333-136.533333c-64.853333 0-116.053333 44.373333-133.12 102.4H307.2v-102.4-3.413334c78.506667-17.066667 136.533333-85.333333 136.533333-167.253333 0-95.573333-75.093333-170.666667-170.666666-170.666667S102.4 109.226667 102.4 204.8c0 81.92 58.026667 150.186667 136.533333 167.253333V716.8c0 95.573333 75.093333 170.666667 170.666667 170.666667h242.346667c13.653333 58.026667 68.266667 102.4 133.12 102.4 75.093333 0 136.533333-61.44 136.533333-136.533334s-61.44-136.533333-136.533333-136.533333z m0-273.066667c37.546667 0 68.266667 30.72 68.266666 68.266667s-30.72 68.266667-68.266666 68.266667-68.266667-30.72-68.266667-68.266667 30.72-68.266667 68.266667-68.266667zM170.666667 204.8c0-58.026667 44.373333-102.4 102.4-102.4s102.4 44.373333 102.4 102.4-44.373333 102.4-102.4 102.4-102.4-44.373333-102.4-102.4z m614.4 716.8c-37.546667 0-68.266667-30.72-68.266667-68.266667s30.72-68.266667 68.266667-68.266666 68.266667 30.72 68.266666 68.266666-30.72 68.266667-68.266666 68.266667z"
                    fill="#f0f0f0" p-id="22915"></path>
            </svg>
        </a>
        <a onclick="goto_parent()">
            <!-- 回到父节点 -->
            <svg t="1723534925324" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                p-id="9372" width="2em" height="2em">
                <path
                    d="M958.464 884.736H558.08a142.336 142.336 0 0 1-141.312-142.336V204.8l237.568 232.448a48.128 48.128 0 0 0 35.84 14.336 47.104 47.104 0 0 0 35.84-14.336 51.2 51.2 0 0 0 0-72.704l-307.2-307.2a51.2 51.2 0 0 0-71.68 0l-307.2 312.32a51.2 51.2 0 0 0 0 72.704 51.2 51.2 0 0 0 71.68 0L314.368 225.28v517.12a244.736 244.736 0 0 0 243.712 244.736h400.384a51.2 51.2 0 0 0 51.2-51.2 51.2 51.2 0 0 0-51.2-51.2z"
                    fill="#f0f0f0" p-id="9373"></path>
            </svg>
        </a>
        <a onclick="goto_current()">
            <!-- 回到初始节点 -->
            <svg t="1723425106111" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                p-id="13846" width="2em" height="2em">
                <path
                    d="M829.952 475.733333a320.128 320.128 0 0 0-281.685333-281.685333V106.666667a36.266667 36.266667 0 0 0-72.533334 0v87.381333a320.128 320.128 0 0 0-281.685333 281.685333H128a36.266667 36.266667 0 0 0 0 72.533334h66.048a320.128 320.128 0 0 0 281.685333 281.685333V896a36.266667 36.266667 0 1 0 72.533334 0v-66.048a320.128 320.128 0 0 0 281.685333-281.685333H896a36.266667 36.266667 0 1 0 0-72.533334h-66.048z m-65.962667 36.266667a251.989333 251.989333 0 1 1-503.978666 0 251.989333 251.989333 0 0 1 503.978666 0zM512 618.666667a106.666667 106.666667 0 1 0 0-213.333334 106.666667 106.666667 0 0 0 0 213.333334z"
                    fill="#f0f0f0" p-id="13847"></path>
            </svg>
        </a>
        <a onclick="fold_toggle()">
            <!-- 展开/折叠 -->
            <svg t="1723424863782" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                p-id="6170" width="2em" height="2em">
                <path
                    d="M222.991059 707.463529a43.128471 43.128471 0 0 1 57.404235 0l231.785412 219.256471 231.845647-218.774588a43.128471 43.128471 0 0 1 57.344 0 35.719529 35.719529 0 0 1 0 53.007059L540.611765 1007.134118a43.128471 43.128471 0 0 1-57.344 0l-260.818824-246.061177A38.189176 38.189176 0 0 1 210.823529 734.268235c0-9.878588 4.035765-19.275294 12.16753-26.804706z m672.346353-192.692705a37.948235 37.948235 0 0 1 38.309647 37.225411 37.948235 37.948235 0 0 1-38.309647 37.285647H128.662588A37.948235 37.948235 0 0 1 90.352941 551.996235a37.948235 37.948235 0 0 1 38.309647-37.225411h766.674824zM540.611765 65.114353l260.818823 246.061176a35.719529 35.719529 0 0 1 0 53.007059 43.128471 43.128471 0 0 1-57.404235 0L512.180706 145.468235l-231.785412 219.256471a43.128471 43.128471 0 0 1-57.404235 0A36.020706 36.020706 0 0 1 210.823529 337.92c0-9.396706 4.035765-19.275294 11.685647-26.744471l260.758589-246.061176a43.128471 43.128471 0 0 1 57.344 0z"
                    p-id="6171" fill="#f0f0f0"></path>
            </svg>
        </a>
        <a id="preview_btn" onclick="preview_toggle()">
            <!-- 显示/隐藏挖空 -->
            <svg t="1723513150667" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                p-id="2627" id="mx_n_1723513150668" width="2em" height="2em">
                <path
                    d="M512 844.606c-58.519 0-118.836-16.271-179.277-48.363-46.915-24.909-94.073-59.416-140.164-102.56C114.987 621.071 67.13 549.159 65.13 546.132a37.002 37.002 0 0 1 0-40.795c2-3.026 49.857-74.938 127.429-147.551 46.091-43.144 93.249-77.65 140.164-102.56 60.441-32.092 120.759-48.363 179.277-48.363s118.836 16.271 179.277 48.363c46.915 24.909 94.073 59.416 140.164 102.56C909.012 430.398 956.87 502.31 958.87 505.337a37.002 37.002 0 0 1 0 40.795c-2 3.026-49.857 74.938-127.429 147.551-46.091 43.144-93.249 77.65-140.164 102.56-60.441 32.091-120.758 48.363-179.277 48.363z m-370.487-318.87c46.516 62.165 197.934 244.87 370.487 244.87 172.541 0 323.95-182.679 370.486-244.871C835.95 463.543 684.541 280.864 512 280.864S188.049 463.543 141.513 525.736z"
                    fill="#f0f0f0" p-id="2628"></path>
                <path
                    d="M512 687.889c-89.412 0-162.154-72.742-162.154-162.154S422.588 363.581 512 363.581s162.154 72.742 162.154 162.154S601.412 687.889 512 687.889z m0-250.308c-48.608 0-88.154 39.546-88.154 88.154s39.546 88.154 88.154 88.154 88.154-39.546 88.154-88.154-39.546-88.154-88.154-88.154z"
                    fill="#f0f0f0" p-id="2629"></path>
            </svg>
        </a>
    </div>
    {{#Notes}}
    <div class="q-header">
        <span>
            <img src="__bookmark.svg" />
            <b> 笔记 </b>
        </span>
    </div>
    <div id="notes">{{Notes}}</div>
    {{/Notes}}
</div>

<div id="outline">
    <div style="flex: 1;">
        {{#Source}}
        <div class="source2">
            <div style="flex: 1">来源：{{Source}}</div>
            <div><a onclick="switch_mode()" class="mode">切换模式</a></div>
        </div>
        {{/Source}}
        <div id="q_div">
            {{Front}}
        </div>
        <hr>
        <div id="a_div">
            {{Back}}
        </div>
        {{#Notes}}
        <div class="q-header">
            <span>
                <img src="__bookmark.svg" />
                <b> 笔记 </b>
            </span>
        </div>
        <div id="notes">{{Notes}}</div>
        {{/Notes}}
        <div id="nav">
            <div>
                <a id="prev" class="nav_btn" onclick="preview_toggle()">显示/隐藏遮罩</a>
            </div>
        </div>
    </div>
</div>

<script>
    var path = JSON.parse(`{{Path}}`);
    var fold_flag = false;
    var show_flag = false;
    var expand_flag = true;
    var current_path = path;
    var mode = "outline";
    if (Persistence.isAvailable()) {
        mode = Persistence.getItem("mode");
    }
    function render(path, level) {
        let data = window.sessionStorage.getItem('mindmap_data');
        let mind_data = JSON.parse(data);
        var node = mind_data;
        for (const p of path) {
            node = node.children[p];
        }
        console.log({ mind_data });
        const svgEl = document.querySelector('#markmap');
        svgEl.innerHTML = '';
        window.markmap.Markmap.create(svgEl, { initialExpandLevel: level, maxWidth: 300, duration: 0 }, node);
    }
    function goto_parent() {
        if (current_path.length > 0) {
            current_path.pop();
            fold_flag = false;
            render(current_path, 1);
        } else {
            alert("已经是根节点了");
        }
    }
    function goto_current() {
        let path = JSON.parse(`{{Path}}`);
        current_path = path;
        fold_flag = false;
        expand_flag = true;
        render(path, 2)
    }
    function expand_toggle() {
        if (expand_flag) {
            render([], undefined)
            fold_flag = true;
        } else {
            current_path = [];
            render([], 1);
            fold_flag = false;
        }
        expand_flag = !expand_flag;
    }
    function fold_toggle() {
        if (fold_flag) {
            render(current_path, 1)
        } else {
            render(current_path, 2)
        }
        fold_flag = !fold_flag;
    }
    function preview_toggle() {
        let btn = document.querySelector('#preview_btn');
        if (show_flag) {
            // btn.innerHTML = `<img src="__eye.svg" />`;
            document.querySelectorAll("span.cloze").forEach(ele => {
                if (ele.classList.contains("activated")) {
                    ele.classList.toggle("activated");
                }
            });
        } else {
            // btn.innerHTML = `<img src="__noeye.svg" />`;
            document.querySelectorAll("span.cloze").forEach(ele => {
                if (!ele.classList.contains("activated")) {
                    ele.classList.toggle("activated");
                }
            });
        }
        show_flag = !show_flag;
    }
    function init() {
        if (mode === "mindmap") {
            document.getElementById("outline").style.display = "none";
            document.getElementById("mindmap").style.display = "flex";
            goto_current();
            preview_toggle();
        } else {
            document.getElementById("mindmap").style.display = "none";
            document.getElementById("outline").style.display = "flex";
        }
    }
    function switch_mode() {
        mode = mode === "outline" ? "mindmap" : "outline";
        if (Persistence.isAvailable()) {
            Persistence.setItem("mode", mode);
        }
        init();
    }
    init();
</script>