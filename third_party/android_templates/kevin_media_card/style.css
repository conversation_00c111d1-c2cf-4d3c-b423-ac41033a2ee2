/* 前端样式 */
.media-container {
    width: 100%;
    /* max-width: 800px; */
    margin: 20px auto;
    background: var(--bg-color);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px var(--shadow-color);
    transition: all 0.3s ease;
}

/* 视频/音频播放器自适应 */
.media-player {
    width: 100%;
    height: 450px;
    /* 视频默认高度 */
    object-fit: contain;
    background: #000;
}

/* 音频专用样式 */
audio.media-player {
    height: 80px;
    /* 音频控制栏高度 */
}

.controls-container {
    padding: 12px;
    background: var(--control-bg);
}

.progress-bar input[type="range"] {
    width: 100%;
    height: 5px;
    margin-top: 1.5em;
    background: linear-gradient(to right,
            var(--progress-color) 0%,
            var(--progress-color) var(--progress, 0%),
            var(--border-color) var(--progress, 0%),
            var(--border-color) 100%);
    border-radius: 3px;
    -webkit-appearance: none;
}

/* Webkit浏览器样式 */
.progress-bar input[type="range"]::-webkit-slider-runnable-track {
    background: transparent;
}

.progress-bar input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 15px;
    height: 15px;
    background: #fff;
    border-radius: 50%;
    border: 2px solid var(--progress-color);
    margin-top: -5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Firefox浏览器样式 */
.progress-bar input[type="range"]::-moz-range-track {
    background: transparent;
}

.progress-bar input[type="range"]::-moz-range-thumb {
    width: 15px;
    height: 15px;
    background: #fff;
    border-radius: 50%;
    border: 2px solid var(--progress-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.progress-bar input[type="range"]::-moz-range-progress {
    background: var(--progress-color);
    height: 5px;
    border-radius: 3px;
}

.control-buttons {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 10px;
}

.btn-icon {
    background: none;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--text-primary);
    font-size: 1.2em;
    cursor: pointer;
    transition: all 0.3s;
    padding: 8px;
    border-radius: 50%;
}

.btn-icon:hover {
    transform: scale(1.1);
    color: var(--progress-color);
    background: rgba(255, 255, 255, 0.2);
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
    .btn-icon {
        background: rgba(0, 0, 0, 0.2);
    }

    .btn-icon:hover {
        background: rgba(0, 0, 0, 0.3);
    }
}

.time-display {
    color: var(--text-primary);
    font-family: monospace;
    font-size: 0.9em;
    margin: 0 10px;
}

/* 后端样式 */
.subtitle-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 25px;
    background: var(--bg-color);
    border-radius: 10px;
    box-shadow: 0 2px 8px var(--shadow-color);
}

.subtitle-content {
    font-size: 1.2em;
    line-height: 1.6;
    color: var(--text-primary);
}

/* 内容区域样式 */
.content-container {
    width: 100%;
    padding: 20px;
    background: var(--bg-color);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.subtitle-section,
.meaning-section,
.note-section {
    margin: 25px 0;
    padding: 1em;
    border-radius: 8px;
    background: var(--section-bg);
    border-color: var(--progress-color);
    box-shadow: 0 2px 6px var(--shadow-color);
}

.subtitle-section {
    border-left: 4px solid var(--progress-color);
}

.meaning-section {
    border-left: 4px solid #6c5ce7;
}

.note-section {
    border-left: 4px solid #ffb74d;
    background: #fff3cd;
}

h3 {
    color: var(--text-primary);
    margin: 0 0 15px 0;
    font-size: 1.1em;
    font-weight: 600;
}

.meaning-content {
    font-size: 1.1em;
    line-height: 1.5;
    color: var(--text-primary);
}

.note-content {
    color: var(--text-secondary);
    font-style: italic;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .media-container {
        border-radius: 0;
        margin: 10px 0;
    }

    .media-player {
        height: 250px;
        /* 移动端视频高度 */
    }

    .content-container {
        padding: 10px;
    }
}

/* 循环按钮激活状态 */
#loopBtn[data-looping="true"] {
    color: var(--progress-color) !important;
    background: rgba(79, 163, 209, 0.1);
    border-radius: 50%;
    transform: scale(1.1);
}

:root {
    /* 明亮模式变量 */
    --bg-color: #ffffff;
    --control-bg: #f0f0f0;
    --text-primary: #333333;
    --text-secondary: #666666;
    --progress-color: #4fa3d1;
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --section-bg: #f8f9fa;
}

@media (prefers-color-scheme: dark) {
    :root {
        /* 暗黑模式变量 */
        --bg-color: #1a1a1a;
        --control-bg: #2a2a2a;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --progress-color: #6ec1e4;
        --border-color: #404040;
        --shadow-color: rgba(0, 0, 0, 0.3);
        --section-bg: #2d2d2d;
    }
}

/* 新增速度控制样式 */
.speed-control {
    position: relative;
    display: inline-block;
    margin-left: auto;
}

.speed-options {
    position: absolute;
    bottom: 100%;
    right: 0;
    background: var(--section-bg);
    border-radius: 4px;
    box-shadow: 0 2px 8px var(--shadow-color);
    display: none;
    min-width: 120px; 
    z-index: 100;
    padding: 4px 0;
    overflow: hidden;   /* 防止内容溢出 */
}

.speed-options button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 8px 16px;  /* 增加水平内边距 */
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 0.9em;
    cursor: pointer;
    transition: background 0.2s;
    box-sizing: border-box;
    white-space: nowrap;
    text-align: center;  /* 双重保障居中 */
}

.speed-options button:hover {
    background: rgba(79, 163, 209, 0.1);
}

.speed-options button[data-speed="1.0"] {
    font-weight: bold;
    color: var(--text-primary);
}

.speed-options button.active {
    color: var(--progress-color) !important;
    font-weight: bold;
    background: rgba(79, 163, 209, 0.1);
}

.speed-control:hover .speed-options {
    display: block;
}