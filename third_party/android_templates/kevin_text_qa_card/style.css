
:root{
    --yellow: #ffeba2;
    --blue: #ff5656;
}

.card {
    font-family: arial;
    font-size: 20px;
    text-align: auto;
    color: black;
    background-color: white;
}

html, body, #content, #qa, .container {
    height: 100%;
    margin: 0;
    padding: 0;
}

.cloze-hide {
    font-weight: bold;
    color: var(--blue);
    border-bottom: 1px solid var(--blue);
    cursor: pointer;
    /* filter: blur(0.25em); */
}

.cloze-show {
    font-weight: bold;
    color:var(--blue);
    border-bottom: 1px solid var(--blue);
    cursor: pointer;
}

.cloze-hide2 {
    font-weight: bold;
    color: var(--yellow);
    border-bottom: 1px solid var(--yellow);
    cursor: pointer;
    /* filter: blur(0.25em); */
}

.cloze-show2 {
    font-weight: bold;
    color: var(--yellow);
    border-bottom: 1px solid var(--yellow);
    cursor: pointer;
}
table {
    border-collapse: collapse;
}

table,th,td {
    border: 1px solid #ccc;
}

mark.red{
    background-color: red;
}

mark.yellow{
    background-color: yellow;
}

mark.green{
    background-color: green;
}

mark.blue{
    background-color: blue;
}

mark.cyan{
    background-color: cyan;
}

mark.magenta{
    background-color: magenta;
}

.font_red{
    color: red;
}


#q_div, #a_div {
    font-size: 1.2em;
    color: #333;
    background-color: #fbfbfb;
    border: 2px solid #ddd;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 0.4em;
}

.hint {
    display: inline-block;
    font-size: 1em;
    margin: 0.5em 0;
    color: #00c482;
    background: #e8f7ec;
    border-radius: 5px;
}

.hint_btn {
    background-color: #4CAF50; /* Green */
    border: none;
    color: #f0f0f0;
    padding: 0.4em;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 0.2em;
    cursor: pointer;
    border-radius: 0.8em;
}

.hint_btn:hover {
    background-color: #45a049;
}

.show_btn {
    background-color: #3153f5; /* Green */
    border: none;
    color: #f0f0f0;
    padding: 0.4em 1em;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: large;
    margin: 1em;
    cursor: pointer;
    border-radius: 0.8em;
}

.show_btn:hover {
    background-color: #1d2a7c;
}

* {
    margin: 0;
}