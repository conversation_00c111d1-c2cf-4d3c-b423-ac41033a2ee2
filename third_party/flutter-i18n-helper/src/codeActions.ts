import * as vscode from 'vscode';

export const HARDCODED_STRING_DIAGNOSTIC_CODE = 'i18n-hardcoded-string';

export class I18nCodeActionProvider implements vscode.CodeActionProvider {

    public static readonly providedCodeActionKinds = [
        vscode.CodeActionKind.QuickFix
    ];

    provideCodeActions(document: vscode.TextDocument, range: vscode.Range | vscode.Selection, context: vscode.CodeActionContext, token: vscode.CancellationToken): vscode.ProviderResult<(vscode.Command | vscode.CodeAction)[]> {
        const relevantDiagnostics = context.diagnostics.filter(
            diagnostic => diagnostic.code === HARDCODED_STRING_DIAGNOSTIC_CODE
        );

        return relevantDiagnostics.map(diagnostic => this.createCommandCodeAction(diagnostic));
    }

    private createCommandCodeAction(diagnostic: vscode.Diagnostic): vscode.CodeAction {
        const action = new vscode.CodeAction('Extract to i18n file', vscode.CodeActionKind.QuickFix);
        
        // --- <PERSON><PERSON><PERSON> IS HERE: PASS ARGUMENTS TO THE COMMAND ---
        action.command = {
            command: 'i18n-helper.extractValue',
            title: 'Extract hardcoded string as a translation value.',
            tooltip: 'This will run the command to extract the selected string into your source language JSON file.',
            // Pass the diagnostic's range as an argument.
            // The `extractAsValue` command will now receive this.
            arguments: [{ range: diagnostic.range }]
        };
        
        action.diagnostics = [diagnostic];
        action.isPreferred = true;

        return action;
    }
}