import * as vscode from 'vscode';

export class Logger {
    private static _outputChannel: vscode.OutputChannel;

    public static initialize(context: vscode.ExtensionContext) {
        if (!this._outputChannel) {
            this._outputChannel = vscode.window.createOutputChannel('I18n Helper');
            context.subscriptions.push(this._outputChannel);
        }
    }

    public static log(message: string, ...data: any[]) {
        const timestamp = new Date().toLocaleTimeString();
        this._outputChannel.appendLine(`[${timestamp}] [INFO] ${message}`);
        if (data.length > 0) {
            data.forEach(d => {
                this._outputChannel.appendLine(`       ${JSON.stringify(d, null, 2)}`);
            });
        }
    }

    public static error(message: string, error?: any) {
        const timestamp = new Date().toLocaleTimeString();
        this._outputChannel.appendLine(`[${timestamp}] [ERROR] ${message}`);
        if (error) {
            if (error instanceof Error) {
                this._outputChannel.appendLine(error.stack || error.message);
            } else {
                this._outputChannel.appendLine(`        ${JSON.stringify(error, null, 2)}`);
            }
        }
    }

    public static show() {
        this._outputChannel.show();
    }
}