import * as vscode from 'vscode';
import { getConfiguration, getI18nDirUri, readJsonFile, getValueFromPath } from './utils';

let translations: Record<string, any> | null = null;
let decorationType: vscode.TextEditorDecorationType;
let missingDecorationType: vscode.TextEditorDecorationType;

function initializeDecorations() {
    // Dispose of old decorations if they exist to prevent memory leaks on re-initialization
    if (decorationType) {
        decorationType.dispose();
    }
    if (missingDecorationType) {
        missingDecorationType.dispose();
    }

    decorationType = vscode.window.createTextEditorDecorationType({
        after: {
            margin: '0 0 0 1em',
            color: new vscode.ThemeColor('editorCodeLens.foreground'),
            fontStyle: 'italic',
        },
        rangeBehavior: vscode.DecorationRangeBehavior.ClosedOpen,
    });

    missingDecorationType = vscode.window.createTextEditorDecorationType({
        after: {
            margin: '0 0 0 1em',
            color: new vscode.ThemeColor('errorForeground'),
            fontStyle: 'italic',
            contentText: '❓ Missing',
        },
    });
}

async function loadTranslations() {
    const config = getConfiguration();
    const targetLang = config.get<string>('inlineDisplayLanguage');
    if (!targetLang) {
        translations = null;
        return;
    }

    const i18nDirUri = await getI18nDirUri();
    if (!i18nDirUri) {
        translations = null;
        return;
    }
    
    const langFileUri = vscode.Uri.joinPath(i18nDirUri, `${targetLang}.json`);
    translations = await readJsonFile(langFileUri);
}

export async function updateDecorations(editor: vscode.TextEditor | undefined) {
    if (!editor || editor.document.languageId !== 'dart') {
        return;
    }

    // Ensure decorations are initialized
    if (!decorationType || !missingDecorationType) {
        initializeDecorations();
    }

    await loadTranslations();

    const text = editor.document.getText();
    const decorations: vscode.DecorationOptions[] = [];
    const missingDecorations: vscode.DecorationOptions[] = [];
    
    // --- THE ONLY CHANGE IS THIS REGULAR EXPRESSION ---
    const regEx = /(?:'|")([^'"]+)(?:'|")\s*\.(?:tr|trParams)\b/g;
    
    let match;
    while ((match = regEx.exec(text))) {
        const key = match[1];
        // The range of the match, e.g., from the start of `"key"` to the end of `.trParams`
        const startPos = editor.document.positionAt(match.index);
        const endPos = editor.document.positionAt(match.index + match[0].length);
        const range = new vscode.Range(startPos, endPos);

        if (translations) {
            const translationValue = getValueFromPath(translations, key);

            if (translationValue !== undefined && typeof translationValue === 'string' && translationValue) {
                // For .trParams, the translation might contain placeholders like `%s` or `{name}`.
                // We show a shortened version and indicate it has params.
                const displayValue = translationValue.includes('%') || translationValue.includes('{')
                    ? `${translationValue.substring(0, 25)}... (params)`
                    : translationValue;

                const decoration = {
                    range,
                    renderOptions: { after: { contentText: `» ${displayValue}` } },
                };
                decorations.push(decoration);
            } else {
                // Key exists but is empty, or not a string, or getValueFromPath failed for a nested key
                 missingDecorations.push({ range });
            }
        } else {
            // translations file itself doesn't exist.
            missingDecorations.push({ range });
        }
    }

    editor.setDecorations(decorationType, decorations);
    editor.setDecorations(missingDecorationType, missingDecorations);
}

export function setupInlineDecorator(context: vscode.ExtensionContext) {
    initializeDecorations();

    let timeout: NodeJS.Timeout | undefined = undefined;

    const triggerUpdateDecorations = (editor: vscode.TextEditor | undefined, delay = 500) => {
        if (timeout) {
            clearTimeout(timeout);
        }
        timeout = setTimeout(() => updateDecorations(editor), delay);
    };

    if (vscode.window.activeTextEditor) {
        triggerUpdateDecorations(vscode.window.activeTextEditor, 100);
    }

    context.subscriptions.push(
        vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor) {
                triggerUpdateDecorations(editor);
            }
        })
    );

    context.subscriptions.push(
        vscode.workspace.onDidChangeTextDocument(event => {
            if (vscode.window.activeTextEditor && event.document === vscode.window.activeTextEditor.document) {
                triggerUpdateDecorations(vscode.window.activeTextEditor);
            }
        })
    );
    
    context.subscriptions.push(
        vscode.workspace.onDidChangeConfiguration(async e => {
            if (e.affectsConfiguration('i18n-helper.inlineDisplayLanguage') || e.affectsConfiguration('i18n-helper.i18nDir')) {
                 if (vscode.window.activeTextEditor) {
                    await loadTranslations();
                    triggerUpdateDecorations(vscode.window.activeTextEditor, 0); // Update immediately
                }
            }
        })
    );
}