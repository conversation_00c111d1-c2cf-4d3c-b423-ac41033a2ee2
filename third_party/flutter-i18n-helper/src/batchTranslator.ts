import * as vscode from 'vscode';
import { getConfiguration, getI18nDirUri, readJsonFile } from './utils';
import * as path from 'path';
import { Logger } from './logger';
import fetch, { RequestInit } from 'node-fetch';
import { HttpsProxyAgent } from 'https-proxy-agent';

type TranslateFunction = (chunk: Record<string, string>, sourceLang: string, targetLang: string) => Promise<Record<string, string> | null>;

/**
 * Handles translation using the Gemini API.
 */
async function translateChunkWithGemini(chunk: Record<string, string>, sourceLang: string, targetLang: string): Promise<Record<string, string> | null> {
    Logger.log(`[Gemini] Starting translation for ${targetLang}`);
    const config = getConfiguration();
    const apiKey = config.get<string>('geminiApiKey');
    const modelName = config.get<string>('geminiModel', 'gemini-pro');

    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${apiKey}`;
    const prompt = `Translate the following JSON object's values from language code '${sourceLang}' to language code '${targetLang}'. Only return a valid JSON object with the translated values. Do not add any extra text, explanations, or markdown code fences.\n\nInput:\n${JSON.stringify(chunk, null, 2)}\n\nOutput:`;

    const requestBody = {
        contents: [{ parts: [{ "text": prompt }] }],
        safetySettings: [
            { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" },
            { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
            { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
            { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
        ],
    };

    const fetchOptions: RequestInit = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
    };

    const proxySetting = vscode.workspace.getConfiguration('http').get<string>('proxy');
    if (proxySetting) {
        Logger.log(`[Gemini] Using proxy for this request: ${proxySetting}`);
        fetchOptions.agent = new HttpsProxyAgent(proxySetting);
    }

    try {
        const response = await fetch(API_URL, fetchOptions);
        if (!response.ok) {
            const errorBody = await response.text();
            throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
        }
        const data: any = await response.json();
        const translatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!translatedText) {
            Logger.error('[Gemini] Could not find translated text in API response.', data);
            throw new Error('Invalid response structure from Gemini API.');
        }

        const cleanedJson = translatedText.replace(/```json/g, '').replace(/```/g, '').trim();
        return JSON.parse(cleanedJson);
    } catch (e: any) {
        Logger.error(`[Gemini] Error during translation for ${targetLang}.`, e);
        vscode.window.showErrorMessage(`Gemini translation failed: ${e.message}. Check logs.`);
        Logger.show();
        return null;
    }
}

/**
 * Handles translation using an OpenAI-compatible API.
 */
async function translateChunkWithOpenAI(chunk: Record<string, string>, sourceLang: string, targetLang: string): Promise<Record<string, string> | null> {
    Logger.log(`[OpenAI] Starting translation for ${targetLang}`);
    const config = getConfiguration();
    
    // --- THE FIX IS HERE ---
    // Provide a default empty object to config.get() to prevent it from returning `undefined`.
    // This satisfies TypeScript's strict null checks.
    const openAIConfig = config.get(
        'openai',
        { apiKey: '', baseUrl: '', model: '', prompt: '' }
    );
    
    const { apiKey, baseUrl, model, prompt: systemPromptTemplate } = openAIConfig;

    const API_URL = `${baseUrl.replace(/\/$/, '')}/chat/completions`;

    const systemPrompt = systemPromptTemplate
        .replace('{sourceLang}', sourceLang)
        .replace('{targetLang}', targetLang);
        
    const userPrompt = JSON.stringify(chunk, null, 2);

    const requestBody = {
        model: model,
        messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: userPrompt }
        ],
        response_format: { type: "json_object" } 
    };

    const fetchOptions: RequestInit = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
        },
        body: JSON.stringify(requestBody),
    };

    const proxySetting = vscode.workspace.getConfiguration('http').get<string>('proxy');
    if (proxySetting) {
        Logger.log(`[OpenAI] Using proxy for this request: ${proxySetting}`);
        fetchOptions.agent = new HttpsProxyAgent(proxySetting);
    }

    try {
        const response = await fetch(API_URL, fetchOptions);
        if (!response.ok) {
            const errorBody = await response.text();
            throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
        }
        const data: any = await response.json();
        const translatedJsonString = data.choices?.[0]?.message?.content;

        if (!translatedJsonString) {
            Logger.error('[OpenAI] Could not find translated text in API response.', data);
            throw new Error('Invalid response structure from OpenAI API.');
        }
        
        return JSON.parse(translatedJsonString);
    } catch (e: any) {
        Logger.error(`[OpenAI] Error during translation for ${targetLang}.`, e);
        vscode.window.showErrorMessage(`OpenAI translation failed: ${e.message}. Check logs.`);
        Logger.show();
        return null;
    }
}

/**
 * Main command function for batch translation.
 */
export async function batchTranslate() {
    const config = getConfiguration();

    const chosenProvider = await vscode.window.showQuickPick(['Gemini', 'OpenAI'], {
        placeHolder: 'Select the AI provider for this translation task',
        title: 'Batch Translation Provider',
    });

    if (!chosenProvider) {
        return;
    }
    
    let translateFunction: TranslateFunction;
    
    if (chosenProvider === 'Gemini') {
        if (!config.get<string>('geminiApiKey')) {
            vscode.window.showErrorMessage('Gemini API Key is not configured. Please set `i18n-helper.geminiApiKey` in your settings.');
            return;
        }
        translateFunction = translateChunkWithGemini;
    } else {
        const openAIConfig = config.get('openai', { apiKey: '', baseUrl: '', model: '' });
        if (!openAIConfig.apiKey || !openAIConfig.baseUrl || !openAIConfig.model) {
            vscode.window.showErrorMessage('OpenAI settings (baseUrl, apiKey, model) are not fully configured. Please set them in `i18n-helper.openai`.');
            return;
        }
        translateFunction = translateChunkWithOpenAI;
    }
    
    const mode = await vscode.window.showQuickPick(['Incremental', 'Full'], {
        placeHolder: 'Select translation mode',
    });

    if (!mode) { return; }

    const sourceLang = config.get<string>('sourceLanguage');
    const i18nDirUri = await getI18nDirUri();

    if (!sourceLang || !i18nDirUri) {
        vscode.window.showErrorMessage('Please configure sourceLanguage and i18nDir in settings.');
        return;
    }
    
    const sourceFileUri = vscode.Uri.joinPath(i18nDirUri, `${sourceLang}.json`);
    const sourceData = await readJsonFile(sourceFileUri);
    if (!sourceData) {
        vscode.window.showErrorMessage(`Source language file not found or is invalid: ${sourceFileUri.fsPath}`);
        return;
    }
    
    Logger.log(`Starting batch translation process with ${chosenProvider}...`);

    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: `Translating with ${chosenProvider}...`,
        cancellable: true
    }, async (progress, token) => {
        const entries = await vscode.workspace.fs.readDirectory(i18nDirUri);

        for (const [fileName, fileType] of entries) {
            if (token.isCancellationRequested) break;
            if (fileType !== vscode.FileType.File || !fileName.endsWith('.json') || fileName === `${sourceLang}.json`) continue;

            const targetLang = path.basename(fileName, '.json');
            progress.report({ message: `Translating for ${targetLang}...` });

            const targetFileUri = vscode.Uri.joinPath(i18nDirUri, fileName);
            const targetData = await readJsonFile(targetFileUri) || {};
            
            const keysToTranslate = Object.keys(sourceData).filter(key => 
                mode === 'Full' || !targetData.hasOwnProperty(key) || targetData[key] === '' || targetData[key] === null
            );

            if (keysToTranslate.length === 0) {
                Logger.log(`${targetLang} is up to date. Skipping.`);
                continue;
            }

            const chunk = keysToTranslate.reduce((obj, key) => {
                if (sourceData[key] && typeof sourceData[key] === 'string') {
                     obj[key] = sourceData[key];
                }
                return obj;
            }, {} as Record<string, string>);

            if (Object.keys(chunk).length === 0) continue;

            const translatedChunk = await translateFunction(chunk, sourceLang, targetLang);
            if (translatedChunk && !token.isCancellationRequested) {
                const currentTargetData = await readJsonFile(targetFileUri) || {};
                const combinedData = { ...currentTargetData, ...translatedChunk };
                const orderedData: Record<string, string> = {};
                for (const key of Object.keys(sourceData)) {
                    if (combinedData.hasOwnProperty(key)) {
                        orderedData[key] = combinedData[key];
                    }
                }
                const newContent = JSON.stringify(orderedData, null, 2);
                await vscode.workspace.fs.writeFile(targetFileUri, Buffer.from(newContent, 'utf8'));
                Logger.log(`Successfully updated ${fileName}.`);
            }
        }
        
        vscode.window.showInformationMessage(`Batch translation with ${chosenProvider} completed!`);
        vscode.commands.executeCommand('i18n-helper._internal.refreshAll');
    });
}