import * as vscode from 'vscode';
import * as path from 'path';

/**
 * Gets the plugin's configuration object.
 * @returns {vscode.WorkspaceConfiguration}
 */
export function getConfiguration(): vscode.WorkspaceConfiguration {
    return vscode.workspace.getConfiguration('i18n-helper');
}

/**
 * Gets the configured i18n directory Uri.
 * Supports relative (to workspace) and absolute paths.
 * @returns {Promise<vscode.Uri | undefined>}
 */
export async function getI18nDirUri(): Promise<vscode.Uri | undefined> {
    const config = getConfiguration();
    const i18nDirSetting = config.get<string>('i18nDir');

    if (!i18nDirSetting) {
        return undefined;
    }

    if (path.isAbsolute(i18nDirSetting)) {
        const dirUri = vscode.Uri.file(i18nDirSetting);
        try {
            const stat = await vscode.workspace.fs.stat(dirUri);
            if (stat.type === vscode.FileType.Directory) {
                return dirUri;
            } else {
                vscode.window.showErrorMessage(`I18n Helper: The configured absolute path is not a directory: ${i18nDirSetting}`);
                return undefined;
            }
        } catch (error) {
            vscode.window.showErrorMessage(`I18n Helper: The configured absolute path does not exist: ${i18nDirSetting}`);
            return undefined;
        }
    } else {
        if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {
            vscode.window.showWarningMessage('I18n Helper: Cannot resolve relative i18n path. No workspace folder is open.');
            return undefined;
        }
        const workspaceFolder = vscode.workspace.workspaceFolders[0].uri;
        return vscode.Uri.joinPath(workspaceFolder, i18nDirSetting);
    }
}

/**
 * Asynchronously reads and parses a JSON file.
 * @param {vscode.Uri} fileUri The Uri of the file to read.
 * @returns {Promise<Record<string, any> | null>} The parsed JSON object, or null on failure.
 */
export async function readJsonFile(fileUri: vscode.Uri): Promise<Record<string, any> | null> {
    try {
        const fileContents = await vscode.workspace.fs.readFile(fileUri);
        return JSON.parse(Buffer.from(fileContents).toString('utf8'));
    } catch (e) {
        if (e instanceof vscode.FileSystemError && e.code === 'FileNotFound') {
            return null;
        }
        console.error(`[I18n Helper] Error reading or parsing JSON file: ${fileUri.fsPath}`, e);
        return null;
    }
}

/**
 * Gets a value from an object based on a dot-separated path.
 * It first checks for a direct (flat) key match before attempting to traverse a nested path.
 * @param {Record<string, any>} obj The object to search.
 * @param {string} path The path string, e.g., 'home.title'.
 * @returns {any} The found value, or undefined if not found.
 */
export function getValueFromPath(obj: Record<string, any>, path: string): any {
    if (!obj) {
        return undefined;
    }
    // 1. Check for a direct match first (for flat keys like "a.b.c")
    if (Object.prototype.hasOwnProperty.call(obj, path)) {
        return obj[path];
    }

    // 2. If no direct match, try to traverse the path (for nested keys)
    return path.split('.').reduce((o, k) => {
        if (o && typeof o === 'object' && Object.prototype.hasOwnProperty.call(o, k)) {
            return o[k];
        }
        return undefined;
    }, obj);
}

/**
 * Sets a value in an object based on a dot-separated path.
 * Creates nested objects if they don't exist.
 * @param {Record<string, any>} obj The object to modify.
 * @param {string} path The path string, e.g., 'home.title'.
 * @param {any} value The value to set.
 */
export function setValueByPath(obj: Record<string, any>, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop();
    
    if (!lastKey) {
        console.error(`[I18n Helper] Invalid path for setValueByPath: ${path}`);
        return;
    }

    let current = obj;
    for (const key of keys) {
        if (typeof current[key] !== 'object' || current[key] === null) {
            current[key] = {};
        }
        current = current[key];
    }
    current[lastKey] = value;
}

/**
 * Deletes a key from an object based on a dot-separated path.
 * Handles both flat and nested keys.
 * @param {Record<string, any>} obj The object to modify.
 * @param {string} path The path string, e.g., 'home.title'.
 * @returns {boolean} True if a key was deleted, false otherwise.
 */
export function deleteValueByPath(obj: Record<string, any>, path: string): boolean {
    // First, try deleting as a flat key
    if (Object.prototype.hasOwnProperty.call(obj, path)) {
        delete obj[path];
        return true;
    }

    // Then, try deleting as a nested key
    const keys = path.split('.');
    const lastKey = keys.pop();
    if (!lastKey) { return false; }

    const parentPath = keys.join('.');
    const parent = parentPath ? getValueFromPath(obj, parentPath) : obj;

    if (parent && typeof parent === 'object' && Object.prototype.hasOwnProperty.call(parent, lastKey)) {
        delete parent[lastKey];
        // Optional: clean up empty parent objects, but can be complex. Skipping for now.
        return true;
    }
    return false;
}

/**
 * Calculates the Levenshtein distance between two strings.
 * @param {string} a The first string.
 * @param {string} b The second string.
 * @returns {number} The Levenshtein distance.
 */
export function getLevenshteinDistance(a: string, b: string): number {
    const matrix = Array(b.length + 1).fill(null).map(() => Array(a.length + 1).fill(null));

    for (let i = 0; i <= a.length; i += 1) {
        matrix[0][i] = i;
    }

    for (let j = 0; j <= b.length; j += 1) {
        matrix[j][0] = j;
    }

    for (let j = 1; j <= b.length; j += 1) {
        for (let i = 1; i <= a.length; i += 1) {
            const indicator = a[i - 1] === b[j - 1] ? 0 : 1;
            matrix[j][i] = Math.min(
                matrix[j][i - 1] + 1,        // deletion
                matrix[j - 1][i] + 1,        // insertion
                matrix[j - 1][i - 1] + indicator, // substitution
            );
        }
    }

    return matrix[b.length][a.length];
}