import * as vscode from 'vscode';
import * as path from 'path';
import { getI18nDirUri, readJsonFile, getConfiguration, getValueFromPath, getLevenshteinDistance } from './utils';
import { HARDCODED_STRING_DIAGNOSTIC_CODE } from './codeActions';
import { Logger } from './logger';

const diagnosticCollection = vscode.languages.createDiagnosticCollection('i18n-helper');

// --- Workspace-level state ---
const dartFileKeyCache = new Map<string, Set<string>>();
let allTranslations: Map<string, Record<string, any>> = new Map();
let allLangs: string[] = [];
let sourceLang: string = 'en';
let allSourceKeys: string[] = [];
let isInitialScanComplete = false;

// --- Lightweight, instant analysis for the active editor (unchanged) ---
export function updateInstantDiagnosticsForDocument(document: vscode.TextDocument) {
    if (document.languageId !== 'dart' || document.isClosed) {
        return;
    }
    const config = getConfiguration();
    const diagnostics: vscode.Diagnostic[] = [];
    const text = document.getText();
    const enableHardcodedCheck = config.get<boolean>('diagnostics.enableHardcodedStrings', true);
    if (enableHardcodedCheck) {
        const hardcodedStringRegex = /(['"])([^'"\n\r]*[\u4e00-\u9fa5][^'"\n\r]*)\1(?!\.tr\b)/g;
        let hardcodedMatch;
        while ((hardcodedMatch = hardcodedStringRegex.exec(text))) {
            const fullMatch = hardcodedMatch[0];
            const stringContent = hardcodedMatch[2];
            const index = hardcodedMatch.index;
            const position = document.positionAt(index);
            const line = document.lineAt(position.line);
            const lineText = line.text;
            const singleLineCommentIndex = lineText.indexOf('//');
            if (singleLineCommentIndex !== -1 && position.character > singleLineCommentIndex) continue;
            const textUpToMatch = document.getText(new vscode.Range(new vscode.Position(0, 0), position));
            if (textUpToMatch.lastIndexOf('/*') > textUpToMatch.lastIndexOf('*/')) continue;
            const trimmedLine = line.text.trim();
            if (trimmedLine.startsWith('import ') || trimmedLine.startsWith('export ')) continue;
            const range = new vscode.Range(position, document.positionAt(index + fullMatch.length));
            const diagnostic = new vscode.Diagnostic(
                range,
                `I18n: Found hardcoded string. Consider extracting "${stringContent}".`,
                vscode.DiagnosticSeverity.Information
            );
            diagnostic.source = 'I18n Helper';
            diagnostic.code = HARDCODED_STRING_DIAGNOSTIC_CODE;
            diagnostics.push(diagnostic);
        }
    }
    if (isInitialScanComplete) {
        const i18nKeyRegex = /(?:'|")([^'"]+)(?:'|")\s*\.(?:tr|trParams)\b/g;
        let match;
        while ((match = i18nKeyRegex.exec(text))) {
            const key = match[1];
            const diagnosticInfo = getDiagnosticInfoForKey(key);
            if (diagnosticInfo) {
                const range = new vscode.Range(document.positionAt(match.index), document.positionAt(match.index + match[0].length));
                const diagnostic = new vscode.Diagnostic(range, diagnosticInfo.message, diagnosticInfo.severity);
                diagnostic.source = 'I18n Helper';
                diagnostics.push(diagnostic);
            }
        }
    }
    const existingUnusedKeyDiagnostics = diagnosticCollection.get(document.uri)
        ?.filter(d => d.message.startsWith('I18n: Unused key'));
    diagnosticCollection.set(document.uri, [...diagnostics, ...(existingUnusedKeyDiagnostics || [])]);
}

// --- Deeper, workspace-aware analysis functions (mostly unchanged) ---
function collectKeys(obj: Record<string, any>, prefix = ''): string[] {
    return Object.keys(obj).reduce((res: string[], el) => {
        const prop = prefix ? prefix + '.' + el : el;
        if (typeof obj[el] === 'object' && obj[el] !== null && !Array.isArray(obj[el])) {
            return [...res, ...collectKeys(obj[el], prop)];
        }
        return [...res, prop];
    }, []);
}

async function loadAllI18nData() {
    allTranslations.clear();
    allLangs = [];
    allSourceKeys = [];
    const config = getConfiguration();
    sourceLang = config.get<string>('sourceLanguage', 'en');
    const i18nDirUri = await getI18nDirUri();
    if (!i18nDirUri) return;
    try {
        const entries = await vscode.workspace.fs.readDirectory(i18nDirUri);
        for (const [fileName, fileType] of entries) {
            if (fileType === vscode.FileType.File && fileName.endsWith('.json')) {
                const lang = path.basename(fileName, '.json');
                allLangs.push(lang);
                const fileUri = vscode.Uri.joinPath(i18nDirUri, fileName);
                const data = await readJsonFile(fileUri);
                if (data) {
                    allTranslations.set(lang, data);
                    if (lang === sourceLang) {
                        allSourceKeys = [...Object.keys(data), ...collectKeys(data)];
                        allSourceKeys = [...new Set(allSourceKeys)];
                    }
                }
            }
        }
        allLangs = [...new Set(allLangs)];
    } catch (error) {
        console.error("[Diag] Failed to load i18n files for diagnostics", error);
    }
}

interface KeyDiagnosticInfo {
    severity: vscode.DiagnosticSeverity;
    message: string;
}

function getDiagnosticInfoForKey(key: string): KeyDiagnosticInfo | null {
    const config = getConfiguration();
    const sourceData = allTranslations.get(sourceLang);
    const sourceValue = getValueFromPath(sourceData || {}, key);
    if (sourceValue === undefined) {
        let message = `I18n: Key '${key}' not found in source language file (${sourceLang}.json).`;
        const enableSuggestions = config.get<boolean>('diagnostics.enableKeySuggestions', true);
        if (enableSuggestions && allSourceKeys.length > 0) {
            const distanceThreshold = config.get<number>('diagnostics.suggestionDistanceThreshold', 3);
            const suggestions = allSourceKeys
                .map(k => ({ key: k, distance: getLevenshteinDistance(key, k) }))
                .filter(item => item.distance > 0 && item.distance <= distanceThreshold)
                .sort((a, b) => a.distance - b.distance)
                .slice(0, 3)
                .map(item => `'${item.key}'`);
            if (suggestions.length > 0) {
                message += ` Did you mean ${suggestions.join(' or ')}?`;
            }
        }
        return { severity: vscode.DiagnosticSeverity.Error, message: message };
    }
    if (sourceValue === null || (typeof sourceValue === 'string' && sourceValue.trim() === '')) {
        return { severity: vscode.DiagnosticSeverity.Error, message: `I18n: Key '${key}' is empty in the source language file (${sourceLang}.json).`};
    }
    const missingInLangs: string[] = [];
    for (const lang of allLangs) {
        if (lang === sourceLang) continue;
        const targetData = allTranslations.get(lang);
        const targetValue = getValueFromPath(targetData || {}, key);
        if (targetValue === undefined || targetValue === null || (typeof targetValue === 'string' && targetValue.trim() === '')) {
            missingInLangs.push(lang);
        }
    }
    if (missingInLangs.length > 0) {
        return { severity: vscode.DiagnosticSeverity.Warning, message: `I18n: Key '${key}' is missing or empty in: ${missingInLangs.join(', ')}.` };
    }
    return null;
}

// --- START OF REFACTORED/NEW CODE ---

/**
 * Generates all diagnostics for a given Dart document during a full scan.
 * This combines hardcoded string checks and missing key checks.
 * @param document The Dart document to analyze.
 * @returns An array of diagnostics for the document.
 */
function generateDiagnosticsForDartFile(document: vscode.TextDocument): vscode.Diagnostic[] {
    const config = getConfiguration();
    const diagnostics: vscode.Diagnostic[] = [];
    const text = document.getText();

    // 1. Hardcoded string check
    const enableHardcodedCheck = config.get<boolean>('diagnostics.enableHardcodedStrings', true);
    if (enableHardcodedCheck) {
        const hardcodedStringRegex = /(['"])([^'"\n\r]*[\u4e00-\u9fa5][^'"\n\r]*)\1(?!\.tr\b)/g;
        let match;
        while ((match = hardcodedStringRegex.exec(text))) {
            const position = document.positionAt(match.index);
            const lineText = document.lineAt(position.line).text;
            if (lineText.trim().startsWith('//') || lineText.trim().startsWith('import ') || lineText.trim().startsWith('export ')) continue;
            
            const range = new vscode.Range(position, document.positionAt(match.index + match[0].length));
            const diagnostic = new vscode.Diagnostic(
                range,
                `I18n: Found hardcoded string. Consider extracting "${match[2]}".`,
                vscode.DiagnosticSeverity.Information
            );
            diagnostic.source = 'I18n Helper';
            diagnostic.code = HARDCODED_STRING_DIAGNOSTIC_CODE;
            diagnostics.push(diagnostic);
        }
    }

    // 2. I18n key validity checks
    const i18nKeyRegex = /(?:'|")([^'"]+)(?:'|")\s*\.(?:tr|trParams)\b/g;
    let match;
    while ((match = i18nKeyRegex.exec(text))) {
        const key = match[1];
        const diagnosticInfo = getDiagnosticInfoForKey(key);
        if (diagnosticInfo) {
            const range = new vscode.Range(document.positionAt(match.index), document.positionAt(match.index + match[0].length));
            const diagnostic = new vscode.Diagnostic(range, diagnosticInfo.message, diagnosticInfo.severity);
            diagnostic.source = 'I18n Helper';
            diagnostics.push(diagnostic);
        }
    }

    return diagnostics;
}

/**
 * Generates diagnostics for unused keys in the source language file.
 * This function now returns the diagnostics instead of setting them directly.
 * @param usedKeys A set of all keys used in the workspace.
 * @returns An object containing the source file URI and its diagnostics.
 */
async function generateUnusedKeyDiagnostics(usedKeys: Set<string>): Promise<{ uri: vscode.Uri; diagnostics: vscode.Diagnostic[] } | null> {
    const i18nDirUri = await getI18nDirUri();
    if (!i18nDirUri || !sourceLang) return null;

    const sourceFileUri = vscode.Uri.joinPath(i18nDirUri, `${sourceLang}.json`);
    let document;
    try {
        document = await vscode.workspace.openTextDocument(sourceFileUri);
    } catch (e) {
        return null;
    }

    const diagnostics: vscode.Diagnostic[] = [];
    const unusedKeys = allSourceKeys.filter(key => !usedKeys.has(key));
    const text = document.getText();

    for (const key of unusedKeys) {
        const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const keyRegex = new RegExp(`"(${escapedKey})"\\s*:`, 'g');
        let match;
        if ((match = keyRegex.exec(text))) {
            const keyStartIndex = match.index + 1;
            const keyEndIndex = keyStartIndex + match[1].length;
            const range = new vscode.Range(document.positionAt(keyStartIndex), document.positionAt(keyEndIndex));
            const diagnostic = new vscode.Diagnostic(range, `I18n: Unused key '${key}'.`, vscode.DiagnosticSeverity.Warning);
            diagnostic.source = 'I18n Helper';
            diagnostics.push(diagnostic);
        }
    }
    
    return { uri: sourceFileUri, diagnostics: diagnostics };
}


/**
 * Performs a full scan of the workspace, building diagnostics in memory before updating.
 */
export async function scanWorkspace() {
    isInitialScanComplete = false;
    const newDiagnosticsByUri = new Map<string, vscode.Diagnostic[]>();

    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Window,
        title: 'I18n: Full Workspace Scan...',
        cancellable: false
    }, async (progress) => {
        progress.report({ message: 'Loading translation files...' });
        await loadAllI18nData();

        dartFileKeyCache.clear();
        const dartFiles = await vscode.workspace.findFiles('**/*.dart', '**/build/**');
        
        progress.report({ message: `Analyzing ${dartFiles.length} Dart files...` });
        for (const fileUri of dartFiles) {
            try {
                const document = await vscode.workspace.openTextDocument(fileUri);
                
                // Update key usage cache
                const usedKeysInDoc = new Set<string>();
                const text = document.getText();
                const i18nKeyRegex = /(?:'|")([^'"]+)(?:'|")\s*\.(?:tr|trParams)\b/g;
                let match;
                while ((match = i18nKeyRegex.exec(text))) {
                    usedKeysInDoc.add(match[1]);
                }
                dartFileKeyCache.set(document.uri.toString(), usedKeysInDoc);

                // Generate diagnostics for this file and store them in our temporary map
                const fileDiagnostics = generateDiagnosticsForDartFile(document);
                if (fileDiagnostics.length > 0) {
                    newDiagnosticsByUri.set(fileUri.toString(), fileDiagnostics);
                }

            } catch (error) {
                console.error(`Could not open or process ${fileUri.fsPath}`, error);
            }
        }

        progress.report({ message: `Checking for unused keys...` });
        const allUsedKeys = new Set<string>();
        for (const keys of dartFileKeyCache.values()) {
            keys.forEach(key => allUsedKeys.add(key));
        }
        const unusedKeyResult = await generateUnusedKeyDiagnostics(allUsedKeys);
        if (unusedKeyResult) {
            newDiagnosticsByUri.set(unusedKeyResult.uri.toString(), unusedKeyResult.diagnostics);
        }
    });

    // --- Atomic Update Step ---
    // 1. Get all URIs that currently have diagnostics
    const oldUris = new Set<string>();
    diagnosticCollection.forEach(uri => oldUris.add(uri.toString()));

    // 2. Get all URIs from our new scan
    const newUris = new Set(newDiagnosticsByUri.keys());

    // 3. Combine them to get a complete list of files to update
    const allUrisToUpdate = new Set([...oldUris, ...newUris]);

    // 4. Perform the update
    for (const uriString of allUrisToUpdate) {
        const uri = vscode.Uri.parse(uriString);
        const diagnostics = newDiagnosticsByUri.get(uriString) || []; // Use new diagnostics or an empty array to clear old ones
        diagnosticCollection.set(uri, diagnostics);
    }

    isInitialScanComplete = true;
    Logger.log("Workspace scan complete and diagnostics updated.");
}


// Setup function remains the same, but it will now benefit from the refactored scanWorkspace
export function setupDiagnostics(context: vscode.ExtensionContext) {
    scanWorkspace(); // Initial slow scan
    
    let debounceTimer: NodeJS.Timeout;
    context.subscriptions.push(
        vscode.workspace.onDidChangeTextDocument(event => {
            if (vscode.window.activeTextEditor && event.document === vscode.window.activeTextEditor.document) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    updateInstantDiagnosticsForDocument(event.document);
                }, 300);
            }
        })
    );

    context.subscriptions.push(
        vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor) {
                updateInstantDiagnosticsForDocument(editor.document);
            }
        })
    );
    
    context.subscriptions.push(
        vscode.workspace.onDidSaveTextDocument(async (doc) => {
            if (doc.languageId === 'dart' || (doc.fileName.endsWith('.json') && (await getI18nDirUri())?.fsPath && doc.uri.fsPath.startsWith((await getI18nDirUri())!.fsPath))) {
                Logger.log(`Relevant file saved: ${doc.uri.fsPath}. Triggering full diagnostic refresh.`);
                await scanWorkspace(); // Always trigger a full, safe scan on save
            }
        })
    );
    
    context.subscriptions.push(
        vscode.workspace.onDidDeleteFiles(async (event) => {
            const i18nDirUri = await getI18nDirUri();
            const relevantFileDeleted = event.files.some(file => 
                file.path.endsWith('.dart') || 
                (i18nDirUri && file.path.endsWith('.json') && file.fsPath.startsWith(i18nDirUri.fsPath))
            );
            if (relevantFileDeleted) {
                await scanWorkspace();
            }
        })
    );

    context.subscriptions.push(
        vscode.workspace.onDidChangeConfiguration(async e => {
            if (e.affectsConfiguration('i18n-helper')) {
                 await scanWorkspace();
            }
        })
    );
}