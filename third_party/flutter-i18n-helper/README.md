# Flutter I18n Helper

一个 VS Code 扩展，旨在极大地提升您在使用 `get` 或类似方案进行 Flutter/Dart 国际化 (i18n) 时的开发效率。本插件提供了内联翻译预览、基于 Gemini AI 的批量翻译以及 JSON 文件键同步等强大功能。

  
*(建议：在此处放置一个展示内联翻译功能的 GIF 动图)*

## ✨ 功能特性

*   **内联翻译预览**: 在您的 Dart 代码中，直接将 `'.key'.tr` 形式的国际化键渲染成指定目标语言的文本。如果翻译缺失，也会明确标记出来，让您一目了然。
*   **AI 批量翻译**: 集成 Google Gemini API，可以根据您的源语言 JSON 文件，自动为其他语言生成翻译。
    *   **增量更新**: 只翻译目标文件中值为空的键，高效补充新词条。
    *   **全量更新**: 重新翻译所有键，适用于大规模文本修订。
*   **JSON 键同步**: 以一个参考语言文件（如 `en.json`）为基准，一键同步所有其他语言文件的键结构。多余的键将被删除，缺失的键将被补充（值为空字符串），确保所有语言文件结构统一。

## 📥 安装

1.  打开 **VS Code**。
2.  进入**扩展**视图 (`Ctrl+Shift+X`)。
3.  搜索 `Flutter I18n Helper` (或您为插件设定的名字)。
4.  点击 **Install**。
5.  重新加载 VS Code。

## ⚙️ 配置

在使用插件前，请先在 VS Code 的设置 (`settings.json`) 中进行配置。您可以通过 `文件 > 首选项 > 设置` 打开设置界面，然后搜索 "Flutter I18n Helper"。

| 设置项                               | 描述                                                                                                                              | 默认值           |
| ------------------------------------ | --------------------------------------------------------------------------------------------------------------------------------- | ---------------- |
| `flutterI18nHelper.i18nDir`          | i18n JSON 文件所在的目录路径 (相对于工作区根目录)。                                                                                    | `assets/i18n`    |
| `flutterI18nHelper.sourceLanguage`   | 作为翻译和同步基准的源语言代码。例如，`en` 对应 `en.json` 文件。                                                                       | `en`             |
| `flutterI18nHelper.inlineDisplayLanguage` | 在 Dart 编辑器中，内联预览时显示的目标语言代码。例如，设置为 `zh` 将会显示 `zh.json` 中的翻译。                                      | `zh`             |
| `flutterI18nHelper.geminiApiKey`     | 用于批量翻译功能的 Google Gemini API 密钥。**此项为使用批量翻译功能的必需项**。 [在此获取您的密钥](https://aistudio.google.com/app/apikey)。 | `""` (空)        |
| `flutterI18nHelper.geminiModel`      | 用于翻译的 Gemini 模型。`gemini-pro` 是一个性价比很高的选择。                                                                         | `gemini-pro`     |

**示例 `settings.json` 配置:**
```json
{
    "flutterI18nHelper.i18nDir": "lib/i18n",
    "flutterI18nHelper.sourceLanguage": "en",
    "flutterI18nHelper.inlineDisplayLanguage": "es",
    "flutterI18nHelper.geminiApiKey": "YOUR_GEMINI_API_KEY_HERE"
}
```

## 🚀 使用方法

### 1. 内联翻译预览

此功能是自动启用的。当您打开一个 Dart 文件时，插件会自动检测 `'.key'.tr` 格式的代码，并在其后显示 `inlineDisplayLanguage` 设置语言对应的翻译。

*   ✅ **找到翻译**: `'hello'.tr` » 你好
*   ❓ **缺失翻译**: `'new_key'.tr` ❓ Missing (并带有红色下划线高亮)

### 2. 同步 JSON 键

当您在源语言文件 (如 `en.json`) 中添加或删除了键，可以使用此命令来保持所有其他语言文件结构一致。

1.  打开命令面板 (`Ctrl+Shift+P` 或 `Cmd+Shift+P`)。
2.  输入并选择 `I18n: Synchronize JSON Keys`。
3.  插件将自动处理所有 i18n 目录下的 JSON 文件。

### 3. 批量翻译

此功能可以帮您快速填充翻译。

1.  确保您已在设置中配置了 `geminiApiKey`。
2.  打开命令面板 (`Ctrl+Shift+P` 或 `Cmd+Shift+P`)。
3.  输入并选择 `I18n: Batch Translate with Gemini`。
4.  在弹出的选项中选择翻译模式：
    *   **Incremental (增量)**: 只翻译值为空字符串的键。推荐日常使用。
    *   **Full (全量)**: 重新翻译所有键。请谨慎使用。
5.  插件将在后台开始翻译，并通过通知显示进度。

---

## 👨‍💻  给开发者：打包与发布

如果您是此插件的开发者，并希望将其打包或发布到 VS Code Marketplace，请遵循以下步骤。

### 准备工作

您需要安装 `vsce`，这是 VS Code 官方的扩展打包和发布工具。

```bash
npm install -g vsce
```

您还需要在 [Azure DevOps](https://azure.microsoft.com/en-us/services/devops/) 上创建一个组织，以便获取发布插件所需的个人访问令牌 (Personal Access Token)。

### 打包插件

打包会将您的插件代码编译并打包成一个 `.vsix` 文件。这个文件可以用于手动安装或分发。

在您的项目根目录下运行：

```bash
vsce package
```

命令成功后，会在根目录下生成一个类似 `flutter-i18n-helper-0.0.1.vsix` 的文件。

### 发布插件到 Marketplace

1.  **创建发布者 (Publisher)**:
    *   访问 [VS Code Marketplace Publisher Management](https://marketplace.visualstudio.com/manage/publishers/) 页面。
    *   登录并创建一个新的发布者。记下您的发布者 ID。

2.  **登录 `vsce`**:
    使用您创建的发布者 ID 登录。您需要一个 Azure DevOps 的个人访问令牌作为密码。

    ```bash
    vsce login <your_publisher_id>
    ```

3.  **更新版本号**:
    在每次发布更新之前，请务必在 `package.json` 文件中增加 `version` 字段的值（例如，从 `0.0.1` 更新到 `0.0.2`）。VS Code Marketplace 不允许重复发布相同的版本。

4.  **发布**:
    一切准备就绪后，运行以下命令进行发布：

    ```bash
    vsce publish
    ```

    如果这是您的第一次发布，插件可能需要几分钟到几小时才能在 Marketplace 上出现。后续的更新通常会更快。

## ⚠️ 已知问题

*   当 JSON 文件非常大时，批量翻译功能可能会因为超出 Gemini API 的单次请求 token 限制而失败。未来的版本可能会通过分块请求来解决这个问题。
