#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TextIn PDF to Markdown API 测试程序
API文档: https://www.textin.com/document/pdf_to_markdown
"""

import os
import sys
import json
import argparse
import requests
from pathlib import Path


class TextInPDF2Markdown:
    """TextIn PDF to Markdown API 调用类"""
    
    API_URL = "https://api.textin.com/ai/service/v1/pdf_to_markdown"
    
    def __init__(self, app_id, secret_code):
        """
        初始化API调用类
        
        Args:
            app_id (str): TextIn平台的APP ID
            secret_code (str): TextIn平台的Secret Code
        """
        self.app_id = app_id
        self.secret_code = secret_code
        
    def convert(self, file_path, params=None):
        """
        将文件转换为Markdown
        
        Args:
            file_path (str): 要转换的文件路径
            params (dict, optional): API参数字典
            
        Returns:
            dict: API返回的结果
        """
        # 验证文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        # 设置请求头
        headers = {
            "x-ti-app-id": self.app_id,
            "x-ti-secret-code": self.secret_code
        }
        
        # 默认参数设置
        default_params = {
            "parse_mode": "auto",
            "table_flavor": "md",
            "get_image": "objects",
            "apply_document_tree": 1,
            "markdown_details": 1,
            "page_details": 1
        }
        
        # 更新参数
        if params:
            default_params.update(params)
            
        # 准备文件
        file_name = os.path.basename(file_path)
        files = {"file": (file_name, open(file_path, "rb"), self._get_content_type(file_path))}
        
        try:
            # 发送请求
            response = requests.post(
                self.API_URL,
                headers=headers,
                data=default_params,
                files=files
            )
            
            # 关闭文件
            files["file"][1].close()
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析JSON响应
            result = response.json()
            
            # 检查API返回的错误代码
            if result.get("code") != 200:
                error_msg = f"API错误: {result.get('code')} - {result.get('message')}"
                return {"success": False, "error": error_msg, "data": result}
                
            return {"success": True, "error": None, "data": result}
            
        except requests.exceptions.RequestException as e:
            return {"success": False, "error": str(e), "data": None}
        except json.JSONDecodeError as e:
            return {"success": False, "error": f"JSON解析错误: {str(e)}", "data": None}
        except Exception as e:
            return {"success": False, "error": f"未知错误: {str(e)}", "data": None}
            
    def _get_content_type(self, file_path):
        """
        根据文件扩展名获取MIME类型
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: MIME类型
        """
        ext = os.path.splitext(file_path)[1].lower()
        
        mime_types = {
            ".pdf": "application/pdf",
            ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".doc": "application/msword",
            ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".xls": "application/vnd.ms-excel",
            ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            ".ppt": "application/vnd.ms-powerpoint",
            ".png": "image/png",
            ".jpg": "image/jpeg",
            ".jpeg": "image/jpeg",
            ".txt": "text/plain",
            ".html": "text/html",
            ".htm": "text/html"
        }
        
        return mime_types.get(ext, "application/octet-stream")


def save_markdown(result, output_path=None):
    """
    保存转换后的Markdown内容到文件
    
    Args:
        result (dict): API返回的结果
        output_path (str, optional): 输出文件路径
    """
    if not result["success"] or not result["data"]:
        print("无法保存Markdown：转换失败")
        return
    
    try:
        markdown_content = result["data"]["result"]["markdown"]
        
        if not output_path:
            # 如果未指定输出路径，生成默认路径
            output_path = "output.md"
            
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
            
        # 保存Markdown文件
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)
            
        print(f"Markdown已保存到: {output_path}")
        
    except KeyError:
        print("API响应中未找到Markdown内容")
    except Exception as e:
        print(f"保存Markdown时出错: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="TextIn PDF to Markdown API 测试程序")
    
    parser.add_argument("--app-id", required=True, help="TextIn平台的APP ID")
    parser.add_argument("--secret-code", required=True, help="TextIn平台的Secret Code")
    parser.add_argument("--file", required=True, help="要转换的文件路径")
    parser.add_argument("--output", help="输出Markdown文件路径")
    parser.add_argument("--pdf-pwd", help="PDF密码（如需）")
    parser.add_argument("--table-flavor", choices=["md", "html", "none"], default="md", 
                        help="Markdown表格格式")
    parser.add_argument("--get-image", choices=["none", "page", "objects", "both"], default="objects", 
                        help="获取Markdown中的图片方式")
    parser.add_argument("--page-start", type=int, help="PDF开始页码")
    parser.add_argument("--page-count", type=int, help="PDF页数")
    parser.add_argument("--parse-mode", choices=["auto", "scan"], default="auto", 
                        help="PDF解析模式")
    
    args = parser.parse_args()
    
    # 构建API参数
    params = {}
    
    if args.pdf_pwd:
        params["pdf_pwd"] = args.pdf_pwd
    if args.table_flavor:
        params["table_flavor"] = args.table_flavor
    if args.get_image:
        params["get_image"] = args.get_image
    if args.page_start is not None:
        params["page_start"] = args.page_start
    if args.page_count is not None:
        params["page_count"] = args.page_count
    if args.parse_mode:
        params["parse_mode"] = args.parse_mode
        
    # 创建API调用实例
    converter = TextInPDF2Markdown(args.app_id, args.secret_code)
    
    # 调用API
    print(f"开始转换文件: {args.file}")
    result = converter.convert(args.file, params)
    
    # 输出结果
    if result["success"]:
        print("转换成功！")
        save_markdown(result, args.output)
    else:
        print(f"转换失败: {result['error']}")
        if result["data"]:
            print(f"API返回: {json.dumps(result['data'], ensure_ascii=False, indent=2)}")


if __name__ == "__main__":
    main()
