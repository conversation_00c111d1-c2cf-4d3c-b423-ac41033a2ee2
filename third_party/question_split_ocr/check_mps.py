import torch

if torch.backends.mps.is_available():
    print("✅ MPS is available! You can use the M1 Pro GPU for training.")
    device = torch.device("mps")
    # 创建一个张量并移动到 MPS 设备上，进行一次简单的运算
    x = torch.ones(5, device=device)
    y = x * 2
    print("Simple tensor operation on MPS device successful:", y)
else:
    print("❌ MPS not available.")
    print("Please check the following:")
    print("1. Your macOS version is 12.3 or newer.")
    print("2. You have installed the correct PyTorch version for Apple Silicon.")