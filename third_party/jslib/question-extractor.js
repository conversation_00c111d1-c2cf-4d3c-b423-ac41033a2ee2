/**
 * HTML试题提取器 - 根据正则表达式提取题目、选项和答案
 * 支持保留原始HTML格式，保证输出合法HTML，并正确拆分同一标签内的多个选项
 */

const fs = require('fs');
const cheerio = require('cheerio'); // 添加cheerio依赖用于处理HTML

/**
 * 从HTML文本中提取题目、选项和答案
 * @param {string} htmlContent - HTML内容
 * @returns {Array} 提取到的题目对象数组
 */
function extractQuestions(htmlContent) {
  // 将HTML片段包装在一个根元素中以便解析
  const wrappedHtml = `<div id="content">${htmlContent}</div>`;
  const $ = cheerio.load(wrappedHtml, { decodeEntities: false });
  const contentText = $('#content').text();
  
  // 定义正则模式
  const questionPattern = /(\d+)\./g;
  const optionPattern = /([A-G])\./g;
  const answerPattern = /答案[:：]\s*([A-G]+)/g;
  
  // 查找所有问题起始位置
  const questions = [];
  let questionMatch;
  
  while ((questionMatch = questionPattern.exec(contentText)) !== null) {
    const questionNum = questionMatch[1];
    const questionStartPos = questionMatch.index;
    const questionEndPos = findNextPatternPos(contentText, questionStartPos + 1, questionPattern);
    
    // 提取此问题范围内的所有文本
    const questionText = contentText.substring(questionStartPos, questionEndPos !== -1 ? questionEndPos : contentText.length);
    
    // 查找选项
    const options = [];
    let optionMatch;
    const optionRegExp = new RegExp(optionPattern.source, 'g');
    
    while ((optionMatch = optionRegExp.exec(questionText)) !== null) {
      const optionLetter = optionMatch[1];
      const optionStartPos = questionStartPos + optionMatch.index;
      const nextOptionPos = findNextPatternPos(contentText, optionStartPos + 1, optionPattern);
      const answerPos = findNextPatternPos(contentText, optionStartPos, answerPattern);
      
      // 确定选项结束位置(下一个选项、答案或问题结束)
      let optionEndPos;
      if (nextOptionPos !== -1 && (answerPos === -1 || nextOptionPos < answerPos)) {
        optionEndPos = nextOptionPos;
      } else if (answerPos !== -1) {
        optionEndPos = answerPos;
      } else {
        optionEndPos = questionEndPos !== -1 ? questionEndPos : contentText.length;
      }
      
      options.push({
        optionLetter,
        optionStartPos,
        optionEndPos
      });
    }
    
    // 查找答案
    const answerRegExp = new RegExp(answerPattern.source, 'g');
    let answerMatch;
    let answerText = '';
    let answerStartPos = -1;
    let answerEndPos = -1;
    
    while ((answerMatch = answerRegExp.exec(questionText)) !== null) {
      answerText = answerMatch[1];
      answerStartPos = questionStartPos + answerMatch.index;
      answerEndPos = questionEndPos !== -1 ? questionEndPos : contentText.length;
    }
    
    // 创建问题对象
    const questionObj = {
      questionNumber: questionNum,
      questionContent: extractCleanHTML($, contentText, questionStartPos, options.length > 0 ? options[0].optionStartPos : answerStartPos),
      options: options.map((opt) => ({
        optionLetter: opt.optionLetter,
        optionContent: extractCleanHTML($, contentText, opt.optionStartPos, opt.optionEndPos)
      })),
      answer: answerText,
      answerContent: answerStartPos !== -1 ? extractCleanHTML($, contentText, answerStartPos, answerEndPos) : ''
    };
    
    questions.push(questionObj);
  }
  
  return questions;
}

/**
 * 查找下一个匹配模式的位置
 * @param {string} text - 文本内容
 * @param {number} startPos - 开始查找的位置
 * @param {RegExp} pattern - 正则表达式模式
 * @returns {number} 下一个匹配位置或-1(未找到)
 */
function findNextPatternPos(text, startPos, pattern) {
  const regExp = new RegExp(pattern.source, 'g');
  regExp.lastIndex = startPos;
  const match = regExp.exec(text);
  return match ? match.index : -1;
}

/**
 * 根据文本位置提取合法的HTML片段
 * @param {Object} $ - Cheerio实例
 * @param {string} contentText - 纯文本内容
 * @param {number} startPos - 开始位置
 * @param {number} endPos - 结束位置
 * @returns {string} 合法的HTML片段
 */
function extractCleanHTML($, contentText, startPos, endPos) {
  if (startPos >= endPos) return '';
  
  // 获取文本片段
  const textSegment = contentText.substring(startPos, endPos);
  
  // 查找包含该文本片段的元素
  const result = [];
  $('#content').find('*').each(function() {
    const element = $(this);
    const elementText = element.text();
    
    if (elementText.includes(textSegment) || textSegment.includes(elementText)) {
      // 找到完全匹配或部分匹配的元素
      const html = element.html();
      if (html && html.trim() !== '') {
        result.push({
          element: element,
          html: element.prop('outerHTML'),
          text: elementText
        });
      }
    }
  });
  
  // 找到最精确匹配的元素
  result.sort((a, b) => {
    // 优先选择完全包含文本片段的最小元素
    const aContains = a.text.includes(textSegment);
    const bContains = b.text.includes(textSegment);
    
    if (aContains && !bContains) return -1;
    if (!aContains && bContains) return 1;
    
    // 如果都包含或都不包含，选择文本长度更接近的
    return Math.abs(a.text.length - textSegment.length) - Math.abs(b.text.length - textSegment.length);
  });
  
  // 如果找到匹配的元素，返回其HTML
  if (result.length > 0) {
    return result[0].html;
  }
  
  // 如果没找到精确匹配，创建一个新的span元素包装文本
  return `<span>${textSegment}</span>`;
}

/**
 * 将提取的题目转换为JSON格式并保存
 * @param {Array} questions - 题目数组
 * @param {string} outputPath - 输出路径
 */
function saveQuestionsToJSON(questions, outputPath) {
  const jsonContent = JSON.stringify(questions, null, 2);
  fs.writeFileSync(outputPath, jsonContent, 'utf8');
  console.log(`已提取 ${questions.length} 个题目并保存到 ${outputPath}`);
}

/**
 * 主函数：处理HTML文件并提取题目
 * @param {string} inputPath - 输入HTML文件路径
 * @param {string} outputPath - 输出JSON文件路径
 */
function processHTMLFile(inputPath, outputPath) {
  try {
    const htmlContent = fs.readFileSync(inputPath, 'utf8');
    const questions = extractQuestions(htmlContent);
    saveQuestionsToJSON(questions, outputPath);
    return questions;
  } catch (error) {
    console.error('处理文件时出错:', error);
    return [];
  }
}

// 导出函数，便于在其他模块中使用
module.exports = {
  extractQuestions,
  processHTMLFile
};

// 示例使用
if (require.main === module) {
  // 如果直接运行此脚本
//   const exampleHTML = `
//  <p> <span style="font-family: '宋体'; font-size: 11pt">2. 下列关于"四不伤害"的内容，说法错误的是( )。</span> </p><p> <span style="font-family: '宋体'; font-size: 11pt">A. 不伤害他人 B. 不伤害自己</span> </p><p> <span style="font-family: '宋体'; font-size: 11pt">C. 不被别人伤害 D. 不被自己伤害</span> </p><p> <span style="font-family: '宋体'; font-size: 11pt">答案：D</span> </p>
//   `;
  const exampleHTML = fs.readFileSync('questions.html', 'utf8');

  const questions = extractQuestions(exampleHTML);
  console.log(JSON.stringify(questions, null, 2));
  
  // 如果提供了命令行参数，则处理文件
  if (process.argv.length >= 4) {
    const inputFile = process.argv[2];
    const outputFile = process.argv[3];
    processHTMLFile(inputFile, outputFile);
  }
}