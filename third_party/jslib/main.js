const fs = require('fs');
const QuestionExtractor = require('./question-extractor.js');

// 从文件读取HTML
const htmlContent = fs.readFileSync('questions.html', 'utf8');

// 解析题目
const result = QuestionExtractor.parseQuestions(htmlContent);

// 输出JSON格式的题目
console.log(JSON.stringify(result.json, null, 2));

// 保存为HTML文件
fs.writeFileSync('parsed-questions.html', QuestionExtractor.exportAsHTML(result), 'utf8');
