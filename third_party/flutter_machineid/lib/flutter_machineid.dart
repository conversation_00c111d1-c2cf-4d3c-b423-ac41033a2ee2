
import 'flutter_machineid_platform_interface.dart';

/// Flutter plugin for getting unique machine identifiers.
///
/// This plugin provides the same functionality as the original machineid library
/// by <PERSON><PERSON><PERSON><PERSON><PERSON>, returning OS native machine UUIDs/GUIDs that are stable
/// across reboots and usually persist through OS updates.
///
/// The plugin supports:
/// - Windows: Uses MachineGuid from registry
/// - macOS: Uses IOPlatformUUID
/// - Linux: Uses /var/lib/dbus/machine-id or /etc/machine-id
///
/// For security, use [protectedID] instead of [id] to get a cryptographically
/// secure hash of the machine ID.
class FlutterMachineid {
  /// Returns the platform specific machine id of the current host OS.
  ///
  /// The returned ID should be considered "confidential" and you should
  /// consider using [protectedID] instead for security.
  ///
  /// Returns null if the machine ID cannot be determined.
  static Future<String?> get id async {
    return FlutterMachineidPlatform.instance.getId();
  }

  /// Returns a hashed version of the machine ID in a cryptographically secure way,
  /// using a fixed, application-specific key.
  ///
  /// Internally, this function calculates HMAC-SHA256 of the application ID,
  /// keyed by the machine ID.
  ///
  /// [appId] should be a unique identifier for your application.
  ///
  /// Returns null if the machine ID cannot be determined.
  static Future<String?> protectedID(String appId) async {
    return FlutterMachineidPlatform.instance.getProtectedId(appId);
  }

  /// Legacy method for backward compatibility.
  /// Use [id] instead.
  @Deprecated('Use FlutterMachineid.id instead')
  Future<String?> getPlatformVersion() {
    return FlutterMachineidPlatform.instance.getPlatformVersion();
  }
}
