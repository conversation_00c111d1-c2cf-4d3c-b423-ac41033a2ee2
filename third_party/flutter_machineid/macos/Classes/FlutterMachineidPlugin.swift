import Cocoa
import FlutterMacOS
import Foundation
import CommonCrypto

public class FlutterMachineidPlugin: NSObject, FlutterPlugin {
  public static func register(with registrar: FlutterPluginRegistrar) {
    let channel = FlutterMethodChannel(name: "flutter_machineid", binaryMessenger: registrar.messenger)
    let instance = FlutterMachineidPlugin()
    registrar.addMethodCallDelegate(instance, channel: channel)
  }

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    switch call.method {
    case "getId":
      getMachineId(result: result)
    case "getProtectedId":
      guard let args = call.arguments as? [String: Any],
            let appId = args["appId"] as? String else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "appId parameter is required", details: nil))
        return
      }
      getProtectedId(appId: appId, result: result)
    case "getPlatformVersion":
      result("macOS " + ProcessInfo.processInfo.operatingSystemVersionString)
    default:
      result(FlutterMethodNotImplemented)
    }
  }

  private func getMachineId(result: @escaping FlutterResult) {
    let task = Process()
    task.launchPath = "/usr/sbin/ioreg"
    task.arguments = ["-rd1", "-c", "IOPlatformExpertDevice"]

    let pipe = Pipe()
    task.standardOutput = pipe
    task.standardError = Pipe()

    do {
      try task.run()
      task.waitUntilExit()

      let data = pipe.fileHandleForReading.readDataToEndOfFile()
      let output = String(data: data, encoding: .utf8) ?? ""

      if let machineId = extractIOPlatformUUID(from: output) {
        result(machineId.trimmingCharacters(in: .whitespacesAndNewlines))
      } else {
        result(FlutterError(code: "MACHINE_ID_ERROR", message: "Failed to extract IOPlatformUUID from ioreg output", details: output))
      }
    } catch {
      result(FlutterError(code: "MACHINE_ID_ERROR", message: "Failed to run ioreg command", details: error.localizedDescription))
    }
  }

  private func extractIOPlatformUUID(from output: String) -> String? {
    let lines = output.components(separatedBy: .newlines)
    for line in lines {
      if line.contains("IOPlatformUUID") {
        let parts = line.components(separatedBy: "\" = \"")
        if parts.count == 2 {
          return parts[1].trimmingCharacters(in: CharacterSet(charactersIn: "\""))
        }
      }
    }
    return nil
  }

  private func getProtectedId(appId: String, result: @escaping FlutterResult) {
    getMachineIdString { machineId in
      guard let machineId = machineId else {
        result(FlutterError(code: "MACHINE_ID_ERROR", message: "Failed to retrieve machine ID", details: nil))
        return
      }

      let protectedId = self.calculateHMAC(key: machineId, data: appId)
      result(protectedId)
    }
  }

  private func getMachineIdString(completion: @escaping (String?) -> Void) {
    let task = Process()
    task.launchPath = "/usr/sbin/ioreg"
    task.arguments = ["-rd1", "-c", "IOPlatformExpertDevice"]

    let pipe = Pipe()
    task.standardOutput = pipe
    task.standardError = Pipe()

    do {
      try task.run()
      task.waitUntilExit()

      let data = pipe.fileHandleForReading.readDataToEndOfFile()
      let output = String(data: data, encoding: .utf8) ?? ""

      if let machineId = extractIOPlatformUUID(from: output) {
        completion(machineId.trimmingCharacters(in: .whitespacesAndNewlines))
      } else {
        completion(nil)
      }
    } catch {
      completion(nil)
    }
  }

  private func calculateHMAC(key: String, data: String) -> String {
    let keyData = Data(key.utf8)
    let dataData = Data(data.utf8)

    var result = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))

    keyData.withUnsafeBytes { keyBytes in
      dataData.withUnsafeBytes { dataBytes in
        CCHmac(CCHmacAlgorithm(kCCHmacAlgSHA256),
               keyBytes.bindMemory(to: UInt8.self).baseAddress, keyData.count,
               dataBytes.bindMemory(to: UInt8.self).baseAddress, dataData.count,
               &result)
      }
    }

    return result.map { String(format: "%02x", $0) }.joined()
  }
}
