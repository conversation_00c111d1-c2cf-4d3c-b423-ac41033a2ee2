#include "flutter_machineid_plugin.h"

// This must be included before many other Windows headers.
#include <windows.h>

// For getPlatformVersion; remove unless needed for your plugin implementation.
#include <VersionHelpers.h>

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>

#include <memory>
#include <sstream>
#include <string>
#include <vector>
#include <iomanip>

// For HMAC-SHA256
#include <wincrypt.h>
#pragma comment(lib, "crypt32.lib")
#pragma comment(lib, "advapi32.lib")

namespace flutter_machineid {

// Helper function to get machine ID from Windows registry
std::string GetMachineId() {
  HKEY hKey;
  LONG result = RegOpenKeyExA(HKEY_LOCAL_MACHINE,
                              "SOFTWARE\\Microsoft\\Cryptography",
                              0,
                              KEY_READ | KEY_WOW64_64KEY,
                              &hKey);

  if (result != ERROR_SUCCESS) {
    return "";
  }

  DWORD dataSize = 0;
  result = RegQueryValueExA(hKey, "MachineGuid", NULL, NULL, NULL, &dataSize);

  if (result != ERROR_SUCCESS || dataSize == 0) {
    RegCloseKey(hKey);
    return "";
  }

  std::vector<char> buffer(dataSize);
  result = RegQueryValueExA(hKey, "MachineGuid", NULL, NULL,
                           reinterpret_cast<LPBYTE>(buffer.data()), &dataSize);

  RegCloseKey(hKey);

  if (result != ERROR_SUCCESS) {
    return "";
  }

  // Remove null terminator if present
  std::string machineId(buffer.data());
  return machineId;
}

// Helper function to trim whitespace
std::string Trim(const std::string& str) {
  size_t start = str.find_first_not_of(" \t\n\r");
  if (start == std::string::npos) return "";

  size_t end = str.find_last_not_of(" \t\n\r");
  return str.substr(start, end - start + 1);
}

// Helper function to convert bytes to hex string
std::string BytesToHex(const std::vector<BYTE>& bytes) {
  std::ostringstream oss;
  oss << std::hex << std::setfill('0');
  for (BYTE b : bytes) {
    oss << std::setw(2) << static_cast<int>(b);
  }
  return oss.str();
}

// Helper function to calculate HMAC-SHA256
std::string CalculateHMAC(const std::string& key, const std::string& data) {
  HCRYPTPROV hProv = 0;
  HCRYPTHASH hHash = 0;
  HCRYPTKEY hKey = 0;

  // Acquire cryptographic provider
  if (!CryptAcquireContext(&hProv, NULL, NULL, PROV_RSA_AES, CRYPT_VERIFYCONTEXT)) {
    return "";
  }

  // Create hash object
  if (!CryptCreateHash(hProv, CALG_HMAC, 0, 0, &hHash)) {
    CryptReleaseContext(hProv, 0);
    return "";
  }

  // Set HMAC algorithm to SHA256
  HMAC_INFO hmacInfo = {0};
  hmacInfo.HashAlgid = CALG_SHA_256;
  if (!CryptSetHashParam(hHash, HP_HMAC_INFO, (BYTE*)&hmacInfo, 0)) {
    CryptDestroyHash(hHash);
    CryptReleaseContext(hProv, 0);
    return "";
  }

  // Set the key
  if (!CryptHashData(hHash, (BYTE*)key.c_str(), key.length(), 0)) {
    CryptDestroyHash(hHash);
    CryptReleaseContext(hProv, 0);
    return "";
  }

  // Hash the data
  if (!CryptHashData(hHash, (BYTE*)data.c_str(), data.length(), 0)) {
    CryptDestroyHash(hHash);
    CryptReleaseContext(hProv, 0);
    return "";
  }

  // Get hash size
  DWORD hashSize = 0;
  DWORD hashSizeSize = sizeof(DWORD);
  if (!CryptGetHashParam(hHash, HP_HASHSIZE, (BYTE*)&hashSize, &hashSizeSize, 0)) {
    CryptDestroyHash(hHash);
    CryptReleaseContext(hProv, 0);
    return "";
  }

  // Get hash value
  std::vector<BYTE> hashValue(hashSize);
  if (!CryptGetHashParam(hHash, HP_HASHVAL, hashValue.data(), &hashSize, 0)) {
    CryptDestroyHash(hHash);
    CryptReleaseContext(hProv, 0);
    return "";
  }

  // Clean up
  CryptDestroyHash(hHash);
  CryptReleaseContext(hProv, 0);

  return BytesToHex(hashValue);
}

// static
void FlutterMachineidPlugin::RegisterWithRegistrar(
    flutter::PluginRegistrarWindows *registrar) {
  auto channel =
      std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
          registrar->messenger(), "flutter_machineid",
          &flutter::StandardMethodCodec::GetInstance());

  auto plugin = std::make_unique<FlutterMachineidPlugin>();

  channel->SetMethodCallHandler(
      [plugin_pointer = plugin.get()](const auto &call, auto result) {
        plugin_pointer->HandleMethodCall(call, std::move(result));
      });

  registrar->AddPlugin(std::move(plugin));
}

FlutterMachineidPlugin::FlutterMachineidPlugin() {}

FlutterMachineidPlugin::~FlutterMachineidPlugin() {}

void FlutterMachineidPlugin::HandleMethodCall(
    const flutter::MethodCall<flutter::EncodableValue> &method_call,
    std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {

  if (method_call.method_name().compare("getId") == 0) {
    std::string machineId = GetMachineId();
    if (machineId.empty()) {
      result->Error("MACHINE_ID_ERROR", "Failed to retrieve machine ID");
    } else {
      result->Success(flutter::EncodableValue(Trim(machineId)));
    }
  }
  else if (method_call.method_name().compare("getProtectedId") == 0) {
    const auto* arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    if (!arguments) {
      result->Error("INVALID_ARGUMENTS", "Arguments must be a map");
      return;
    }

    auto appId_it = arguments->find(flutter::EncodableValue("appId"));
    if (appId_it == arguments->end()) {
      result->Error("MISSING_APP_ID", "appId parameter is required");
      return;
    }

    const auto* appId = std::get_if<std::string>(&appId_it->second);
    if (!appId) {
      result->Error("INVALID_APP_ID", "appId must be a string");
      return;
    }

    std::string machineId = GetMachineId();
    if (machineId.empty()) {
      result->Error("MACHINE_ID_ERROR", "Failed to retrieve machine ID");
    } else {
      std::string protectedId = CalculateHMAC(Trim(machineId), *appId);
      if (protectedId.empty()) {
        result->Error("HMAC_ERROR", "Failed to calculate HMAC");
      } else {
        result->Success(flutter::EncodableValue(protectedId));
      }
    }
  }
  else if (method_call.method_name().compare("getPlatformVersion") == 0) {
    std::ostringstream version_stream;
    version_stream << "Windows ";
    if (IsWindows10OrGreater()) {
      version_stream << "10+";
    } else if (IsWindows8OrGreater()) {
      version_stream << "8";
    } else if (IsWindows7OrGreater()) {
      version_stream << "7";
    }
    result->Success(flutter::EncodableValue(version_stream.str()));
  }
  else {
    result->NotImplemented();
  }
}

}  // namespace flutter_machineid
