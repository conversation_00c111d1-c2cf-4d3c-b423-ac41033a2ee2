#ifndef FLUTTER_PLUGIN_FLUTTER_MACHINEID_PLUGIN_H_
#define FLUTTER_PLUGIN_FLUTTER_MACHINEID_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace flutter_machineid {

class FlutterMachineidPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  FlutterMachineidPlugin();

  virtual ~FlutterMachineidPlugin();

  // Disallow copy and assign.
  FlutterMachineidPlugin(const FlutterMachineidPlugin&) = delete;
  FlutterMachineidPlugin& operator=(const FlutterMachineidPlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace flutter_machineid

#endif  // FLUTTER_PLUGIN_FLUTTER_MACHINEID_PLUGIN_H_
