#include "include/flutter_machineid/flutter_machineid_plugin.h"

#include <flutter_linux/flutter_linux.h>
#include <gtk/gtk.h>
#include <sys/utsname.h>

#include <cstring>
#include <fstream>
#include <string>
#include <algorithm>
#include <sstream>
#include <iomanip>

// For HMAC-SHA256
#include <openssl/hmac.h>
#include <openssl/sha.h>

#include "flutter_machineid_plugin_private.h"

#define FLUTTER_MACHINEID_PLUGIN(obj) \
  (G_TYPE_CHECK_INSTANCE_CAST((obj), flutter_machineid_plugin_get_type(), \
                              FlutterMachineidPlugin))

struct _FlutterMachineidPlugin {
  GObject parent_instance;
};

G_DEFINE_TYPE(FlutterMachineidPlugin, flutter_machineid_plugin, g_object_get_type())

// Helper function to trim whitespace
static std::string trim(const std::string& str) {
  size_t start = str.find_first_not_of(" \t\n\r");
  if (start == std::string::npos) return "";

  size_t end = str.find_last_not_of(" \t\n\r");
  return str.substr(start, end - start + 1);
}

// Helper function to read file content
static std::string read_file(const std::string& filename) {
  std::ifstream file(filename);
  if (!file.is_open()) {
    return "";
  }

  std::string content;
  std::getline(file, content);
  file.close();

  return content;
}

// Helper function to get machine ID from Linux
static std::string get_machine_id() {
  const char* dbus_path = "/var/lib/dbus/machine-id";
  const char* dbus_path_etc = "/etc/machine-id";

  std::string id = read_file(dbus_path);
  if (id.empty()) {
    id = read_file(dbus_path_etc);
  }

  return trim(id);
}

// Helper function to convert bytes to hex string
static std::string bytes_to_hex(const unsigned char* bytes, size_t length) {
  std::ostringstream oss;
  oss << std::hex << std::setfill('0');
  for (size_t i = 0; i < length; ++i) {
    oss << std::setw(2) << static_cast<int>(bytes[i]);
  }
  return oss.str();
}

// Helper function to calculate HMAC-SHA256
static std::string calculate_hmac(const std::string& key, const std::string& data) {
  unsigned char result[SHA256_DIGEST_LENGTH];
  unsigned int result_len = 0;

  HMAC(EVP_sha256(),
       key.c_str(), key.length(),
       reinterpret_cast<const unsigned char*>(data.c_str()), data.length(),
       result, &result_len);

  return bytes_to_hex(result, result_len);
}

// Called when a method call is received from Flutter.
static void flutter_machineid_plugin_handle_method_call(
    FlutterMachineidPlugin* self,
    FlMethodCall* method_call) {
  g_autoptr(FlMethodResponse) response = nullptr;

  const gchar* method = fl_method_call_get_name(method_call);

  if (strcmp(method, "getId") == 0) {
    std::string machine_id = get_machine_id();
    if (machine_id.empty()) {
      response = FL_METHOD_RESPONSE(fl_method_error_response_new(
          "MACHINE_ID_ERROR", "Failed to retrieve machine ID", nullptr));
    } else {
      g_autoptr(FlValue) result = fl_value_new_string(machine_id.c_str());
      response = FL_METHOD_RESPONSE(fl_method_success_response_new(result));
    }
  } else if (strcmp(method, "getProtectedId") == 0) {
    FlValue* args = fl_method_call_get_args(method_call);
    if (fl_value_get_type(args) != FL_VALUE_TYPE_MAP) {
      response = FL_METHOD_RESPONSE(fl_method_error_response_new(
          "INVALID_ARGUMENTS", "Arguments must be a map", nullptr));
    } else {
      FlValue* app_id_value = fl_value_lookup_string(args, "appId");
      if (app_id_value == nullptr || fl_value_get_type(app_id_value) != FL_VALUE_TYPE_STRING) {
        response = FL_METHOD_RESPONSE(fl_method_error_response_new(
            "MISSING_APP_ID", "appId parameter is required", nullptr));
      } else {
        const gchar* app_id = fl_value_get_string(app_id_value);
        std::string machine_id = get_machine_id();
        if (machine_id.empty()) {
          response = FL_METHOD_RESPONSE(fl_method_error_response_new(
              "MACHINE_ID_ERROR", "Failed to retrieve machine ID", nullptr));
        } else {
          std::string protected_id = calculate_hmac(machine_id, std::string(app_id));
          g_autoptr(FlValue) result = fl_value_new_string(protected_id.c_str());
          response = FL_METHOD_RESPONSE(fl_method_success_response_new(result));
        }
      }
    }
  } else if (strcmp(method, "getPlatformVersion") == 0) {
    response = get_platform_version();
  } else {
    response = FL_METHOD_RESPONSE(fl_method_not_implemented_response_new());
  }

  fl_method_call_respond(method_call, response, nullptr);
}

FlMethodResponse* get_platform_version() {
  struct utsname uname_data = {};
  uname(&uname_data);
  g_autofree gchar *version = g_strdup_printf("Linux %s", uname_data.version);
  g_autoptr(FlValue) result = fl_value_new_string(version);
  return FL_METHOD_RESPONSE(fl_method_success_response_new(result));
}

static void flutter_machineid_plugin_dispose(GObject* object) {
  G_OBJECT_CLASS(flutter_machineid_plugin_parent_class)->dispose(object);
}

static void flutter_machineid_plugin_class_init(FlutterMachineidPluginClass* klass) {
  G_OBJECT_CLASS(klass)->dispose = flutter_machineid_plugin_dispose;
}

static void flutter_machineid_plugin_init(FlutterMachineidPlugin* self) {}

static void method_call_cb(FlMethodChannel* channel, FlMethodCall* method_call,
                           gpointer user_data) {
  FlutterMachineidPlugin* plugin = FLUTTER_MACHINEID_PLUGIN(user_data);
  flutter_machineid_plugin_handle_method_call(plugin, method_call);
}

void flutter_machineid_plugin_register_with_registrar(FlPluginRegistrar* registrar) {
  FlutterMachineidPlugin* plugin = FLUTTER_MACHINEID_PLUGIN(
      g_object_new(flutter_machineid_plugin_get_type(), nullptr));

  g_autoptr(FlStandardMethodCodec) codec = fl_standard_method_codec_new();
  g_autoptr(FlMethodChannel) channel =
      fl_method_channel_new(fl_plugin_registrar_get_messenger(registrar),
                            "flutter_machineid",
                            FL_METHOD_CODEC(codec));
  fl_method_channel_set_method_call_handler(channel, method_call_cb,
                                            g_object_ref(plugin),
                                            g_object_unref);

  g_object_unref(plugin);
}
