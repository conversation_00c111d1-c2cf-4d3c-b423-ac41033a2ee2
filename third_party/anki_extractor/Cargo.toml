[package]
name = "anki_extractor"
version = "0.1.0"
edition = "2021"

[dependencies]
# ... clap, rusqlite, serde, serde_json, regex, tempfile, anyhow, thiserror, sanitize-filename ...
clap = { version = "4.4", features = ["derive"] }
zip = "0.6"
rusqlite = { version = "0.36.0", features = ["bundled"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
regex = "1.10"
tempfile = "3.8"
anyhow = "1.0"
thiserror = "1.0"
sanitize-filename = "0.5.0"

# 新增依赖
tar = "0.4"
zstd = "0.13"
