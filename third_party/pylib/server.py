import argparse
import asyncio
import json
import traceback
import uuid
import os
import inspect
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from fastapi import FastAPI, UploadFile, File, BackgroundTasks, HTTPException, Query, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sse_starlette.sse import EventSourceResponse
from pydantic import BaseModel, Field
import uvicorn
from loguru import logger
from src import (
    convert_pdf2img, convert_anydoc2pdf, convert_pdf2docx,
    resize_pdf_by_dim, resize_pdf_by_scale, resize_pdf_by_paper_size,
    expand_pdf_by_blank, expand_pdf_by_file,
    split_pdf_by_chunk, split_pdf_by_page, split_pdf_by_toc,
    recover_permission_pdf,
    cut_pdf_by_grid, cut_pdf_by_breakpoints, cut_pdf_by_page,
    combine_pdf_by_grid,
    reorder_pdf,
    crop_pdf_by_bbox, crop_pdf_by_page_margin, crop_pdf_by_rect_annot,
    merge_pdf, merge_pdf_by_page,
    add_doc_background_by_pdf,
    insert_pdf,
    set_metadata,
    extract_annotations_from_pdf,
    text_to_speech,
    detect_watermark_by_type,
    remove_watermark_by_type,
    remove_watermark_by_content,
    remove_watermark_by_image,
    remove_watermark_by_text,
    remove_watermark_by_path,
    remove_watermark_by_pixel,
    detect_watermark_by_content,
    detect_watermark_by_image,
    detect_watermark_by_path,
)
from src.extract import extract_images_from_pdf, extract_images_as_pages
from src.utils import ProgressReporter

# Pydantic模型定义
class TaskProgress(BaseModel):
    current: float = 0.0
    total: float = 100.0
    message: str = ""

class TaskResponse(BaseModel):
    task_id: str
    status: str

class TaskStatus(BaseModel):
    status: str
    progress: TaskProgress
    result: Optional[Any] = None
    error: Optional[str] = None
    operation: str

# 通用任务请求模型
class TaskRequest(BaseModel):
    operation: str = Field(..., description="操作类型")
    params: Dict[str, Any] = Field(default_factory=dict, description="操作参数")

app = FastAPI(
    title="PDF Guru API", 
    description="PDF处理API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 存储任务状态和结果
tasks: Dict[str, Dict[str, Any]] = {}

# 进度回调函数 - 用于更新任务状态
def update_task_progress(task_id: str, current: float, total: float, message: str, status: str = None):
    """更新任务进度信息"""
    if task_id not in tasks:
        return
        
    # 如果提供了状态参数，则直接使用
    if status is None:
        status = "processing" if current < total else "completed"
    
    # 更新任务信息，使用新的进度格式
    tasks[task_id].update({
        "status": status,
        "message": message,
        "data": {
            "current": current,
            "total": total
        }
    })

    # 如果是完成状态，确保设置进度为100%
    if status == "completed" and current < total:
        tasks[task_id]["data"]["current"] = total

# 初始化进度报告器
def initialize_progress_reporter():
    """初始化进度报告器单例"""
    # 获取进度报告器单例
    progress_reporter = ProgressReporter.get_instance()
    
    # 设置任务更新回调函数
    progress_reporter.set_update_callback(update_task_progress)
    
    return progress_reporter

# 获取进度报告器实例
progress_reporter = initialize_progress_reporter()

# 任务执行函数
async def run_task(task_id: str, task_func, **kwargs):
    """后台执行任务并更新状态"""
    try:
        # 设置当前任务ID
        progress_reporter.set_current_task_id(task_id)
        
        # 执行任务前更新状态为processing
        update_task_progress(task_id, 0, 100, "开始处理", status="processing")
        
        # 过滤参数，只保留函数所需的参数
        filtered_kwargs = filter_function_params(task_func, kwargs)
        
        # 执行任务 - 检查函数是否为异步函数
        if asyncio.iscoroutinefunction(task_func):
            # 如果是异步函数，使用await调用
            result = await task_func(**filtered_kwargs)
        else:
            # 如果是同步函数，直接调用
            result = task_func(**filtered_kwargs)
        
        # 如果任务没有通过progress_reporter报告完成状态，则在这里更新
        if tasks[task_id]["status"] != "completed":
            update_task_progress(task_id, 100, 100, "完成", status="completed")
            tasks[task_id]["result"] = result
        elif "result" not in tasks[task_id] or tasks[task_id]["result"] is None:
            # 如果已报告完成但没有结果，则更新结果
            tasks[task_id]["result"] = result
            
    except Exception as e:
        logger.error(f"任务执行失败: {e}")
        logger.error(traceback.format_exc())
        tasks[task_id].update({
            "status": "error",
            "message": f"错误: {str(e)}",
            "error": str(e),
            "data": {"current": 0, "total": 100},
            "traceback": traceback.format_exc()
        })
    finally:
        # 清除当前任务ID
        progress_reporter.set_current_task_id(None)

def filter_function_params(func, params):
    """过滤参数，只保留函数所需的参数"""
    sig = inspect.signature(func)
    valid_params = {}
    
    # 获取函数的参数列表
    func_params = list(sig.parameters.keys())
    
    # 只保留函数所需的参数
    for key, value in params.items():
        if key in func_params:
            valid_params[key] = value
    
    return valid_params

# SSE事件流
async def event_generator(task_id: str):
    """为指定任务ID生成SSE事件流"""
    prev_status = None
    prev_data = None
    
    while True:
        if task_id not in tasks:
            yield json.dumps({"event": "error", "data": {"message": "任务不存在"}})
            break
        
        task_info = tasks[task_id]
        current_status = task_info["status"]
        current_data = task_info.get("data", {"current": 0, "total": 100})
        current_message = task_info.get("message", "")
        
        # 状态或进度发生变化时发送事件
        if (current_status != prev_status or 
            current_data.get("current") != prev_data.get("current") if prev_data else True or
            current_status == "processing"):
            
            # 构建符合指定格式的数据
            event_data = {
                "status": current_status,
                "message": current_message,
                "data": current_data,
            }
            
            # 添加结果或错误信息
            if current_status == "completed" and "result" in task_info:
                event_data["result"] = task_info["result"]
            elif current_status == "error" and "error" in task_info:
                event_data["error"] = task_info["error"]
                
            yield json.dumps({"event": current_status, "data": event_data})
            prev_status = current_status
            prev_data = current_data
            
        if current_status in ["completed", "error"]:
            break
            
        await asyncio.sleep(0.1)  # 减少轮询间隔，提高响应速度

# API路由
@app.get("/", summary="API健康检查")
async def read_root():
    """检查API服务是否正常运行"""
    return {"message": "PDF Guru API服务正在运行", "status": "ok"}

@app.post("/task", response_model=TaskResponse, summary="创建处理任务")
async def create_task(
    background_tasks: BackgroundTasks, 
    task_request: TaskRequest
):
    """
    创建一个新的PDF处理任务
    
    - **operation**: 任务类型，可选值：
        - pdf2img: PDF转图片
        - epub2pdf: EPUB转PDF
        - mobi2pdf: MOBI转PDF
        - pdf2docx: PDF转DOCX
        - pdf_merge: 合并PDF
        - pdf_scale: 调整PDF大小
        - pdf_expand: 扩展PDF页面
        - pdf_split: 拆分PDF
        - pdf_crop: 裁剪PDF
        - pdf_recover_permission: 恢复PDF权限
        - pdf_cut: 切割PDF
        - pdf_reorder: 重排PDF页面
        - pdf_combine: 组合PDF页面
        - pdf_background: 添加PDF背景
        - pdf_insert: 插入PDF页面
        - pdf_meta: 设置PDF元数据
        - extract_annotations: 提取PDF注释
        - pdf_extract_images: 提取PDF中的图片
        - pdf_extract_pages_as_images: 将PDF页面转换为图片
    
    - **params**: 根据操作类型不同而不同的参数
    """
    operation = task_request.operation
    params = task_request.params
    
    task_id = str(uuid.uuid4())
    tasks[task_id] = {"status": "pending", "operation": operation}
    
    # 根据操作类型选择相应的处理函数
    task_funcs = {
        "pdf2img": convert_pdf2img,
        "epub2pdf": convert_anydoc2pdf,
        "mobi2pdf": convert_anydoc2pdf,
        "pdf2docx": convert_pdf2docx,
        "pdf_merge": merge_pdf if params.get("type") == "file" else merge_pdf_by_page,
        "pdf_scale": {
            "custom": resize_pdf_by_dim,
            "ratio": resize_pdf_by_scale,
            "fixed": resize_pdf_by_paper_size
        }.get(params.get("type", "")),
        "pdf_expand": {
            "blank": expand_pdf_by_blank,
            "file": expand_pdf_by_file
        }.get(params.get("type", "")),
        "pdf_split": {
            "uniform": split_pdf_by_chunk,
            "custom": split_pdf_by_page,
            "bookmark": split_pdf_by_toc
        }.get(params.get("type", "")),
        "pdf_crop": {
            "bbox": crop_pdf_by_bbox,
            "margin": crop_pdf_by_page_margin,
            "annotate": crop_pdf_by_rect_annot
        }.get(params.get("type", "")),
        "pdf_recover_permission": recover_permission_pdf,
        "pdf_cut": {
            "grid": cut_pdf_by_grid,
            "page": cut_pdf_by_page,
            "custom": cut_pdf_by_breakpoints
        }.get(params.get("type", "")),
        "pdf_reorder": reorder_pdf,
        "pdf_combine": combine_pdf_by_grid,
        "pdf_background": add_doc_background_by_pdf,
        "pdf_insert": insert_pdf,
        "pdf_meta": set_metadata,
        "extract_annotations": extract_annotations_from_pdf,
        "text_to_speech": text_to_speech,
        "detect_watermark_by_type": detect_watermark_by_type,
        "remove_watermark_by_type": remove_watermark_by_type,
        "detect_watermark_by_content": detect_watermark_by_content,
        "remove_watermark_by_content": remove_watermark_by_content,
        "detect_watermark_by_image": detect_watermark_by_image,
        "remove_watermark_by_image": remove_watermark_by_image,
        "remove_watermark_by_text": remove_watermark_by_text,
        "detect_watermark_by_path": detect_watermark_by_path,
        "remove_watermark_by_path": remove_watermark_by_path,
        "remove_watermark_by_pixel": remove_watermark_by_pixel,
        "pdf_extract_images": extract_images_from_pdf,
        "pdf_extract_pages_as_images": extract_images_as_pages
    }
    
    if operation not in task_funcs or (isinstance(task_funcs[operation], dict) and not task_funcs[operation]):
        raise HTTPException(status_code=400, detail=f"不支持的操作类型: {operation}")
    
    task_func = task_funcs[operation]
    if isinstance(task_func, dict):
        task_func = task_func.get(params.get("type", ""))
        if not task_func:
            raise HTTPException(status_code=400, detail=f"不支持的操作子类型: {params.get('type', '')}")
    
    # 整理任务参数 - 将参数中的短横线替换为下划线
    task_params = {}
    for key, value in params.items():
        if value is not None:
            task_params[key.replace("-", "_")] = value
    # 打印函数名和参数
    logger.info(f"执行任务: {task_func.__name__}")
    logger.info(f"任务参数: {task_params}")
    # 启动后台任务
    background_tasks.add_task(run_task, task_id, task_func, **task_params)
    
    return {"task_id": task_id, "status": "pending"}

@app.get("/task/{task_id}", response_model=TaskStatus, summary="获取任务状态")
async def get_task_status(task_id: str):
    """获取指定任务ID的状态和进度信息"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return tasks[task_id]

@app.get("/task/{task_id}/events", summary="获取任务事件流")
async def task_events(task_id: str):
    """
    获取指定任务ID的SSE事件流，用于实时监控任务进度
    
    返回事件流格式:
    ```
    event: processing
    data: {"status": "processing", "progress": {"current": 50, "total": 100, "message": "正在处理..."}}
    
    event: completed
    data: {"status": "completed", "progress": {"current": 100, "total": 100, "message": "完成"}, "result": {...}}
    ```
    """
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return EventSourceResponse(event_generator(task_id))

@app.delete("/task/{task_id}", summary="删除任务记录")
async def delete_task(task_id: str):
    """删除指定任务ID的任务记录和结果"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    tasks.pop(task_id)
    return {"message": "任务已删除"}

@app.get("/tasks", summary="获取所有任务")
async def list_tasks():
    """获取所有任务的ID和状态"""
    task_list = [{"task_id": tid, "status": task_info["status"], "operation": task_info["operation"]} 
                for tid, task_info in tasks.items()]
    return {"tasks": task_list}

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="PDF Guru API服务")
    parser.add_argument(
        "--host", 
        type=str, 
        default="0.0.0.0", 
        help="指定服务器主机地址"
    )
    parser.add_argument(
        "-p",
        "--port", 
        type=int, 
        default=52026, 
        help="指定服务器端口"
    )
    
    args = parser.parse_args()
    logdir = Path().home() / ".pdf_guru"
    logpath = os.path.join(logdir, "guru_server.log")
    log_config = {
        "version": 1,
        "disable_existing_loggers": True,
        "handlers": {
            "file_handler": {
                "class": "logging.FileHandler",
                "filename": logpath,
                "level": "DEBUG",
            },
        },
        "root": {
            "handlers": ["file_handler"],
            "level": "DEBUG",
        },
    }

    # 启动FastAPI服务器
    logger.info(f"启动PDF Guru API服务于 {args.host}:{args.port}")
    uvicorn.run(app, host=args.host, port=args.port, reload=False, log_config=log_config)