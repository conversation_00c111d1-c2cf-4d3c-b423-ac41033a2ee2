import os
import traceback
from pathlib import Path
import asyncio
import edge_tts
from loguru import logger

async def text_to_speech(
    text       : str,
    voice_name : str ="zh-CN-XiaoxiaoNeural",
    rate       : str ="+0%",
    volume     : str ="+0%",
    pitch      : str ="+0Hz",
    output_path: str = None,
):
    try:
        # 确保文本不为空
        if not text or not text.strip():
            logger.error("输入文本为空")
            return "输入文本为空"
            
        # 确保输出路径存在
        if output_path:
            output_dir = os.path.dirname(output_path)
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        else:
            output_path = os.path.expanduser("~/Desktop/tts_output.wav")
            Path(os.path.dirname(output_path)).mkdir(parents=True, exist_ok=True)
        
        text = text.strip()
        tts = edge_tts.Communicate(
            text   = text,
            voice  = voice_name,
            rate   = rate,
            volume = volume,
            pitch  = pitch,
        )
        
        with open(output_path, "wb") as f:
            async for chunk in tts.stream():
                if chunk["type"] == "audio":
                    f.write(chunk["data"])
        return output_path
    except Exception as e:
        error_msg = f"TTS错误: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        return error_msg
