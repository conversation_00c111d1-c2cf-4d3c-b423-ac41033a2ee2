
import fitz

from .utils import progress_reporter, parse_range, parse_range
from loguru import logger


def insert_pdf(
    *, 
    doc_path: str = "",
    output_path: str = "",
    insert_mode: str = "",
    insert_pos: int = 0,
    pos_type: str = "",
    count: int = 0,
    orientation: str = "",
    paper_size: str = "",
    page_range: str = "",
    doc_path2: str = "",
):
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": 100})
    doc: fitz.Document = fitz.open(doc_path)
    writer: fitz.Document = fitz.open()
    if insert_mode == "blank":
        if paper_size == "same":
            fmt = doc[0].rect
        else:
            fmt = fitz.paper_rect(f"{paper_size}-l") if orientation == "landscape" else fitz.paper_rect(paper_size)
        if pos_type in ['before_first', 'after_first', 'before_last', 'after_last', 'before_page', 'after_page']:
            if pos_type == 'before_first':
                insert_pos = 1
            elif pos_type == 'after_first':
                insert_pos = 2
            elif pos_type == 'before_last':
                insert_pos = doc.page_count
            elif pos_type == 'after_last':
                insert_pos = doc.page_count+1
            elif pos_type == 'before_page':
                pass
            elif pos_type == 'after_page':
                insert_pos = insert_pos + 1
            if insert_pos - 2 >= 0:
                writer.insert_pdf(doc, from_page=0, to_page=insert_pos-2)
            for i in range(count):
                writer.new_page(-1, width=fmt.width, height=fmt.height)
            if insert_pos-1 < doc.page_count:
                writer.insert_pdf(doc, from_page=insert_pos-1, to_page=-1)
        elif pos_type == 'before_odd':
            for i in range(doc.page_count):
                if (i + 1) % 2 == 1:  # Odd page
                    for j in range(count):
                        writer.new_page(-1, width=fmt.width, height=fmt.height)
                writer.insert_pdf(doc, from_page=i, to_page=i)
        elif pos_type == 'before_even':
            for i in range(doc.page_count):
                if (i + 1) % 2 == 0:  # Even page
                    for j in range(count):
                        writer.new_page(-1, width=fmt.width, height=fmt.height)
                writer.insert_pdf(doc, from_page=i, to_page=i)
        elif pos_type == 'before_all':
            for i in range(doc.page_count):
                for j in range(count):
                    writer.new_page(-1, width=fmt.width, height=fmt.height)
                writer.insert_pdf(doc, from_page=i, to_page=i)
        elif pos_type == 'after_all':
            for i in range(doc.page_count):
                writer.insert_pdf(doc, from_page=i, to_page=i)
                for j in range(count):
                    writer.new_page(-1, width=fmt.width, height=fmt.height)
        writer.ez_save(output_path, garbage=4)
    elif insert_mode == "file":
        doc2: fitz.Document = fitz.open(doc_path2)
        page_range_list = []
        # Parse page range
        if page_range:
            page_range_list = parse_range(page_range, doc2.page_count)
        else:
            page_range_list = list(range(doc2.page_count))
            
        if pos_type in ['before_first', 'after_first', 'before_last', 'after_last', 'before_page', 'after_page']:
            if pos_type == 'before_first':
                insert_pos = 1
            elif pos_type == 'after_first':
                insert_pos = 2
            elif pos_type == 'before_last':
                insert_pos = doc.page_count
            elif pos_type == 'after_last':
                insert_pos = doc.page_count+1
            elif pos_type == 'before_page':
                pass
            elif pos_type == 'after_page':
                insert_pos = insert_pos + 1
                
            # Insert pages up to the insert position
            if insert_pos - 2 >= 0:
                writer.insert_pdf(doc, from_page=0, to_page=insert_pos-2)
                
            # Insert pages from the second document
            for page_num in page_range_list:
                writer.insert_pdf(doc2, from_page=page_num, to_page=page_num)
                
            # Insert remaining pages from the original document
            if insert_pos-1 < doc.page_count:
                writer.insert_pdf(doc, from_page=insert_pos-1, to_page=-1)
        elif pos_type == 'before_odd':
            for i in range(doc.page_count):
                if (i + 1) % 2 == 1:  # Odd page
                    for page_num in page_range_list:
                        writer.insert_pdf(doc2, from_page=page_num, to_page=page_num)
                writer.insert_pdf(doc, from_page=i, to_page=i)
        elif pos_type == 'before_even':
            for i in range(doc.page_count):
                if (i + 1) % 2 == 0:  # Even page
                    for page_num in page_range_list:
                        writer.insert_pdf(doc2, from_page=page_num, to_page=page_num)
                writer.insert_pdf(doc, from_page=i, to_page=i)
        elif pos_type == 'before_all':
            for i in range(doc.page_count):
                for page_num in page_range_list:
                    writer.insert_pdf(doc2, from_page=page_num, to_page=page_num)
                writer.insert_pdf(doc, from_page=i, to_page=i)
        elif pos_type == 'after_all':
            for i in range(doc.page_count):
                writer.insert_pdf(doc, from_page=i, to_page=i)
                for page_num in page_range_list:
                    writer.insert_pdf(doc2, from_page=page_num, to_page=page_num)
            
        writer.ez_save(output_path, garbage=4)
    doc.close()
    writer.close()
    if doc_path2:
        doc2 = fitz.open(doc_path2)
        doc2.close()
    progress_reporter("completed", "已完成", output_path)
