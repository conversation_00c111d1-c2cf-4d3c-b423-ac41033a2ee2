import cv2
import numpy as np
import easyocr
import os
from pathlib import Path
from scipy.signal import find_peaks

# 初始化 OCR 阅读器（支持中英文）
reader = easyocr.Reader(['ch_sim', 'en'])

# 读取图像
image_path = '/Users/<USER>/Downloads/Snipaste_2025-04-29_20-52-16.png'
output_block_path = str(Path(image_path).parent / 'output_block_boxes.png')
output_char_path = str(Path(image_path).parent / 'output_char_boxes.png')

# 读取图像
image = cv2.imread(image_path)
if image is None:
    raise ValueError("无法加载图像，请检查路径是否正确。")

# 复制图像用于分别绘制 block 和 char 框
image_for_blocks = image.copy()
image_for_chars = image.copy()

def enhance_image(img):
    """图像增强：CLAHE + 锐化"""
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(gray)
    kernel = np.array([[0, -1, 0],
                       [-1, 5, -1],
                       [0, -1, 0]])
    enhanced = cv2.filter2D(enhanced, -1, kernel)
    return enhanced

gray_enhanced = enhance_image(image)

# OCR 识别结果
results = reader.readtext(image_path)

for detection in results:
    bbox = np.array(detection[0]).astype(np.int32)  # 四个顶点坐标 [[x1,y1], [x2,y2], ...]
    text = detection[1]                             # 识别出的文本
    confidence = detection[2]                       # 置信度

    if not text.strip():
        continue

    print(f"\n原始文本: '{text}' 置信度: {confidence:.2f}")

    # ----------------------------
    # 绘制文本块边框（红色）
    # ----------------------------
    for i in range(4):
        pt1 = tuple(bbox[i])
        pt2 = tuple(bbox[(i + 1) % 4])
        cv2.line(image_for_blocks, pt1, pt2, (0, 0, 255), 2)

    # ----------------------------
    # 提取文本块区域（不旋转矫正！）
    # ----------------------------
    x_coords = [p[0] for p in bbox]
    y_coords = [p[1] for p in bbox]
    x_min, x_max = min(x_coords), max(x_coords)
    y_min, y_max = min(y_coords), max(y_coords)

    block_img = gray_enhanced[y_min:y_max, x_min:x_max]

    # 形态学膨胀（连接粘连字符）
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    binary = cv2.adaptiveThreshold(block_img, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY_INV, 11, 2)
    binary = cv2.dilate(binary, kernel, iterations=1)

    # ----------------------------
    # 方法一：垂直投影 + 峰值检测
    # ----------------------------
    vertical_projection = np.sum(binary, axis=0)
    threshold = np.mean(vertical_projection) + 0.5 * np.std(vertical_projection)
    peaks, _ = find_peaks(-vertical_projection, height=-threshold)

    char_positions = []
    for i in range(len(peaks) - 1):
        start = peaks[i]
        end = peaks[i + 1]
        width = end - start
        if width > 6:
            char_positions.append((start, end))

    # ----------------------------
    # 方法二：连通域分析辅助修正
    # ----------------------------
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary, connectivity=8)
    valid_components = []

    for label in range(1, num_labels):
        x, y, w, h, area = stats[label]
        if area < 50:
            continue
        aspect_ratio = h / w
        if 0.2 <= aspect_ratio <= 5:
            valid_components.append((x, y, w, h))

    # ----------------------------
    # 映射回原图并绘制字符框（精准坐标 + 合并高度）
    # ----------------------------
    used_x = set()
    full_height = y_max - y_min

    for x, y, w, h in valid_components:
        global_x_min = x_min + x
        global_x_max = global_x_min + w
        global_y_min = y_min + y
        global_y_max = global_y_min + h

        # 强制字符高度扩展为 block 高度
        global_y_min = y_min
        global_y_max = y_max

        if w < 6:
            continue

        char_box = [
            (global_x_min, global_y_min),
            (global_x_max, global_y_min),
            (global_x_max, global_y_max),
            (global_x_min, global_y_max)
        ]

        center_x = (global_x_min + global_x_max) // 2
        if any(abs(center_x - cx) < 5 for cx in used_x):
            continue
        used_x.add(center_x)

        for i in range(4):
            pt1 = char_box[i]
            pt2 = char_box[(i + 1) % 4]
            cv2.line(image_for_chars, pt1, pt2, (0, 255, 0), 1)

# ----------------------------
# 保存输出图像
# ----------------------------
cv2.imwrite(output_block_path, image_for_blocks)
cv2.imwrite(output_char_path, image_for_chars)

print(f"\n文本块框已绘制完成，结果已保存至：{os.path.abspath(output_block_path)}")
print(f"字符级框已绘制完成，结果已保存至：{os.path.abspath(output_char_path)}")