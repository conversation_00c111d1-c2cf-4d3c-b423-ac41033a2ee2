import fitz
from .utils import parse_range, progress_reporter, convert_length

def resize_pdf_by_dim(
    *,
    doc_path: str = "", 
    output_path: str = "",
    width: float = 0,
    height: float = 0,
    unit: str = "pt",
    page_range: str = "",
):
    doc: fitz.Document = fitz.open(doc_path)
    writer: fitz.Document = fitz.open()
    width, height = convert_length(width, unit, "pt"), convert_length(height, unit, "pt")
    roi_indices = parse_range(page_range, doc.page_count)
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})

    for idx, i in enumerate(roi_indices):
        progress_reporter("processing", f"正在处理第{i+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
        if i not in roi_indices:
            writer.insert_pdf(doc, from_page=i, to_page=i)
            continue
        page = doc[i]
        new_page: fitz.Page = writer.new_page(width=width, height=height)
        new_page.show_pdf_page(new_page.rect, doc, page.number, rotate=page.rotation)
    # 恢复书签
    toc = doc.get_toc()
    if toc:
        writer.set_toc(toc)
    writer.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)

def resize_pdf_by_scale(
    *, 
    doc_path: str = "",
    output_path: str = "",
    scale: float = 0,
    page_range: str = "",
):
    doc: fitz.Document = fitz.open(doc_path)
    writer: fitz.Document = fitz.open()
    roi_indices = parse_range(page_range, doc.page_count)
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})
    for idx, i in enumerate(roi_indices):
        progress_reporter("processing", f"正在处理第{i+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
        if i not in roi_indices:
            writer.insert_pdf(doc, from_page=i, to_page=i)
            continue
        page = doc[i]
        new_page: fitz.Page = writer.new_page(width=page.rect.width*scale, height=page.rect.height*scale)
        new_page.show_pdf_page(new_page.rect, doc, page.number, rotate=page.rotation)
    # 恢复书签
    toc = doc.get_toc()
    if toc:
        writer.set_toc(toc)
    writer.ez_save(output_path)
    progress_reporter("completed", "已完成", output_path)

def resize_pdf_by_paper_size(
    *, 
    doc_path: str = "",
    output_path: str = "",
    paper_size: str = "",
    page_range: str = "all",
):
    doc: fitz.Document = fitz.open(doc_path)
    writer: fitz.Document = fitz.open()
    roi_indices = parse_range(page_range, doc.page_count)
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})
    for idx, i in enumerate(roi_indices):
        progress_reporter("processing", f"正在处理第{i+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
        if i not in roi_indices:
            writer.insert_pdf(doc, from_page=i, to_page=i)
            continue
        page = doc[i]
        if page.rect.width > page.rect.height:
            fmt = fitz.paper_rect(f"{paper_size}-l")
        else:
            fmt = fitz.paper_rect(f"{paper_size}")
        new_page: fitz.Page = writer.new_page(width=fmt.width, height=fmt.height)
        new_page.show_pdf_page(new_page.rect, doc, page.number, rotate=page.rotation)
    # 恢复书签
    toc = doc.get_toc()
    if toc:
        writer.set_toc(toc)
    writer.ez_save(output_path)
    progress_reporter("completed", "已完成", output_path)

