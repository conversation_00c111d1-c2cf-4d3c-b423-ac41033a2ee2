import os
import traceback
from pathlib import Path
from typing import Tuple

import fitz

from loguru import logger
from .utils import progress_reporter, parse_range, convert_length

def crop_pdf_by_bbox(
    *, 
    doc_path: str = "",
    output_path: str = "",
    bbox: Tuple[int, int, int, int] = (0, 0, 0, 0),
    unit: str = "pt",
    keep_page_size: bool = True,
    keep_annotation: bool = True,
    output_format: str = "pdf",
    dpi: int = 300,
    page_range: str = "all",
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    writer: fitz.Document = fitz.open()
    if unit != "pt":
        bbox = tuple(map(lambda x: convert_length(x, unit, "pt"), bbox))
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})

    for idx, page_index in enumerate(roi_indices):
        progress_reporter("processing", f"正在处理第{idx+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
        page = doc[page_index]
        if not keep_annotation:
            for annot in page.annots():
                page.delete_annot(annot)
        page_width, page_height = page.rect.width, page.rect.height
        if keep_page_size:
            new_page = writer.new_page(-1, width=page_width, height=page_height)
            new_page.show_pdf_page(new_page.rect, doc, page_index, clip=bbox)
        else:
            new_page = writer.new_page(-1, width=bbox[2]-bbox[0], height=bbox[3]-bbox[1])
            new_page.show_pdf_page(new_page.rect, doc, page_index, clip=bbox)
    p = Path(doc_path)
    if output_format == "pdf":
        writer.ez_save(output_path, garbage=4)
    else:
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        for page_index in range(writer.page_count):
            page = writer[page_index]
            # 转图片
            output_path = str(output_dir / f"{p.stem}-裁剪-{page_index+1}.{output_format}")
            if output_format == "png":
                pix = page.get_pixmap(matrix=fitz.Matrix(dpi/72, dpi/72))
                pix.set_dpi(dpi, dpi)
                pix.save(output_path)
            elif output_format == "svg":
                out = page.get_svg_image(matrix=fitz.Matrix(dpi/72, dpi/72), text_as_path=1)
                with open(output_path, "w") as f:
                    f.write(out)
    progress_reporter("completed", "已完成", output_path)

def crop_pdf_by_page_margin(
    *, 
    doc_path: str = "",
    output_path: str = "",
    top: float = 0,
    bottom: float = 0,
    left: float = 0,
    right: float = 0,
    unit: str = "pt",
    keep_page_size: bool = True,
    keep_annotation: bool = True,
    output_format: str = "pdf",
    dpi: int = 300,
    page_range: str = "all",
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    writer: fitz.Document = fitz.open()
    if unit != "pt":
        top = convert_length(top, unit, "pt")
        bottom = convert_length(bottom, unit, "pt")
        left = convert_length(left, unit, "pt")
        right = convert_length(right, unit, "pt")
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})

    for idx, page_index in enumerate(roi_indices):
        progress_reporter("processing", f"正在处理第{idx+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
        page = doc[page_index]
        if not keep_annotation:
            for annot in page.annots():
                page.delete_annot(annot)
        page_rect = page.rect
        x0, y0, x1, y1 = page_rect
        page_width, page_height = page.rect.width, page.rect.height
        bbox = fitz.Rect(
            x0 + left,      # 左上角 x 坐标
            y0 + top,       # 左上角 y 坐标
            x1 - right,     # 右下角 x 坐标
            y1 - bottom     # 右下角 y 坐标
        )
        if keep_page_size:
            new_page = writer.new_page(-1, width=page_width, height=page_height)
            new_page.show_pdf_page(new_page.rect, doc, page_index, clip=bbox)
        else:
            new_page = writer.new_page(-1, width=bbox[2]-bbox[0], height=bbox[3]-bbox[1])
            new_page.show_pdf_page(new_page.rect, doc, page_index, clip=bbox)
    p = Path(doc_path)
    if output_format == "pdf":
        writer.ez_save(output_path, garbage=4)
    else:
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        for page_index in range(writer.page_count):
            page = writer[page_index]
            # 转图片
            output_path = str(output_dir / f"{p.stem}-裁剪-{page_index+1}.{output_format}")
            if output_format == "png":
                pix = page.get_pixmap(matrix=fitz.Matrix(dpi/72, dpi/72))
                pix.set_dpi(dpi, dpi)
                pix.save(output_path)
            elif output_format == "svg":
                out = page.get_svg_image(matrix=fitz.Matrix(dpi/72, dpi/72), text_as_path=1)
                with open(output_path, "w") as f:
                    f.write(out)
    progress_reporter("completed", "已完成", output_path)

def crop_pdf_by_rect_annot(
    *, 
    doc_path: str = "",
    output_path: str = "",
    keep_page_size: bool = True,
    keep_annotation: bool = True,
    expand_mode: bool = False,
    output_format: str = "pdf",
    dpi: int = 300,
    page_range: str = "all",
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    writer: fitz.Document = fitz.open()
    if not expand_mode:
        FLAG = False
        progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})
        for idx, page_index in enumerate(roi_indices):
            progress_reporter("processing", f"正在处理第{idx+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
            page = doc[page_index]
            rect_list = []
            for annot in page.annots():
                if annot.type[0] == 4: # Square
                    rect_list.append(annot.rect)
                    page.delete_annot(annot)
                    continue
                if not keep_annotation:
                    page.delete_annot(annot)
            if rect_list:
                FLAG = True
                logger.debug(f"{rect_list=}")
                page_width, page_height = page.rect.width, page.rect.height
                for rect in rect_list:
                    if keep_page_size:
                        new_page = writer.new_page(-1, width=page_width, height=page_height)
                        new_page.show_pdf_page(new_page.rect, doc, page_index, clip=rect)
                    else:
                        new_page = writer.new_page(-1, width=rect[2]-rect[0], height=rect[3]-rect[1])
                        new_page.show_pdf_page(new_page.rect, doc, page_index, clip=rect)
        if not FLAG:
            raise ValueError("未找到矩形标注!")
    else:
        rect_list = []
        progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})
        for idx, page_index in enumerate(roi_indices):
            progress_reporter("processing", f"正在处理第{idx+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
            page = doc[page_index]
            for annot in page.annots():
                if annot.type[0] == 4:
                    rect_list.append(annot.rect)
                    page.delete_annot(annot)
                    continue
                if not keep_annotation:
                    page.delete_annot(annot)
        if not rect_list:
            raise ValueError("未找到矩形标注!")
        if len(rect_list) > 1:
            raise ValueError("最多只能一个矩形标注!")
        region = rect_list[0]
        bbox = fitz.Rect(region)
        for page_index in roi_indices:
            try:
                page = doc[page_index]
                page_width, page_height = page.rect.width, page.rect.height
                if keep_page_size:
                    new_page = writer.new_page(-1, width=page_width, height=page_height)
                    new_page.show_pdf_page(new_page.rect, doc, page_index, clip=bbox)
                else:
                    new_page = writer.new_page(-1, width=bbox[2]-bbox[0], height=bbox[3]-bbox[1])
                    new_page.show_pdf_page(new_page.rect, doc, page_index, clip=bbox)
            except:
                logger.error(traceback.format_exc())
    p = Path(doc_path)
    if output_format == "pdf":
        writer.ez_save(output_path, garbage=4)
    else:
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        for page_index in range(writer.page_count):
            page = writer[page_index]
            # 转图片
            output_path = str(output_dir / f"{p.stem}-裁剪-{page_index+1}.{output_format}")
            if output_format == "png":
                pix = page.get_pixmap(matrix=fitz.Matrix(dpi/72, dpi/72))
                pix.set_dpi(dpi, dpi)
                pix.save(output_path)
            elif output_format == "svg":
                out = page.get_svg_image(matrix=fitz.Matrix(dpi/72, dpi/72), text_as_path=1)
                with open(output_path, "w") as f:
                    f.write(out)
    progress_reporter("completed", "已完成", output_path)