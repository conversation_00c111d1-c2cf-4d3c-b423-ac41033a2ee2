import os
import fitz  # PyMuPDF
import json
from typing import Dict, List, Any, Optional, Tuple, Union
from .utils import parse_range
from loguru import logger

def map_annot_type(pymupdf_type: int) -> str:
    """
    将PyMuPDF的注释类型映射到Rust端期望的类型
    
    Args:
        pymupdf_type: PyMuPDF的注释类型代码
        
    Returns:
        对应的注释类型字符串
    """
    # PyMuPDF注释类型映射
    # 参考: https://pymupdf.readthedocs.io/en/latest/annot.html#Annot.type
    type_map = {
        fitz.PDF_ANNOT_TEXT: "Text",                  # 文本注释
        fitz.PDF_ANNOT_LINK: "Link",                  # 链接
        fitz.PDF_ANNOT_FREE_TEXT: "FreeText",         # 自由文本
        fitz.PDF_ANNOT_LINE: "Line",                  # 线条
        fitz.PDF_ANNOT_SQUARE: "Square",              # 方形
        fitz.PDF_ANNOT_CIRCLE: "Circle",              # 圆形
        fitz.PDF_ANNOT_POLYGON: "Polygon",            # 多边形
        fitz.PDF_ANNOT_POLY_LINE: "PolyLine",         # 折线
        fitz.PDF_ANNOT_HIGHLIGHT: "Highlight",        # 高亮
        fitz.PDF_ANNOT_UNDERLINE: "Underline",        # 下划线
        fitz.PDF_ANNOT_SQUIGGLY: "Squiggly",          # 波浪线
        fitz.PDF_ANNOT_STRIKE_OUT: "StrikeOut",       # 删除线
        fitz.PDF_ANNOT_STAMP: "Stamp",                # 图章
        fitz.PDF_ANNOT_CARET: "Caret",                # 插入符号
        fitz.PDF_ANNOT_INK: "Ink",                    # 墨迹
        fitz.PDF_ANNOT_POPUP: "Popup",                # 弹出框
        fitz.PDF_ANNOT_FILE_ATTACHMENT: "FileAttachment",  # 文件附件
        fitz.PDF_ANNOT_SOUND: "Sound",                # 声音
        fitz.PDF_ANNOT_MOVIE: "Movie",                # 视频
        fitz.PDF_ANNOT_WIDGET: "Widget",              # 小部件
        fitz.PDF_ANNOT_SCREEN: "Screen",              # 屏幕
        fitz.PDF_ANNOT_PRINTER_MARK: "PrinterMark",   # 打印标记
        fitz.PDF_ANNOT_TRAP_NET: "TrapNet",           # 陷印网络
        fitz.PDF_ANNOT_WATERMARK: "Watermark",        # 水印
        fitz.PDF_ANNOT_3D: "3D",                      # 3D对象
        fitz.PDF_ANNOT_REDACT: "Redact",              # 编辑
    }
    
    return type_map.get(pymupdf_type, f"Other:{pymupdf_type}")

def extract_annotations_from_pdf(
    input_path: str = "",
    page_range: str = "",
    num_cols: int = 1,
    is_full_page_mode: bool = False
) -> List[Dict[str, Any]]:
    """
    Extract annotations from PDF using PyMuPDF.
    
    Args:
        input_path: Path to the PDF file
        page_range: Page range string (e.g. "1-3,5,7-9")
        num_cols: Number of columns to divide the page width (for determining annotation column)
        is_full_page_mode: Whether to add a full page annotation for pages with annotations
        
    Returns:
        List of annotation objects compatible with Rust's Annotation struct
    """
    try:
        # Open the PDF document
        doc = fitz.open(input_path)
        pages = parse_range(page_range, doc.page_count)
        annotations = []
        
        # Keep track of pages with annotations for full-page mode
        pages_with_annotations = set()
        
        # Process each page
        for page_idx in pages:
            # PyMuPDF uses 0-based indexing for pages
            page = doc[page_idx]  
            page_width = page.rect.width
            page_height = page.rect.height
            
            # Convert to 1-based page number for output
            page_num = page_idx + 1
            
            has_annotations = False
            
            # Process annotations on the page
            for annot in page.annots():
                try:
                    # Extract annotation details
                    annot_type = map_annot_type(annot.type[0])
                    
                    # Skip unknown annotation types
                    if annot_type.startswith("Other:"):
                        continue
                    
                    # Get annotation rectangle - PyMuPDF坐标系(左上角为原点)
                    pymupdf_rect = [annot.rect.x0, annot.rect.y0, annot.rect.x1, annot.rect.y1]
                    
                    # 转换为PDF坐标系(左下角为原点)
                    # 只需要将y坐标从页面顶部翻转到页面底部
                    rect = [
                        pymupdf_rect[0],                    # x0保持不变
                        page_height - pymupdf_rect[3],      # y0 = 页高 - y1(PyMuPDF)
                        pymupdf_rect[2],                    # x1保持不变
                        page_height - pymupdf_rect[1]       # y1 = 页高 - y0(PyMuPDF)
                    ]
                    
                    # 判断是否为零宽(高)批注
                    # 计算批注宽度和高度
                    width = abs(rect[2] - rect[0])
                    height = abs(rect[3] - rect[1])
                    if width <= 1.0 or height <= 1.0:
                        continue  # 跳过零宽(高)批注
                    
                    # Extract quad points (for highlights, underlines, etc.)
                    quad_points = None
                    if hasattr(annot, "vertices") and annot.vertices:
                        # Convert vertices to quad points format
                        points = []
                        for v in annot.vertices:
                            # 处理不同类型的vertices
                            if hasattr(v, 'x') and hasattr(v, 'y'):
                                # 对象类型，有x和y属性 - 转换为PDF坐标系
                                points.extend([v.x, page_height - v.y])
                            elif isinstance(v, (list, tuple)) and len(v) >= 2:
                                # 元组或列表类型 - 转换为PDF坐标系
                                points.extend([v[0], page_height - v[1]])
                            else:
                                # 其他情况，跳过
                                continue
                        quad_points = points
                    
                    # Extract color
                    color = None
                    if annot.colors:
                        stroke_color = annot.colors.get("stroke")
                        if stroke_color:
                            # Convert RGB color scale from 0-1 to 0-255
                            color = [c for c in stroke_color[:3]]
                    
                    # Extract text content
                    contents = annot.info.get("content", "")
                    if contents:
                        contents = contents.strip()
                    
                    # Calculate column based on the center x-coordinate
                    center_x = (rect[0] + rect[2]) / 2
                    column_width = page_width / num_cols
                    column = min(max(int((center_x / column_width) + 0.5), 1), num_cols)
                    
                    # Generate a unique ID for the annotation if none exists
                    annot_id = annot.info.get("id") or f"{page_num}-{rect}"
                    
                    # Creation and modification dates
                    creation_date = annot.info.get("creationDate")
                    mod_date = annot.info.get("modDate")
                    
                    # Add annotation to the list
                    annotations.append({
                        "page_num": page_num,
                        "column": column,
                        "annot_type": annot_type,
                        "rect": rect,
                        "quad_points": quad_points,
                        "color": color,
                        "contents": contents if contents else None,
                        "creation_date": creation_date,
                        "mod_date": mod_date,
                        "id": annot_id
                    })
                    
                    has_annotations = True
                except Exception as e:
                    logger.error(f"Error processing annotation: {e}")
                    continue
            
            # Track pages with annotations for full page mode
            if has_annotations:
                pages_with_annotations.add(page_num)
        
        # Add full page annotations if requested
        if is_full_page_mode:
            file_name = os.path.splitext(os.path.basename(input_path))[0]
            
            for page_num in pages_with_annotations:
                page_idx = page_num - 1  # Convert back to 0-based index for PyMuPDF
                page = doc[page_idx]
                
                # PDF坐标系中的全页矩形 (左下角为原点)
                page_rect = [0, 0, page.rect.width, page.rect.height]
                
                annotations.append({
                    "page_num": page_num,
                    "column": 1,
                    "annot_type": "Square",
                    "rect": page_rect,
                    "quad_points": None,
                    "color": None,
                    "contents": None,
                    "creation_date": None,
                    "mod_date": None,
                    "id": f"{file_name}_page{page_num}_fullpage"
                })
        
        doc.close()
        logger.info(f"Extracted {len(annotations)} annotations")
        return annotations
        
    except Exception as e:
        logger.error(f"Failed to extract annotations: {str(e)}")
        raise Exception(f"Failed to extract annotations: {str(e)}")


def delete_annotations(input_path: str = "", page_range: str = "", output_path: str = "", annotation_ids: List[str] = []):
    """
    删除PDF文件中的注释
    
    Args:
        input_path: PDF文件路径
        page_range: 页面范围字符串（例如"1-3,5,7-9"）
        output_path: 输出文件路径，如果为空则覆盖原文件
        annotation_ids: 要删除的注释ID列表，如果为空则删除指定页面的所有注释
        
    Returns:
        None
    """
    try:
        # 打开PDF文档
        doc = fitz.open(input_path)
        
        # 解析页码范围
        pages = parse_range(page_range, doc.page_count)
        
        # 处理每一页
        for page_idx in pages:
            page = doc[page_idx]
            page_num = page_idx + 1  # 转换为1-based页码
            
            if annotation_ids:
                # 如果提供了注释ID，只删除指定ID的注释
                annots_to_delete = []
                for annot in page.annots():
                    # 获取注释ID
                    annot_id = annot.info.get("id")
                    if not annot_id:
                        # 尝试使用创建日期作为ID
                        annot_id = annot.info.get("creationDate")
                    if not annot_id:
                        # 使用矩形坐标作为ID
                        rect = annot.rect
                        annot_id = f"{page_num}-[{rect.x0}, {rect.y0}, {rect.x1}, {rect.y1}]"
                    
                    # 检查是否在要删除的ID列表中
                    if annot_id in annotation_ids:
                        annots_to_delete.append(annot)
                
                # 删除匹配的注释
                for annot in annots_to_delete:
                    page.delete_annot(annot)
            else:
                # 如果没有提供注释ID，删除页面上的所有注释
                # 由于删除注释会改变annots()的结果，我们需要先收集所有注释
                annots = list(page.annots())
                for annot in annots:
                    page.delete_annot(annot)
        
        # 保存文档
        save_path = output_path if output_path else input_path
        doc.save(save_path)
        doc.close()
        
        logger.info(f"Successfully deleted annotations from {input_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to delete annotations: {str(e)}")
        raise Exception(f"Failed to delete annotations: {str(e)}")