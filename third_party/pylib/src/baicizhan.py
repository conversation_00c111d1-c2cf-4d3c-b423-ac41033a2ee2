import os
import re
import json
import binascii
from pathlib import Path
import glob
import requests
search_word_url = "http://www.baicizhan-helper.cn/search/word/adquate"
base_url = "http://www.baicizhan-helper.cn/word/3931"
headers = {
  "Host": "www.baicizhan-helper.cn",
  "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "access_token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI2MCIsImlhdCI6MTc1MjMyODMxMiwiZXhwIjoxNzU3NTEyMzEyfQ.mAqszd44BMNzsKqV5Pabpxd8CZWqIJeqnU_uxAeco_WfEKQfTM8PkugbUkSZqZJsi_qmkrQoZ5eBWzflzCPTlA",
  "Accept": "*/*",
  "Accept-Encoding": "gzip, deflate",
  "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
}

params = {
  "withDict": "true",
  "withMedia": "false",
  "withSimilarWords": "false"
}

class ZPKParser():
    def __init__(self, path, output_dir = ""):
        self.path = path
        self.output_dir = output_dir
        if not output_dir:
            p = Path(self.path)
            self.output_dir = str(p.parent / p.stem)
            os.makedirs(self.output_dir, exist_ok=True)
        self.data = self.readZpk()
    
    def parse(self):
        fileList = self.parseFileList()
        self.parseMeta()
        for file in fileList:
            p = Path(file)
            print(p.suffix)
            if p.suffix == ".jpg" or p.suffix == ".jpeg":
                self.parseJPG(p.name)
            elif p.suffix == ".png":
                self.parsePNG(p.name)

    def parseMeta(self):
        with open(self.path,'r',encoding='utf-8',errors='ignore') as f:
            content         = f.read()
            topic_id        = self.readMetaAttr("topic_id", content)
            word_level_id   = self.readMetaAttr("word_level_id", content)
            tag_id          = self.readMetaAttr("tag_id", content)
            word            = self.readMetaAttrWord("word", content)
            word_audio      = self.readMetaAttr("word_audio", content)
            image_file      = self.readMetaAttr("image_file", content)
            accent          = self.readMetaAttr("accent", content)
            mean_cn         = self.readMetaAttr("mean_cn", content)
            mean_en         = self.readMetaAttr("mean_en", content)
            sentence_phrase = self.readMetaAttr("sentence_phrase", content)
            deformation_img = self.readMetaAttr("deformation_img", content)
            sentence        = self.readMetaAttr("sentence", content)
            sentence_trans  = self.readMetaAttr("sentence_trans", content)
            sentence_audio  = self.readMetaAttr("sentence_audio", content)
            cloze_data      = self.readMetaAttr("cloze_data", content)
            cloze           = self.readMetaAttr("cloze", content)
            options         = self.readMetaAttr("options", content)
            tips            = self.readMetaAttr("tips", content)
            word_etyma      = self.readMetaAttr("word_etyma", content)
            d = {"topic_id":topic_id, "word_level_id":word_level_id, "tag_id": tag_id, "word": word, "word_audio": word_audio, "image_file":image_file, "accent":accent, "mean_cn":mean_cn, "mean_en":mean_en, "sentence_phrase":sentence_phrase, "deformation_img":deformation_img, "sentence":sentence, "sentence_trans":sentence_trans, "sentence_audio":sentence_audio, "cloze_data": cloze_data, "cloze":cloze, "options":options, "tips":tips, "word_etyma":word_etyma}
            word = word.replace('"', '')
            self.meta = d
            output_path = Path(self.output_dir) / "word.json"
            with open(output_path, 'w', encoding = 'utf-8') as f:
                json.dump(d, f, ensure_ascii = False)
            return word

    def parseMP3(self):
        try:
            # res = re.findall("ff e3 20 c4.*aa aa", data)
            res = re.findall("49 44 33.*aa aa", self.data)
            output_path = Path(self.output_dir) / "audio.mp3"
            with open(output_path, 'wb') as bmp_file:
                bmp_file.write(bytearray.fromhex(res))
        except:
            print("未解析出mp3")
            return "Error"

    def parseAAC(self):
        try:
            res = re.findall("ff f1 5c 40.*00 07", self.data)[0]
            output_path = Path(self.output_dir) / "audio.aac"
            with open(output_path, 'wb') as bmp_file:
                bmp_file.write(bytearray.fromhex(res)) 
        except:
            print("未解析出aac")
            return "Error"

    def parseJPG(self, name):
        try:
            res = re.findall("ff d8 ff.*ff d9",self.data)[0]
            output_path = Path(self.output_dir) / name
            with open(output_path, 'wb') as bmp_file:
                bmp_file.write(bytearray.fromhex(res))
        except:
            print("未解析出jpg")
            return "Error"

    def parsePNG(self, name):
        try:
            res = re.findall("89 50 4e 47.*ae 42 60 82",self.data)[0]
            output_path = Path(self.output_dir) / name
            with open(output_path, 'wb') as bmp_file:
                bmp_file.write(bytearray.fromhex(res))
        except:
            print("未解析出png")
            return "Error"

    def parseFileList(self):
        if not self.data:
            self.data = self.readZpk()
        reverseData = self.data[::-1]
        res = re.findall('a0 (.*?) 00 00 00 00 00 00', reverseData)
        if len(res)>0:
            res = res[0][::-1]
            filelist = self.hex2chr(res).split("\n")
            print('文件列表处理完成')
            return filelist
        else:
            return False

    def hex2chr(self, data):
        res = ''.join(list(map(chr, map(self.int16, data.split(' ')))))
        return res

    def int16(self, num):
        return int(num, 16)

    def readZpk(self):
        with open(self.path, 'rb') as f:
            a=f.read()
            b2a_hex = binascii.b2a_hex(a)
            b2a_hex_str = str(b2a_hex, encoding = "utf-8")
            alist = []
            for i in range(0, len(b2a_hex_str), 2):
                alist.append(b2a_hex_str[i:i+2])
            b2a_hex_str = ' '.join(alist)
            return b2a_hex_str

    def readMetaAttr(self, name, content):
        res = re.findall('\"' + name + '\":(.*?),', content)
        if len(res) > 0:
            attr = res[0]
            if attr[0] == '"' and attr[-1] == '"':
                attr = attr[1:-1]
            return attr
        else:
            return ''

    def readMetaAttrWord(self, name, content):
        res = re.findall('\"' + name + '\":(.*?)}', content)
        if len(res) > 0:
            attr = res[0]
            if attr[0] == '"' and attr[-1] == '"':
                attr = attr[1:-1]
            return attr
        else:
            return ''

    def write(self):
        data = ''
        with open("img.mp3", 'wb') as bmp_file:
            bmp_file.write(bytearray.fromhex(data))

def batch_extract(input_dir, output_dir):
    path_list = glob.glob(str(Path(input_dir) / "**/*.zpk"), recursive=True)
    if not output_dir:
        output_dir = str(Path(input_dir).parent / "zpk_output")
        os.makedirs(output_dir, exist_ok=True)
    print(path_list)
    print(len(path_list))
    for path in path_list:
        parser = ZPKParser(path, output_dir)
        parser.parse()



if __name__ == "__main__":
    # parser = ZPKParser("/Users/<USER>/Downloads/zp_4585_667_0_20250305153402.zpk")
    # parser = ZPKParser("/Users/<USER>/Downloads/zpack/919/1/zp_17189_919_0_20250509163055.zpk")
    # parser = ZPKParser("/Users/<USER>/Downloads/zpack/919/1/zp_5259_919_0_20250224140918.zpk")
    # parser.parse()
    batch_extract("/Users/<USER>/Downloads/zpack", "")
