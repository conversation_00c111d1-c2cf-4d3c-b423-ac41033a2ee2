import fitz
from .utils import progress_reporter, parse_range


def _create_grid_rectangles(n_row: int, n_col: int, unit_w: float, unit_h: float, layout_order: str):
    """
    Create grid rectangles based on the specified layout order.

    Args:
        n_row: Number of rows in the grid
        n_col: Number of columns in the grid
        unit_w: Width of each grid cell
        unit_h: Height of each grid cell
        layout_order: Layout pattern ("row_lr", "row_rl", "col_lr", "col_rl")

    Returns:
        List of fitz.Rect objects representing grid positions
    """
    r_tab = []

    if layout_order == "row_lr":
        # Row-first, left-to-right (default)
        for i in range(n_row):
            for j in range(n_col):
                rect = fitz.Rect(j*unit_w, i*unit_h, (j+1)*unit_w, (i+1)*unit_h)
                r_tab.append(rect)

    elif layout_order == "row_rl":
        # Row-first, right-to-left
        for i in range(n_row):
            for j in range(n_col-1, -1, -1):
                rect = fitz.Rect(j*unit_w, i*unit_h, (j+1)*unit_w, (i+1)*unit_h)
                r_tab.append(rect)

    elif layout_order == "col_lr":
        # Column-first, left-to-right
        for j in range(n_col):
            for i in range(n_row):
                rect = fitz.Rect(j*unit_w, i*unit_h, (j+1)*unit_w, (i+1)*unit_h)
                r_tab.append(rect)

    elif layout_order == "col_rl":
        # Column-first, right-to-left
        for j in range(n_col-1, -1, -1):
            for i in range(n_row):
                rect = fitz.Rect(j*unit_w, i*unit_h, (j+1)*unit_w, (i+1)*unit_h)
                r_tab.append(rect)

    else:
        raise ValueError(f"Invalid layout_order: {layout_order}. Must be one of: 'row_lr', 'row_rl', 'col_lr', 'col_rl'")

    return r_tab


def combine_pdf_by_grid(
    *,
    doc_path: str = "",
    output_path: str = "",
    n_row: int = 1,
    n_col: int = 1,
    layout_order: str = "row_lr",
    page_range: str = "all",
):
    """
    Combine PDF pages into a grid layout with dynamic sizing.

    This function automatically determines the optimal page size based on the actual
    content being combined. For each group of consecutive n_row * n_col pages, it
    finds the maximum page width and height among all pages in that group and uses
    these dimensions to create the combined page.

    Args:
        doc_path: Path to the input PDF document
        output_path: Path for the output PDF document
        n_row: Number of rows in the grid
        n_col: Number of columns in the grid
        layout_order: Layout pattern for arranging pages in the grid. Options:
            - "row_lr": Row-first, left-to-right (default)
            - "row_rl": Row-first, right-to-left
            - "col_lr": Column-first, left-to-right
            - "col_rl": Column-first, right-to-left
        page_range: Range of pages to process (e.g., "1-10", "all", "even", "odd")
    """
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": 1})
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    batch_size = n_row * n_col
    writer: fitz.Document = fitz.open()

    # Process pages in batches
    for batch_start in range(0, len(roi_indices), batch_size):
        batch_end = min(batch_start + batch_size, len(roi_indices))
        batch_pages = roi_indices[batch_start:batch_end]

        # Find maximum dimensions for this batch
        max_width = 0
        max_height = 0
        for page_index in batch_pages:
            page_rect = doc[page_index].rect
            max_width = max(max_width, page_rect.width)
            max_height = max(max_height, page_rect.height)

        # Calculate combined page dimensions
        combined_width = max_width * n_col
        combined_height = max_height * n_row

        # Calculate unit dimensions and create grid rectangles
        unit_w, unit_h = max_width, max_height
        r_tab = _create_grid_rectangles(n_row, n_col, unit_w, unit_h, layout_order)

        # Create new page for this batch
        page = writer.new_page(-1, width=combined_width, height=combined_height)

        # Place pages in grid
        for idx, page_index in enumerate(batch_pages):
            progress_reporter("processing", f"正在处理第{batch_start + idx + 1}页",
                            data={"current": float(batch_start + idx + 1), "total": float(len(roi_indices))})
            grid_position = idx % batch_size
            page.show_pdf_page(r_tab[grid_position], doc, page_index)

    writer.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)
