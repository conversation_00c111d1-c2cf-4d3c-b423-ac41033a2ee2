import pandas as pd
import re
import argparse
import os # 导入os模块，用于处理文件路径

def process_excel(input_excel_path, output_excel_path, options_col_num, answer_col_num, analysis_col_num):
    """
    处理Excel文件，对特定列进行转换。

    Args:
        input_excel_path (str): 输入Excel文件的路径。
        output_excel_path (str): 输出Excel文件的路径。
        options_col_num (int): 选项列的列编号（从1开始）。
        answer_col_num (int): 答案列的列编号（从1开始）。
        analysis_col_num (int): 解析列的列编号（从1开始）。
    """

    try:
        # 读取Excel文件
        df = pd.read_excel(input_excel_path)

        # 将列编号转换为从0开始的索引
        options_col_index = options_col_num - 1
        answer_col_index = answer_col_num - 1
        analysis_col_index = analysis_col_num - 1

        # 检查列索引是否有效
        num_columns = len(df.columns)
        if not (0 <= options_col_index < num_columns and
                0 <= answer_col_index < num_columns and
                0 <= analysis_col_index < num_columns):
            print(f"错误：输入的列编号超出了文件的列范围。文件共有 {num_columns} 列。")
            return

        # --- 转换选项列 ---
        options_col_name = df.columns[options_col_index]
        df[options_col_name] = df[options_col_name].astype(str).replace('nan', '', regex=False)
        df[options_col_name] = df[options_col_name].str.replace('|', '||', regex=False)
        print(f"已处理选项列 '{options_col_name}'：将 '|' 替换为 '||'")

        # --- 转换答案列 ---
        answer_col_name = df.columns[answer_col_index]
        def convert_answer(answer_str):
            if pd.isna(answer_str) or str(answer_str).lower() == 'nan':
                return ""
            answer_str = str(answer_str).upper()
            mapping = {'A': '1', 'B': '2', 'C': '3', 'D': '4', 'E': '5'} # 可以根据需要扩展映射
            converted_parts = []
            for char in answer_str:
                if char in mapping:
                    converted_parts.append(mapping[char])
            return "||".join(converted_parts)

        df[answer_col_name] = df[answer_col_name].apply(convert_answer)
        print(f"已处理答案列 '{answer_col_name}'：将 ABCD 转换为 1234 并用 '||' 拼接")

        # --- 转换解析列 ---
        analysis_col_name = df.columns[analysis_col_index]
        df[analysis_col_name] = df[analysis_col_name].astype(str).replace('nan', '', regex=False)
        df[analysis_col_name] = df[analysis_col_name].str.replace(
            "<span class='zd'>", "<span class='highlight'>", regex=True
        )
        print(f"已处理解析列 '{analysis_col_name}'：将 '<span class='zd'>' 替换为 '<span class='highlight'>'")

        # 保存到新的Excel文件
        df.to_excel(output_excel_path, index=False)
        print(f"处理完成，结果已保存到 '{output_excel_path}'")

    except FileNotFoundError:
        print(f"错误：找不到文件 '{input_excel_path}'。请检查路径是否正确。")
    except Exception as e:
        print(f"处理文件时发生错误：{e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="处理Excel文件，对特定列进行转换。\n示例用法: python excel_processor_cli.py input.xlsx output.xlsx --options_col 2 --answer_col 3 --analysis_col 4",
        formatter_class=argparse.RawTextHelpFormatter
    )

    parser.add_argument(
        "input_excel",
        help="要处理的Excel文件的路径。"
    )
    parser.add_argument(
        "output_excel",
        nargs='?', # 使此参数成为可选的
        default=None, # 默认值为None
        help="要保存的新的Excel文件的路径。\n如果未提供，则将生成一个默认名称，例如 'processed_input.xlsx'。"
    )
    parser.add_argument(
        "--options_col",
        type=int,
        required=True,
        help="选项列的编号（从1开始）。"
    )
    parser.add_argument(
        "--answer_col",
        type=int,
        required=True,
        help="答案列的编号（从1开始）。"
    )
    parser.add_argument(
        "--analysis_col",
        type=int,
        required=True,
        help="解析列的编号（从1开始）。"
    )

    args = parser.parse_args()

    # 处理输出文件路径的默认值
    output_file_to_save = args.output_excel
    if output_file_to_save is None:
        # 从输入文件名生成默认输出文件名
        input_basename = os.path.splitext(os.path.basename(args.input_excel))[0]
        output_file_to_save = f"processed_{input_basename}.xlsx"
        print(f"未指定输出文件路径，将使用默认路径: '{output_file_to_save}'")

    # 验证列编号是否为正整数
    if args.options_col < 1 or args.answer_col < 1 or args.analysis_col < 1:
        print("错误：列编号必须是大于等于1的正整数。")
    else:
        process_excel(args.input_excel, output_file_to_save, args.options_col, args.answer_col, args.analysis_col)