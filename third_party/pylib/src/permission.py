
import fitz
from .utils import progress_reporter, parse_range

def recover_permission_pdf(
    *, 
    doc_path: str = "",
    output_path: str = "",
):
    doc: fitz.Document = fitz.open(doc_path)
    if doc.is_encrypted:
        raise ValueError("文件已加密，请先解密!")
    new_doc = fitz.Document()
    new_doc.insert_pdf(doc)
    new_doc.save(output_path)
    progress_reporter("completed", "已完成", output_path)