import traceback
import os
from pathlib import Path
from typing import List, Dict, Any

import fitz
from loguru import logger

from .utils import progress_reporter, parse_range


def extract_images_from_pdf(
    *,
    doc_path: str = "",
    output_path: str = "",
    page_range: str = "all",
    image_format: str = "png",
    min_width: int = 100,
    min_height: int = 100,
) -> Dict[str, Any]:
    """
    从PDF文件中提取图片
    
    参数:
    - doc_path: PDF文件路径
    - output_path: 输出目录路径
    - page_range: 页面范围，如 "1-3,5-6" 或 "all"
    - image_format: 图片格式，支持 "png", "jpg", "jpeg"
    - min_width: 最小图片宽度，小于此值的图片将被忽略
    - min_height: 最小图片高度，小于此值的图片将被忽略
    
    返回:
    - 包含提取结果的字典，包括提取的图片数量和文件路径列表
    """
    try:
        # 打开PDF文档
        doc: fitz.Document = fitz.open(doc_path)
        p = Path(doc_path)
        
        # 解析页面范围
        roi_indices = parse_range(page_range, doc.page_count)
        
        # 创建输出目录
        output_dir = Path(output_path)
        output_dir.mkdir(exist_ok=True, parents=True)
        
        # 初始化结果
        extracted_images = []
        total_images = 0
        
        # 报告开始处理
        progress_reporter("processing", "正在扫描PDF中的图片...", data={"current": 0, "total": len(roi_indices)})
        
        # 遍历指定页面
        for page_idx, page_num in enumerate(roi_indices):
            try:
                page = doc[page_num]
                
                # 更新进度
                progress_reporter(
                    "processing", 
                    f"正在处理第{page_num + 1}页", 
                    data={"current": float(page_idx + 1), "total": float(len(roi_indices))}
                )
                
                # 获取页面中的图片列表
                image_list = page.get_images(full=True)
                
                for img_idx, img in enumerate(image_list):
                    try:
                        # 获取图片引用
                        xref = img[0]
                        
                        # 提取图片数据
                        pix = fitz.Pixmap(doc, xref)
                        
                        # 检查图片尺寸
                        if pix.width < min_width or pix.height < min_height:
                            logger.debug(f"跳过小尺寸图片: {pix.width}x{pix.height}")
                            pix = None
                            continue
                        
                        # 处理CMYK颜色空间
                        if pix.n - pix.alpha < 4:  # 不是CMYK
                            # 生成文件名
                            img_filename = f"{p.stem}_page{page_num + 1}_img{img_idx + 1}.{image_format}"
                            img_path = output_dir / img_filename
                            
                            # 保存图片
                            if image_format.lower() in ["jpg", "jpeg"]:
                                pix.save(str(img_path), output="jpeg")
                            else:
                                pix.save(str(img_path), output="png")
                            
                            # 记录提取的图片信息
                            extracted_images.append({
                                "filename": img_filename,
                                "path": str(img_path),
                                "page": page_num + 1,
                                "width": pix.width,
                                "height": pix.height,
                                "size": os.path.getsize(img_path)
                            })
                            
                            total_images += 1
                            logger.info(f"提取图片: {img_filename} ({pix.width}x{pix.height})")
                        else:
                            # CMYK图片需要转换为RGB
                            pix_rgb = fitz.Pixmap(fitz.csRGB, pix)
                            
                            # 生成文件名
                            img_filename = f"{p.stem}_page{page_num + 1}_img{img_idx + 1}.{image_format}"
                            img_path = output_dir / img_filename
                            
                            # 保存图片
                            if image_format.lower() in ["jpg", "jpeg"]:
                                pix_rgb.save(str(img_path), output="jpeg")
                            else:
                                pix_rgb.save(str(img_path), output="png")
                            
                            # 记录提取的图片信息
                            extracted_images.append({
                                "filename": img_filename,
                                "path": str(img_path),
                                "page": page_num + 1,
                                "width": pix_rgb.width,
                                "height": pix_rgb.height,
                                "size": os.path.getsize(img_path)
                            })
                            
                            total_images += 1
                            logger.info(f"提取图片: {img_filename} ({pix_rgb.width}x{pix_rgb.height})")
                            
                            pix_rgb = None
                        
                        pix = None
                        
                    except Exception as e:
                        logger.error(f"提取第{page_num + 1}页第{img_idx + 1}张图片时出错: {e}")
                        logger.error(traceback.format_exc())
                        continue
                        
            except Exception as e:
                logger.error(f"处理第{page_num + 1}页时出错: {e}")
                logger.error(traceback.format_exc())
                continue
        
        # 关闭文档
        doc.close()
        
        # 准备返回结果
        result = {
            "total_images": total_images,
            "output_directory": str(output_dir),
            "extracted_images": extracted_images,
            "pages_processed": len(roi_indices)
        }
        
        # 报告完成
        if total_images > 0:
            progress_reporter("completed", f"成功提取 {total_images} 张图片", data=result)
        else:
            progress_reporter("completed", "未找到符合条件的图片", data=result)
        
        return result
        
    except Exception as e:
        error_msg = f"PDF图片提取失败: {e}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        progress_reporter("error", error_msg)
        raise Exception(error_msg)


def extract_images_as_pages(
    *,
    doc_path: str = "",
    output_path: str = "",
    page_range: str = "all",
    dpi: int = 300,
    image_format: str = "png",
) -> Dict[str, Any]:
    """
    将PDF页面转换为图片（页面级提取）
    
    参数:
    - doc_path: PDF文件路径
    - output_path: 输出目录路径
    - page_range: 页面范围，如 "1-3,5-6" 或 "all"
    - dpi: 图片分辨率
    - image_format: 图片格式，支持 "png", "jpg", "jpeg"
    
    返回:
    - 包含提取结果的字典
    """
    try:
        # 打开PDF文档
        doc: fitz.Document = fitz.open(doc_path)
        p = Path(doc_path)
        
        # 解析页面范围
        roi_indices = parse_range(page_range, doc.page_count)
        
        # 创建输出目录
        output_dir = Path(output_path)
        output_dir.mkdir(exist_ok=True, parents=True)
        
        # 初始化结果
        extracted_pages = []
        
        # 报告开始处理
        progress_reporter("processing", "正在转换PDF页面为图片...", data={"current": 0, "total": len(roi_indices)})
        
        # 遍历指定页面
        for page_idx, page_num in enumerate(roi_indices):
            try:
                page = doc[page_num]
                
                # 更新进度
                progress_reporter(
                    "processing", 
                    f"正在转换第{page_num + 1}页", 
                    data={"current": float(page_idx + 1), "total": float(len(roi_indices))}
                )
                
                # 渲染页面为图片
                mat = fitz.Matrix(dpi / 72, dpi / 72)  # 缩放矩阵
                pix = page.get_pixmap(matrix=mat)
                
                # 生成文件名
                img_filename = f"{p.stem}_page{page_num + 1:04d}.{image_format}"
                img_path = output_dir / img_filename
                
                # 保存图片
                if image_format.lower() in ["jpg", "jpeg"]:
                    pix.save(str(img_path), output="jpeg")
                else:
                    pix.save(str(img_path), output="png")
                
                # 记录提取的页面信息
                extracted_pages.append({
                    "filename": img_filename,
                    "path": str(img_path),
                    "page": page_num + 1,
                    "width": pix.width,
                    "height": pix.height,
                    "size": os.path.getsize(img_path)
                })
                
                pix = None
                logger.info(f"转换页面: {img_filename}")
                
            except Exception as e:
                logger.error(f"转换第{page_num + 1}页时出错: {e}")
                logger.error(traceback.format_exc())
                continue
        
        # 关闭文档
        doc.close()
        
        # 准备返回结果
        result = {
            "total_pages": len(extracted_pages),
            "output_directory": str(output_dir),
            "extracted_pages": extracted_pages,
            "pages_processed": len(roi_indices)
        }
        
        # 报告完成
        progress_reporter("completed", f"成功转换 {len(extracted_pages)} 个页面", data=result)
        
        return result
        
    except Exception as e:
        error_msg = f"PDF页面转换失败: {e}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        progress_reporter("error", error_msg)
        raise Exception(error_msg)
