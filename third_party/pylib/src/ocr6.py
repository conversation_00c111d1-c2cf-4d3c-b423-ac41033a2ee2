import cv2
import numpy as np
from pathlib import Path
from PIL import Image
from paddleocr import PPStructure, draw_structure_result, save_structure_res

def find_keywords_in_ocr_result(ocr_result, keywords):
    """在OCR结果中查找关键词并返回它们的位置"""
    matches = {}
    print(ocr_result)
    for idx, text_block in enumerate(ocr_result):
        print(f"block: {idx}")
        print(f"text: {text_block}")
        res = text_block.get('res', [])
        for block in res:
            text = block.get('text', '')
            text_region = block.get('text_region', [])
            text_word = block.get('text_word', [])
            text_word_region = block.get('text_word_region', [])
            # Sort text_word_region based on top-left coordinates (x, y)
            if text_word_region:
                # Create list of (index, region) tuples for sorting
                indexed_regions = list(enumerate(text_word_region))
                
                # Sort based on y coordinate first, then x coordinate of top-left point
                sorted_regions = sorted(indexed_regions, 
                    key=lambda x: (x[1][0][0], x[1][0][1]))
                
                # Reorder text_word_region and text_word to match the sorted order
                original_indices = [i for i, _ in sorted_regions]
                text_word_region = [text_word_region[i] for i in original_indices]
            for keyword in keywords:
                print(keyword)
                if keyword in text:
                    print(keyword)
                    if text_word and text_word_region:
                        # 找出关键词在文本中的字符位置
                        char_index = 0
                        word_indices = []
                        keyword_start_char = text.find(keyword)
                        keyword_end_char = keyword_start_char + len(keyword) - 1
                        
                        # 找出关键词涉及的所有单字位置
                        for i, word in enumerate(text_word):
                            word_start_char = char_index
                            word_end_char = char_index + len(word) - 1
                            
                            # 如果单字区间与关键词区间有交集，则加入
                            if not (word_end_char < keyword_start_char or word_start_char > keyword_end_char):
                                word_indices.append(i)
                            
                            char_index += len(word)
                        
                        if word_indices:
                            # 获取关键词的边界框
                            regions = [text_word_region[i] for i in word_indices]
                            min_x = min([min(r[0][0], r[3][0]) for r in regions])
                            min_y = min([min(r[0][1], r[1][1]) for r in regions])
                            max_x = max([max(r[1][0], r[2][0]) for r in regions])
                            max_y = max([max(r[2][1], r[3][1]) for r in regions])
                            
                            if keyword not in matches:
                                matches[keyword] = []
                            
                            matches[keyword].append({
                                'region': [[min_x, min_y], [max_x, min_y], [max_x, max_y], [min_x, max_y]],
                                'text_context': text
                            })
                    else:
                        # 使用整个文本块的区域
                        if keyword not in matches:
                            matches[keyword] = []
                        
                        matches[keyword].append({
                            'region': text_region,
                            'text_context': text
                        })
        
    return matches


def highlight_keywords_in_image(image_path, ocr_result, keywords, output_path=None):
    """在图像中高亮显示关键词"""
    # 读取图像
    img = cv2.imread(str(image_path))
    if img is None:
        raise ValueError(f"无法读取图像: {image_path}")
    
    # 查找关键词
    matches = find_keywords_in_ocr_result(ocr_result, keywords)
    
    # 为不同关键词分配不同的颜色
    colors = [
        (0, 0, 255),   # 红色
        (0, 255, 0),   # 绿色
        (255, 0, 0),   # 蓝色
        (0, 255, 255), # 黄色
        (255, 0, 255), # 紫色
        (255, 255, 0)  # 青色
    ]
    
    # 在图像上绘制关键词边界框
    for i, (keyword, keyword_matches) in enumerate(matches.items()):
        keyword_color = colors[i % len(colors)]
        
        for match in keyword_matches:
            region = match['region']
            
            # 将region转换为整数坐标的多边形
            points = np.array(region, dtype=np.int32)
            
            # 绘制多边形
            cv2.polylines(img, [points], True, keyword_color, 2)
            
            # 计算边界框上方文本位置
            text_x = int(min(p[0] for p in region))
            text_y = int(min(p[1] for p in region)) - 10
            text_y = max(text_y, 15)  # 确保文本不会超出图像上边界
            
            # 绘制关键词文本
            cv2.putText(img, keyword, (text_x, text_y), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, keyword_color, 2)
    
    # 保存结果
    if output_path:
        cv2.imwrite(str(output_path), img)
        print(f"已保存标注结果到: {output_path}")
    
    return img, matches


# 直接使用示例
if __name__ == "__main__":
    img_path = '/Users/<USER>/Downloads/Snipaste_2025-04-29_20-52-16.png'
    ppstructure_engine = PPStructure(
        show_log=True,
        lang="ch",
        return_word_box=True,
        recovery=True
    )
    img = cv2.imread(img_path)
    ocr_result = ppstructure_engine(img)
    final_result = []
    for obj in ocr_result:
        obj.pop("img")
        final_result.append(obj)
    # 要查找的关键词
    keywords = ['视觉语言模型', '使用场景', '结合视觉内容', 'OCR']
    
    # 输出路径
    output_path = Path(img_path).parent / f"{Path(img_path).stem}_keywords_highlighted.png"
    
    # 在图像中高亮显示关键词
    highlighted_img, matches = highlight_keywords_in_image(
        img_path, 
        ocr_result, 
        keywords, 
        output_path
    )
    
    # 打印找到的关键词信息
    for keyword, occurrences in matches.items():
        print(f"关键词 '{keyword}' 在图像中出现了 {len(occurrences)} 次")