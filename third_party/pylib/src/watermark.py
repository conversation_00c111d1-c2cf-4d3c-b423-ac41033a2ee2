import copy
import math
import os
import re
import tempfile
import traceback
from pathlib import Path
from typing import List, Tu<PERSON>, Union

import cv2
import fitz
import numpy as np
import ulid
from loguru import logger
from PIL import Image
from .utils import progress_reporter, parse_range, hex_to_rgb
from . import pdf_redactor


# ---------------------------------------------------------------------------- #
#                                     类型水印                                     #
# ---------------------------------------------------------------------------- #
def detect_watermark_by_type(
    doc_path: str,
    wm_page_number: int,
    output_path: str = None
):
    doc: fitz.Document = fitz.open(doc_path)
    writer: fitz.Document = fitz.open()
    page = doc[wm_page_number]
    page.clean_contents()
    xref = page.get_contents()[0]
    stream = doc.xref_stream(xref)
    wm_count = 0
    if stream:
        stream = bytearray(stream)
        if stream.find(b"/Subtype/Watermark"):
            i1 = 0
            while True:
                i1 = stream.find(b"/Artifact", i1)  # start of definition
                if i1 < 0: break  # none more left: done
                i2 = stream.find(b"EMC", i1)  # end of definition
                i1 = i2 + 3
                wm_count += 1
    if wm_count == 0:
        raise Exception("该文件没有找到水印!")
    stream_t = stream.copy()
    for wm_index in range(1, wm_count+1):
        count = 0
        i1 = 0
        while True:
            i1 = stream.find(b"/Artifact", i1)  # start of definition
            if i1 < 0: break  # none more left: done
            i2 = stream.find(b"EMC", i1)  # end of definition
            count += 1
            if wm_index == 0:
                stream[i1 : i2+3] = b""  # remove the full definition source "/Artifact ... EMC"
            else:
                if count == wm_index:
                    stream[i1 : i2+3] = b""  # remove the full definition source "/Artifact ... EMC"
                else:
                    i1 = i2 + 3
            doc.update_stream(xref, stream, compress=True)
        writer.insert_pdf(doc, from_page=wm_page_number, to_page=wm_page_number)
        stream = stream_t.copy()
    p = Path(doc_path)
    if not output_path:
        output_path = str(p.parent / f"{p.stem}-识别水印索引.pdf")
    if writer.page_count > 0:
        writer.ez_save(output_path, garbage=4)
    else:
        raise Exception("该文件没有找到水印!")


def remove_watermark_by_type(
    doc_path: str,
    page_range: str = "all",
    wm_index: List[int] = [1],
    output_path: str = None
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    WATERMARK_FLAG = False
    wm_index = [v-1 for v in wm_index] # 转为0-based
    progress_reporter("processing", "开始处理...", data={"current": 0, "total": len(roi_indices)})

    for idx, page_index in enumerate(roi_indices):
        progress_reporter("processing", "处理中...", data={"current": float(idx+1), "total": len(roi_indices)})
        page: fitz.Page = doc[page_index]
        page.clean_contents()
        xref = page.get_contents()[0]
        stream = doc.xref_stream(xref)
        
        if stream:
            stream = bytearray(stream)
            if stream.find(b"/Subtype/Watermark"):
                WATERMARK_FLAG = True
                count = 0
                i1 = 0
                while True:
                    i1 = stream.find(b"/Artifact", i1)  # start of definition
                    if i1 < 0: break  # none more left: done
                    i2 = stream.find(b"EMC", i1)  # end of definition
                    if count in wm_index:
                        stream[i1 : i2+3] = b""  # remove the full definition source "/Artifact ... EMC"
                    else:
                        i1 = i2 + 3
                    count += 1
                doc.update_stream(xref, stream, compress=True)
        logger.debug(f"page_index: {page_index} WATERMARK_FLAG: {WATERMARK_FLAG}")
    if not WATERMARK_FLAG:
        raise Exception("该文件没有找到水印!")
    doc.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)


# ---------------------------------------------------------------------------- #
#                                     内容水印                                     #
# ---------------------------------------------------------------------------- #
def detect_watermark_by_content(
    doc_path: str,
    wm_page_number: int,
    output_path: str = None
):
    doc: fitz.Document = fitz.open(doc_path)
    writer: fitz.Document = fitz.open()
    page = doc[wm_page_number]
    keys = doc.xref_get_keys(page.xref)
    logger.debug(keys)
    out = doc.xref_get_key(page.xref, "Contents")
    logger.debug(f"Contents: {out}")
    if out[0] == 'array':
        parts = list(out)[1][1:-1].split(" ")
        indirect_objs = list(map(lambda x: " ".join(x), [parts[i:i+3] for i in range(0, len(parts), 3)]))
        for i in range(len(indirect_objs)):
            t = f'[{" ".join(indirect_objs[:i]+indirect_objs[i+1:])}]'
            doc.xref_set_key(page.xref, "Contents", t)
            writer.insert_pdf(doc, from_page=wm_page_number, to_page=wm_page_number)
    p = Path(doc_path)
    if not output_path:
        output_path = str(p.parent / f"{p.stem}-识别水印索引.pdf")
    if writer.page_count > 0:
        writer.ez_save(output_path, garbage=4)
    else:
        raise Exception("该文件没有找到水印!")


def remove_watermark_by_content(
    doc_path: str,
    wm_index: List[int],
    page_range: str = "all",
    output_path: str = None
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    for idx, page_index in enumerate(roi_indices):
        progress_reporter("processing", "处理中...", data={"current": float(idx+1), "total": len(roi_indices)})
        page = doc[page_index]
        out = doc.xref_get_key(page.xref, "Contents")
        if out[0] == 'array':
            parts = list(out)[1][1:-1].split(" ")
            indirect_objs = list(map(lambda x: " ".join(x), [parts[i:i+3] for i in range(0, len(parts), 3)]))
            for i in wm_index:
                if i>=0:
                    del indirect_objs[i-1]
                else:
                    del indirect_objs[i]
            filtered_objs = f'[{" ".join(indirect_objs)}]'
            doc.xref_set_key(page.xref, "Contents", filtered_objs)
    p = Path(doc_path)
    if not output_path:
        output_path = str(p.parent / f"{p.stem}-去水印版.pdf")
    doc.ez_save(output_path, garbage=4)

# ---------------------------------------------------------------------------- #
#                                     图片水印                                     #
# ---------------------------------------------------------------------------- #

def detect_watermark_by_image(
    doc_path: str,
    wm_page_number: int,
    output_path: str = None
):
    doc: fitz.Document = fitz.open(doc_path)
    if not output_path:
        p = Path(doc_path)
        output_path = str(p.parent / f"{p.stem}-图片")
    Path(output_path).mkdir(parents=True, exist_ok=True)
    page = doc[wm_page_number]
    image_list = page.get_images()
    logger.debug(f"page{wm_page_number}: img_list({len(image_list)}): {image_list}")
    for i, img in enumerate(image_list):
        xref = img[0] # get the XREF of the image
        pix = fitz.Pixmap(doc, xref) # create a Pixmap
        if pix.n - pix.alpha > 3: # CMYK: convert to RGB first
            pix = fitz.Pixmap(fitz.csRGB, pix)
        savepath = str(Path(output_path) / f"{i+1}.png")
        pix.save(savepath) # save the image as PNG
        pix = None


def remove_watermark_by_image(
    doc_path: str,
    wm_index_list: List[int],
    page_range: str = "all",
    output_path: str = None
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    logger.debug(f"{wm_index_list=}")
    for idx, page_index in enumerate(roi_indices):
        progress_reporter("processing", "处理中...", data={"current": float(idx+1), "total": len(roi_indices)})
        page = doc[page_index]
        # option2:
        img_list = page.get_images()
        for idx_ in wm_index_list:
            try:
                xref = img_list[idx_-1][0]
                page.delete_image(xref)
            except:
                logger.error(traceback.format_exc())
    p = Path(doc_path)
    if not output_path:
        output_path = str(p.parent / f"{p.stem}-去水印版.pdf")
    doc.ez_save(output_path, garbage=4)

# ---------------------------------------------------------------------------- #
#                                     文字水印                                     #
# ---------------------------------------------------------------------------- #
def remove_watermark_by_text(
    doc_path: str,
    wm_text: str,
    output_path: str = None
):
    # TODO: 需要把stream先uncompress，然后再进行处理
    # doc: fitz.Document = fitz.open(doc_path)
    # xreflen = doc.xref_length()
    # for i in range(xreflen):
    #     # obj = doc.xref_object(i, compressed=False)
    #     # update stream to uncompressed stream
    #     stream = doc.xref_stream(i)
    #     doc.update_stream(i, stream)
    # p = Path(doc_path)
    # doc.save(f"{p.parent}/{p.stem}-uncompress.pdf")
    options = pdf_redactor.RedactorOptions()
    options.content_filters = [
        # First convert all dash-like characters to dashes.
        (
            re.compile(wm_text),
            lambda m : ""
        ),
    ]
    p = Path(doc_path)
    if not output_path:
        output_path = str(p.parent / f"{p.stem}-去水印版.pdf")
    options.input_stream = doc_path
    options.output_stream = output_path
    pdf_redactor.redactor(options)


# ---------------------------------------------------------------------------- #
#                                     路径水印                                     #
# ---------------------------------------------------------------------------- #
def detect_watermark_by_path(
    doc_path: str,
    wm_page_number: int,
    output_path: str = None
):
    doc: fitz.Document = fitz.open(doc_path)
    page: fitz.Page = doc[wm_page_number]
    page.clean_contents()
    xref = page.get_contents()[0]
    stream = doc.xref_stream(xref)
    stream = bytearray(stream).decode("utf-8")
    matches = re.finditer(r"((-?(\d+)?\.\d+\s){2,}c?m.*?h)", stream, flags=re.MULTILINE|re.DOTALL)
    blocks = []
    buffer = []
    for matchNum, match in enumerate(matches, start=1):
        if not buffer:
            buffer.append(match.span())
        else:
            if match.span()[0] == buffer[-1][1]+1:
                buffer.append(match.span())
            else:
                blocks.append([buffer[0][0], buffer[-1][1]])
                buffer = [match.span()]
    if buffer:
        blocks.append([buffer[0][0], buffer[-1][1]])
    # logger.debug(blocks)
    logger.debug(f"len(blocks): {len(blocks)}")
    if not blocks:
        raise Exception("该文件没有找到水印!")
    writer: fitz.Document = fitz.open()
    for idx, block in enumerate(blocks[::-1]):
        progress_reporter("processing", "处理中...", data={"current": float(idx+1), "total": len(blocks)})
        new_stream = copy.deepcopy(stream)
        new_stream = new_stream[:block[0]] + new_stream[block[1]:]
        new_stream = new_stream.encode("utf-8")
        doc.update_stream(xref, new_stream, compress=True)
        writer.insert_pdf(doc, from_page=wm_page_number, to_page=wm_page_number, start_at=0)
        # new_page = writer[0]
        # new_page.add_text_annot(fitz.Point(0, 0), f"id: {len(blocks)-idx} block: {block}")
    p = Path(doc_path)
    if not output_path:
        output_path = str(p.parent / f"{p.stem}-识别水印索引.pdf")
    writer.ez_save(output_path, garbage=4)
    writer.close()
    doc.close()

def remove_watermark_by_path(
    doc_path: str,
    wm_index_str: str,
    page_range: str = "all",
    output_path: str = None
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    wm_index_list = parse_range(wm_index_str, 999999)
    wm_index_list = [v+1 for v in wm_index_list]
    WATERMARK_FLAG = False
    for idx, page_index in enumerate(roi_indices):
        progress_reporter("processing", "处理中...", data={"current": float(idx+1), "total": len(roi_indices)})
        page: fitz.Page = doc[page_index]
        page.clean_contents()
        xref = page.get_contents()[0]
        stream = doc.xref_stream(xref)
        stream = bytearray(stream).decode("utf-8")
        matches = re.finditer(r"((-?(\d+)?\.\d+\s){2,}c?m.*?h)", stream, flags=re.MULTILINE|re.DOTALL)
        blocks = []
        buffer = []
        for matchNum, match in enumerate(matches, start=1):
            if not buffer:
                buffer.append(match.span())
            else:
                if match.span()[0] == buffer[-1][1]+1:
                    buffer.append(match.span())
                else:
                    blocks.append([buffer[0][0], buffer[-1][1]])
                    buffer = [match.span()]
        if buffer:
            blocks.append([buffer[0][0], buffer[-1][1]])
        logger.debug(blocks)
        new_stream = copy.deepcopy(stream)
        n_blocks = len(blocks)
        for idx, block in enumerate(blocks[::-1]):
            # logger.debug(f"{n_blocks - idx}")
            if n_blocks - idx in wm_index_list:
                # logger.debug(f"hit block: {block}")
                new_stream = new_stream[:block[0]] + new_stream[block[1]:]
        if len(new_stream) < len(stream):
            WATERMARK_FLAG = True
            new_stream = new_stream.encode("utf-8")
            doc.update_stream(xref, new_stream, compress=True)
    if not WATERMARK_FLAG:
        raise Exception("该文件没有找到水印!")
    p = Path(doc_path)
    if not output_path:
        output_path = str(p.parent / f"{p.stem}-去水印版.pdf")
    doc.ez_save(output_path, garbage=4)


# ---------------------------------------------------------------------------- #
#                                     色阶水印                                     #
# ---------------------------------------------------------------------------- #

def remove_watermark_by_pixel(
    doc_path: str,
    threshold_below: int = 128,
    threshold_high: int = 255,
    threshold_target: str = "#ffffff",
    is_limit_region: bool = False,
    page_range: str = "all",
    dpi: int = 300,
    output_path: str = None
):
    doc: fitz.Document = fitz.open(doc_path)
    writer: fitz.Document = fitz.open()
    roi_indices = parse_range(page_range, doc.page_count)
    tempdir = tempfile.gettempdir()
    rgb = hex_to_rgb(threshold_target)
    logger.debug(f"{rgb=}")
    bbox_list = []
    if is_limit_region:
        for i in range(doc.page_count):
            page = doc[i]
            w, h = page.rect.width, page.rect.height
            for annot in page.annots():
                if annot.type[0] == 4: # Rectangle
                    bbox = [annot.rect.x0/w, annot.rect.y0/h, annot.rect.x1/w, annot.rect.y1/h]
                    bbox_list.append(bbox)
                    page.delete_annot(annot)
    logger.debug(f"bbox_list:{bbox_list}")
    for i in range(doc.page_count):
        progress_reporter("processing", "处理中...", data={"current": float(i+1), "total": len(doc)})
        if i in roi_indices:
            page: fitz.Page = doc[i]
            new_page: fitz.Page  = writer.new_page(width=page.rect.width, height=page.rect.height)
            pix         = page.get_pixmap(matrix=fitz.Matrix(dpi/72, dpi/72), alpha=False)
            pix_np      = np.frombuffer(buffer=pix.samples, dtype=np.uint8).reshape((pix.height, pix.width, -1))
            pix_gray    = page.get_pixmap(matrix=fitz.Matrix(dpi/72, dpi/72), alpha=False, colorspace=fitz.csGRAY)
            pix_gray_np = np.frombuffer(buffer=pix_gray.samples, dtype=np.uint8).reshape((pix_gray.height, pix_gray.width, -1)).squeeze()
            # logger.warning(f"{pix_np.shape=}")
            # logger.warning(f"{pix_gray_np.shape=}")
            mask = np.zeros_like(pix_gray_np, dtype=bool)
            mask[(pix_gray_np >= threshold_below) & (pix_gray_np <= threshold_high)] = True
            if is_limit_region:
                h, w = mask.shape[:2]
                logger.debug(f"{w=}, {h=}")
                roi_mask = np.zeros_like(mask, dtype=bool)
                for bbox in bbox_list:
                    x1, y1, x2, y2 = int(bbox[0]*w), int(bbox[1]*h), int(bbox[2]*w), int(bbox[3]*h)
                    roi_mask[y1:y2, x1:x2] = True
                mask[~roi_mask] = False
            updated_pix = pix_np.copy()
            updated_pix[mask] = rgb
            savepath = str(Path(tempdir) / f"{str(ulid.new())}.png")
            img = cv2.cvtColor(updated_pix, cv2.COLOR_RGB2BGR)
            cv2.imwrite(savepath, img)
            updated_pix = fitz.Pixmap(savepath)
            new_page.insert_image(new_page.rect, pixmap=updated_pix)
            os.remove(savepath)
        else:
            writer.insert_pdf(doc, from_page=i, to_page=i)
    p = Path(doc_path)
    if not output_path:
        output_path = str(p.parent / f"{p.stem}-去水印版.pdf")
    writer.ez_save(output_path, garbage=4)

