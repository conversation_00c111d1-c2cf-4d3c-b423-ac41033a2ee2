import cv2
from pathlib import Path
import os
from PIL import Image
from paddleocr import PPStructure, draw_structure_result, save_structure_res

ppstructure_engine = PPStructure(
    show_log=True,
    lang="ch",
    return_word_box=True,
    recovery=True
)

img_path = '/Users/<USER>/Downloads/Snipaste_2025-04-29_20-52-16.png'
save_folder = '/Users/<USER>/Downloads/ppocr'
output_image_file = str(Path(img_path).parent / 'output__ppstructure.png') # Changed output name
img = cv2.imread(img_path)
result = ppstructure_engine(img)
save_structure_res(result, save_folder,os.path.basename(img_path).split('.')[0])
cleaned_result = []
for obj in result:
    obj.pop('img')
    cleaned_result.append(obj)
print(cleaned_result)


font_path = '/Users/<USER>/code/anki-guru/anki_guru/assets/fonts/SourceHanSansSC-Normal.ttf' # PaddleOCR下提供字体包
image = Image.open(img_path).convert('RGB')
im_show = draw_structure_result(image, result,font_path=font_path)
im_show = Image.fromarray(im_show)
im_show.save(output_image_file)