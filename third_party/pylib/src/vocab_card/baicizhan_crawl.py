import asyncio
import httpx  # The async-capable requests replacement
import jsonlines
import os
import sys
import time
import collections
from loguru import logger
from tqdm.asyncio import tqdm  # Use the asyncio-compatible version of tqdm

# --- 配置区 ---

# API基础URL和头信息
BASE_URL = "http://www.baicizhan-helper.cn"
HEADERS = {
    "Host": "www.baicizhan-helper.cn",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "access_token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI2MCIsImlhdCI6MTc1MjMyODMxMiwiZXhwIjoxNzU3NTEyMzEyfQ.mAqszd44BMNzsKqV5Pabpxd8CZWqIJeqnU_uxAeco_WfEKQfTM8PkugbUkSZqZJsi_qmkrQoZ5eBWzflzCPTlA",
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
}

# 输入和输出文件名
INPUT_WORDS_FILE = "/Users/<USER>/Downloads/ky_words.txt"
OUTPUT_JSONL_FILE = "/Users/<USER>/Downloads/ky_word_details.jsonl"
LOG_FILE = "/Users/<USER>/Downloads/word_fetcher.log"

# 并发和速率限制配置
MAX_CONCURRENT_REQUESTS = 10  # 控制同时运行的协程数量
REQUESTS_PER_PERIOD = 100
PERIOD_IN_SECONDS = 60

# --- 日志配置 ---
logger.remove()
logger.add(
    sys.stdout, level="INFO",
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{message}</cyan>"
)
logger.add(
    LOG_FILE, level="DEBUG", rotation="10 MB", retention="7 days", encoding="utf-8",
    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level:<8} | {name}:{function}:{line} - {message}"
)

# --- 异步速率限制器 ---
class AsyncRateLimiter:
    """一个异步的、线程安全的速率限制器，使用滑动窗口算法。"""
    def __init__(self, requests_per_period, period_in_seconds):
        self.requests = collections.deque()
        self.requests_per_period = requests_per_period
        self.period_in_seconds = period_in_seconds
        self.lock = asyncio.Lock()

    async def acquire(self):
        """获取一个请求许可，如果达到速率限制则异步等待。"""
        async with self.lock:
            while True:
                now = time.monotonic()
                while self.requests and self.requests[0] <= now - self.period_in_seconds:
                    self.requests.popleft()

                if len(self.requests) < self.requests_per_period:
                    self.requests.append(now)
                    break
                
                oldest_request_time = self.requests[0]
                wait_time = oldest_request_time + self.period_in_seconds - now
                logger.debug(f"速率达到上限，等待 {wait_time:.2f} 秒...")
                await asyncio.sleep(wait_time)

# --- 脚本功能区 ---

def load_processed_words(filename):
    # This remains synchronous as it's a one-off operation at startup
    if not os.path.exists(filename):
        return set()
    processed_words = set()
    logger.info(f"正在读取已处理文件 '{filename}'...")
    try:
        with jsonlines.open(filename, mode='r') as reader:
            for data_obj in reader:
                word = data_obj.get("dict", {}).get("word_basic_info", {}).get("word")
                if word:
                    processed_words.add(word.lower())
    except Exception as e:
        logger.warning(f"读取或解析 '{filename}' 时出错: {e}。")
    return processed_words

@logger.catch
async def get_topic_id(client: httpx.AsyncClient, word: str, rate_limiter: AsyncRateLimiter):
    await rate_limiter.acquire()
    search_url = f"{BASE_URL}/search/word/{word}"
    try:
        response = await client.get(search_url, timeout=10)
        response.raise_for_status()
        data = response.json()
        if data.get("code") == 200 and data.get("data"):
            for item in data["data"]:
                if item.get("word").lower() == word.lower():
                    return item.get("topic_id")
            logger.warning(f"'{word}': 未找到完全匹配，使用相似词 '{data['data'][0].get('word')}'")
            return data["data"][0].get("topic_id")
        else:
            logger.error(f"'{word}': 搜索失败，API消息: {data.get('message', '无')}")
    except httpx.HTTPError as e:
        logger.error(f"'{word}': 搜索时网络/HTTP错误: {e}")

@logger.catch
async def get_word_details_data(client: httpx.AsyncClient, topic_id: int, rate_limiter: AsyncRateLimiter):
    await rate_limiter.acquire()
    details_url = f"{BASE_URL}/word/{topic_id}"
    params = {"withDict": "true", "withMedia": "false", "withSimilarWords": "false"}
    try:
        response = await client.get(details_url, params=params, timeout=15)
        response.raise_for_status()
        full_response = response.json()
        if full_response.get("code") == 200 and "data" in full_response:
            return full_response["data"]
        else:
            logger.error(f"Topic_id {topic_id}: 获取详情失败，API消息: {full_response.get('message')}")
    except httpx.HTTPError as e:
        logger.error(f"Topic_id {topic_id}: 获取详情时网络/HTTP错误: {e}")

async def process_word(client: httpx.AsyncClient, word: str, rate_limiter: AsyncRateLimiter, semaphore: asyncio.Semaphore):
    """使用信号量控制并发，处理单个单词的完整流程。"""
    async with semaphore:
        logger.debug(f"开始处理单词: {word}")
        topic_id = await get_topic_id(client, word, rate_limiter)
        if not topic_id:
            return word, None
        
        details_data = await get_word_details_data(client, topic_id, rate_limiter)
        if not details_data:
            return word, None
            
        return word, details_data

async def main():
    logger.info("--- 单词详情获取脚本启动 (异步模式) ---")
    
    if not os.path.exists(INPUT_WORDS_FILE):
        logger.critical(f"输入文件 '{INPUT_WORDS_FILE}' 不存在！")
        return

    processed_words = load_processed_words(OUTPUT_JSONL_FILE)
    logger.info(f"已找到 {len(processed_words)} 个已处理的单词。")
    
    with open(INPUT_WORDS_FILE, 'r', encoding='utf-8') as f:
        words_to_process = [line.strip().lower() for line in f if line.strip() and line.strip().lower() not in processed_words]

    if not words_to_process:
        logger.info("没有需要处理的新单词。")
        logger.info("--- 所有任务已完成 ---")
        return
        
    logger.info(f"准备处理 {len(words_to_process)} 个新单词...")

    rate_limiter = AsyncRateLimiter(REQUESTS_PER_PERIOD, PERIOD_IN_SECONDS)
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
    success_count = 0
    fail_count = 0

    # 使用 httpx.AsyncClient 作为上下文管理器，它会自动处理连接的打开和关闭
    async with httpx.AsyncClient(headers=HEADERS) as client:
        # 使用 jsonlines 的同步写入。对于大多数情况，磁盘I/O的短暂阻塞是可接受的。
        with jsonlines.open(OUTPUT_JSONL_FILE, mode='a') as writer:
            tasks = [
                asyncio.create_task(process_word(client, word, rate_limiter, semaphore))
                for word in words_to_process
            ]
            
            # 使用tqdm显示进度
            for future in tqdm(asyncio.as_completed(tasks), total=len(tasks), desc="处理单词"):
                word, data = await future
                if data:
                    try:
                        writer.write(data)
                        logger.success(f"'{word}' 的数据已成功保存。")
                        success_count += 1
                    except Exception as e:
                        logger.exception(f"写入 '{word}' 的数据时发生意外错误。")
                        fail_count += 1
                else:
                    logger.warning(f"'{word}' 处理失败，未获取到数据。")
                    fail_count += 1
                
    logger.info("--- 所有任务已完成 ---")
    logger.info(f"处理结果: {success_count} 个成功, {fail_count} 个失败。")

if __name__ == "__main__":
    # 运行主异步函数
    asyncio.run(main())