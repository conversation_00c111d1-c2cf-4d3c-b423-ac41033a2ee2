import argparse
import asyncio
import json
import re
from pymongo import MongoClient, UpdateOne
from loguru import logger
import aiohttp
from aiolimiter import AsyncLimiter

# --- 1. 日志与数据库连接 ---
logger.add("word_generator.log", level="DEBUG", rotation="10 MB", retention="7 days")

def get_db_collection(mongo_uri, db_name, collection_name):
    """通用数据库连接函数"""
    try:
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        client.admin.command('ping')
        db = client[db_name]
        collection = db[collection_name]
        return client, collection
    except Exception as e:
        logger.error(f"连接到 {db_name}/{collection_name} 失败: {e}")
        return None, None

# --- 2. 数据查询函数 ---
def query_word_from_db(collection, word: str) -> dict:
    if collection is None: return None
    word_lower = word.lower()
    result = collection.find_one({"queryWord": word_lower}) or collection.find_one({"resource.word.word": word_lower})
    if not result: logger.trace(f"在集合 '{collection.name}' 中未找到单词 '{word}' 的数据。")
    return result

# --- 3. 数据转换函数 (为保持完整性而包含，无改动) ---

def clean_sentence_markup(raw_text: str) -> str:
    if not isinstance(raw_text, str): return ""
    return "".join(re.findall(r'#([^$]*)\$', raw_text))

def transform_bing_response(data: dict) -> dict:
    if not data or 'apiResponse' not in data or 'value' not in data['apiResponse'] or not data['apiResponse']['value']:
        return {"error": "无效或空的必应输入数据"}
    source_data = data['apiResponse']['value'][0]
    target = {"word": source_data.get('name', ''), "pronunciation": {"uk": None, "us": None}, "audio_url": {"uk": None, "us": None}, "variant": {}, "tags": [], "definitions": [], "sentences": [], "image_urls": [], "phrases": [], "dictionaries": [{"name": "必应词典", "url_template": f"https://cn.bing.com/dict/search?q={source_data.get('name', '')}"}], "synonyms": "", "antonyms": "", "etymology": "", "mnemonic": None}
    if pron_audio := source_data.get('pronunciationAudio', {}):
        if url := pron_audio.get('contentUrl'):
            target['audio_url']['uk'] = target['audio_url']['us'] = url
    pos_map, all_synonyms = {"名词": "n.", "动词": "v.", "形容词": "adj.", "副词": "adv."}, []
    for group in source_data.get('meaningGroups', []):
        if not (pos_info := group.get('partsOfSpeech')): continue
        description, pos_name = pos_info[0].get('description', ''), pos_info[0].get('name', '')
        if description in ['发音', '权威英汉双解发音']:
            if group.get('meanings') and (pron := group['meanings'][0].get('richDefinitions', [{}])[0].get('fragments', [{}])[0].get('text')):
                target['pronunciation']['us' if pos_name.lower() == 'us' else 'uk'] = f"/{pron}/"
        elif description == '权威英汉双解':
            pos_abbr = pos_map.get(pos_name, pos_name)
            if meanings := group.get('meanings'):
                for sense_block in meanings[0].get('richDefinitions', []):
                    entry = {"pos": pos_abbr, "meaning_en": "", "meaning_zh": "", "sentences": []}
                    for item in sense_block.get('subDefinitions', []):
                        if 'Definition' in item.get('domains', []) and len(frags := item.get('fragments', [])) >= 2:
                            entry['meaning_en'], entry['meaning_zh'] = frags[0].get('text', ''), frags[1].get('text', '')
                        elif 'Examples' in item.get('domains', []) and 'Sentence' in item.get('domains', []) and len(frags := item.get('fragments', [])) >= 2:
                            entry['sentences'].append({"sentence_en": frags[0].get('text', ''), "sentence_zh": frags[1].get('text', ''), "image_url": None, "audio_url": None})
                    if entry['meaning_en'] or entry['meaning_zh']: target['definitions'].append(entry)
        elif description == '图片':
            if meanings := group.get('meanings'):
                target['image_urls'].extend(item['image']['contentUrl'] for item in meanings[0].get('synonyms', []) if 'image' in item and 'contentUrl' in item['image'])
        elif description == '词组':
            if meanings := group.get('meanings'):
                for rich_def in meanings[0].get('richDefinitions', []):
                    for i in range(0, len(ex := rich_def.get('examples', [])), 2):
                        if i + 1 < len(ex): target['phrases'].append({"phrase_en": ex[i], "phrase_zh": ex[i+1]})
        elif description == '分类词典':
            if meanings := group.get('meanings'):
                all_synonyms.extend(syn['name'] for syn in meanings[0].get('synonyms', []) if 'name' in syn)
    if all_synonyms: target['synonyms'] = ", ".join(sorted(list(set(all_synonyms))))
    if not target['pronunciation']['us'] and not target['pronunciation']['uk'] and (top_pron := source_data.get('pronunciation')):
        target['pronunciation']['us'] = target['pronunciation']['uk'] = f"/{top_pron}/"
    return target

def transform_baicizhan_response(data: dict) -> dict:
    if not data or 'resource' not in data: return {}
    resource, word_info = data['resource'], data.get('resource', {}).get('word', {})
    transformed = {"word": word_info.get('word'), "pronunciation": {"uk": word_info.get('accentUk'), "us": word_info.get('accentUs')}, "audio_url": {"uk": word_info.get('audioUk'), "us": word_info.get('audioUs')}, "sentences": [], "mnemonic": None, "variant": {}}
    variant_map = {"现在分词": "present_participle", "过去分词": "past_participle", "过去式": "past_tense", "第三人称单数": "third_person"}
    for var in resource.get('variant') or []:
        if (key := variant_map.get(var.get('type'))) and (val := var.get('variant')):
            transformed['variant'][key] = val
    if mnemonic_info := resource.get('mnemonic', {}):
        if content := mnemonic_info.get('content'):
            transformed['mnemonic'] = content
    for sentence in resource.get('sentences', []):
        if sentence.get('sentenceEn') and sentence.get('translate'):
            transformed['sentences'].append({"sentence_en": sentence['sentenceEn'], "sentence_zh": sentence['translate'], "image_url": sentence.get('image'), "audio_url": sentence.get('audio')})
    return transformed

# --- 4. 数据融合与主逻辑 ---
def generate_word_object(word: str, bcz_coll, bing_coll) -> dict:
    bcz_data = query_word_from_db(bcz_coll, word)
    bing_data = query_word_from_db(bing_coll, word)
    if bing_data is None:
        logger.warning(f"必应词典中没有 '{word}' 的数据，无法生成。")
        return None
    final_object = transform_bing_response(bing_data)
    if "error" in final_object:
        logger.warning(f"转换必应数据失败 for '{word}': {final_object['error']}")
        return None
    final_object['word'] = word.lower()
    if bcz_data:
        bcz_transformed = transform_baicizhan_response(bcz_data)
        if bcz_transformed.get('sentences'): final_object['sentences'] = bcz_transformed['sentences']
        if bcz_transformed.get('variant'): final_object['variant'] = bcz_transformed['variant']
        if bcz_transformed.get('mnemonic'): final_object['mnemonic'] = bcz_transformed['mnemonic']
        for key in ['us', 'uk']:
            if bcz_transformed.get('pronunciation', {}).get(key): final_object['pronunciation'][key] = bcz_transformed['pronunciation'][key]
            if bcz_transformed.get('audio_url', {}).get(key): final_object['audio_url'][key] = bcz_transformed['audio_url'][key]
        final_object['dictionaries'].append({"name": "百词斩", "url_template": f"https://www.baicizhan.com/login?d_word={word}"})
    if not final_object['sentences'] and (all_def_sents := [s for d in final_object.get('definitions', []) for s in d.get('sentences', [])]):
        final_object['sentences'] = all_def_sents
    return final_object

# --- 5. 命令行接口与功能实现 ---

def run_generate(args):
    """执行 'generate' 子命令"""
    bcz_client, bcz_coll = get_db_collection(args.mongo_uri, args.bcz_db, args.bcz_coll)
    bing_client, bing_coll = get_db_collection(args.mongo_uri, args.bing_db, args.bing_coll)
    final_client, final_coll = (None, None)
    if args.upload:
        final_client, final_coll = get_db_collection(args.mongo_uri, args.final_db, args.final_coll)
    try:
        if bing_coll is None:
            logger.error("无法连接到必应数据库，程序终止。")
            return
        word_object = generate_word_object(args.word, bcz_coll, bing_coll)
        if not word_object: return
        output_json = json.dumps(word_object, ensure_ascii=False, indent=2)
        if args.output:
            try:
                with open(args.output, 'w', encoding='utf-8') as f: f.write(output_json)
                logger.success(f"已成功将单词 '{args.word}' 的数据保存到文件: {args.output}")
            except Exception as e:
                logger.error(f"保存文件失败: {e}")
        else:
            print(output_json)
        if args.upload:
            if final_coll is None:
                logger.error("需要上传但无法连接到最终数据库，上传失败。")
                return
            try:
                result = final_coll.update_one({"word": word_object["word"]}, {"$set": word_object}, upsert=True)
                if result.upserted_id: logger.success(f"成功将新单词 '{word_object['word']}' 上传到数据库。")
                elif result.modified_count > 0: logger.success(f"成功更新了数据库中单词 '{word_object['word']}' 的数据。")
                else: logger.info(f"数据库中单词 '{word_object['word']}' 的数据已是最新，未做改动。")
            except Exception:
                logger.exception(f"上传单词 '{word_object['word']}' 到数据库失败。")
    finally:
        if bcz_client: bcz_client.close()
        if bing_client: bing_client.close()
        if final_client: final_client.close()
        logger.trace("数据库连接已关闭。")

async def process_and_upload_batch(batch, bcz_coll, bing_coll, final_coll, limiter):
    """异步处理一个批次的单词并上传"""
    async with limiter:
        generated_objects = [obj for word in batch if (obj := await asyncio.to_thread(generate_word_object, word, bcz_coll, bing_coll))]
        if generated_objects:
            try:
                operations = [UpdateOne({"word": obj["word"]}, {"$set": obj}, upsert=True) for obj in generated_objects]
                result = await asyncio.to_thread(final_coll.bulk_write, operations)
                logger.success(f"成功上传/更新 {result.upserted_count + result.modified_count} 个单词。")
            except Exception:
                logger.exception(f"批量上传批次 {batch} 失败。")

async def run_upload_async(args):
    """执行 'upload' 子命令的异步主逻辑"""
    logger.info("开始执行 'upload' 任务...")
    bcz_client, bcz_coll = get_db_collection(args.mongo_uri, args.bcz_db, args.bcz_coll)
    bing_client, bing_coll = get_db_collection(args.mongo_uri, args.bing_db, args.bing_coll)
    final_client, final_coll = get_db_collection(args.mongo_uri, args.final_db, args.final_coll)
    try:
        if any([bcz_coll is None, bing_coll is None, final_coll is None]):
            logger.error("数据库连接不完整，任务终止。")
            return
        with open(args.file, 'r', encoding='utf-8') as f:
            all_words = sorted(list(set(line.strip().lower() for line in f if line.strip())))
        logger.info(f"从文件 '{args.file}' 中读取到 {len(all_words)} 个唯一单词。")
        existing_words = {doc['word'] for doc in final_coll.find({}, {"word": 1, "_id": 0})}
        logger.info(f"目标数据库中已存在 {len(existing_words)} 个单词。")
        words_to_process = [word for word in all_words if word not in existing_words]
        if not words_to_process:
            logger.info("所有单词均已存在于目标数据库中，任务完成。")
            return
        logger.info(f"需要处理 {len(words_to_process)} 个新单词。")
        limiter = AsyncLimiter(1, args.batch_interval)
        tasks = [process_and_upload_batch(words_to_process[i:i + args.batch_size], bcz_coll, bing_coll, final_coll, limiter) for i in range(0, len(words_to_process), args.batch_size)]
        logger.info(f"共 {len(tasks)} 个批次待处理，每 {args.batch_interval} 秒处理一个批次。")
        await asyncio.gather(*tasks)
        logger.success("所有批次处理完毕！")
    finally:
        if bcz_client: bcz_client.close()
        if bing_client: bing_client.close()
        if final_client: final_client.close()
        logger.info("所有数据库连接已关闭。")

def run_list(args):
    """执行 'list' 子命令"""
    logger.info("开始执行 'list' 任务...")
    final_client, final_coll = get_db_collection(args.mongo_uri, args.final_db, args.final_coll)

    try:
        if final_coll is None:
            logger.error("无法连接到最终数据库，程序终止。")
            return

        logger.info("正在查询数据库中的所有单词...")
        # 查询所有单词，只返回word字段，按字母顺序排序
        cursor = final_coll.find({}, {"word": 1, "_id": 0}).sort("word", 1)
        words = [doc['word'] for doc in cursor if 'word' in doc]

        logger.info(f"共找到 {len(words)} 个单词。")

        if args.output:
            # 保存到指定文件
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    for word in words:
                        f.write(f"{word}\n")
                logger.success(f"已将 {len(words)} 个单词保存到文件: {args.output}")
            except Exception as e:
                logger.error(f"保存文件失败: {e}")
        else:
            # 输出到控制台
            logger.info("将在控制台打印所有单词:")
            for word in words:
                print(word)

    except Exception as e:
        logger.error(f"查询单词列表失败: {e}")
    finally:
        if final_client:
            final_client.close()
        logger.trace("数据库连接已关闭。")

def main():
    parser = argparse.ArgumentParser(
        description="综合词典数据，生成并上传单词卡片。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    
    # --- FIX: 将共享参数定义为全局参数 ---
    # 分组以获得更好的帮助信息输出
    db_group = parser.add_argument_group('数据库配置 (全局)')
    db_group.add_argument("--mongo-uri", default="******************************************************", help="MongoDB 连接字符串。")
    db_group.add_argument("--bcz-db", default="baicizhan_db", help="百词斩源数据库名。")
    db_group.add_argument("--bcz-coll", default="baicizhan", help="百词斩源集合名。")
    db_group.add_argument("--bing-db", default="bing_db", help="必应源数据库名。")
    db_group.add_argument("--bing-coll", default="bing_words", help="必应源集合名。")
    db_group.add_argument("--final-db", default="final_word_db", help="最终目标数据库名。")
    db_group.add_argument("--final-coll", default="words", help="最终目标集合名。")

    subparsers = parser.add_subparsers(dest="command", required=True)

    # 'generate' 子命令
    p_gen = subparsers.add_parser("generate", help="为单个单词生成综合数据，并可选择上传。")
    p_gen.add_argument("word", help="要生成的英文单词。")
    p_gen.add_argument("-o", "--output", help="保存结果到指定文件 (与--upload互斥)。")
    p_gen.add_argument("--upload", action="store_true", help="生成成功后，直接上传/更新到数据库。")
    p_gen.set_defaults(func=run_generate)

    # 'upload' 子命令
    p_upload = subparsers.add_parser("upload", help="从文件批量生成并上传单词数据。")
    p_upload.add_argument("file", help="包含单词列表的文本文件路径。")
    p_upload.add_argument("--batch-size", type=int, default=10, help="每批处理和上传的单词数量。")
    p_upload.add_argument("--batch-interval", type=int, default=1, help="处理每批次之间的间隔时间（秒）。")
    p_upload.set_defaults(func=lambda args: asyncio.run(run_upload_async(args)))

    # 'list' 子命令
    p_list = subparsers.add_parser("list", help="查询最终数据库中已存在的单词列表。")
    p_list.add_argument("-o", "--output", help="保存单词列表到指定文件，一行一个单词。如不指定则输出到控制台。")
    p_list.set_defaults(func=run_list)

    args = parser.parse_args()
    args.func(args)

if __name__ == "__main__":
    main()