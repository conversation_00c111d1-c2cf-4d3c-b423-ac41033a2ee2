import fitz

from .utils import progress_reporter, parse_range, range_compress

def slice_pdf(
    *, 
    doc_path: str = "",
    output_path: str = "",
    page_range: str = "all",
    is_reverse: bool = False,
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count, is_reverse=is_reverse)
    writer: fitz.Document = fitz.open()
    parts = range_compress(roi_indices)
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(parts)})
    for idx, part in enumerate(parts):
        progress_reporter("processing", f"正在处理第{idx+1}块", data={"current": float(idx+1), "total": float(len(parts))})
        writer.insert_pdf(doc, from_page=part[0], to_page=part[1])
    writer.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)


def reorder_pdf(
    *, 
    doc_path: str = "",
    output_path: str = "",
    page_range: str = "",
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count, is_unique=False)
    writer: fitz.Document = fitz.open()
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})
    for idx, i in enumerate(roi_indices):
        progress_reporter("processing", f"正在处理第{idx+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
        writer.insert_pdf(doc, from_page=i, to_page=i)
    writer.ez_save(output_path, garbage=4) 
    progress_reporter("completed", "已完成", output_path)