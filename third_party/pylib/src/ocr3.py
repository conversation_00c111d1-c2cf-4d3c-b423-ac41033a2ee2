import cv2
import numpy as np
import re
import string
import math # Kept for potential bbox processing helpers if needed

# Import PaddleOCR
try:
    from paddleocr import PaddleOCR
except ImportError:
    print("PaddleOCR library not found. Please install it:")
    print("pip install paddlepaddle -i https://pypi.baidu.com/simple")
    print("pip install paddleocr -i https://pypi.baidu.com/simple")
    exit()

# --- 辅助函数 ---

def combine_segment_boxes(segment_bboxes_list):
    """
    给定多个字/词段的四点 bounding box 列表，计算它们的联合外接矩形。

    Args:
        segment_bboxes_list (list): 包含多个 [[x1, y1], [x2, y2], [x3, y3], [x4, y4]] 形式的列表。

    Returns:
        list or None: [min_x, min_y, max_x, max_y] 形式的联合外接矩形，如果列表为空返回 None。
    """
    if not segment_bboxes_list:
        return None

    all_points = []
    for bbox_4_points in segment_bboxes_list:
        # Ensure the bbox has the expected 4 points structure
        if isinstance(bbox_4_points, list) and len(bbox_4_points) == 4:
            # Ensure each point is a list/tuple of 2 coordinates
            if all(isinstance(point, (list, tuple)) and len(point) == 2 for point in bbox_4_points):
                 all_points.extend(bbox_4_points)
            else:
                print(f"Warning: Skipping malformed bbox points: {bbox_4_points}")
        else:
            print(f"Warning: Skipping unexpected bbox format: {bbox_4_points}")


    if not all_points:
        return None

    # Convert to numpy array for easy min/max calculation
    points_np = np.array(all_points, dtype=np.int32)
    min_x = np.min(points_np[:, 0])
    min_y = np.min(points_np[:, 1])
    max_x = np.max(points_np[:, 0])
    max_y = np.max(points_np[:, 1])

    # Return as [x1, y1, x2, y2] rectangle format
    return [min_x, min_y, max_x, max_y]


# --- 文本清理 (用于匹配) ---

# Define punctuation to IGNORE during keyword MATCHING
IGNORABLE_MATCHING_PUNCTUATION = set(string.punctuation + "，。！？【】『』《》（）：；‘’“”")

def clean_text_for_matching(text):
    """
    清理文本以便进行关键词匹配：转小写，移除特定标点，保留其他字符（包括空格）。
    """
    if text is None:
        return ""

    # Convert entire text to lowercase first
    text_lower = text.lower()
    cleaned_chars = []

    # Iterate through the lowercase text to filter punctuation
    for char in text_lower:
        # If the character is NOT in the set of ignorable punctuation, keep it
        if char not in IGNORABLE_MATCHING_PUNCTUATION:
             cleaned_chars.append(char)
        # Note: Ignored punctuation is removed from the cleaned text

    return "".join(cleaned_chars)

# --- 主要功能函数 (利用 PaddleOCR 的 word box) ---

def find_keywords_with_paddle_word_boxes(image_path, keywords, paddle_ocr_reader):
    """
    使用 PaddleOCR (开启 return_word_box=True) 识别字/词段及其坐标，
    然后查找关键词并组合其字/词段的坐标。

    Args:
        image_path (str): 图像文件路径。
        keywords (list): 待查找的关键词列表 (字符串)。
        paddle_ocr_reader: 已初始化的 PaddleOCR 对象 (需设置 return_word_box=True)。

    Returns:
        tuple: (image_with_boxes, found_keyword_info)
            image_with_boxes (numpy.ndarray): 绘制了关键词框的图像副本。
            found_keyword_info (list): 找到的关键词及其坐标列表
                                      [(keyword, [x1, y1, x2, y2]), ...]
    """
    img = cv2.imread(image_path)
    if img is None:
        print(f"Error: Could not read image from {image_path}")
        return None, []

    img_height, img_width, _ = img.shape
    image_with_boxes = img.copy()
    found_keyword_info = []

    # 对关键词进行清理，用于匹配
    cleaned_keywords = [(kw, clean_text_for_matching(kw)) for kw in keywords]
    # Filter out keywords that become empty after cleaning
    cleaned_keywords = [(orig_kw, cl_kw) for orig_kw, cl_kw in cleaned_keywords if cl_kw]

    # 1. 使用 PaddleOCR 读取字/词段 (开启 return_word_box=True)
    print("Running PaddleOCR with return_word_box=True...")
    # PaddleOCR output format when return_word_box=True:
    # The documentation linked suggests it might return a flat list of blocks/segments directly
    # like [[bbox, (text, score)], [bbox, (text, score)], ...].
    # Let's try running it and inspecting the structure or rely on the common structure:
    # list of pages (usually 1), each page is a list of lines, each line is a list of segments.
    # [[[bbox, (text, score)], [bbox, (text, score)], ...], [[bbox, (text, score)], ...], ...]

    paddle_ocr_results = paddle_ocr_reader.ocr(image_path, det=True, rec=True, cls=True)
    print(paddle_ocr_results)
    # Flatten the results into a list of segments: [[bbox, (text, score)], ...]
    all_segments_raw = []
    if paddle_ocr_results and paddle_ocr_results[0]: # Check if results and the first page exist
        # Iterate through the results of the first page. Each item here is expected to be a list of segments for a line.
        for line_segments in paddle_ocr_results[0]:
             # Check if the line_segments item is a list and its first element looks like a bbox
             # This helps handle potential variations in PaddleOCR's output structure
             if isinstance(line_segments, list) and len(line_segments) > 0 and \
                isinstance(line_segments[0], list) and len(line_segments[0]) == 4:
                 all_segments_raw.extend(line_segments)
             # Add checks for other potential structures if the above fails for your version


    if not all_segments_raw:
        print("No text segments detected by PaddleOCR.")
        return image_with_boxes, []

    # Prepare segment info and build the combined cleaned text and index map
    # Store tuples: (original_text, cleaned_text, bbox_4_points)
    segment_info = []
    combined_cleaned_text = ""
    # Map from index in combined_cleaned_text to (original_segment_index, original_char_index_in_segment)
    # This map will allow us to find which segment and character inside it a character in the combined text corresponds to.
    combined_index_to_segment_char_map = []

    current_segment_index = 0
    for segment in all_segments_raw:
         if isinstance(segment, list) and len(segment) >= 2:
             bbox_4_points = segment[0]
             text_score_tuple = segment[1]
             if isinstance(bbox_4_points, list) and len(bbox_4_points) == 4 and \
                isinstance(text_score_tuple, tuple) and len(text_score_tuple) >= 1:
                original_text = text_score_tuple[0]

                if original_text:
                    cleaned_text = clean_text_for_matching(original_text)
                    # Store segment info including original text, cleaned text, and bbox
                    segment_info.append((original_text, cleaned_text, bbox_4_points))

                    # Build the combined cleaned text and the index map
                    for char_index_in_segment, char in enumerate(cleaned_text):
                        combined_cleaned_text += char
                        # Map the current index in combined_cleaned_text back to
                        # (which original segment it came from, which char index in the original segment's *cleaned* text)
                        combined_index_to_segment_char_map.append((current_segment_index, char_index_in_segment))

                    current_segment_index += 1 # Move to the next segment index in our segment_info list

    if not segment_info:
         print("No valid text segments found after processing.")
         return image_with_boxes, []

    # 2. 在组合的清理文本中查找关键词
    # We find matches in the combined_cleaned_text and then use the map
    # to get the corresponding segments.

    for original_keyword, cleaned_keyword in cleaned_keywords:
        if not cleaned_keyword: continue

        start_combined_index = 0
        while True:
            # Find the keyword match in the combined cleaned text string
            match_start_in_combined = combined_cleaned_text.find(cleaned_keyword, start_combined_index)

            if match_start_in_combined == -1:
                break # No more matches for this keyword

            # Calculate the end index (inclusive) of the match in the combined text
            match_end_in_combined_inclusive = match_start_in_combined + len(cleaned_keyword) - 1

            # Use the map to find the original segments corresponding to the start and end of the match
            try:
                # Look up the map for the character at the start of the match in combined_cleaned_text
                start_segment_idx_in_info, _ = combined_index_to_segment_char_map[match_start_in_combined]
                # Look up the map for the character at the end of the match in combined_cleaned_text
                end_segment_idx_in_info, _ = combined_index_to_segment_char_map[match_end_in_combined_inclusive]

                # Collect bboxes for all original segments from the start segment index to the end segment index (inclusive)
                # These segments constitute the keyword match.
                matched_segment_bboxes = [segment_info[k][2] for k in range(start_segment_idx_in_info, end_segment_idx_in_info + 1)]

                # Combine the bboxes of the matched segments
                combined_bbox_rect = combine_segment_boxes(matched_segment_bboxes)

                if combined_bbox_rect:
                     # Add to results (avoiding strict duplicates based on estimated box)
                     box_rect = combined_bbox_rect # already [x1, y1, x2, y2]
                     is_duplicate = False
                     # Check if this box is very similar to one already found for this keyword
                     for existing_keyword, existing_box in found_keyword_info:
                          if existing_keyword == original_keyword:
                               box1 = np.array(box_rect)
                               box2 = np.array(existing_box)
                               # Check if max absolute difference in corner coordinates is small (e.g., 5 pixels tolerance)
                               if np.max(np.abs(box1 - box2)) < 5:
                                    is_duplicate = True
                                    break
                     if not is_duplicate:
                          found_keyword_info.append((original_keyword, box_rect))

            except IndexError:
                 # This should theoretically not happen if the map is built correctly and indices are within bounds,
                 # but included as a defensive measure.
                 print(f"Warning: Index mapping failed during match lookup for '{original_keyword}' at combined index {match_start_in_combined}-{match_end_in_combined_inclusive}. Map length: {len(combined_index_to_segment_char_map)}. Skipping this match.")
                 pass # Continue to next find iteration

            # Move the search start index in the combined text past the current match
            start_combined_index = match_end_in_combined_inclusive + 1

    # 3. 在图像副本上绘制关键词的 bounding box
    colors = {} # 用于给不同关键词使用不同颜色
    color_palette = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
    color_index = 0

    for keyword, box in found_keyword_info:
        if keyword not in colors:
            colors[keyword] = color_palette[color_index % len(color_palette)]
            color_index += 1
        color = colors[keyword]

        x1, y1, x2, y2 = box
        # 确保坐标是整数
        x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)

        # 绘制矩形框
        cv2.rectangle(image_with_boxes, (x1, y1), (x2, y2), color, 2)

        # 可选：在框旁边或上方添加关键词文本
        # text_label = f"{keyword}"
        # (text_width, text_height), baseline = cv2.getTextSize(text_label, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)
        # put_text_y = y1 - 10 if y1 - 10 > text_height + 5 else y2 + text_height + 5
        # put_text_y = max(0, put_text_y)
        # cv2.putText(image_with_boxes, text_label, (x1, put_text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)

    return image_with_boxes, found_keyword_info

# --- 示例用法 ---

if __name__ == '__main__':
    from pathlib import Path
    image_file = '/Users/<USER>/Downloads/Snipaste_2025-04-29_20-52-16.png'  # <--- 修改为你的图片路径
    # 替换为你想查找的关键词列表
    keywords_to_find = ['语言模型', '使用', '多轮对话', '事物', '文本', '图片', '调用'] # <--- 修改为你需要查找的关键词

    # 初始化 PaddleOCR reader (只需要一次)
    # lang: 指定语言模型，例如 'en', 'ch'. 可以用 ',' 连接多种语言，如 'en,ch'.
    # use_gpu: 是否使用 GPU
    # use_angle_cls: 是否启用方向分类器，有助于处理不同方向的文本
    # return_word_box: 关键参数！设置为 True 以获取字/词级别的坐标
    print("Initializing PaddleOCR reader with return_word_box=True...")

    try:
        # Attempt with combined language setting. This is generally preferred if it works.
        # PaddleOCR(lang='en,ch') uses different models internally depending on version.
        # If specific models are needed for word boxes or robustness, you might need model_dir etc.
        paddle_reader = PaddleOCR(
            lang='ch',
            use_gpu=False,
            use_angle_cls=True,
            **{'return_word_box': True, 'recovery':True})
        print("PaddleOCR reader initialized with lang='en,ch'.")
    except Exception as e:
        print(f"Could not initialize PaddleOCR with lang='en,ch' and return_word_box=True: {e}")
        print("Please check your PaddleOCR installation and model availability for lang='en,ch' with return_word_box=True.")
        # Depending on the error, you might need to download specific models manually or adjust 'lang'.
        # Example for downloading models (run this in terminal):
        # paddleocr --download --lang ch --type rec
        # paddleocr --download --lang en --type rec
        # Then you might need to specify model_dir in PaddleOCR init.
        # Ex: paddle_reader = PaddleOCR(lang='ch', rec_model_dir='path/to/ch_rec_model', **{'return_word_box': True})
        exit()

    # The punctuation_width_mode parameter is no longer needed as we use actual segment boxes.
    # The cleaning function is still used for robust matching of keyword strings against segment strings.

    # 查找关键词并绘制框
    print(f"Processing image: {image_file}")

    # Call the main function using the PaddleOCR reader with word boxes enabled
    image_output, found_keywords = find_keywords_with_paddle_word_boxes(
        image_file,
        keywords_to_find,
        paddle_reader # Pass the PaddleOCR reader
    )

    if image_output is not None:
        # 保存结果图像
        output_image_file = str(Path(image_file).parent / 'output_char_boxes.png')
        cv2.imwrite(output_image_file, image_output)
        print(f"Result image saved as: {output_image_file}")

        # Print found keywords and their coordinates
        print("\nFound Keywords and their coordinates (rectangle [x1, y1, x2, y2]) using PaddleOCR word boxes:")
        if found_keywords:
            for keyword, box in found_keywords:
                print(f"  Keyword: '{keyword}', Box: {box}")
        else:
            print("  No keywords found.")

        # Display result image (optional)
        # cv2.imshow("Keywords Highlighted (PaddleOCR Word Boxes)", image_output)
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()
    else:
        print("Processing failed.")