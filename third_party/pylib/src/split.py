import traceback
from pathlib import Path

import fitz

from .utils import progress_reporter, parse_range, range_compress
from loguru import logger


def split_pdf_by_chunk(
    *, 
    doc_path: str = "",
    output_path: str = "",
    chunk_size: int = 0,
):
    doc: fitz.Document = fitz.open(doc_path)
    p = Path(doc_path)
    n = doc.page_count // chunk_size
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": n})
    for idx, i in enumerate(range(0, doc.page_count, chunk_size)):
        progress_reporter("processing", f"正在处理第{idx+1}块", data={"current": float(idx+1), "total": float(n)})
        savepath = str(Path(output_path) / f"{p.stem}-{i+1}-{min(i+chunk_size, doc.page_count)}.pdf")
        writer:fitz.Document = fitz.open()
        writer.insert_pdf(doc, from_page=i, to_page=min(i+chunk_size, doc.page_count)-1)
        writer.save(savepath, garbage=3, deflate=True)
    progress_reporter("completed", "已完成", output_path)

def split_pdf_by_page(
    *, 
    doc_path: str = "",
    output_path: str = "",
    page_range: str = "all",
):
    doc: fitz.Document = fitz.open(doc_path)
    p = Path(doc_path)
    indices_list = parse_range(page_range, doc.page_count, is_multi_range=True)
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(indices_list)})
    for i, indices in enumerate(indices_list):
        progress_reporter("processing", f"正在处理第{i+1}块", data={"current": float(i+1), "total": float(len(indices_list))})
        writer: fitz.Document = fitz.open()
        parts = range_compress(indices)
        for part in parts:
            writer.insert_pdf(doc, from_page=part[0], to_page=part[1])
        writer.save(str(Path(output_path) / f"{p.stem}-part{i}.pdf"), garbage=3, deflate=True)
    progress_reporter("completed", "已完成", output_path)

def split_pdf_by_toc(
    *, 
    doc_path: str = "",
    output_path: str = "",
    level: int = 1,
):
    doc: fitz.Document = fitz.open(doc_path)
    p = Path(doc_path)
    
    # 获取文档的目录结构
    toc = doc.get_toc(simple=False)
    if not toc:
        logger.warning(f"文档 {doc_path} 没有目录结构，无法按目录拆分")
        progress_reporter("error", "文档没有目录结构，无法按目录拆分")
        return
    
    # 筛选指定级别的目录项
    filtered_toc = [item for item in toc if item[0] <= level]
    if not filtered_toc:
        logger.warning(f"文档 {doc_path} 没有{level}级或更低级别的目录项")
        progress_reporter("error", f"没有{level}级或更低级别的目录项")
        return
    
    # 添加结束标记，用于确定最后一章节的结束页码
    filtered_toc.append([filtered_toc[-1][0], "END", doc.page_count + 1, 0])
    
    # 创建拆分范围，并构建完整路径
    split_ranges = []
    path_stack = [[] for _ in range(level + 1)]  # 用于存储每一级的路径
    
    for i in range(len(filtered_toc) - 1):
        current = filtered_toc[i]
        next_item = filtered_toc[i + 1]
        
        current_level = current[0]
        
        # 更新当前级别的路径
        path_stack[current_level] = current[1]
        # 清除所有高于当前级别的路径
        for j in range(current_level + 1, level + 1):
            path_stack[j] = ""
            
        # 构建完整路径
        full_path = []
        for j in range(1, current_level + 1):
            if path_stack[j]:
                full_path.append(path_stack[j])
        
        start_page = current[2] - 1  # PyMuPDF页码从0开始
        end_page = next_item[2] - 2  # 下一章节的起始页前一页
        
        if i == len(filtered_toc) - 2:  # 最后一个实际章节
            end_page = doc.page_count - 1
        
        # 确保页码范围有效
        if start_page <= end_page:
            # 保存标题和完整路径
            split_ranges.append((start_page, end_page, current[1], full_path))
    
    # 开始处理
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(split_ranges)})
    
    for i, (start_page, end_page, title, full_path) in enumerate(split_ranges):
        progress_reporter("processing", f"正在处理 '{title}'", data={"current": float(i+1), "total": float(len(split_ranges))})
        
        try:
            # 根据level决定使用的标题
            if level > 1 and full_path:
                # 使用完整路径
                path_title = "-".join(full_path)
                display_title = path_title
            else:
                # 仅使用当前标题
                display_title = title
            
            # 创建安全的文件名
            safe_title = "".join(c for c in display_title if c.isalnum() or c in " -_").strip()
            if not safe_title:
                safe_title = f"chapter-{i+1}"
            
            # 限制文件名长度
            if len(safe_title) > 50:
                safe_title = safe_title[:47] + "..."
            
            # 保存路径
            savepath = str(Path(output_path) / f"{p.stem}-{safe_title}-{start_page+1}-{end_page+1}.pdf")
            
            # 创建新PDF
            writer = fitz.open()
            writer.insert_pdf(doc, from_page=start_page, to_page=end_page)
            writer.save(savepath, garbage=3, deflate=True)
            writer.close()
        except Exception as e:
            logger.error(f"处理章节 '{title}' 时出错: {e}")
            logger.error(traceback.format_exc())
    
    doc.close()
    progress_reporter("completed", "已完成", output_path)