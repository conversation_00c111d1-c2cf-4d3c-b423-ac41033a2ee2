import os
from pathlib import Path
from typing import List

import fitz

from .utils import progress_reporter, parse_range


def cut_pdf_by_grid(
    *, 
    doc_path: str = "",
    output_path: str = "",
    n_row: int = 1,
    n_col: int = 1,
    page_range: str = "all",
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    writer: fitz.Document = fitz.open()
    is_key_dict = {i:False for i in range(doc.page_count)}
    for idx, page_index in enumerate(roi_indices):
        if page_index in is_key_dict:
            is_key_dict[page_index] = True
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(doc)})
    for idx, page_index in enumerate(range(len(doc))):
        progress_reporter("processing", f"正在处理第{idx+1}页", data={"current": float(idx+1), "total": float(len(doc))})
        if not is_key_dict[page_index]:
            page = doc[page_index]
            new_page = writer.new_page(-1, width=page.rect.width, height=page.rect.height)
            new_page.show_pdf_page(page.rect, doc, page_index)
        else:
            page = doc[page_index]
            page_width, page_height = page.rect.width, page.rect.height
            width, height = page_width/n_col, page_height/n_row
            for i in range(n_row):
                for j in range(n_col):
                    bbox = fitz.Rect(j*width, i*height, (j+1)*width, (i+1)*height)
                    # bbox += d
                    tmp_page = writer.new_page(-1, width=bbox.width, height=bbox.height)
                    tmp_page.show_pdf_page(tmp_page.rect, doc, page_index, clip=bbox)
    writer.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)

def cut_pdf_by_page(
    *, 
    doc_path: str = "",
    output_path: str = "",
    paper_size: str = "a4", 
    orientation: str = "portrait",
    margin: List[float] = [10, 10, 10, 10],
    page_range: str = "all",
):
    """
    将PDF文件切割成指定大小的页面。
    :param doc_path: 输入PDF文件路径。
    :param paper_size: 纸张大小，默认为A4。
    :param orientation: 纸张方向，默认为纵向。
    :param margin: 页边距，默认为10mm。四个值分别表示上、下、左、右边距。
    :param page_range: 切割的页面范围，默认为全部页面。支持输入整数或整数范围，例如1-3,5,7表示切割第1、2、3、5、7页。
    :param output_path: 输出PDF文件夹路径。
    """
    doc = fitz.open(doc_path)
    scaled_doc = fitz.open()
    writer = fitz.open()
    fmt = fitz.paper_rect(paper_size)
    if orientation == "landscape":
        fmt = fitz.paper_rect(f"{paper_size}-l")
    roi_indices = parse_range(page_range, doc.page_count)
    is_key_dict = {i:False for i in range(doc.page_count)}
    for idx, page_index in enumerate(roi_indices):
        if page_index in is_key_dict:
            is_key_dict[page_index] = True
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})
    for idx, page_index in enumerate(range(len(doc))):
        progress_reporter("processing", f"正在处理第{idx+1}页", data={"current": float(idx+1), "total": float(len(doc))})
        if not is_key_dict[page_index]:
            page = doc[page_index]
            new_page = writer.new_page(-1, width=page.rect.width, height=page.rect.height)
            new_page.show_pdf_page(page.rect, doc, page_index)
        else:
            page = doc[page_index]
            page_w, page_h = page.rect.width, page.rect.height
            real_w = fmt.width - (margin[2] + margin[3])
            real_h = fmt.height - (margin[0] + margin[1])
            scale_factor = real_w / page_w
            scale_w, scale_h = page_w * scale_factor, page_h * scale_factor
            scaled_page = scaled_doc.new_page(width=scale_w, height=scale_h)
            scaled_page.show_pdf_page(scaled_page.rect, doc, page_index)
            cur_h = 0
            # 对当前页进行切割
            while cur_h < scale_h:
                # 定义切割区域
                next_h = min(cur_h + fmt.height, scale_h)
                clip_rect = fitz.Rect(0, cur_h, fmt.width, next_h)
                # 创建新的PDF页面
                new_page: fitz.Page = writer.new_page(width=fmt.width, height=fmt.height)
                real_rect = fitz.Rect(margin[2], margin[0], fmt.width-margin[3], fmt.height - margin[1])
                if next_h == scale_h:
                    real_rect = fitz.Rect(margin[2], margin[0], fmt.width-margin[3], margin[0]+clip_rect.height)
                # 将切割后的内容绘制到新页面上
                new_page.show_pdf_page(real_rect, scaled_doc, page_index, clip=clip_rect)
                cur_h = next_h
    writer.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)

def cut_pdf_by_breakpoints(
    *, 
    doc_path: str = "",
    output_path: str = "",
    h_breakpoints: List[float] = [],
    v_breakpoints: List[float] = [],
    page_range: str = "all",
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count)
    writer: fitz.Document = fitz.open()
    if h_breakpoints:
        h_breakpoints = [v for v in h_breakpoints if 0 <= v <= 1]
        h_breakpoints = [0] + h_breakpoints + [1]
        h_breakpoints.sort()
    else:
        h_breakpoints = [0., 1.]
    if v_breakpoints:
        v_breakpoints = [v for v in v_breakpoints if 0 <= v <= 1]
        v_breakpoints = [0] + v_breakpoints + [1]
        v_breakpoints.sort()
    else:
        v_breakpoints = [0., 1.]
    is_key_dict = {i:False for i in range(doc.page_count)}
    for idx, page_index in enumerate(roi_indices):
        if page_index in is_key_dict:
            is_key_dict[page_index] = True
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(roi_indices)})

    for idx, page_index in enumerate(roi_indices):
        progress_reporter("processing", f"正在处理第{idx+1}页", data={"current": float(idx+1), "total": float(len(roi_indices))})
        if not is_key_dict[page_index]:
            page = doc[page_index]
            new_page = writer.new_page(-1, width=page.rect.width, height=page.rect.height)
            new_page.show_pdf_page(page.rect, doc, page_index)
        else:
            page = doc[page_index]
            page_width, page_height = page.rect.width, page.rect.height
            for i in range(len(h_breakpoints)-1):
                for j in range(len(v_breakpoints)-1):
                    bbox = fitz.Rect(v_breakpoints[j]*page_width, h_breakpoints[i]*page_height, v_breakpoints[j+1]*page_width, h_breakpoints[i+1]*page_height)
                    new_page = writer.new_page(-1, width=bbox.width, height=bbox.height)
                    new_page.show_pdf_page(new_page.rect, doc, page_index, clip=bbox)
    writer.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)
