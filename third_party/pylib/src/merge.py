from typing import List
from pathlib import Path
import fitz
import traceback
from loguru import logger

from .utils import progress_reporter, parse_range


def merge_pdf(
    *, 
    doc_path_list: List[str] = [],
    output_path: str = "",
):
    with fitz.open() as writer:
        toc_list = []
        cur_page_number = 0
        progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(doc_path_list)})
        for idx, doc_path in enumerate(doc_path_list):
            progress_reporter("processing", f"正在处理第{idx+1}个文件", data={"current": float(idx+1), "total": float(len(doc_path_list))})
            with fitz.open(doc_path) as doc_temp:
                toc_temp = doc_temp.get_toc(simple=True)
                if toc_temp:
                    toc_temp = list(map(lambda x: [x[0], x[1], x[2]+cur_page_number], toc_temp))
                else:
                    toc_temp = [[1, Path(doc_path).stem, cur_page_number+1]]
                toc_list.extend(toc_temp)
                cur_page_number += doc_temp.page_count
                writer.insert_pdf(doc_temp)
        logger.debug(f"{toc_list=}")
        try:
            writer.set_toc(toc_list)
        except:
            logger.error(traceback.format_exc())
        writer.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)
            
def merge_pdf_by_page(
    *, 
    doc_path: str,
    output_path: str,
    page_range: str = "all",
):
    doc: fitz.Document = fitz.open(doc_path)
    roi_indices = parse_range(page_range, doc.page_count, is_multi_range=True)
    writer: fitz.Document = fitz.open()
    is_merge_dict = {i:False for i in range(doc.page_count)}
    for idx, sub_roi_indices in enumerate(roi_indices):
        for page_index in sub_roi_indices:
            logger.debug(f"idx:{idx}, page_index:{page_index}")
            if page_index in is_merge_dict:
                is_merge_dict[page_index] = True
    pno = 0
    progress_reporter("processing", "正在处理...", data={"current": 0, "total": len(doc)})

    while pno < len(doc):
        progress_reporter("processing", f"正在处理第{pno+1}页", data={"current": float(pno+1), "total": float(len(doc))})
        if not is_merge_dict[pno]:
            page = doc[pno]
            new_page = writer.new_page(-1, width=page.rect.width, height=page.rect.height)
            new_page.show_pdf_page(page.rect, doc, pno)
            pno += 1
        else:
            for sub_roi_indices in roi_indices:
                if pno in sub_roi_indices:
                    y_offset = []
                    max_w = 0
                    for i, page_index in enumerate(sub_roi_indices):
                        page = doc[page_index]
                        y_offset.append(page.rect.height)
                        max_w = max(max_w, page.rect.width)
                    new_page = writer.new_page(-1, width=max_w, height=sum(y_offset))
                    cur_y = 0
                    for i, page_index in enumerate(sub_roi_indices):
                        page: fitz.Page = doc[page_index]
                        new_page.show_pdf_page(fitz.Rect(0, cur_y, page.rect.width, cur_y+page.rect.height), doc, page_index)
                        cur_y += y_offset[i]
                        pno = page_index + 1
                    break
    writer.ez_save(output_path, garbage=4)
    progress_reporter("completed", "已完成", output_path)
