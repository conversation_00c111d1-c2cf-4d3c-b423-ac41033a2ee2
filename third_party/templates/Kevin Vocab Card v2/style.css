<style>
    @import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Noto+Sans+SC:wght@400;700&family=Lato:wght@400;700&display=swap');
    
    :root {
        --custom-border-radius: 0.5rem;
    }

    body {
        font-family: 'Lato', 'Noto Sans SC', sans-serif;
        background-color: var(--pico-background-color);
        padding: 20px;
        box-sizing: border-box;
        color: var(--pico-color);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        margin: 0;
        overscroll-behavior-y: contain;
    }
    .word-card-container { width: 100%;  margin: 0 auto; }
    
    article > header { padding: 1.5rem; }
    article > .card-body { padding: 0 1rem 1.5rem 1rem; display: flex; flex-direction: column; gap: 1rem; }

    .card-front-content { display: flex; flex-direction: column; justify-content: center; align-items: center; flex-grow: 1; padding: 30px; text-align: center; min-height: 350px; }
    .card-front-content .word { font-size: 3.5rem; margin-bottom: 20px; }
    .card-front-content .pronunciation-container { flex-direction: column; gap: 10px; }
    
    .word { font-family: 'Merriweather', serif; font-size: 2.5rem; color: var(--pico-h1-color); flex-grow: 1; margin: 0; }
    .pronunciation-container { display: flex; align-items: center; gap: 1rem; flex-wrap: wrap; }
    .pronunciation-block { display: flex; align-items: center; gap: 0.5rem; background-color: var(--pico-form-element-background-color); padding: 0.5rem 1rem; border-radius: var(--custom-border-radius); }
    .pronunciation-flag { font-size: 1.5rem; line-height: 1; }
    .pronunciation-text { font-size: 1.1rem; color: var(--pico-muted-color); }
    
    .pronunciation-play-btn.outline { padding: 0.5rem; width: 40px; height: 40px; border-radius: 50%; }
    .pronunciation-play-btn svg { width: 20px; height: 20px; fill: currentColor; }

    .card-top-info { padding-bottom: 1rem; border-bottom: 1px solid var(--pico-muted-border-color); }
    
    .tags { display: flex; flex-wrap: wrap; gap: 0.5rem; margin-bottom: 1rem; }
    .tag { background-color: var(--pico-form-element-background-color); color: var(--pico-primary); padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.85rem; font-weight: 700; border: 1px solid var(--pico-form-element-border-color); }
    
    .inflections-list { display: flex; flex-wrap: wrap; gap: 0.25rem 1.5rem; }
    .inflection-item { font-size: 0.95rem; color: var(--pico-color); }
    .inflection-label { font-weight: 700; color: var(--pico-primary); margin-right: 0.25rem; }
    
    .main-section-container, details.main-section {
        border: 1px solid var(--pico-muted-border-color);
        border-radius: var(--custom-border-radius);
        background-color: var(--pico-card-background-color);
    }
    .main-section-container { padding: 1rem; }
    details.main-section > summary { padding: 1rem; font-weight: normal; }
    details.main-section > .section-content { padding: 0 1rem 1rem 1rem; }

    .section-title { color: var(--pico-primary); display: inline; margin: 0; font-size: 1.1rem; font-weight: 700;}
    .main-section-container > .section-title { display: block; margin-bottom: 1rem; }
    
    .section-content { padding-top: 1rem; }
    .main-section-container > .section-content { padding-top: 0; }
    
    .definition-item, .definition-item-static {
        background-color: var(--pico-card-sectioning-background-color);
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: var(--custom-border-radius);
        border: 1px solid var(--pico-card-border-color);
    }
    .definition-item:last-child, .definition-item-static:last-child { margin-bottom: 0; }
    
    .definition-item > summary { position: relative; padding: 0; display: block; }
    .definition-item > summary::after { position: absolute; float: none; right: 0; top: 50%; transform: translateY(-50%) rotate(-90deg); }
    .definition-item[open] > summary::after { transform: translateY(-50%) rotate(0); }
    .definition-item > .section-content { padding: 1rem 0 0 0; }

    .part-of-speech { font-weight: 700; font-size: 0.9rem; color: var(--pico-primary); background-color: rgba(var(--pico-primary-rgb), 0.1); padding: 3px 8px; border-radius: 0.25rem; margin-right: 8px; vertical-align: baseline; }
    .meaning-en, .meaning-zh { display: block; }
    .meaning-en { margin-bottom: 8px; font-size: 1rem; line-height: 1.7; color: var(--pico-color); }
    .meaning-zh { font-size: 0.95rem; color: var(--pico-muted-color); }
    
    /* Tab System Styles */
    .tabs-container input[type="radio"] { display: none; }
    .tabs-nav { display: flex; flex-wrap: wrap; border-bottom: 2px solid var(--pico-form-element-border-color); margin-bottom: 1rem; justify-content: center; }
    .tabs-nav label { padding: 0.8rem 1.75rem; cursor: pointer; border-bottom: 2px solid transparent; margin-bottom: -2px; transition: all 0.2s ease-in-out; color: var(--pico-muted-color); font-weight: 600; text-align: center; }
    .tabs-nav label:hover { color: var(--pico-primary); }
    #tab-pos-0:checked ~ .tabs-nav label[for="tab-pos-0"], #tab-pos-1:checked ~ .tabs-nav label[for="tab-pos-1"], #tab-pos-2:checked ~ .tabs-nav label[for="tab-pos-2"], #tab-pos-3:checked ~ .tabs-nav label[for="tab-pos-3"], #tab-pos-4:checked ~ .tabs-nav label[for="tab-pos-4"] { color: var(--pico-primary); border-bottom-color: var(--pico-primary); }
    .tab-panel { display: none; }
    #tab-pos-0:checked ~ .tabs-content > #panel-pos-0, #tab-pos-1:checked ~ .tabs-content > #panel-pos-1, #tab-pos-2:checked ~ .tabs-content > #panel-pos-2, #tab-pos-3:checked ~ .tabs-content > #panel-pos-3, #tab-pos-4:checked ~ .tabs-content > #panel-pos-4 { display: block; }
    
    /* ▼▼▼ MODIFICATION 2: Updated styles for sentence blocks ▼▼▼ */
    .example-sentence-block {
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid var(--pico-muted-border-color);
        background-color: var(--pico-card-background-color);
        border-radius: var(--custom-border-radius);
    }
    .example-sentence-block:last-child { margin-bottom: 0; }
    .sentence-en { display: flex; justify-content: space-between; align-items: center; gap: 10px; font-style: italic; color: var(--pico-color); margin: 0 0 5px 0; }
    .sentence-zh { font-size: 0.9rem; color: var(--pico-muted-color); margin: 0; }
    .sentence-audio-btn { background: none; border: none; cursor: pointer; padding: 0.25rem; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 50%; transition: background-color 0.2s ease; flex-shrink: 0; }
    .sentence-audio-btn:hover { background-color: var(--pico-form-element-selected-background-color); }
    .sentence-audio-btn svg { width: 18px; height: 18px; fill: var(--pico-primary); }
    
    .phrase-en { font-weight: 600; margin-right: 1em;}
    .phrase-zh { color: var(--pico-muted-color); }
    
    .image-slider { display: flex; gap: 1rem; overflow-x: auto; padding-bottom: 1rem; scroll-snap-type: x mandatory; -ms-overflow-style: none; scrollbar-width: none; }
    .image-slider::-webkit-scrollbar { display: none; }
    .image-slider img { width: 90%; max-width: 400px; height: auto; object-fit: cover; border-radius: var(--custom-border-radius); flex-shrink: 0; scroll-snap-align: center; }
    
    .dictionary-links { display: flex; flex-wrap: wrap; gap: 0.5rem; }
    .dictionary-links a { display: inline-flex; align-items: center; gap: 0.5rem; }
    
    [data-synonyms-antonyms] p { margin: 0 0 0.5rem 0; font-size: 0.95rem; }
    [data-etymology] { font-size: 0.95rem; line-height: 1.6; margin: 0; }
    .anki-note { font-size: 1rem; line-height: 1.7; }
    .anki-note * { max-width: 100%; }
</style>
<script>
    var displayConfig = { definitionMode: 'both', pronunciationConfig: 'both' };
    var inflectionLabels = { plural: "复数", third_person: "三单", past_tense: "过去式", past_participle: "过去分词", present_participle: "现在分词" };
    var externalLinkSVG = `<svg viewBox="0 0 24 24" style="width:1em; height:1em; fill:currentColor;"><path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"></path></svg>`;

    function createPronunciationBlock(type, pronText, audioUrl, audioPlayer) { const block = document.createElement('div'); block.className = 'pronunciation-block'; if (displayConfig.pronunciationConfig === 'both') { const flag = document.createElement('span'); flag.className = 'pronunciation-flag'; flag.textContent = type === 'uk' ? '🇬🇧' : '🇺🇸'; block.appendChild(flag); } const text = document.createElement('span'); text.className = 'pronunciation-text'; text.textContent = pronText; block.appendChild(text); if (audioUrl) { const btn = document.createElement('button'); btn.className = 'pronunciation-play-btn outline'; btn.title = `Play ${type.toUpperCase()} pronunciation`; btn.innerHTML = `<svg viewBox="0 0 24 24"><path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"></path></svg>`; btn.addEventListener('click', (e) => { e.stopPropagation(); if (audioPlayer) { audioPlayer.src = audioUrl; audioPlayer.play(); } }); block.appendChild(btn); } return block; }
    
    // ▼▼▼ MODIFICATION 2: Changed from 'div' to 'article' ▼▼▼
    function createSentenceBlock(sentence, audioPlayer) {
        const block = document.createElement('article'); // Use <article> tag
        block.className = 'example-sentence-block';
        
        const pEn = document.createElement('p');
        pEn.className = 'sentence-en';
        const textSpan = document.createElement('span');
        textSpan.textContent = sentence.sentence_en;
        pEn.appendChild(textSpan);
        if (sentence.audio_url) {
            const btn = document.createElement('button');
            btn.className = 'sentence-audio-btn';
            btn.title = 'Play sentence audio';
            btn.innerHTML = `<svg viewBox="0 0 24 24"><path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"></path></svg>`;
            btn.addEventListener('click', e => {
                e.preventDefault();
                audioPlayer.src = sentence.audio_url;
                audioPlayer.play();
            });
            pEn.appendChild(btn);
        }
        block.appendChild(pEn);
        
        const pZh = document.createElement('p');
        pZh.className = 'sentence-zh';
        pZh.textContent = sentence.sentence_zh;
        block.appendChild(pZh);
        
        if (sentence.image_url) {
            const img = document.createElement('img');
            img.className = 'example-sentence-image';
            img.src = sentence.image_url;
            img.alt = `Visual for "${sentence.sentence_en}"`;
            img.loading = 'lazy';
            block.appendChild(img);
        }
        return block;
    }

    function renderDefinitionItem(def, sentenceAudioPlayer) { const hasSentences = def.sentences && def.sentences.length > 0; const rootEl = hasSentences ? document.createElement('details') : document.createElement('div'); rootEl.className = hasSentences ? 'definition-item' : 'definition-item-static'; const summaryContent = document.createElement('div'); if (displayConfig.definitionMode !== 'zh') { const span = document.createElement('span'); span.className = 'meaning-en'; span.innerHTML = `<span class="part-of-speech">${def.pos}</span>${def.meaning_en}`; summaryContent.appendChild(span); } if (displayConfig.definitionMode !== 'en') { const span = document.createElement('span'); span.className = 'meaning-zh'; span.innerHTML = displayConfig.definitionMode === 'zh' ? `<span class="part-of-speech">${def.pos}</span>${def.meaning_zh}` : def.meaning_zh; summaryContent.appendChild(span); } if (hasSentences) { const summaryElement = document.createElement('summary'); summaryElement.appendChild(summaryContent); const collapsibleContent = document.createElement('div'); collapsibleContent.className = 'section-content'; def.sentences.forEach(sentence => { collapsibleContent.appendChild(createSentenceBlock(sentence, sentenceAudioPlayer)); }); rootEl.appendChild(summaryElement); rootEl.appendChild(collapsibleContent); } else { rootEl.appendChild(summaryContent); } return rootEl; }

    // ▼▼▼ MODIFICATION 1: Robust swipe function for tabs ▼▼▼
    function enableSwipeForTabs(tabsContainer) {
        const contentArea = tabsContainer.querySelector('.tabs-content');
        if (!contentArea) return;

        let touchStartX = 0; let touchStartY = 0;
        let touchEndX = 0; let touchEndY = 0;
        const SWIPE_THRESHOLD = 50;

        contentArea.addEventListener('touchstart', (event) => {
            touchStartX = event.touches[0].clientX;
            touchStartY = event.touches[0].clientY;
        }, { passive: true }); // We can keep passive here for the start event

        contentArea.addEventListener('touchmove', (event) => {
             // If we're not tracking a start, don't do anything
            if (touchStartX === 0) return;
            
            const deltaX = event.touches[0].clientX - touchStartX;
            const deltaY = event.touches[0].clientY - touchStartY;

            // If the swipe is more horizontal than vertical, prevent default scrolling
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                event.preventDefault();
            }
        }, { passive: false }); // `passive` must be false to use `preventDefault`

        contentArea.addEventListener('touchend', (event) => {
            touchEndX = event.changedTouches[0].clientX;
            handleSwipe();
            // Reset coordinates
            touchStartX = 0;
            touchStartY = 0;
        });

        function handleSwipe() {
            const deltaX = touchEndX - touchStartX;
            if (Math.abs(deltaX) < SWIPE_THRESHOLD) return;

            const radios = Array.from(tabsContainer.querySelectorAll('input[type="radio"]'));
            if (radios.length <= 1) return;

            const currentIndex = radios.findIndex(radio => radio.checked);
            if (currentIndex === -1) return;

            let nextIndex;
            if (deltaX < 0) { // Swipe Left -> Next Tab
                nextIndex = (currentIndex + 1) % radios.length;
            } else { // Swipe Right -> Previous Tab
                nextIndex = (currentIndex - 1 + radios.length) % radios.length;
            }
            radios[nextIndex].checked = true;
        }
    }

    function initializeCard() {
        let data; try { const dataEl = document.getElementById('anki-data'); if (!dataEl || !dataEl.textContent.trim()) return; data = JSON.parse(dataEl.textContent); } catch (e) { console.error("Anki Card Error:", e); document.body.innerHTML = `<div style="color: var(--pico-del-color); padding: 20px; text-align: left;"><strong>Template Error:</strong> Could not parse 'Data' field.<br><br><strong>Details:</strong><br>${e}</div>`; return; }
        const context = document.querySelector('.word-card-container article'); if (!context) return;
        if (context.querySelector('.card-front-content')) { context.querySelector('[data-word]').textContent = data.word; const frontPronunciationContainer = context.querySelector('[data-pronunciation-container]'); frontPronunciationContainer.innerHTML = ''; const wordAudioPlayer = context.querySelector('[data-word-audio-player]'); if (data.pronunciation?.uk) { frontPronunciationContainer.appendChild(createPronunciationBlock('uk', data.pronunciation.uk, data.audio_url?.uk, wordAudioPlayer)); } if (data.pronunciation?.us) { frontPronunciationContainer.appendChild(createPronunciationBlock('us', data.pronunciation.us, data.audio_url?.us, wordAudioPlayer)); } }
        if (context.querySelector('.card-body')) {
            context.querySelector('header [data-word]').textContent = data.word; const pronunciationContainer = context.querySelector('header [data-pronunciation-container]'); pronunciationContainer.innerHTML = ''; const wordAudioPlayer = context.querySelector('article > [data-word-audio-player]'); if (data.pronunciation?.uk) { pronunciationContainer.appendChild(createPronunciationBlock('uk', data.pronunciation.uk, data.audio_url?.uk, wordAudioPlayer)); } if (data.pronunciation?.us) { pronunciationContainer.appendChild(createPronunciationBlock('us', data.pronunciation.us, data.audio_url?.us, wordAudioPlayer)); }
            const setupOptionalSection = (selector, dataItem, renderFn) => { const element = context.querySelector(selector); if (!element) return; const section = element.closest('.main-section-container, details.main-section, .card-top-info'); let itemExists = Array.isArray(dataItem) ? dataItem.length > 0 : (typeof dataItem === 'object' && dataItem !== null ? Object.keys(dataItem).length > 0 : !!dataItem); if (itemExists) { if (section) section.style.display = ''; if (renderFn) { element.innerHTML = ''; renderFn(element, dataItem); } } else { if (section) section.style.display = 'none'; } };
            const sentenceAudioPlayer = context.querySelector('[data-sentence-audio-player]');
            setupOptionalSection('[data-tags]', data.tags, (el, items) => { items.forEach(t => { const e = document.createElement('span'); e.className = 'tag'; e.textContent = t; el.appendChild(e); }); });
            setupOptionalSection('[data-inflections]', data.inflections, (el, items) => { Object.entries(items).forEach(([key, value]) => { const label = inflectionLabels[key] || key; const item = document.createElement('span'); item.className = 'inflection-item'; item.innerHTML = `<span class="inflection-label">${label}:</span>${value}`; el.appendChild(item); }); });
            if (!data.tags?.length && !Object.keys(data.inflections || {}).length) { const topInfo = context.querySelector('.card-top-info'); if (topInfo) topInfo.style.display = 'none'; }
            setupOptionalSection('[data-image-mnemonic]', data.image_urls, (el, urls) => { urls.forEach(url => { const img = document.createElement('img'); img.src = url; img.alt = `Mnemonic for ${data.word}`; img.loading = 'lazy'; el.appendChild(img); }); });
            setupOptionalSection('[data-phrases]', data.phrases, (el, items) => { items.forEach(p => { const li = document.createElement('li'); li.innerHTML = `<span class="phrase-en">${p.phrase_en}</span><span class="phrase-zh">${p.phrase_zh}</span>`; el.appendChild(li); }); });
            setupOptionalSection('[data-sentences-container]', data.sentences, (el, items) => { items.forEach(sentence => { el.appendChild(createSentenceBlock(sentence, sentenceAudioPlayer)); }); });
            setupOptionalSection('[data-dictionaries]', data.dictionaries, (el, items) => { items.forEach(dict => { const link = document.createElement('a'); link.href = dict.url_template.replace('{word}', data.word); link.target = '_blank'; link.rel = 'noopener noreferrer'; link.innerHTML = `<span>${dict.name}</span>${externalLinkSVG}`; el.appendChild(link); }); });
            setupOptionalSection('[data-synonyms-antonyms]', data.synonyms || data.antonyms, (el, _) => { if(data.synonyms) { const p = document.createElement('p'); p.innerHTML = `<strong>同义词:</strong> ${data.synonyms}`; el.appendChild(p); } if(data.antonyms) { const p = document.createElement('p'); p.innerHTML = `<strong>反义词:</strong> ${data.antonyms}`; el.appendChild(p); } });
            setupOptionalSection('[data-etymology]', data.etymology, (el, item) => { el.textContent = item; });
            
            setupOptionalSection('[data-definitions-container]', data.definitions, (container, definitions) => {
                const groupedByPos = definitions.reduce((acc, def) => { (acc[def.pos] = acc[def.pos] || []).push(def); return acc; }, {});
                const posKeys = Object.keys(groupedByPos);
                const useTabs = posKeys.length > 1 && posKeys.some(key => groupedByPos[key].length > 1);
                if (useTabs) {
                    const tabsContainer = document.createElement('div'); tabsContainer.className = 'tabs-container';
                    const nav = document.createElement('nav'); nav.className = 'tabs-nav';
                    const content = document.createElement('div'); content.className = 'tabs-content';
                    posKeys.forEach((pos, index) => {
                        const radioId = `tab-pos-${index}`; const panelId = `panel-pos-${index}`;
                        const radio = document.createElement('input'); radio.type = 'radio'; radio.id = radioId; radio.name = 'pos-tabs'; if (index === 0) radio.checked = true;
                        const label = document.createElement('label'); label.htmlFor = radioId; label.textContent = pos; nav.appendChild(label);
                        const panel = document.createElement('div'); panel.className = 'tab-panel'; panel.id = panelId; groupedByPos[pos].forEach(def => { panel.appendChild(renderDefinitionItem(def, sentenceAudioPlayer)); }); content.appendChild(panel);
                        tabsContainer.appendChild(radio);
                    });
                    tabsContainer.appendChild(nav); tabsContainer.appendChild(content); container.appendChild(tabsContainer);
                    enableSwipeForTabs(tabsContainer);
                } else {
                    definitions.forEach(def => { container.appendChild(renderDefinitionItem(def, sentenceAudioPlayer)); });
                }
            });
        }
    }
</script>