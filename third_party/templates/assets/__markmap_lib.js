/**
 * Minified by jsDelivr using Terser v5.19.2.
 * Original file: /npm/markmap-lib@0.17.0/dist/browser/index.iife.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(e,t){"use strict";const n={jsdelivr:e=>`https://cdn.jsdelivr.net/npm/${e}`,unpkg:e=>`https://unpkg.com/${e}`};class r{constructor(){this.providers={...n},this.provider="jsdelivr"}async getFastestProvider(e=5e3,t="npm2url/dist/index.cjs"){const n=new AbortController;let r=0;try{return await new Promise(((s,i)=>{Promise.all(Object.entries(this.providers).map((async([e,r])=>{try{await async function(e,t){const n=await fetch(e,{signal:t});if(!n.ok)throw n;await n.text()}(r(t),n.signal),s(e)}catch{}}))).then((()=>i(new Error("All providers failed")))),r=setTimeout(i,e,new Error("Timed out"))}))}finally{n.abort(),clearTimeout(r)}}async findFastestProvider(e,t){return this.provider=await this.getFastestProvider(e,t),this.provider}setProvider(e,t){t?this.providers[e]=t:delete this.providers[e]}getFullUrl(e,t=this.provider){if(e.includes("://"))return e;const n=this.providers[t];if(!n)throw new Error(`Provider ${t} not found`);return n(e)}}class s{constructor(){this.listeners=[]}tap(e){return this.listeners.push(e),()=>this.revoke(e)}revoke(e){const t=this.listeners.indexOf(e);t>=0&&this.listeners.splice(t,1)}revokeAll(){this.listeners.splice(0)}call(...e){for(const t of this.listeners)t(...e)}}function i(){}Math.random().toString(36).slice(2,8);
/*! @gera2ld/jsx-dom v2.2.2 | ISC License */
const a=1,o=2,c="http://www.w3.org/2000/svg",l="http://www.w3.org/1999/xlink",u={show:l,actuate:l,href:l},h=e=>"string"==typeof e||"number"==typeof e,p=e=>(null==e?void 0:e.vtype)===a,d=e=>(null==e?void 0:e.vtype)===o;function f(e,t,...n){return function(e,t){let n;if("string"==typeof e)n=a;else{if("function"!=typeof e)throw new Error("Invalid VNode type");n=o}return{vtype:n,type:e,props:t}}(e,t=Object.assign({},t,{children:1===n.length?n[0]:n}))}function E(e){return e.children}const m={isSvg:!1};function T(e,t){Array.isArray(t)||(t=[t]),(t=t.filter(Boolean)).length&&e.append(...t)}const _={className:"class",labelFor:"for"};function A(e,t,n,r){if(t=_[t]||t,!0===n)e.setAttribute(t,"");else if(!1===n)e.removeAttribute(t);else{const s=r?u[t]:void 0;void 0!==s?e.setAttributeNS(s,t,n):e.setAttribute(t,n)}}function g(e,t){return Array.isArray(e)?e.map((e=>g(e,t))).reduce(((e,t)=>e.concat(t)),[]):C(e,t)}function C(e,t=m){if(null==e||"boolean"==typeof e)return null;if(e instanceof Node)return e;if(d(e)){const{type:n,props:r}=e;if(n===E){const e=document.createDocumentFragment();if(r.children){T(e,g(r.children,t))}return e}return C(n(r),t)}if(h(e))return document.createTextNode(`${e}`);if(p(e)){let n;const{type:r,props:s}=e;if(t.isSvg||"svg"!==r||(t=Object.assign({},t,{isSvg:!0})),n=t.isSvg?document.createElementNS(c,r):document.createElement(r),function(e,t,n){for(const r in t)if("key"!==r&&"children"!==r&&"ref"!==r)if("dangerouslySetInnerHTML"===r)e.innerHTML=t[r].__html;else if("innerHTML"===r||"textContent"===r||"innerText"===r||"value"===r&&["textarea","select"].includes(e.tagName)){const n=t[r];null!=n&&(e[r]=n)}else r.startsWith("on")?e[r.toLowerCase()]=t[r]:A(e,r,t[r],n.isSvg)}(n,s,t),s.children){let e=t;t.isSvg&&"foreignObject"===r&&(e=Object.assign({},e,{isSvg:!1}));const i=g(s.children,e);null!=i&&T(n,i)}const{ref:i}=s;return"function"==typeof i&&i(n),n}throw new Error("mount: Invalid Vnode!")}function I(...e){return C(f(...e))}const N=function(e){const t={};return function(...n){const r=`${n[0]}`;let s=t[r];return s||(s={value:e(...n)},t[r]=s),s.value}}((e=>{document.head.append(I("link",{rel:"preload",as:"script",href:e}))})),D={};async function S(e,t){var n;const r="script"===e.type&&(null==(n=e.data)?void 0:n.src)||"";if(e.loaded||(e.loaded=D[r]),!e.loaded){const n=function(){const e={};return e.promise=new Promise(((t,n)=>{e.resolve=t,e.reject=n})),e}();if(e.loaded=n.promise,"script"===e.type&&(document.head.append(I("script",{...e.data,onLoad:()=>n.resolve(),onError:n.reject})),r?D[r]=e.loaded:n.resolve()),"iife"===e.type){const{fn:r,getParams:s}=e.data;r(...(null==s?void 0:s(t))||[]),n.resolve()}}await e.loaded}async function k(e,t){e.forEach((e=>{var t;"script"===e.type&&(null==(t=e.data)?void 0:t.src)&&N(e.data.src)})),t={getMarkmap:()=>window.markmap,...t};for(const n of e)await S(n,t)}function b(e){return{type:"script",data:{src:e}}}function O(e){return{type:"stylesheet",data:{href:e}}}const R={xml:!1,decodeEntities:!0},L={_useHtmlParser2:!0,xmlMode:!0};function y(e){return(null==e?void 0:e.xml)?"boolean"==typeof e.xml?L:{...L,...e.xml}:null!=e?e:void 0}var M,F;(F=M||(M={})).Root="root",F.Text="text",F.Directive="directive",F.Comment="comment",F.Script="script",F.Style="style",F.Tag="tag",F.CDATA="cdata",F.Doctype="doctype";const x=M.Root,P=M.Text,v=M.Directive,B=M.Comment,w=M.Script,U=M.Style,H=M.Tag,G=M.CDATA,q=M.Doctype;let Y=class{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(e){this.parent=e}get previousSibling(){return this.prev}set previousSibling(e){this.prev=e}get nextSibling(){return this.next}set nextSibling(e){this.next=e}cloneNode(e=!1){return ie(this,e)}};class j extends Y{constructor(e){super(),this.data=e}get nodeValue(){return this.data}set nodeValue(e){this.data=e}}class z extends j{constructor(){super(...arguments),this.type=M.Text}get nodeType(){return 3}}class V extends j{constructor(){super(...arguments),this.type=M.Comment}get nodeType(){return 8}}class Q extends j{constructor(e,t){super(t),this.name=e,this.type=M.Directive}get nodeType(){return 1}}class $ extends Y{constructor(e){super(),this.children=e}get firstChild(){var e;return null!==(e=this.children[0])&&void 0!==e?e:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(e){this.children=e}}class W extends ${constructor(){super(...arguments),this.type=M.CDATA}get nodeType(){return 4}}class X extends ${constructor(){super(...arguments),this.type=M.Root}get nodeType(){return 9}}class K extends ${constructor(e,t,n=[],r=("script"===e?M.Script:"style"===e?M.Style:M.Tag)){super(n),this.name=e,this.attribs=t,this.type=r}get nodeType(){return 1}get tagName(){return this.name}set tagName(e){this.name=e}get attributes(){return Object.keys(this.attribs).map((e=>{var t,n;return{name:e,value:this.attribs[e],namespace:null===(t=this["x-attribsNamespace"])||void 0===t?void 0:t[e],prefix:null===(n=this["x-attribsPrefix"])||void 0===n?void 0:n[e]}}))}}function Z(e){return(t=e).type===M.Tag||t.type===M.Script||t.type===M.Style;var t}function J(e){return e.type===M.CDATA}function ee(e){return e.type===M.Text}function te(e){return e.type===M.Comment}function ne(e){return e.type===M.Directive}function re(e){return e.type===M.Root}function se(e){return Object.prototype.hasOwnProperty.call(e,"children")}function ie(e,t=!1){let n;if(ee(e))n=new z(e.data);else if(te(e))n=new V(e.data);else if(Z(e)){const r=t?ae(e.children):[],s=new K(e.name,{...e.attribs},r);r.forEach((e=>e.parent=s)),null!=e.namespace&&(s.namespace=e.namespace),e["x-attribsNamespace"]&&(s["x-attribsNamespace"]={...e["x-attribsNamespace"]}),e["x-attribsPrefix"]&&(s["x-attribsPrefix"]={...e["x-attribsPrefix"]}),n=s}else if(J(e)){const r=t?ae(e.children):[],s=new W(r);r.forEach((e=>e.parent=s)),n=s}else if(re(e)){const r=t?ae(e.children):[],s=new X(r);r.forEach((e=>e.parent=s)),e["x-mode"]&&(s["x-mode"]=e["x-mode"]),n=s}else{if(!ne(e))throw new Error(`Not implemented yet: ${e.type}`);{const t=new Q(e.name,e.data);null!=e["x-name"]&&(t["x-name"]=e["x-name"],t["x-publicId"]=e["x-publicId"],t["x-systemId"]=e["x-systemId"]),n=t}}return n.startIndex=e.startIndex,n.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(n.sourceCodeLocation=e.sourceCodeLocation),n}function ae(e){const t=e.map((e=>ie(e,!0)));for(let e=1;e<t.length;e++)t[e].prev=t[e-1],t[e-1].next=t[e];return t}const oe={withStartIndices:!1,withEndIndices:!1,xmlMode:!1};class ce{constructor(e,t,n){this.dom=[],this.root=new X(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(n=t,t=oe),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:oe,this.elementCB=null!=n?n:null}onparserinit(e){this.parser=e}onreset(){this.dom=[],this.root=new X(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(e){this.handleCallback(e)}onclosetag(){this.lastNode=null;const e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)}onopentag(e,t){const n=this.options.xmlMode?M.Tag:void 0,r=new K(e,t,void 0,n);this.addNode(r),this.tagStack.push(r)}ontext(e){const{lastNode:t}=this;if(t&&t.type===M.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{const t=new z(e);this.addNode(t),this.lastNode=t}}oncomment(e){if(this.lastNode&&this.lastNode.type===M.Comment)return void(this.lastNode.data+=e);const t=new V(e);this.addNode(t),this.lastNode=t}oncommentend(){this.lastNode=null}oncdatastart(){const e=new z(""),t=new W([e]);this.addNode(t),e.parent=t,this.lastNode=e}oncdataend(){this.lastNode=null}onprocessinginstruction(e,t){const n=new Q(e,t);this.addNode(n)}handleCallback(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e}addNode(e){const t=this.tagStack[this.tagStack.length-1],n=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),n&&(e.prev=n,n.next=e),e.parent=t,this.lastNode=null}}const le=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map((e=>e.charCodeAt(0)))),ue=new Uint16Array("Ȁaglq\tɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map((e=>e.charCodeAt(0))));var he;const pe=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),de=null!==(he=String.fromCodePoint)&&void 0!==he?he:function(e){let t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e),t};function fe(e){var t;return e>=55296&&e<=57343||e>1114111?65533:null!==(t=pe.get(e))&&void 0!==t?t:e}var Ee,me;(me=Ee||(Ee={}))[me.NUM=35]="NUM",me[me.SEMI=59]="SEMI",me[me.EQUALS=61]="EQUALS",me[me.ZERO=48]="ZERO",me[me.NINE=57]="NINE",me[me.LOWER_A=97]="LOWER_A",me[me.LOWER_F=102]="LOWER_F",me[me.LOWER_X=120]="LOWER_X",me[me.LOWER_Z=122]="LOWER_Z",me[me.UPPER_A=65]="UPPER_A",me[me.UPPER_F=70]="UPPER_F",me[me.UPPER_Z=90]="UPPER_Z";var Te,_e,Ae,ge,Ce,Ie;function Ne(e){return e>=Ee.ZERO&&e<=Ee.NINE}function De(e){return e===Ee.EQUALS||function(e){return e>=Ee.UPPER_A&&e<=Ee.UPPER_Z||e>=Ee.LOWER_A&&e<=Ee.LOWER_Z||Ne(e)}(e)}(_e=Te||(Te={}))[_e.VALUE_LENGTH=49152]="VALUE_LENGTH",_e[_e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",_e[_e.JUMP_TABLE=127]="JUMP_TABLE",(ge=Ae||(Ae={}))[ge.EntityStart=0]="EntityStart",ge[ge.NumericStart=1]="NumericStart",ge[ge.NumericDecimal=2]="NumericDecimal",ge[ge.NumericHex=3]="NumericHex",ge[ge.NamedEntity=4]="NamedEntity",(Ie=Ce||(Ce={}))[Ie.Legacy=0]="Legacy",Ie[Ie.Strict=1]="Strict",Ie[Ie.Attribute=2]="Attribute";class Se{constructor(e,t,n){this.decodeTree=e,this.emitCodePoint=t,this.errors=n,this.state=Ae.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=Ce.Strict}startEntity(e){this.decodeMode=e,this.state=Ae.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case Ae.EntityStart:return e.charCodeAt(t)===Ee.NUM?(this.state=Ae.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=Ae.NamedEntity,this.stateNamedEntity(e,t));case Ae.NumericStart:return this.stateNumericStart(e,t);case Ae.NumericDecimal:return this.stateNumericDecimal(e,t);case Ae.NumericHex:return this.stateNumericHex(e,t);case Ae.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===Ee.LOWER_X?(this.state=Ae.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=Ae.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,n,r){if(t!==n){const s=n-t;this.result=this.result*Math.pow(r,s)+parseInt(e.substr(t,s),r),this.consumed+=s}}stateNumericHex(e,t){const n=t;for(;t<e.length;){const s=e.charCodeAt(t);if(!(Ne(s)||(r=s,r>=Ee.UPPER_A&&r<=Ee.UPPER_F||r>=Ee.LOWER_A&&r<=Ee.LOWER_F)))return this.addToNumericResult(e,n,t,16),this.emitNumericEntity(s,3);t+=1}var r;return this.addToNumericResult(e,n,t,16),-1}stateNumericDecimal(e,t){const n=t;for(;t<e.length;){const r=e.charCodeAt(t);if(!Ne(r))return this.addToNumericResult(e,n,t,10),this.emitNumericEntity(r,2);t+=1}return this.addToNumericResult(e,n,t,10),-1}emitNumericEntity(e,t){var n;if(this.consumed<=t)return null===(n=this.errors)||void 0===n||n.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===Ee.SEMI)this.consumed+=1;else if(this.decodeMode===Ce.Strict)return 0;return this.emitCodePoint(fe(this.result),this.consumed),this.errors&&(e!==Ee.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){const{decodeTree:n}=this;let r=n[this.treeIndex],s=(r&Te.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){const i=e.charCodeAt(t);if(this.treeIndex=be(n,r,this.treeIndex+Math.max(1,s),i),this.treeIndex<0)return 0===this.result||this.decodeMode===Ce.Attribute&&(0===s||De(i))?0:this.emitNotTerminatedNamedEntity();if(r=n[this.treeIndex],s=(r&Te.VALUE_LENGTH)>>14,0!==s){if(i===Ee.SEMI)return this.emitNamedEntityData(this.treeIndex,s,this.consumed+this.excess);this.decodeMode!==Ce.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;const{result:t,decodeTree:n}=this,r=(n[t]&Te.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,r,this.consumed),null===(e=this.errors)||void 0===e||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,n){const{decodeTree:r}=this;return this.emitCodePoint(1===t?r[e]&~Te.VALUE_LENGTH:r[e+1],n),3===t&&this.emitCodePoint(r[e+2],n),n}end(){var e;switch(this.state){case Ae.NamedEntity:return 0===this.result||this.decodeMode===Ce.Attribute&&this.result!==this.treeIndex?0:this.emitNotTerminatedNamedEntity();case Ae.NumericDecimal:return this.emitNumericEntity(0,2);case Ae.NumericHex:return this.emitNumericEntity(0,3);case Ae.NumericStart:return null===(e=this.errors)||void 0===e||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case Ae.EntityStart:return 0}}}function ke(e){let t="";const n=new Se(e,(e=>t+=de(e)));return function(e,r){let s=0,i=0;for(;(i=e.indexOf("&",i))>=0;){t+=e.slice(s,i),n.startEntity(r);const a=n.write(e,i+1);if(a<0){s=i+n.end();break}s=i+a,i=0===a?s+1:s}const a=t+e.slice(s);return t="",a}}function be(e,t,n,r){const s=(t&Te.BRANCH_LENGTH)>>7,i=t&Te.JUMP_TABLE;if(0===s)return 0!==i&&r===i?n:-1;if(i){const t=r-i;return t<0||t>=s?-1:e[n+t]-1}let a=n,o=a+s-1;for(;a<=o;){const t=a+o>>>1,n=e[t];if(n<r)a=t+1;else{if(!(n>r))return e[t+s];o=t-1}}return-1}const Oe=ke(le);function Re(e,t=Ce.Legacy){return Oe(e,t)}ke(ue);const Le=/["&'<>$\x80-\uFFFF]/g,ye=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]),Me=null!=String.prototype.codePointAt?(e,t)=>e.codePointAt(t):(e,t)=>55296==(64512&e.charCodeAt(t))?1024*(e.charCodeAt(t)-55296)+e.charCodeAt(t+1)-56320+65536:e.charCodeAt(t);function Fe(e){let t,n="",r=0;for(;null!==(t=Le.exec(e));){const s=t.index,i=e.charCodeAt(s),a=ye.get(i);void 0!==a?(n+=e.substring(r,s)+a,r=s+1):(n+=`${e.substring(r,s)}&#x${Me(e,s).toString(16)};`,r=Le.lastIndex+=Number(55296==(64512&i)))}return n+e.substr(r)}function xe(e,t){return function(n){let r,s=0,i="";for(;r=e.exec(n);)s!==r.index&&(i+=n.substring(s,r.index)),i+=t.get(r[0].charCodeAt(0)),s=r.index+1;return i+n.substring(s)}}const Pe=xe(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),ve=xe(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]])),Be=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map((e=>[e.toLowerCase(),e]))),we=new Map(["definitionURL","attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map((e=>[e.toLowerCase(),e]))),Ue=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function He(e){return e.replace(/"/g,"&quot;")}const Ge=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function qe(e,t={}){const n="length"in e?e:[e];let r="";for(let e=0;e<n.length;e++)r+=Ye(n[e],t);return r}function Ye(e,t){switch(e.type){case x:return qe(e.children,t);case q:case v:return`<${e.data}>`;case B:return function(e){return`\x3c!--${e.data}--\x3e`}(e);case G:return function(e){return`<![CDATA[${e.children[0].data}]]>`}(e);case w:case U:case H:return function(e,t){var n;"foreign"===t.xmlMode&&(e.name=null!==(n=Be.get(e.name))&&void 0!==n?n:e.name,e.parent&&je.has(e.parent.name)&&(t={...t,xmlMode:!1}));!t.xmlMode&&ze.has(e.name)&&(t={...t,xmlMode:"foreign"});let r=`<${e.name}`;const s=function(e,t){var n;if(!e)return;const r=!1===(null!==(n=t.encodeEntities)&&void 0!==n?n:t.decodeEntities)?He:t.xmlMode||"utf8"!==t.encodeEntities?Fe:Pe;return Object.keys(e).map((n=>{var s,i;const a=null!==(s=e[n])&&void 0!==s?s:"";return"foreign"===t.xmlMode&&(n=null!==(i=we.get(n))&&void 0!==i?i:n),t.emptyAttrs||t.xmlMode||""!==a?`${n}="${r(a)}"`:n})).join(" ")}(e.attribs,t);s&&(r+=` ${s}`);0===e.children.length&&(t.xmlMode?!1!==t.selfClosingTags:t.selfClosingTags&&Ge.has(e.name))?(t.xmlMode||(r+=" "),r+="/>"):(r+=">",e.children.length>0&&(r+=qe(e.children,t)),!t.xmlMode&&Ge.has(e.name)||(r+=`</${e.name}>`));return r}(e,t);case P:return function(e,t){var n;let r=e.data||"";!1===(null!==(n=t.encodeEntities)&&void 0!==n?n:t.decodeEntities)||!t.xmlMode&&e.parent&&Ue.has(e.parent.name)||(r=t.xmlMode||"utf8"!==t.encodeEntities?Fe(r):ve(r));return r}(e,t)}}const je=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),ze=new Set(["svg","math"]);function Ve(e,t){return qe(e,t)}function Qe(e){return Array.isArray(e)?e.map(Qe).join(""):se(e)&&!te(e)?Qe(e.children):ee(e)?e.data:""}function $e(e){return Array.isArray(e)?e.map($e).join(""):se(e)&&(e.type===M.Tag||J(e))?$e(e.children):ee(e)?e.data:""}function We(e){return se(e)?e.children:[]}function Xe(e){return e.parent||null}function Ke(e){const t=Xe(e);if(null!=t)return We(t);const n=[e];let{prev:r,next:s}=e;for(;null!=r;)n.unshift(r),({prev:r}=r);for(;null!=s;)n.push(s),({next:s}=s);return n}function Ze(e){let{next:t}=e;for(;null!==t&&!Z(t);)({next:t}=t);return t}function Je(e){let{prev:t}=e;for(;null!==t&&!Z(t);)({prev:t}=t);return t}function et(e){if(e.prev&&(e.prev.next=e.next),e.next&&(e.next.prev=e.prev),e.parent){const t=e.parent.children,n=t.lastIndexOf(e);n>=0&&t.splice(n,1)}e.next=null,e.prev=null,e.parent=null}function tt(e,t,n=!0,r=1/0){return nt(e,Array.isArray(t)?t:[t],n,r)}function nt(e,t,n,r){const s=[],i=[t],a=[0];for(;;){if(a[0]>=i[0].length){if(1===a.length)return s;i.shift(),a.shift();continue}const t=i[0][a[0]++];if(e(t)&&(s.push(t),--r<=0))return s;n&&se(t)&&t.children.length>0&&(a.unshift(0),i.unshift(t.children))}}function rt(e,t,n=!0){let r=null;for(let s=0;s<t.length&&!r;s++){const i=t[s];Z(i)&&(e(i)?r=i:n&&i.children.length>0&&(r=rt(e,i.children,!0)))}return r}const st={tag_name:e=>"function"==typeof e?t=>Z(t)&&e(t.name):"*"===e?Z:t=>Z(t)&&t.name===e,tag_type:e=>"function"==typeof e?t=>e(t.type):t=>t.type===e,tag_contains:e=>"function"==typeof e?t=>ee(t)&&e(t.data):t=>ee(t)&&t.data===e};function it(e,t){return"function"==typeof t?n=>Z(n)&&t(n.attribs[e]):n=>Z(n)&&n.attribs[e]===t}function at(e,t){return n=>e(n)||t(n)}function ot(e){const t=Object.keys(e).map((t=>{const n=e[t];return Object.prototype.hasOwnProperty.call(st,t)?st[t](n):it(t,n)}));return 0===t.length?null:t.reduce(at)}function ct(e,t,n=!0,r=1/0){return tt(st.tag_name(e),t,n,r)}var lt,ut;function ht(e,t){const n=[],r=[];if(e===t)return 0;let s=se(e)?e:e.parent;for(;s;)n.unshift(s),s=s.parent;for(s=se(t)?t:t.parent;s;)r.unshift(s),s=s.parent;const i=Math.min(n.length,r.length);let a=0;for(;a<i&&n[a]===r[a];)a++;if(0===a)return lt.DISCONNECTED;const o=n[a-1],c=o.children,l=n[a],u=r[a];return c.indexOf(l)>c.indexOf(u)?o===t?lt.FOLLOWING|lt.CONTAINED_BY:lt.FOLLOWING:o===e?lt.PRECEDING|lt.CONTAINS:lt.PRECEDING}function pt(e){return(e=e.filter(((e,t,n)=>!n.includes(e,t+1)))).sort(((e,t)=>{const n=ht(e,t);return n&lt.PRECEDING?-1:n&lt.FOLLOWING?1:0})),e}(ut=lt||(lt={}))[ut.DISCONNECTED=1]="DISCONNECTED",ut[ut.PRECEDING=2]="PRECEDING",ut[ut.FOLLOWING=4]="FOLLOWING",ut[ut.CONTAINS=8]="CONTAINS",ut[ut.CONTAINED_BY=16]="CONTAINED_BY";const dt=["url","type","lang"],ft=["fileSize","bitrate","framerate","samplingrate","channels","duration","height","width"];function Et(e){return ct("media:content",e).map((e=>{const{attribs:t}=e,n={medium:t.medium,isDefault:!!t.isDefault};for(const e of dt)t[e]&&(n[e]=t[e]);for(const e of ft)t[e]&&(n[e]=parseInt(t[e],10));return t.expression&&(n.expression=t.expression),n}))}function mt(e,t){return ct(e,t,!0,1)[0]}function Tt(e,t,n=!1){return Qe(ct(e,t,n,1)).trim()}function _t(e,t,n,r,s=!1){const i=Tt(n,r,s);i&&(e[t]=i)}function At(e){return"rss"===e||"feed"===e||"rdf:RDF"===e}const gt=Object.freeze(Object.defineProperty({__proto__:null,get DocumentPosition(){return lt},append:function(e,t){et(t);const{parent:n}=e,r=e.next;if(t.next=r,t.prev=e,e.next=t,t.parent=n,r){if(r.prev=t,n){const e=n.children;e.splice(e.lastIndexOf(r),0,t)}}else n&&n.children.push(t)},appendChild:function(e,t){if(et(t),t.next=null,t.parent=e,e.children.push(t)>1){const n=e.children[e.children.length-2];n.next=t,t.prev=n}else t.prev=null},compareDocumentPosition:ht,existsOne:function e(t,n){return n.some((n=>Z(n)&&(t(n)||e(t,n.children))))},filter:tt,find:nt,findAll:function(e,t){const n=[],r=[t],s=[0];for(;;){if(s[0]>=r[0].length){if(1===r.length)return n;r.shift(),s.shift();continue}const t=r[0][s[0]++];Z(t)&&(e(t)&&n.push(t),t.children.length>0&&(s.unshift(0),r.unshift(t.children)))}},findOne:rt,findOneChild:function(e,t){return t.find(e)},getAttributeValue:function(e,t){var n;return null===(n=e.attribs)||void 0===n?void 0:n[t]},getChildren:We,getElementById:function(e,t,n=!0){return Array.isArray(t)||(t=[t]),rt(it("id",e),t,n)},getElements:function(e,t,n,r=1/0){const s=ot(e);return s?tt(s,t,n,r):[]},getElementsByTagName:ct,getElementsByTagType:function(e,t,n=!0,r=1/0){return tt(st.tag_type(e),t,n,r)},getFeed:function(e){const t=mt(At,e);return t?"feed"===t.name?function(e){var t;const n=e.children,r={type:"atom",items:ct("entry",n).map((e=>{var t;const{children:n}=e,r={media:Et(n)};_t(r,"id","id",n),_t(r,"title","title",n);const s=null===(t=mt("link",n))||void 0===t?void 0:t.attribs.href;s&&(r.link=s);const i=Tt("summary",n)||Tt("content",n);i&&(r.description=i);const a=Tt("updated",n);return a&&(r.pubDate=new Date(a)),r}))};_t(r,"id","id",n),_t(r,"title","title",n);const s=null===(t=mt("link",n))||void 0===t?void 0:t.attribs.href;s&&(r.link=s);_t(r,"description","subtitle",n);const i=Tt("updated",n);i&&(r.updated=new Date(i));return _t(r,"author","email",n,!0),r}(t):function(e){var t,n;const r=null!==(n=null===(t=mt("channel",e.children))||void 0===t?void 0:t.children)&&void 0!==n?n:[],s={type:e.name.substr(0,3),id:"",items:ct("item",e.children).map((e=>{const{children:t}=e,n={media:Et(t)};_t(n,"id","guid",t),_t(n,"title","title",t),_t(n,"link","link",t),_t(n,"description","description",t);const r=Tt("pubDate",t)||Tt("dc:date",t);return r&&(n.pubDate=new Date(r)),n}))};_t(s,"title","title",r),_t(s,"link","link",r),_t(s,"description","description",r);const i=Tt("lastBuildDate",r);i&&(s.updated=new Date(i));return _t(s,"author","managingEditor",r,!0),s}(t):null},getInnerHTML:function(e,t){return se(e)?e.children.map((e=>Ve(e,t))).join(""):""},getName:function(e){return e.name},getOuterHTML:Ve,getParent:Xe,getSiblings:Ke,getText:function e(t){return Array.isArray(t)?t.map(e).join(""):Z(t)?"br"===t.name?"\n":e(t.children):J(t)?e(t.children):ee(t)?t.data:""},hasAttrib:function(e,t){return null!=e.attribs&&Object.prototype.hasOwnProperty.call(e.attribs,t)&&null!=e.attribs[t]},hasChildren:se,innerText:$e,isCDATA:J,isComment:te,isDocument:re,isTag:Z,isText:ee,nextElementSibling:Ze,prepend:function(e,t){et(t);const{parent:n}=e;if(n){const r=n.children;r.splice(r.indexOf(e),0,t)}e.prev&&(e.prev.next=t),t.parent=n,t.prev=e.prev,t.next=e,e.prev=t},prependChild:function(e,t){if(et(t),t.parent=e,t.prev=null,1!==e.children.unshift(t)){const n=e.children[1];n.prev=t,t.next=n}else t.next=null},prevElementSibling:Je,removeElement:et,removeSubsets:function(e){let t=e.length;for(;--t>=0;){const n=e[t];if(t>0&&e.lastIndexOf(n,t-1)>=0)e.splice(t,1);else for(let r=n.parent;r;r=r.parent)if(e.includes(r)){e.splice(t,1);break}}return e},replaceElement:function(e,t){const n=t.prev=e.prev;n&&(n.next=t);const r=t.next=e.next;r&&(r.prev=t);const s=t.parent=e.parent;if(s){const n=s.children;n[n.lastIndexOf(e)]=t,e.parent=null}},testElement:function(e,t){const n=ot(e);return!n||n(t)},textContent:Qe,uniqueSort:pt},Symbol.toStringTag,{value:"Module"}));function Ct(e,t,n){return e?e(null!=t?t:e._root.children,null,void 0,n).toString():""}function It(e){const t=e||(this?this.root():[]);let n="";for(let e=0;e<t.length;e++)n+=Qe(t[e]);return n}function Nt(e,t){if(t===e)return!1;let n=t;for(;n&&n!==n.parent;)if(n=n.parent,n===e)return!0;return!1}function Dt(e){if(Array.isArray(e))return!0;if("object"!=typeof e||!Object.prototype.hasOwnProperty.call(e,"length")||"number"!=typeof e.length||e.length<0)return!1;for(let t=0;t<e.length;t++)if(!(t in e))return!1;return!0}const St=Object.freeze(Object.defineProperty({__proto__:null,contains:Nt,html:function(e,t){return Ct(this,function(e,t){return!t&&"object"==typeof e&&null!=e&&!("length"in e)&&!("type"in e)}(e)?void(t=e):e,{...R,...null==this?void 0:this._options,...y(null!=t?t:{})})},merge:function(e,t){if(!Dt(e)||!Dt(t))return;let n=e.length;const r=+t.length;for(let s=0;s<r;s++)e[n++]=t[s];return e.length=n,e},parseHTML:function(e,t,n="boolean"==typeof t&&t){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t);const r=this.load(e,R,!1);return n||r("script").remove(),r.root()[0].children.slice()},root:function(){return this(this._root)},text:It,xml:function(e){return Ct(this,e,{...this._options,xmlMode:!0})}},Symbol.toStringTag,{value:"Module"}));function kt(e){return null!=e.cheerio}function bt(e,t){const n=e.length;for(let r=0;r<n;r++)t(e[r],r);return e}function Ot(e){const t="length"in e?Array.prototype.map.call(e,(e=>ie(e,!0))):[ie(e,!0)],n=new X(t);return t.forEach((e=>{e.parent=n})),t}var Rt,Lt;function yt(e){const t=e.indexOf("<");if(t<0||t>e.length-3)return!1;const n=e.charCodeAt(t+1);return(n>=Rt.LowerA&&n<=Rt.LowerZ||n>=Rt.UpperA&&n<=Rt.UpperZ||n===Rt.Exclamation)&&e.includes(">",t+2)}(Lt=Rt||(Rt={}))[Lt.LowerA=97]="LowerA",Lt[Lt.LowerZ=122]="LowerZ",Lt[Lt.UpperA=65]="UpperA",Lt[Lt.UpperZ=90]="UpperZ",Lt[Lt.Exclamation=33]="Exclamation";const Mt=Object.prototype.hasOwnProperty,Ft=/\s+/,xt="data-",Pt={null:null,true:!0,false:!1},vt=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,Bt=/^{[^]*}$|^\[[^]*]$/;function wt(e,t,n){var r;if(e&&Z(e))return null!==(r=e.attribs)&&void 0!==r||(e.attribs={}),t?Mt.call(e.attribs,t)?!n&&vt.test(t)?t:e.attribs[t]:"option"===e.name&&"value"===t?It(e.children):"input"!==e.name||"radio"!==e.attribs.type&&"checkbox"!==e.attribs.type||"value"!==t?void 0:"on":e.attribs}function Ut(e,t,n){null===n?jt(e,t):e.attribs[t]=`${n}`}function Ht(e,t,n){return t in e?e[t]:!n&&vt.test(t)?void 0!==wt(e,t,!1):wt(e,t,n)}function Gt(e,t,n,r){t in e?e[t]=n:Ut(e,t,!r&&vt.test(t)?n?"":null:`${n}`)}function qt(e,t,n){var r;const s=e;null!==(r=s.data)&&void 0!==r||(s.data={}),"object"==typeof t?Object.assign(s.data,t):"string"==typeof t&&void 0!==n&&(s.data[t]=n)}function Yt(e,t){let n,r,s;var i;null==t?(n=Object.keys(e.attribs).filter((e=>e.startsWith(xt))),r=n.map((e=>e.slice(xt.length).replace(/[_.-](\w|$)/g,((e,t)=>t.toUpperCase()))))):(n=[xt+(i=t,i.replace(/[A-Z]/g,"-$&").toLowerCase())],r=[t]);for(let t=0;t<n.length;++t){const i=n[t],a=r[t];if(Mt.call(e.attribs,i)&&!Mt.call(e.data,a)){if(s=e.attribs[i],Mt.call(Pt,s))s=Pt[s];else if(s===String(Number(s)))s=Number(s);else if(Bt.test(s))try{s=JSON.parse(s)}catch(e){}e.data[a]=s}}return null==t?e.data:s}function jt(e,t){e.attribs&&Mt.call(e.attribs,t)&&delete e.attribs[t]}function zt(e){return e?e.trim().split(Ft):[]}const Vt=Object.freeze(Object.defineProperty({__proto__:null,addClass:function e(t){if("function"==typeof t)return bt(this,((n,r)=>{if(Z(n)){const s=n.attribs.class||"";e.call([n],t.call(n,r,s))}}));if(!t||"string"!=typeof t)return this;const n=t.split(Ft),r=this.length;for(let e=0;e<r;e++){const t=this[e];if(!Z(t))continue;const r=wt(t,"class",!1);if(r){let e=` ${r} `;for(let t=0;t<n.length;t++){const r=`${n[t]} `;e.includes(` ${r}`)||(e+=r)}Ut(t,"class",e.trim())}else Ut(t,"class",n.join(" ").trim())}return this},attr:function(e,t){if("object"==typeof e||void 0!==t){if("function"==typeof t){if("string"!=typeof e)throw new Error("Bad combination of arguments.");return bt(this,((n,r)=>{Z(n)&&Ut(n,e,t.call(n,r,n.attribs[e]))}))}return bt(this,(n=>{Z(n)&&("object"==typeof e?Object.keys(e).forEach((t=>{const r=e[t];Ut(n,t,r)})):Ut(n,e,t))}))}return arguments.length>1?this:wt(this[0],e,this.options.xmlMode)},data:function(e,t){var n;const r=this[0];if(!r||!Z(r))return;const s=r;return null!==(n=s.data)&&void 0!==n||(s.data={}),e?"object"==typeof e||void 0!==t?(bt(this,(n=>{Z(n)&&("object"==typeof e?qt(n,e):qt(n,e,t))})),this):Mt.call(s.data,e)?s.data[e]:Yt(s,e):Yt(s)},hasClass:function(e){return this.toArray().some((t=>{const n=Z(t)&&t.attribs.class;let r=-1;if(n&&e.length)for(;(r=n.indexOf(e,r+1))>-1;){const t=r+e.length;if((0===r||Ft.test(n[r-1]))&&(t===n.length||Ft.test(n[t])))return!0}return!1}))},prop:function(e,t){var n;if("string"==typeof e&&void 0===t){const t=this[0];if(!t||!Z(t))return;switch(e){case"style":{const e=this.css(),t=Object.keys(e);return t.forEach(((t,n)=>{e[n]=t})),e.length=t.length,e}case"tagName":case"nodeName":return t.name.toUpperCase();case"href":case"src":{const r=null===(n=t.attribs)||void 0===n?void 0:n[e];return"undefined"==typeof URL||("href"!==e||"a"!==t.tagName&&"link"!==t.name)&&("src"!==e||"img"!==t.tagName&&"iframe"!==t.tagName&&"audio"!==t.tagName&&"video"!==t.tagName&&"source"!==t.tagName)||void 0===r||!this.options.baseURI?r:new URL(r,this.options.baseURI).href}case"innerText":return $e(t);case"textContent":return Qe(t);case"outerHTML":return this.clone().wrap("<container />").parent().html();case"innerHTML":return this.html();default:return Ht(t,e,this.options.xmlMode)}}if("object"==typeof e||void 0!==t){if("function"==typeof t){if("object"==typeof e)throw new Error("Bad combination of arguments.");return bt(this,((n,r)=>{Z(n)&&Gt(n,e,t.call(n,r,Ht(n,e,this.options.xmlMode)),this.options.xmlMode)}))}return bt(this,(n=>{Z(n)&&("object"==typeof e?Object.keys(e).forEach((t=>{const r=e[t];Gt(n,t,r,this.options.xmlMode)})):Gt(n,e,t,this.options.xmlMode))}))}},removeAttr:function(e){const t=zt(e);for(let e=0;e<t.length;e++)bt(this,(n=>{Z(n)&&jt(n,t[e])}));return this},removeClass:function e(t){if("function"==typeof t)return bt(this,((n,r)=>{Z(n)&&e.call([n],t.call(n,r,n.attribs.class||""))}));const n=zt(t),r=n.length,s=0===arguments.length;return bt(this,(e=>{if(Z(e))if(s)e.attribs.class="";else{const t=zt(e.attribs.class);let s=!1;for(let e=0;e<r;e++){const r=t.indexOf(n[e]);r>=0&&(t.splice(r,1),s=!0,e--)}s&&(e.attribs.class=t.join(" "))}}))},toggleClass:function e(t,n){if("function"==typeof t)return bt(this,((r,s)=>{Z(r)&&e.call([r],t.call(r,s,r.attribs.class||"",n),n)}));if(!t||"string"!=typeof t)return this;const r=t.split(Ft),s=r.length,i="boolean"==typeof n?n?1:-1:0,a=this.length;for(let e=0;e<a;e++){const t=this[e];if(!Z(t))continue;const n=zt(t.attribs.class);for(let e=0;e<s;e++){const t=n.indexOf(r[e]);i>=0&&t<0?n.push(r[e]):i<=0&&t>=0&&n.splice(t,1)}t.attribs.class=n.join(" ")}return this},val:function(e){const t=0===arguments.length,n=this[0];if(!n||!Z(n))return t?void 0:this;switch(n.name){case"textarea":return this.text(e);case"select":{const n=this.find("option:selected");if(!t){if(null==this.attr("multiple")&&"object"==typeof e)return this;this.find("option").removeAttr("selected");const t="object"!=typeof e?[e]:e;for(let e=0;e<t.length;e++)this.find(`option[value="${t[e]}"]`).attr("selected","");return this}return this.attr("multiple")?n.toArray().map((e=>It(e.children))):n.attr("value")}case"input":case"option":return t?this.attr("value"):this.attr("value",e)}}},Symbol.toStringTag,{value:"Module"}));var Qt,$t,Wt,Xt;($t=Qt||(Qt={})).Attribute="attribute",$t.Pseudo="pseudo",$t.PseudoElement="pseudo-element",$t.Tag="tag",$t.Universal="universal",$t.Adjacent="adjacent",$t.Child="child",$t.Descendant="descendant",$t.Parent="parent",$t.Sibling="sibling",$t.ColumnCombinator="column-combinator",(Xt=Wt||(Wt={})).Any="any",Xt.Element="element",Xt.End="end",Xt.Equals="equals",Xt.Exists="exists",Xt.Hyphen="hyphen",Xt.Not="not",Xt.Start="start";const Kt=/^[^\\#]?(?:\\(?:[\da-f]{1,6}\s?|.)|[\w\-\u00b0-\uFFFF])+/,Zt=/\\([\da-f]{1,6}\s?|(\s)|.)/gi,Jt=new Map([[126,Wt.Element],[94,Wt.Start],[36,Wt.End],[42,Wt.Any],[33,Wt.Not],[124,Wt.Hyphen]]),en=new Set(["has","not","matches","is","where","host","host-context"]);function tn(e){switch(e.type){case Qt.Adjacent:case Qt.Child:case Qt.Descendant:case Qt.Parent:case Qt.Sibling:case Qt.ColumnCombinator:return!0;default:return!1}}const nn=new Set(["contains","icontains"]);function rn(e,t,n){const r=parseInt(t,16)-65536;return r!=r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)}function sn(e){return e.replace(Zt,rn)}function an(e){return 39===e||34===e}function on(e){return 32===e||9===e||10===e||12===e||13===e}function cn(e){const t=[],n=ln(t,`${e}`,0);if(n<e.length)throw new Error(`Unmatched selector: ${e.slice(n)}`);return t}function ln(e,t,n){let r=[];function s(e){const r=t.slice(n+e).match(Kt);if(!r)throw new Error(`Expected name, found ${t.slice(n)}`);const[s]=r;return n+=e+s.length,sn(s)}function i(e){for(n+=e;n<t.length&&on(t.charCodeAt(n));)n++}function a(){const e=n+=1;let r=1;for(;r>0&&n<t.length;n++)40!==t.charCodeAt(n)||o(n)?41!==t.charCodeAt(n)||o(n)||r--:r++;if(r)throw new Error("Parenthesis not matched");return sn(t.slice(e,n-1))}function o(e){let n=0;for(;92===t.charCodeAt(--e);)n++;return 1==(1&n)}function c(){if(r.length>0&&tn(r[r.length-1]))throw new Error("Did not expect successive traversals.")}function l(e){r.length>0&&r[r.length-1].type===Qt.Descendant?r[r.length-1].type=e:(c(),r.push({type:e}))}function u(e,t){r.push({type:Qt.Attribute,name:e,action:t,value:s(1),namespace:null,ignoreCase:"quirks"})}function h(){if(r.length&&r[r.length-1].type===Qt.Descendant&&r.pop(),0===r.length)throw new Error("Empty sub-selector");e.push(r)}if(i(0),t.length===n)return n;e:for(;n<t.length;){const e=t.charCodeAt(n);switch(e){case 32:case 9:case 10:case 12:case 13:0!==r.length&&r[0].type===Qt.Descendant||(c(),r.push({type:Qt.Descendant})),i(1);break;case 62:l(Qt.Child),i(1);break;case 60:l(Qt.Parent),i(1);break;case 126:l(Qt.Sibling),i(1);break;case 43:l(Qt.Adjacent),i(1);break;case 46:u("class",Wt.Element);break;case 35:u("id",Wt.Equals);break;case 91:{let e;i(1);let a=null;124===t.charCodeAt(n)?e=s(1):t.startsWith("*|",n)?(a="*",e=s(2)):(e=s(0),124===t.charCodeAt(n)&&61!==t.charCodeAt(n+1)&&(a=e,e=s(1))),i(0);let c=Wt.Exists;const l=Jt.get(t.charCodeAt(n));if(l){if(c=l,61!==t.charCodeAt(n+1))throw new Error("Expected `=`");i(2)}else 61===t.charCodeAt(n)&&(c=Wt.Equals,i(1));let u="",h=null;if("exists"!==c){if(an(t.charCodeAt(n))){const e=t.charCodeAt(n);let r=n+1;for(;r<t.length&&(t.charCodeAt(r)!==e||o(r));)r+=1;if(t.charCodeAt(r)!==e)throw new Error("Attribute value didn't end");u=sn(t.slice(n+1,r)),n=r+1}else{const e=n;for(;n<t.length&&(!on(t.charCodeAt(n))&&93!==t.charCodeAt(n)||o(n));)n+=1;u=sn(t.slice(e,n))}i(0);const e=32|t.charCodeAt(n);115===e?(h=!1,i(1)):105===e&&(h=!0,i(1))}if(93!==t.charCodeAt(n))throw new Error("Attribute selector didn't terminate");n+=1;const p={type:Qt.Attribute,name:e,action:c,value:u,namespace:a,ignoreCase:h};r.push(p);break}case 58:{if(58===t.charCodeAt(n+1)){r.push({type:Qt.PseudoElement,name:s(2).toLowerCase(),data:40===t.charCodeAt(n)?a():null});continue}const e=s(1).toLowerCase();let i=null;if(40===t.charCodeAt(n))if(en.has(e)){if(an(t.charCodeAt(n+1)))throw new Error(`Pseudo-selector ${e} cannot be quoted`);if(i=[],n=ln(i,t,n+1),41!==t.charCodeAt(n))throw new Error(`Missing closing parenthesis in :${e} (${t})`);n+=1}else{if(i=a(),nn.has(e)){const e=i.charCodeAt(0);e===i.charCodeAt(i.length-1)&&an(e)&&(i=i.slice(1,-1))}i=sn(i)}r.push({type:Qt.Pseudo,name:e,data:i});break}case 44:h(),r=[],i(1);break;default:{if(t.startsWith("/*",n)){const e=t.indexOf("*/",n+2);if(e<0)throw new Error("Comment was not terminated");n=e+2,0===r.length&&i(0);break}let a,o=null;if(42===e)n+=1,a="*";else if(124===e){if(a="",124===t.charCodeAt(n+1)){l(Qt.ColumnCombinator),i(2);break}}else{if(!Kt.test(t.slice(n)))break e;a=s(0)}124===t.charCodeAt(n)&&124!==t.charCodeAt(n+1)&&(o=a,42===t.charCodeAt(n+1)?(a="*",n+=2):a=s(1)),r.push("*"===a?{type:Qt.Universal,namespace:o}:{type:Qt.Tag,name:a,namespace:o})}}}return h(),n}function un(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var hn={trueFunc:function(){return!0},falseFunc:function(){return!1}};const pn=un(hn),dn=new Map([[Qt.Universal,50],[Qt.Tag,30],[Qt.Attribute,1],[Qt.Pseudo,0]]);function fn(e){return!dn.has(e.type)}const En=new Map([[Wt.Exists,10],[Wt.Equals,8],[Wt.Not,7],[Wt.Start,6],[Wt.End,6],[Wt.Any,5]]);function mn(e){const t=e.map(Tn);for(let n=1;n<e.length;n++){const r=t[n];if(!(r<0))for(let s=n-1;s>=0&&r<t[s];s--){const n=e[s+1];e[s+1]=e[s],e[s]=n,t[s+1]=t[s],t[s]=r}}}function Tn(e){var t,n;let r=null!==(t=dn.get(e.type))&&void 0!==t?t:-1;return e.type===Qt.Attribute?(r=null!==(n=En.get(e.action))&&void 0!==n?n:4,e.action===Wt.Equals&&"id"===e.name&&(r=9),e.ignoreCase&&(r>>=1)):e.type===Qt.Pseudo&&(e.data?"has"===e.name||"contains"===e.name?r=0:Array.isArray(e.data)?(r=Math.min(...e.data.map((e=>Math.min(...e.map(Tn))))),r<0&&(r=0)):r=2:r=3),r}const _n=/[-[\]{}()*+?.,\\^$|#\s]/g;function An(e){return e.replace(_n,"\\$&")}const gn=new Set(["accept","accept-charset","align","alink","axis","bgcolor","charset","checked","clear","codetype","color","compact","declare","defer","dir","direction","disabled","enctype","face","frame","hreflang","http-equiv","lang","language","link","media","method","multiple","nohref","noresize","noshade","nowrap","readonly","rel","rev","rules","scope","scrolling","selected","shape","target","text","type","valign","valuetype","vlink"]);function Cn(e,t){return"boolean"==typeof e.ignoreCase?e.ignoreCase:"quirks"===e.ignoreCase?!!t.quirksMode:!t.xmlMode&&gn.has(e.name)}const In={equals(e,t,n){const{adapter:r}=n,{name:s}=t;let{value:i}=t;return Cn(t,n)?(i=i.toLowerCase(),t=>{const n=r.getAttributeValue(t,s);return null!=n&&n.length===i.length&&n.toLowerCase()===i&&e(t)}):t=>r.getAttributeValue(t,s)===i&&e(t)},hyphen(e,t,n){const{adapter:r}=n,{name:s}=t;let{value:i}=t;const a=i.length;return Cn(t,n)?(i=i.toLowerCase(),function(t){const n=r.getAttributeValue(t,s);return null!=n&&(n.length===a||"-"===n.charAt(a))&&n.substr(0,a).toLowerCase()===i&&e(t)}):function(t){const n=r.getAttributeValue(t,s);return null!=n&&(n.length===a||"-"===n.charAt(a))&&n.substr(0,a)===i&&e(t)}},element(e,t,n){const{adapter:r}=n,{name:s,value:i}=t;if(/\s/.test(i))return pn.falseFunc;const a=new RegExp(`(?:^|\\s)${An(i)}(?:$|\\s)`,Cn(t,n)?"i":"");return function(t){const n=r.getAttributeValue(t,s);return null!=n&&n.length>=i.length&&a.test(n)&&e(t)}},exists:(e,{name:t},{adapter:n})=>r=>n.hasAttrib(r,t)&&e(r),start(e,t,n){const{adapter:r}=n,{name:s}=t;let{value:i}=t;const a=i.length;return 0===a?pn.falseFunc:Cn(t,n)?(i=i.toLowerCase(),t=>{const n=r.getAttributeValue(t,s);return null!=n&&n.length>=a&&n.substr(0,a).toLowerCase()===i&&e(t)}):t=>{var n;return!!(null===(n=r.getAttributeValue(t,s))||void 0===n?void 0:n.startsWith(i))&&e(t)}},end(e,t,n){const{adapter:r}=n,{name:s}=t;let{value:i}=t;const a=-i.length;return 0===a?pn.falseFunc:Cn(t,n)?(i=i.toLowerCase(),t=>{var n;return(null===(n=r.getAttributeValue(t,s))||void 0===n?void 0:n.substr(a).toLowerCase())===i&&e(t)}):t=>{var n;return!!(null===(n=r.getAttributeValue(t,s))||void 0===n?void 0:n.endsWith(i))&&e(t)}},any(e,t,n){const{adapter:r}=n,{name:s,value:i}=t;if(""===i)return pn.falseFunc;if(Cn(t,n)){const t=new RegExp(An(i),"i");return function(n){const a=r.getAttributeValue(n,s);return null!=a&&a.length>=i.length&&t.test(a)&&e(n)}}return t=>{var n;return!!(null===(n=r.getAttributeValue(t,s))||void 0===n?void 0:n.includes(i))&&e(t)}},not(e,t,n){const{adapter:r}=n,{name:s}=t;let{value:i}=t;return""===i?t=>!!r.getAttributeValue(t,s)&&e(t):Cn(t,n)?(i=i.toLowerCase(),t=>{const n=r.getAttributeValue(t,s);return(null==n||n.length!==i.length||n.toLowerCase()!==i)&&e(t)}):t=>r.getAttributeValue(t,s)!==i&&e(t)}},Nn=new Set([9,10,12,13,32]),Dn="0".charCodeAt(0),Sn="9".charCodeAt(0);function kn(e){return function(e){const t=e[0],n=e[1]-1;if(n<0&&t<=0)return pn.falseFunc;if(-1===t)return e=>e<=n;if(0===t)return e=>e===n;if(1===t)return n<0?pn.trueFunc:e=>e>=n;const r=Math.abs(t),s=(n%r+r)%r;return t>1?e=>e>=n&&e%r===s:e=>e<=n&&e%r===s}(function(e){if("even"===(e=e.trim().toLowerCase()))return[2,0];if("odd"===e)return[2,1];let t=0,n=0,r=i(),s=a();if(t<e.length&&"n"===e.charAt(t)&&(t++,n=r*(null!=s?s:1),o(),t<e.length?(r=i(),o(),s=a()):r=s=0),null===s||t<e.length)throw new Error(`n-th rule couldn't be parsed ('${e}')`);return[n,r*s];function i(){return"-"===e.charAt(t)?(t++,-1):("+"===e.charAt(t)&&t++,1)}function a(){const n=t;let r=0;for(;t<e.length&&e.charCodeAt(t)>=Dn&&e.charCodeAt(t)<=Sn;)r=10*r+(e.charCodeAt(t)-Dn),t++;return t===n?null:r}function o(){for(;t<e.length&&Nn.has(e.charCodeAt(t));)t++}}(e))}function bn(e,t){return n=>{const r=t.getParent(n);return null!=r&&t.isTag(r)&&e(n)}}const On={contains:(e,t,{adapter:n})=>function(r){return e(r)&&n.getText(r).includes(t)},icontains(e,t,{adapter:n}){const r=t.toLowerCase();return function(t){return e(t)&&n.getText(t).toLowerCase().includes(r)}},"nth-child"(e,t,{adapter:n,equals:r}){const s=kn(t);return s===pn.falseFunc?pn.falseFunc:s===pn.trueFunc?bn(e,n):function(t){const i=n.getSiblings(t);let a=0;for(let e=0;e<i.length&&!r(t,i[e]);e++)n.isTag(i[e])&&a++;return s(a)&&e(t)}},"nth-last-child"(e,t,{adapter:n,equals:r}){const s=kn(t);return s===pn.falseFunc?pn.falseFunc:s===pn.trueFunc?bn(e,n):function(t){const i=n.getSiblings(t);let a=0;for(let e=i.length-1;e>=0&&!r(t,i[e]);e--)n.isTag(i[e])&&a++;return s(a)&&e(t)}},"nth-of-type"(e,t,{adapter:n,equals:r}){const s=kn(t);return s===pn.falseFunc?pn.falseFunc:s===pn.trueFunc?bn(e,n):function(t){const i=n.getSiblings(t);let a=0;for(let e=0;e<i.length;e++){const s=i[e];if(r(t,s))break;n.isTag(s)&&n.getName(s)===n.getName(t)&&a++}return s(a)&&e(t)}},"nth-last-of-type"(e,t,{adapter:n,equals:r}){const s=kn(t);return s===pn.falseFunc?pn.falseFunc:s===pn.trueFunc?bn(e,n):function(t){const i=n.getSiblings(t);let a=0;for(let e=i.length-1;e>=0;e--){const s=i[e];if(r(t,s))break;n.isTag(s)&&n.getName(s)===n.getName(t)&&a++}return s(a)&&e(t)}},root:(e,t,{adapter:n})=>t=>{const r=n.getParent(t);return(null==r||!n.isTag(r))&&e(t)},scope(e,t,n,r){const{equals:s}=n;return r&&0!==r.length?1===r.length?t=>s(r[0],t)&&e(t):t=>r.includes(t)&&e(t):On.root(e,t,n)},hover:Rn("isHovered"),visited:Rn("isVisited"),active:Rn("isActive")};function Rn(e){return function(t,n,{adapter:r}){const s=r[e];return"function"!=typeof s?pn.falseFunc:function(e){return s(e)&&t(e)}}}const Ln={empty:(e,{adapter:t})=>!t.getChildren(e).some((e=>t.isTag(e)||""!==t.getText(e))),"first-child"(e,{adapter:t,equals:n}){if(t.prevElementSibling)return null==t.prevElementSibling(e);const r=t.getSiblings(e).find((e=>t.isTag(e)));return null!=r&&n(e,r)},"last-child"(e,{adapter:t,equals:n}){const r=t.getSiblings(e);for(let s=r.length-1;s>=0;s--){if(n(e,r[s]))return!0;if(t.isTag(r[s]))break}return!1},"first-of-type"(e,{adapter:t,equals:n}){const r=t.getSiblings(e),s=t.getName(e);for(let i=0;i<r.length;i++){const a=r[i];if(n(e,a))return!0;if(t.isTag(a)&&t.getName(a)===s)break}return!1},"last-of-type"(e,{adapter:t,equals:n}){const r=t.getSiblings(e),s=t.getName(e);for(let i=r.length-1;i>=0;i--){const a=r[i];if(n(e,a))return!0;if(t.isTag(a)&&t.getName(a)===s)break}return!1},"only-of-type"(e,{adapter:t,equals:n}){const r=t.getName(e);return t.getSiblings(e).every((s=>n(e,s)||!t.isTag(s)||t.getName(s)!==r))},"only-child":(e,{adapter:t,equals:n})=>t.getSiblings(e).every((r=>n(e,r)||!t.isTag(r)))};function yn(e,t,n,r){if(null===n){if(e.length>r)throw new Error(`Pseudo-class :${t} requires an argument`)}else if(e.length===r)throw new Error(`Pseudo-class :${t} doesn't have any arguments`)}const Mn={"any-link":":is(a, area, link)[href]",link:":any-link:not(:visited)",disabled:":is(\n        :is(button, input, select, textarea, optgroup, option)[disabled],\n        optgroup[disabled] > option,\n        fieldset[disabled]:not(fieldset[disabled] legend:first-of-type *)\n    )",enabled:":not(:disabled)",checked:":is(:is(input[type=radio], input[type=checkbox])[checked], option:selected)",required:":is(input, select, textarea)[required]",optional:":is(input, select, textarea):not([required])",selected:"option:is([selected], select:not([multiple]):not(:has(> option[selected])) > :first-of-type)",checkbox:"[type=checkbox]",file:"[type=file]",password:"[type=password]",radio:"[type=radio]",reset:"[type=reset]",image:"[type=image]",submit:"[type=submit]",parent:":not(:empty)",header:":is(h1, h2, h3, h4, h5, h6)",button:":is(button, input[type=button])",input:":is(input, textarea, select, button)",text:"input:is(:not([type!='']), [type=text])"},Fn={};function xn(e,t){const n=t.getSiblings(e);if(n.length<=1)return[];const r=n.indexOf(e);return r<0||r===n.length-1?[]:n.slice(r+1).filter(t.isTag)}function Pn(e){return{xmlMode:!!e.xmlMode,lowerCaseAttributeNames:!!e.lowerCaseAttributeNames,lowerCaseTags:!!e.lowerCaseTags,quirksMode:!!e.quirksMode,cacheResults:!!e.cacheResults,pseudos:e.pseudos,adapter:e.adapter,equals:e.equals}}const vn=(e,t,n,r,s)=>{const i=s(t,Pn(n),r);return i===pn.trueFunc?e:i===pn.falseFunc?pn.falseFunc:t=>i(t)&&e(t)},Bn={is:vn,matches:vn,where:vn,not(e,t,n,r,s){const i=s(t,Pn(n),r);return i===pn.falseFunc?e:i===pn.trueFunc?pn.falseFunc:t=>!i(t)&&e(t)},has(e,t,n,r,s){const{adapter:i}=n,a=Pn(n);a.relativeSelector=!0;const o=t.some((e=>e.some(fn)))?[Fn]:void 0,c=s(t,a,o);if(c===pn.falseFunc)return pn.falseFunc;const l=function(e,t){return e===pn.falseFunc?pn.falseFunc:n=>t.isTag(n)&&e(n)}(c,i);if(o&&c!==pn.trueFunc){const{shouldTestNextSiblings:t=!1}=c;return n=>{if(!e(n))return!1;o[0]=n;const r=i.getChildren(n),s=t?[...r,...xn(n,i)]:r;return i.existsOne(l,s)}}return t=>e(t)&&i.existsOne(l,i.getChildren(t))}};function wn(e,t){const n=t.getParent(e);return n&&t.isTag(n)?n:null}function Un(e,t,n,r,s){const{adapter:i,equals:a}=n;switch(t.type){case Qt.PseudoElement:throw new Error("Pseudo-elements are not supported by css-select");case Qt.ColumnCombinator:throw new Error("Column combinators are not yet supported by css-select");case Qt.Attribute:if(null!=t.namespace)throw new Error("Namespaced attributes are not yet supported by css-select");return n.xmlMode&&!n.lowerCaseAttributeNames||(t.name=t.name.toLowerCase()),In[t.action](e,t,n);case Qt.Pseudo:return function(e,t,n,r,s){var i;const{name:a,data:o}=t;if(Array.isArray(o)){if(!(a in Bn))throw new Error(`Unknown pseudo-class :${a}(${o})`);return Bn[a](e,o,n,r,s)}const c=null===(i=n.pseudos)||void 0===i?void 0:i[a],l="string"==typeof c?c:Mn[a];if("string"==typeof l){if(null!=o)throw new Error(`Pseudo ${a} doesn't have any arguments`);const t=cn(l);return Bn.is(e,t,n,r,s)}if("function"==typeof c)return yn(c,a,o,1),t=>c(t,o)&&e(t);if(a in On)return On[a](e,o,n,r);if(a in Ln){const t=Ln[a];return yn(t,a,o,2),r=>t(r,n,o)&&e(r)}throw new Error(`Unknown pseudo-class :${a}`)}(e,t,n,r,s);case Qt.Tag:{if(null!=t.namespace)throw new Error("Namespaced tag names are not yet supported by css-select");let{name:r}=t;return n.xmlMode&&!n.lowerCaseTags||(r=r.toLowerCase()),function(t){return i.getName(t)===r&&e(t)}}case Qt.Descendant:{if(!1===n.cacheResults||"undefined"==typeof WeakSet)return function(t){let n=t;for(;n=wn(n,i);)if(e(n))return!0;return!1};const t=new WeakSet;return function(n){let r=n;for(;r=wn(r,i);)if(!t.has(r)){if(i.isTag(r)&&e(r))return!0;t.add(r)}return!1}}case"_flexibleDescendant":return function(t){let n=t;do{if(e(n))return!0}while(n=wn(n,i));return!1};case Qt.Parent:return function(t){return i.getChildren(t).some((t=>i.isTag(t)&&e(t)))};case Qt.Child:return function(t){const n=i.getParent(t);return null!=n&&i.isTag(n)&&e(n)};case Qt.Sibling:return function(t){const n=i.getSiblings(t);for(let r=0;r<n.length;r++){const s=n[r];if(a(t,s))break;if(i.isTag(s)&&e(s))return!0}return!1};case Qt.Adjacent:return i.prevElementSibling?function(t){const n=i.prevElementSibling(t);return null!=n&&e(n)}:function(t){const n=i.getSiblings(t);let r;for(let e=0;e<n.length;e++){const s=n[e];if(a(t,s))break;i.isTag(s)&&(r=s)}return!!r&&e(r)};case Qt.Universal:if(null!=t.namespace&&"*"!==t.namespace)throw new Error("Namespaced universal selectors are not yet supported by css-select");return e}}function Hn(e){return e.type===Qt.Pseudo&&("scope"===e.name||Array.isArray(e.data)&&e.data.some((e=>e.some(Hn))))}const Gn={type:Qt.Descendant},qn={type:"_flexibleDescendant"},Yn={type:Qt.Pseudo,name:"scope",data:null};function jn(e,t,n){var r;e.forEach(mn),n=null!==(r=t.context)&&void 0!==r?r:n;const s=Array.isArray(n),i=n&&(Array.isArray(n)?n:[n]);if(!1!==t.relativeSelector)!function(e,{adapter:t},n){const r=!!(null==n?void 0:n.every((e=>{const n=t.isTag(e)&&t.getParent(e);return e===Fn||n&&t.isTag(n)})));for(const t of e){if(t.length>0&&fn(t[0])&&t[0].type!==Qt.Descendant);else{if(!r||t.some(Hn))continue;t.unshift(Gn)}t.unshift(Yn)}}(e,t,i);else if(e.some((e=>e.length>0&&fn(e[0]))))throw new Error("Relative selectors are not allowed when the `relativeSelector` option is disabled");let a=!1;const o=e.map((e=>{if(e.length>=2){const[t,n]=e;t.type!==Qt.Pseudo||"scope"!==t.name||(s&&n.type===Qt.Descendant?e[1]=qn:n.type!==Qt.Adjacent&&n.type!==Qt.Sibling||(a=!0))}return function(e,t,n){var r;return e.reduce(((e,r)=>e===pn.falseFunc?pn.falseFunc:Un(e,r,t,n,jn)),null!==(r=t.rootFunc)&&void 0!==r?r:pn.trueFunc)}(e,t,i)})).reduce(zn,pn.falseFunc);return o.shouldTestNextSiblings=a,o}function zn(e,t){return t===pn.falseFunc||e===pn.trueFunc?e:e===pn.falseFunc||t===pn.trueFunc?t:function(n){return e(n)||t(n)}}const Vn=(e,t)=>e===t,Qn={adapter:gt,equals:Vn};const $n=(Wn=jn,function(e,t,n){const r=function(e){var t,n,r,s;const i=null!=e?e:Qn;return null!==(t=i.adapter)&&void 0!==t||(i.adapter=gt),null!==(n=i.equals)&&void 0!==n||(i.equals=null!==(s=null===(r=i.adapter)||void 0===r?void 0:r.equals)&&void 0!==s?s:Vn),i}(t);return Wn(e,r,n)});var Wn;function Xn(e,t,n=!1){return n&&(e=function(e,t){const n=Array.isArray(e)?e.slice(0):[e],r=n.length;for(let e=0;e<r;e++){const r=xn(n[e],t);n.push(...r)}return n}(e,t)),Array.isArray(e)?t.removeSubsets(e):t.getChildren(e)}const Kn=new Set(["first","last","eq","gt","nth","lt","even","odd"]);function Zn(e){return"pseudo"===e.type&&(!!Kn.has(e.name)||!("not"!==e.name||!Array.isArray(e.data))&&e.data.some((e=>e.some(Zn))))}function Jn(e){const t=[],n=[];for(const r of e)r.some(Zn)?t.push(r):n.push(r);return[n,t]}const er={type:Qt.Universal,namespace:null},tr={type:Qt.Pseudo,name:"scope",data:null};function nr(e,t,n={}){return rr([e],t,n)}function rr(e,t,n={}){if("function"==typeof t)return e.some(t);const[r,s]=Jn(cn(t));return r.length>0&&e.some($n(r,n))||s.some((t=>ar(t,e,n).length>0))}function sr(e,t,n={}){return ir(cn(e),t,n)}function ir(e,t,n){if(0===t.length)return[];const[r,s]=Jn(e);let i;if(r.length){const e=ur(t,r,n);if(0===s.length)return e;e.length&&(i=new Set(e))}for(let e=0;e<s.length&&(null==i?void 0:i.size)!==t.length;e++){const r=s[e];if(0===(i?t.filter((e=>Z(e)&&!i.has(e))):t).length)break;const a=ar(r,t,n);if(a.length)if(i)a.forEach((e=>i.add(e)));else{if(e===s.length-1)return a;i=new Set(a)}}return void 0!==i?i.size===t.length?t:t.filter((e=>i.has(e))):[]}function ar(e,t,n){var r;if(e.some(tn)){const s=null!==(r=n.root)&&void 0!==r?r:function(e){for(;e.parent;)e=e.parent;return e}(t[0]),i={...n,context:t,relativeSelector:!1};return e.push(tr),or(s,e,i,!0,t.length)}return or(t,e,n,!1,t.length)}function or(e,t,n,r,s){const i=t.findIndex(Zn),a=t.slice(0,i),o=t[i],c=t.length-1===i?s:1/0,l=function(e,t,n){const r=null!=t?parseInt(t,10):NaN;switch(e){case"first":return 1;case"nth":case"eq":return isFinite(r)?r>=0?r+1:1/0:0;case"lt":return isFinite(r)?r>=0?Math.min(r,n):1/0:0;case"gt":return isFinite(r)?1/0:0;case"odd":return 2*n;case"even":return 2*n-1;case"last":case"not":return 1/0}}(o.name,o.data,c);if(0===l)return[];const u=(0!==a.length||Array.isArray(e)?0===a.length?(Array.isArray(e)?e:[e]).filter(Z):r||a.some(tn)?cr(e,[a],n,l):ur(e,[a],n):We(e).filter(Z)).slice(0,l);let h=function(e,t,n,r){const s="string"==typeof n?parseInt(n,10):NaN;switch(e){case"first":case"lt":return t;case"last":return t.length>0?[t[t.length-1]]:t;case"nth":case"eq":return isFinite(s)&&Math.abs(s)<t.length?[s<0?t[t.length+s]:t[s]]:[];case"gt":return isFinite(s)?t.slice(s+1):[];case"even":return t.filter(((e,t)=>t%2==0));case"odd":return t.filter(((e,t)=>t%2==1));case"not":{const e=new Set(ir(n,t,r));return t.filter((t=>!e.has(t)))}}}(o.name,u,o.data,n);if(0===h.length||t.length===i+1)return h;const p=t.slice(i+1),d=p.some(tn);if(d){if(tn(p[0])){const{type:e}=p[0];e!==Qt.Sibling&&e!==Qt.Adjacent||(h=Xn(h,gt,!0)),p.unshift(er)}n={...n,relativeSelector:!1,rootFunc:e=>h.includes(e)}}else n.rootFunc&&n.rootFunc!==hn.trueFunc&&(n={...n,rootFunc:hn.trueFunc});return p.some(Zn)?or(h,p,n,!1,s):d?cr(h,[p],n,s):ur(h,[p],n)}function cr(e,t,n,r){return lr(e,$n(t,n,e),r)}function lr(e,t,n=1/0){return nt((e=>Z(e)&&t(e)),Xn(e,gt,t.shouldTestNextSiblings),!0,n)}function ur(e,t,n){const r=(Array.isArray(e)?e:[e]).filter(Z);if(0===r.length)return r;const s=$n(t,n);return s===hn.trueFunc?r:r.filter(s)}const hr=/^\s*[~+]/;function pr(e){return function(t,...n){return function(r){var s;let i=e(t,this);return r&&(i=Rr(i,r,this.options.xmlMode,null===(s=this._root)||void 0===s?void 0:s[0])),this._make(this.length>1&&i.length>1?n.reduce(((e,t)=>t(e)),i):i)}}}const dr=pr(((e,t)=>{const n=[];for(let r=0;r<t.length;r++){const s=e(t[r]);n.push(s)}return(new Array).concat(...n)})),fr=pr(((e,t)=>{const n=[];for(let r=0;r<t.length;r++){const s=e(t[r]);null!==s&&n.push(s)}return n}));function Er(e,...t){let n=null;const r=pr(((e,t)=>{const r=[];return bt(t,(t=>{for(let s;(s=e(t))&&!(null==n?void 0:n(s,r.length));t=s)r.push(s)})),r}))(e,...t);return function(e,t){n="string"==typeof e?t=>nr(t,e,this.options):e?Or(e):null;const s=r.call(this,t);return n=null,s}}function mr(e){return Array.from(new Set(e))}const Tr=fr((({parent:e})=>e&&!re(e)?e:null),mr),_r=dr((e=>{const t=[];for(;e.parent&&!re(e.parent);)t.push(e.parent),e=e.parent;return t}),pt,(e=>e.reverse())),Ar=Er((({parent:e})=>e&&!re(e)?e:null),pt,(e=>e.reverse()));const gr=fr((e=>Ze(e))),Cr=dr((e=>{const t=[];for(;e.next;)Z(e=e.next)&&t.push(e);return t}),mr),Ir=Er((e=>Ze(e)),mr),Nr=fr((e=>Je(e))),Dr=dr((e=>{const t=[];for(;e.prev;)Z(e=e.prev)&&t.push(e);return t}),mr),Sr=Er((e=>Je(e)),mr),kr=dr((e=>Ke(e).filter((t=>Z(t)&&t!==e))),pt),br=dr((e=>We(e).filter(Z)),mr);function Or(e){return"function"==typeof e?(t,n)=>e.call(t,n,t):kt(e)?t=>Array.prototype.includes.call(e,t):function(t){return e===t}}function Rr(e,t,n,r){return"string"==typeof t?sr(t,e,{xmlMode:n,root:r}):e.filter(Or(t))}const Lr=Object.freeze(Object.defineProperty({__proto__:null,add:function(e,t){const n=this._make(e,t),r=pt([...this.get(),...n.get()]);return this._make(r)},addBack:function(e){return this.prevObject?this.add(e?this.prevObject.filter(e):this.prevObject):this},children:br,closest:function(e){var t;const n=[];if(!e)return this._make(n);const r={xmlMode:this.options.xmlMode,root:null===(t=this._root)||void 0===t?void 0:t[0]},s="string"==typeof e?t=>nr(t,e,r):Or(e);return bt(this,(e=>{for(;e&&Z(e);){if(s(e,0)){n.includes(e)||n.push(e);break}e=e.parent}})),this._make(n)},contents:function(){const e=this.toArray().reduce(((e,t)=>se(t)?e.concat(t.children):e),[]);return this._make(e)},each:function(e){let t=0;const n=this.length;for(;t<n&&!1!==e.call(this[t],t,this[t]);)++t;return this},end:function(){var e;return null!==(e=this.prevObject)&&void 0!==e?e:this._make([])},eq:function(e){var t;return 0===(e=+e)&&this.length<=1?this:(e<0&&(e=this.length+e),this._make(null!==(t=this[e])&&void 0!==t?t:[]))},filter:function(e){var t;return this._make(Rr(this.toArray(),e,this.options.xmlMode,null===(t=this._root)||void 0===t?void 0:t[0]))},filterArray:Rr,find:function(e){var t;if(!e)return this._make([]);const n=this.toArray();if("string"!=typeof e){const t=kt(e)?e.toArray():[e];return this._make(t.filter((e=>n.some((t=>Nt(t,e))))))}const r=hr.test(e)?n:this.children().toArray(),s={context:n,root:null===(t=this._root)||void 0===t?void 0:t[0],xmlMode:this.options.xmlMode,lowerCaseTags:this.options.lowerCaseTags,lowerCaseAttributeNames:this.options.lowerCaseAttributeNames,pseudos:this.options.pseudos,quirksMode:this.options.quirksMode};return this._make(function(e,t,n={},r=1/0){if("function"==typeof e)return lr(t,e);const[s,i]=Jn(cn(e)),a=i.map((e=>or(t,e,n,!0,r)));return s.length&&a.push(cr(t,s,n,r)),0===a.length?[]:1===a.length?a[0]:pt(a.reduce(((e,t)=>[...e,...t])))}(e,r,s))},first:function(){return this.length>1?this._make(this[0]):this},get:function(e){return null==e?this.toArray():this[e<0?this.length+e:e]},has:function(e){return this.filter("string"==typeof e?`:has(${e})`:(t,n)=>this._make(n).find(e).length>0)},index:function(e){let t,n;return null==e?(t=this.parent().children(),n=this[0]):"string"==typeof e?(t=this._make(e),n=this[0]):(t=this,n=kt(e)?e[0]:e),Array.prototype.indexOf.call(t,n)},is:function(e){const t=this.toArray();return"string"==typeof e?rr(t.filter(Z),e,this.options):!!e&&t.some(Or(e))},last:function(){return this.length>0?this._make(this[this.length-1]):this},map:function(e){let t=[];for(let n=0;n<this.length;n++){const r=this[n],s=e.call(r,n,r);null!=s&&(t=t.concat(s))}return this._make(t)},next:gr,nextAll:Cr,nextUntil:Ir,not:function(e){let t=this.toArray();if("string"==typeof e){const n=new Set(sr(e,t,this.options));t=t.filter((e=>!n.has(e)))}else{const n=Or(e);t=t.filter(((e,t)=>!n(e,t)))}return this._make(t)},parent:Tr,parents:_r,parentsUntil:Ar,prev:Nr,prevAll:Dr,prevUntil:Sr,siblings:kr,slice:function(e,t){return this._make(Array.prototype.slice.call(this,e,t))},toArray:function(){return Array.prototype.slice.call(this)}},Symbol.toStringTag,{value:"Module"}));function yr(e,t){const n=Array.isArray(e)?e:[e];t?t.children=n:t=null;for(let e=0;e<n.length;e++){const r=n[e];r.parent&&r.parent.children!==n&&et(r),t?(r.prev=n[e-1]||null,r.next=n[e+1]||null):r.prev=r.next=null,r.parent=t}return t}function Mr(e){return function(...t){const n=this.length-1;return bt(this,((r,s)=>{if(!se(r))return;const i="function"==typeof t[0]?t[0].call(r,s,this._render(r.children)):t,a=this._makeDomArray(i,s<n);e(a,r.children,r)}))}}function Fr(e,t,n,r,s){var i,a;const o=[t,n,...r],c=0===t?null:e[t-1],l=t+n>=e.length?null:e[t+n];for(let e=0;e<r.length;++e){const n=r[e],u=n.parent;if(u){const e=u.children.indexOf(n);e>-1&&(u.children.splice(e,1),s===u&&t>e&&o[0]--)}n.parent=s,n.prev&&(n.prev.next=null!==(i=n.next)&&void 0!==i?i:null),n.next&&(n.next.prev=null!==(a=n.prev)&&void 0!==a?a:null),n.prev=0===e?c:r[e-1],n.next=e===r.length-1?l:r[e+1]}return c&&(c.next=r[0]),l&&(l.prev=r[r.length-1]),e.splice(...o)}const xr=Mr(((e,t,n)=>{Fr(t,t.length,0,e,n)})),Pr=Mr(((e,t,n)=>{Fr(t,0,0,e,n)}));function vr(e){return function(t){const n=this.length-1,r=this.parents().last();for(let s=0;s<this.length;s++){const i=this[s],a="function"==typeof t?t.call(i,s,i):"string"!=typeof t||yt(t)?t:r.find(t).clone(),[o]=this._makeDomArray(a,s<n);if(!o||!se(o))continue;let c=o,l=0;for(;l<c.children.length;){const e=c.children[l];Z(e)?(c=e,l=0):l++}e(i,c,[o])}return this}}const Br=vr(((e,t,n)=>{const{parent:r}=e;if(!r)return;const s=r.children,i=s.indexOf(e);yr([e],t),Fr(s,i,0,n,r)})),wr=vr(((e,t,n)=>{se(e)&&(yr(e.children,t),yr(n,e))}));const Ur=Object.freeze(Object.defineProperty({__proto__:null,_makeDomArray:function(e,t){return null==e?[]:kt(e)?t?Ot(e.get()):e.get():Array.isArray(e)?e.reduce(((e,n)=>e.concat(this._makeDomArray(n,t))),[]):"string"==typeof e?this._parse(e,this.options,!1,null).children:t?Ot([e]):[e]},after:function(...e){const t=this.length-1;return bt(this,((n,r)=>{const{parent:s}=n;if(!se(n)||!s)return;const i=s.children,a=i.indexOf(n);if(a<0)return;const o="function"==typeof e[0]?e[0].call(n,r,this._render(n.children)):e;Fr(i,a+1,0,this._makeDomArray(o,r<t),s)}))},append:xr,appendTo:function(e){return(kt(e)?e:this._make(e)).append(this),this},before:function(...e){const t=this.length-1;return bt(this,((n,r)=>{const{parent:s}=n;if(!se(n)||!s)return;const i=s.children,a=i.indexOf(n);if(a<0)return;const o="function"==typeof e[0]?e[0].call(n,r,this._render(n.children)):e;Fr(i,a,0,this._makeDomArray(o,r<t),s)}))},clone:function(){return this._make(Ot(this.get()))},empty:function(){return bt(this,(e=>{se(e)&&(e.children.forEach((e=>{e.next=e.prev=e.parent=null})),e.children.length=0)}))},html:function(e){if(void 0===e){const e=this[0];return e&&se(e)?this._render(e.children):null}return bt(this,(t=>{if(!se(t))return;t.children.forEach((e=>{e.next=e.prev=e.parent=null}));yr(kt(e)?e.toArray():this._parse(`${e}`,this.options,!1,t).children,t)}))},insertAfter:function(e){"string"==typeof e&&(e=this._make(e)),this.remove();const t=[];return this._makeDomArray(e).forEach((e=>{const n=this.clone().toArray(),{parent:r}=e;if(!r)return;const s=r.children,i=s.indexOf(e);i<0||(Fr(s,i+1,0,n,r),t.push(...n))})),this._make(t)},insertBefore:function(e){const t=this._make(e);this.remove();const n=[];return bt(t,(e=>{const t=this.clone().toArray(),{parent:r}=e;if(!r)return;const s=r.children,i=s.indexOf(e);i<0||(Fr(s,i,0,t,r),n.push(...t))})),this._make(n)},prepend:Pr,prependTo:function(e){return(kt(e)?e:this._make(e)).prepend(this),this},remove:function(e){return bt(e?this.filter(e):this,(e=>{et(e),e.prev=e.next=e.parent=null})),this},replaceWith:function(e){return bt(this,((t,n)=>{const{parent:r}=t;if(!r)return;const s=r.children,i="function"==typeof e?e.call(t,n,t):e,a=this._makeDomArray(i);yr(a,null);const o=s.indexOf(t);Fr(s,o,1,a,r),a.includes(t)||(t.parent=t.prev=t.next=null)}))},text:function(e){return void 0===e?It(this):bt(this,"function"==typeof e?(t,n)=>this._make(t).text(e.call(t,n,It([t]))):t=>{if(!se(t))return;t.children.forEach((e=>{e.next=e.prev=e.parent=null}));yr(new z(`${e}`),t)})},toString:function(){return this._render(this)},unwrap:function(e){return this.parent(e).not("body").each(((e,t)=>{this._make(t).replaceWith(t.children)})),this},wrap:Br,wrapAll:function(e){const t=this[0];if(t){const n=this._make("function"==typeof e?e.call(t,0,t):e).insertBefore(t);let r;for(let e=0;e<n.length;e++)"tag"===n[e].type&&(r=n[e]);let s=0;for(;r&&s<r.children.length;){const e=r.children[s];"tag"===e.type?(r=e,s=0):s++}r&&this._make(r).append(this)}return this},wrapInner:wr},Symbol.toStringTag,{value:"Module"}));function Hr(e,t,n,r){if("string"==typeof t){const i=Gr(e),a="function"==typeof n?n.call(e,r,i[t]):n;""===a?delete i[t]:null!=a&&(i[t]=a),e.attribs.style=(s=i,Object.keys(s).reduce(((e,t)=>`${e}${e?" ":""}${t}: ${s[t]};`),""))}else"object"==typeof t&&Object.keys(t).forEach(((n,r)=>{Hr(e,n,t[n],r)}));var s}function Gr(e,t){if(!e||!Z(e))return;const n=function(e){if(e=(e||"").trim(),!e)return{};const t={};let n;for(const r of e.split(";")){const e=r.indexOf(":");if(e<1||e===r.length-1){const e=r.trimEnd();e.length>0&&void 0!==n&&(t[n]+=`;${e}`)}else n=r.slice(0,e).trim(),t[n]=r.slice(e+1).trim()}return t}(e.attribs.style);if("string"==typeof t)return n[t];if(Array.isArray(t)){const e={};return t.forEach((t=>{null!=n[t]&&(e[t]=n[t])})),e}return n}const qr=Object.freeze(Object.defineProperty({__proto__:null,css:function(e,t){return null!=e&&null!=t||"object"==typeof e&&!Array.isArray(e)?bt(this,((n,r)=>{Z(n)&&Hr(n,e,t,r)})):0!==this.length?Gr(this[0],e):void 0}},Symbol.toStringTag,{value:"Module"})),Yr="input,select,textarea,keygen",jr=/%20/g,zr=/\r?\n/g;const Vr=Object.freeze(Object.defineProperty({__proto__:null,serialize:function(){return this.serializeArray().map((e=>`${encodeURIComponent(e.name)}=${encodeURIComponent(e.value)}`)).join("&").replace(jr,"+")},serializeArray:function(){return this.map(((e,t)=>{const n=this._make(t);return Z(t)&&"form"===t.name?n.find(Yr).toArray():n.filter(Yr).toArray()})).filter('[name!=""]:enabled:not(:submit, :button, :image, :reset, :file):matches([checked], :not(:checkbox, :radio))').map(((e,t)=>{var n;const r=this._make(t),s=r.attr("name"),i=null!==(n=r.val())&&void 0!==n?n:"";return Array.isArray(i)?i.map((e=>({name:s,value:e.replace(zr,"\r\n")}))):{name:s,value:i.replace(zr,"\r\n")}})).toArray()}},Symbol.toStringTag,{value:"Module"}));class Qr{constructor(e,t,n){if(this.length=0,this.options=n,this._root=t,e){for(let t=0;t<e.length;t++)this[t]=e[t];this.length=e.length}}}Qr.prototype.cheerio="[cheerio object]",Qr.prototype.splice=Array.prototype.splice,Qr.prototype[Symbol.iterator]=Array.prototype[Symbol.iterator],Object.assign(Qr.prototype,Vt,Lr,Ur,qr,Vr);const $r=new Set([65534,65535,131070,131071,196606,196607,262142,262143,327678,327679,393214,393215,458750,458751,524286,524287,589822,589823,655358,655359,720894,720895,786430,786431,851966,851967,917502,917503,983038,983039,1048574,1048575,1114110,1114111]),Wr="�";var Xr,Kr;(Kr=Xr=Xr||(Xr={}))[Kr.EOF=-1]="EOF",Kr[Kr.NULL=0]="NULL",Kr[Kr.TABULATION=9]="TABULATION",Kr[Kr.CARRIAGE_RETURN=13]="CARRIAGE_RETURN",Kr[Kr.LINE_FEED=10]="LINE_FEED",Kr[Kr.FORM_FEED=12]="FORM_FEED",Kr[Kr.SPACE=32]="SPACE",Kr[Kr.EXCLAMATION_MARK=33]="EXCLAMATION_MARK",Kr[Kr.QUOTATION_MARK=34]="QUOTATION_MARK",Kr[Kr.NUMBER_SIGN=35]="NUMBER_SIGN",Kr[Kr.AMPERSAND=38]="AMPERSAND",Kr[Kr.APOSTROPHE=39]="APOSTROPHE",Kr[Kr.HYPHEN_MINUS=45]="HYPHEN_MINUS",Kr[Kr.SOLIDUS=47]="SOLIDUS",Kr[Kr.DIGIT_0=48]="DIGIT_0",Kr[Kr.DIGIT_9=57]="DIGIT_9",Kr[Kr.SEMICOLON=59]="SEMICOLON",Kr[Kr.LESS_THAN_SIGN=60]="LESS_THAN_SIGN",Kr[Kr.EQUALS_SIGN=61]="EQUALS_SIGN",Kr[Kr.GREATER_THAN_SIGN=62]="GREATER_THAN_SIGN",Kr[Kr.QUESTION_MARK=63]="QUESTION_MARK",Kr[Kr.LATIN_CAPITAL_A=65]="LATIN_CAPITAL_A",Kr[Kr.LATIN_CAPITAL_F=70]="LATIN_CAPITAL_F",Kr[Kr.LATIN_CAPITAL_X=88]="LATIN_CAPITAL_X",Kr[Kr.LATIN_CAPITAL_Z=90]="LATIN_CAPITAL_Z",Kr[Kr.RIGHT_SQUARE_BRACKET=93]="RIGHT_SQUARE_BRACKET",Kr[Kr.GRAVE_ACCENT=96]="GRAVE_ACCENT",Kr[Kr.LATIN_SMALL_A=97]="LATIN_SMALL_A",Kr[Kr.LATIN_SMALL_F=102]="LATIN_SMALL_F",Kr[Kr.LATIN_SMALL_X=120]="LATIN_SMALL_X",Kr[Kr.LATIN_SMALL_Z=122]="LATIN_SMALL_Z",Kr[Kr.REPLACEMENT_CHARACTER=65533]="REPLACEMENT_CHARACTER";const Zr="--",Jr="[CDATA[",es="doctype",ts="script",ns="public",rs="system";function ss(e){return e>=55296&&e<=57343}function is(e){return 32!==e&&10!==e&&13!==e&&9!==e&&12!==e&&e>=1&&e<=31||e>=127&&e<=159}function as(e){return e>=64976&&e<=65007||$r.has(e)}var os,cs;(cs=os=os||(os={})).controlCharacterInInputStream="control-character-in-input-stream",cs.noncharacterInInputStream="noncharacter-in-input-stream",cs.surrogateInInputStream="surrogate-in-input-stream",cs.nonVoidHtmlElementStartTagWithTrailingSolidus="non-void-html-element-start-tag-with-trailing-solidus",cs.endTagWithAttributes="end-tag-with-attributes",cs.endTagWithTrailingSolidus="end-tag-with-trailing-solidus",cs.unexpectedSolidusInTag="unexpected-solidus-in-tag",cs.unexpectedNullCharacter="unexpected-null-character",cs.unexpectedQuestionMarkInsteadOfTagName="unexpected-question-mark-instead-of-tag-name",cs.invalidFirstCharacterOfTagName="invalid-first-character-of-tag-name",cs.unexpectedEqualsSignBeforeAttributeName="unexpected-equals-sign-before-attribute-name",cs.missingEndTagName="missing-end-tag-name",cs.unexpectedCharacterInAttributeName="unexpected-character-in-attribute-name",cs.unknownNamedCharacterReference="unknown-named-character-reference",cs.missingSemicolonAfterCharacterReference="missing-semicolon-after-character-reference",cs.unexpectedCharacterAfterDoctypeSystemIdentifier="unexpected-character-after-doctype-system-identifier",cs.unexpectedCharacterInUnquotedAttributeValue="unexpected-character-in-unquoted-attribute-value",cs.eofBeforeTagName="eof-before-tag-name",cs.eofInTag="eof-in-tag",cs.missingAttributeValue="missing-attribute-value",cs.missingWhitespaceBetweenAttributes="missing-whitespace-between-attributes",cs.missingWhitespaceAfterDoctypePublicKeyword="missing-whitespace-after-doctype-public-keyword",cs.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers="missing-whitespace-between-doctype-public-and-system-identifiers",cs.missingWhitespaceAfterDoctypeSystemKeyword="missing-whitespace-after-doctype-system-keyword",cs.missingQuoteBeforeDoctypePublicIdentifier="missing-quote-before-doctype-public-identifier",cs.missingQuoteBeforeDoctypeSystemIdentifier="missing-quote-before-doctype-system-identifier",cs.missingDoctypePublicIdentifier="missing-doctype-public-identifier",cs.missingDoctypeSystemIdentifier="missing-doctype-system-identifier",cs.abruptDoctypePublicIdentifier="abrupt-doctype-public-identifier",cs.abruptDoctypeSystemIdentifier="abrupt-doctype-system-identifier",cs.cdataInHtmlContent="cdata-in-html-content",cs.incorrectlyOpenedComment="incorrectly-opened-comment",cs.eofInScriptHtmlCommentLikeText="eof-in-script-html-comment-like-text",cs.eofInDoctype="eof-in-doctype",cs.nestedComment="nested-comment",cs.abruptClosingOfEmptyComment="abrupt-closing-of-empty-comment",cs.eofInComment="eof-in-comment",cs.incorrectlyClosedComment="incorrectly-closed-comment",cs.eofInCdata="eof-in-cdata",cs.absenceOfDigitsInNumericCharacterReference="absence-of-digits-in-numeric-character-reference",cs.nullCharacterReference="null-character-reference",cs.surrogateCharacterReference="surrogate-character-reference",cs.characterReferenceOutsideUnicodeRange="character-reference-outside-unicode-range",cs.controlCharacterReference="control-character-reference",cs.noncharacterCharacterReference="noncharacter-character-reference",cs.missingWhitespaceBeforeDoctypeName="missing-whitespace-before-doctype-name",cs.missingDoctypeName="missing-doctype-name",cs.invalidCharacterSequenceAfterDoctypeName="invalid-character-sequence-after-doctype-name",cs.duplicateAttribute="duplicate-attribute",cs.nonConformingDoctype="non-conforming-doctype",cs.missingDoctype="missing-doctype",cs.misplacedDoctype="misplaced-doctype",cs.endTagWithoutMatchingOpenElement="end-tag-without-matching-open-element",cs.closingOfElementWithOpenChildElements="closing-of-element-with-open-child-elements",cs.disallowedContentInNoscriptInHead="disallowed-content-in-noscript-in-head",cs.openElementsLeftAfterEof="open-elements-left-after-eof",cs.abandonedHeadElementChild="abandoned-head-element-child",cs.misplacedStartTagForHeadElement="misplaced-start-tag-for-head-element",cs.nestedNoscriptInHead="nested-noscript-in-head",cs.eofInElementThatCanContainOnlyText="eof-in-element-that-can-contain-only-text";class ls{constructor(e){this.handler=e,this.html="",this.pos=-1,this.lastGapPos=-2,this.gapStack=[],this.skipNextNewLine=!1,this.lastChunkWritten=!1,this.endOfChunkHit=!1,this.bufferWaterline=65536,this.isEol=!1,this.lineStartPos=0,this.droppedBufferSize=0,this.line=1,this.lastErrOffset=-1}get col(){return this.pos-this.lineStartPos+Number(this.lastGapPos!==this.pos)}get offset(){return this.droppedBufferSize+this.pos}getError(e){const{line:t,col:n,offset:r}=this;return{code:e,startLine:t,endLine:t,startCol:n,endCol:n,startOffset:r,endOffset:r}}_err(e){this.handler.onParseError&&this.lastErrOffset!==this.offset&&(this.lastErrOffset=this.offset,this.handler.onParseError(this.getError(e)))}_addGap(){this.gapStack.push(this.lastGapPos),this.lastGapPos=this.pos}_processSurrogate(e){if(this.pos!==this.html.length-1){const t=this.html.charCodeAt(this.pos+1);if(function(e){return e>=56320&&e<=57343}(t))return this.pos++,this._addGap(),1024*(e-55296)+9216+t}else if(!this.lastChunkWritten)return this.endOfChunkHit=!0,Xr.EOF;return this._err(os.surrogateInInputStream),e}willDropParsedChunk(){return this.pos>this.bufferWaterline}dropParsedChunk(){this.willDropParsedChunk()&&(this.html=this.html.substring(this.pos),this.lineStartPos-=this.pos,this.droppedBufferSize+=this.pos,this.pos=0,this.lastGapPos=-2,this.gapStack.length=0)}write(e,t){this.html.length>0?this.html+=e:this.html=e,this.endOfChunkHit=!1,this.lastChunkWritten=t}insertHtmlAtCurrentPos(e){this.html=this.html.substring(0,this.pos+1)+e+this.html.substring(this.pos+1),this.endOfChunkHit=!1}startsWith(e,t){if(this.pos+e.length>this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,!1;if(t)return this.html.startsWith(e,this.pos);for(let t=0;t<e.length;t++){if((32|this.html.charCodeAt(this.pos+t))!==e.charCodeAt(t))return!1}return!0}peek(e){const t=this.pos+e;if(t>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,Xr.EOF;const n=this.html.charCodeAt(t);return n===Xr.CARRIAGE_RETURN?Xr.LINE_FEED:n}advance(){if(this.pos++,this.isEol&&(this.isEol=!1,this.line++,this.lineStartPos=this.pos),this.pos>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,Xr.EOF;let e=this.html.charCodeAt(this.pos);if(e===Xr.CARRIAGE_RETURN)return this.isEol=!0,this.skipNextNewLine=!0,Xr.LINE_FEED;if(e===Xr.LINE_FEED&&(this.isEol=!0,this.skipNextNewLine))return this.line--,this.skipNextNewLine=!1,this._addGap(),this.advance();this.skipNextNewLine=!1,ss(e)&&(e=this._processSurrogate(e));return null===this.handler.onParseError||e>31&&e<127||e===Xr.LINE_FEED||e===Xr.CARRIAGE_RETURN||e>159&&e<64976||this._checkForProblematicCharacters(e),e}_checkForProblematicCharacters(e){is(e)?this._err(os.controlCharacterInInputStream):as(e)&&this._err(os.noncharacterInInputStream)}retreat(e){for(this.pos-=e;this.pos<this.lastGapPos;)this.lastGapPos=this.gapStack.pop(),this.pos--;this.isEol=!1}}var us,hs,ps,ds,fs,Es,ms,Ts,_s,As,gs,Cs;function Is(e,t){for(let n=e.attrs.length-1;n>=0;n--)if(e.attrs[n].name===t)return e.attrs[n].value;return null}(hs=us=us||(us={}))[hs.CHARACTER=0]="CHARACTER",hs[hs.NULL_CHARACTER=1]="NULL_CHARACTER",hs[hs.WHITESPACE_CHARACTER=2]="WHITESPACE_CHARACTER",hs[hs.START_TAG=3]="START_TAG",hs[hs.END_TAG=4]="END_TAG",hs[hs.COMMENT=5]="COMMENT",hs[hs.DOCTYPE=6]="DOCTYPE",hs[hs.EOF=7]="EOF",hs[hs.HIBERNATION=8]="HIBERNATION",(ds=ps=ps||(ps={})).HTML="http://www.w3.org/1999/xhtml",ds.MATHML="http://www.w3.org/1998/Math/MathML",ds.SVG="http://www.w3.org/2000/svg",ds.XLINK="http://www.w3.org/1999/xlink",ds.XML="http://www.w3.org/XML/1998/namespace",ds.XMLNS="http://www.w3.org/2000/xmlns/",(Es=fs=fs||(fs={})).TYPE="type",Es.ACTION="action",Es.ENCODING="encoding",Es.PROMPT="prompt",Es.NAME="name",Es.COLOR="color",Es.FACE="face",Es.SIZE="size",(Ts=ms=ms||(ms={})).NO_QUIRKS="no-quirks",Ts.QUIRKS="quirks",Ts.LIMITED_QUIRKS="limited-quirks",(As=_s=_s||(_s={})).A="a",As.ADDRESS="address",As.ANNOTATION_XML="annotation-xml",As.APPLET="applet",As.AREA="area",As.ARTICLE="article",As.ASIDE="aside",As.B="b",As.BASE="base",As.BASEFONT="basefont",As.BGSOUND="bgsound",As.BIG="big",As.BLOCKQUOTE="blockquote",As.BODY="body",As.BR="br",As.BUTTON="button",As.CAPTION="caption",As.CENTER="center",As.CODE="code",As.COL="col",As.COLGROUP="colgroup",As.DD="dd",As.DESC="desc",As.DETAILS="details",As.DIALOG="dialog",As.DIR="dir",As.DIV="div",As.DL="dl",As.DT="dt",As.EM="em",As.EMBED="embed",As.FIELDSET="fieldset",As.FIGCAPTION="figcaption",As.FIGURE="figure",As.FONT="font",As.FOOTER="footer",As.FOREIGN_OBJECT="foreignObject",As.FORM="form",As.FRAME="frame",As.FRAMESET="frameset",As.H1="h1",As.H2="h2",As.H3="h3",As.H4="h4",As.H5="h5",As.H6="h6",As.HEAD="head",As.HEADER="header",As.HGROUP="hgroup",As.HR="hr",As.HTML="html",As.I="i",As.IMG="img",As.IMAGE="image",As.INPUT="input",As.IFRAME="iframe",As.KEYGEN="keygen",As.LABEL="label",As.LI="li",As.LINK="link",As.LISTING="listing",As.MAIN="main",As.MALIGNMARK="malignmark",As.MARQUEE="marquee",As.MATH="math",As.MENU="menu",As.META="meta",As.MGLYPH="mglyph",As.MI="mi",As.MO="mo",As.MN="mn",As.MS="ms",As.MTEXT="mtext",As.NAV="nav",As.NOBR="nobr",As.NOFRAMES="noframes",As.NOEMBED="noembed",As.NOSCRIPT="noscript",As.OBJECT="object",As.OL="ol",As.OPTGROUP="optgroup",As.OPTION="option",As.P="p",As.PARAM="param",As.PLAINTEXT="plaintext",As.PRE="pre",As.RB="rb",As.RP="rp",As.RT="rt",As.RTC="rtc",As.RUBY="ruby",As.S="s",As.SCRIPT="script",As.SECTION="section",As.SELECT="select",As.SOURCE="source",As.SMALL="small",As.SPAN="span",As.STRIKE="strike",As.STRONG="strong",As.STYLE="style",As.SUB="sub",As.SUMMARY="summary",As.SUP="sup",As.TABLE="table",As.TBODY="tbody",As.TEMPLATE="template",As.TEXTAREA="textarea",As.TFOOT="tfoot",As.TD="td",As.TH="th",As.THEAD="thead",As.TITLE="title",As.TR="tr",As.TRACK="track",As.TT="tt",As.U="u",As.UL="ul",As.SVG="svg",As.VAR="var",As.WBR="wbr",As.XMP="xmp",(Cs=gs=gs||(gs={}))[Cs.UNKNOWN=0]="UNKNOWN",Cs[Cs.A=1]="A",Cs[Cs.ADDRESS=2]="ADDRESS",Cs[Cs.ANNOTATION_XML=3]="ANNOTATION_XML",Cs[Cs.APPLET=4]="APPLET",Cs[Cs.AREA=5]="AREA",Cs[Cs.ARTICLE=6]="ARTICLE",Cs[Cs.ASIDE=7]="ASIDE",Cs[Cs.B=8]="B",Cs[Cs.BASE=9]="BASE",Cs[Cs.BASEFONT=10]="BASEFONT",Cs[Cs.BGSOUND=11]="BGSOUND",Cs[Cs.BIG=12]="BIG",Cs[Cs.BLOCKQUOTE=13]="BLOCKQUOTE",Cs[Cs.BODY=14]="BODY",Cs[Cs.BR=15]="BR",Cs[Cs.BUTTON=16]="BUTTON",Cs[Cs.CAPTION=17]="CAPTION",Cs[Cs.CENTER=18]="CENTER",Cs[Cs.CODE=19]="CODE",Cs[Cs.COL=20]="COL",Cs[Cs.COLGROUP=21]="COLGROUP",Cs[Cs.DD=22]="DD",Cs[Cs.DESC=23]="DESC",Cs[Cs.DETAILS=24]="DETAILS",Cs[Cs.DIALOG=25]="DIALOG",Cs[Cs.DIR=26]="DIR",Cs[Cs.DIV=27]="DIV",Cs[Cs.DL=28]="DL",Cs[Cs.DT=29]="DT",Cs[Cs.EM=30]="EM",Cs[Cs.EMBED=31]="EMBED",Cs[Cs.FIELDSET=32]="FIELDSET",Cs[Cs.FIGCAPTION=33]="FIGCAPTION",Cs[Cs.FIGURE=34]="FIGURE",Cs[Cs.FONT=35]="FONT",Cs[Cs.FOOTER=36]="FOOTER",Cs[Cs.FOREIGN_OBJECT=37]="FOREIGN_OBJECT",Cs[Cs.FORM=38]="FORM",Cs[Cs.FRAME=39]="FRAME",Cs[Cs.FRAMESET=40]="FRAMESET",Cs[Cs.H1=41]="H1",Cs[Cs.H2=42]="H2",Cs[Cs.H3=43]="H3",Cs[Cs.H4=44]="H4",Cs[Cs.H5=45]="H5",Cs[Cs.H6=46]="H6",Cs[Cs.HEAD=47]="HEAD",Cs[Cs.HEADER=48]="HEADER",Cs[Cs.HGROUP=49]="HGROUP",Cs[Cs.HR=50]="HR",Cs[Cs.HTML=51]="HTML",Cs[Cs.I=52]="I",Cs[Cs.IMG=53]="IMG",Cs[Cs.IMAGE=54]="IMAGE",Cs[Cs.INPUT=55]="INPUT",Cs[Cs.IFRAME=56]="IFRAME",Cs[Cs.KEYGEN=57]="KEYGEN",Cs[Cs.LABEL=58]="LABEL",Cs[Cs.LI=59]="LI",Cs[Cs.LINK=60]="LINK",Cs[Cs.LISTING=61]="LISTING",Cs[Cs.MAIN=62]="MAIN",Cs[Cs.MALIGNMARK=63]="MALIGNMARK",Cs[Cs.MARQUEE=64]="MARQUEE",Cs[Cs.MATH=65]="MATH",Cs[Cs.MENU=66]="MENU",Cs[Cs.META=67]="META",Cs[Cs.MGLYPH=68]="MGLYPH",Cs[Cs.MI=69]="MI",Cs[Cs.MO=70]="MO",Cs[Cs.MN=71]="MN",Cs[Cs.MS=72]="MS",Cs[Cs.MTEXT=73]="MTEXT",Cs[Cs.NAV=74]="NAV",Cs[Cs.NOBR=75]="NOBR",Cs[Cs.NOFRAMES=76]="NOFRAMES",Cs[Cs.NOEMBED=77]="NOEMBED",Cs[Cs.NOSCRIPT=78]="NOSCRIPT",Cs[Cs.OBJECT=79]="OBJECT",Cs[Cs.OL=80]="OL",Cs[Cs.OPTGROUP=81]="OPTGROUP",Cs[Cs.OPTION=82]="OPTION",Cs[Cs.P=83]="P",Cs[Cs.PARAM=84]="PARAM",Cs[Cs.PLAINTEXT=85]="PLAINTEXT",Cs[Cs.PRE=86]="PRE",Cs[Cs.RB=87]="RB",Cs[Cs.RP=88]="RP",Cs[Cs.RT=89]="RT",Cs[Cs.RTC=90]="RTC",Cs[Cs.RUBY=91]="RUBY",Cs[Cs.S=92]="S",Cs[Cs.SCRIPT=93]="SCRIPT",Cs[Cs.SECTION=94]="SECTION",Cs[Cs.SELECT=95]="SELECT",Cs[Cs.SOURCE=96]="SOURCE",Cs[Cs.SMALL=97]="SMALL",Cs[Cs.SPAN=98]="SPAN",Cs[Cs.STRIKE=99]="STRIKE",Cs[Cs.STRONG=100]="STRONG",Cs[Cs.STYLE=101]="STYLE",Cs[Cs.SUB=102]="SUB",Cs[Cs.SUMMARY=103]="SUMMARY",Cs[Cs.SUP=104]="SUP",Cs[Cs.TABLE=105]="TABLE",Cs[Cs.TBODY=106]="TBODY",Cs[Cs.TEMPLATE=107]="TEMPLATE",Cs[Cs.TEXTAREA=108]="TEXTAREA",Cs[Cs.TFOOT=109]="TFOOT",Cs[Cs.TD=110]="TD",Cs[Cs.TH=111]="TH",Cs[Cs.THEAD=112]="THEAD",Cs[Cs.TITLE=113]="TITLE",Cs[Cs.TR=114]="TR",Cs[Cs.TRACK=115]="TRACK",Cs[Cs.TT=116]="TT",Cs[Cs.U=117]="U",Cs[Cs.UL=118]="UL",Cs[Cs.SVG=119]="SVG",Cs[Cs.VAR=120]="VAR",Cs[Cs.WBR=121]="WBR",Cs[Cs.XMP=122]="XMP";const Ns=new Map([[_s.A,gs.A],[_s.ADDRESS,gs.ADDRESS],[_s.ANNOTATION_XML,gs.ANNOTATION_XML],[_s.APPLET,gs.APPLET],[_s.AREA,gs.AREA],[_s.ARTICLE,gs.ARTICLE],[_s.ASIDE,gs.ASIDE],[_s.B,gs.B],[_s.BASE,gs.BASE],[_s.BASEFONT,gs.BASEFONT],[_s.BGSOUND,gs.BGSOUND],[_s.BIG,gs.BIG],[_s.BLOCKQUOTE,gs.BLOCKQUOTE],[_s.BODY,gs.BODY],[_s.BR,gs.BR],[_s.BUTTON,gs.BUTTON],[_s.CAPTION,gs.CAPTION],[_s.CENTER,gs.CENTER],[_s.CODE,gs.CODE],[_s.COL,gs.COL],[_s.COLGROUP,gs.COLGROUP],[_s.DD,gs.DD],[_s.DESC,gs.DESC],[_s.DETAILS,gs.DETAILS],[_s.DIALOG,gs.DIALOG],[_s.DIR,gs.DIR],[_s.DIV,gs.DIV],[_s.DL,gs.DL],[_s.DT,gs.DT],[_s.EM,gs.EM],[_s.EMBED,gs.EMBED],[_s.FIELDSET,gs.FIELDSET],[_s.FIGCAPTION,gs.FIGCAPTION],[_s.FIGURE,gs.FIGURE],[_s.FONT,gs.FONT],[_s.FOOTER,gs.FOOTER],[_s.FOREIGN_OBJECT,gs.FOREIGN_OBJECT],[_s.FORM,gs.FORM],[_s.FRAME,gs.FRAME],[_s.FRAMESET,gs.FRAMESET],[_s.H1,gs.H1],[_s.H2,gs.H2],[_s.H3,gs.H3],[_s.H4,gs.H4],[_s.H5,gs.H5],[_s.H6,gs.H6],[_s.HEAD,gs.HEAD],[_s.HEADER,gs.HEADER],[_s.HGROUP,gs.HGROUP],[_s.HR,gs.HR],[_s.HTML,gs.HTML],[_s.I,gs.I],[_s.IMG,gs.IMG],[_s.IMAGE,gs.IMAGE],[_s.INPUT,gs.INPUT],[_s.IFRAME,gs.IFRAME],[_s.KEYGEN,gs.KEYGEN],[_s.LABEL,gs.LABEL],[_s.LI,gs.LI],[_s.LINK,gs.LINK],[_s.LISTING,gs.LISTING],[_s.MAIN,gs.MAIN],[_s.MALIGNMARK,gs.MALIGNMARK],[_s.MARQUEE,gs.MARQUEE],[_s.MATH,gs.MATH],[_s.MENU,gs.MENU],[_s.META,gs.META],[_s.MGLYPH,gs.MGLYPH],[_s.MI,gs.MI],[_s.MO,gs.MO],[_s.MN,gs.MN],[_s.MS,gs.MS],[_s.MTEXT,gs.MTEXT],[_s.NAV,gs.NAV],[_s.NOBR,gs.NOBR],[_s.NOFRAMES,gs.NOFRAMES],[_s.NOEMBED,gs.NOEMBED],[_s.NOSCRIPT,gs.NOSCRIPT],[_s.OBJECT,gs.OBJECT],[_s.OL,gs.OL],[_s.OPTGROUP,gs.OPTGROUP],[_s.OPTION,gs.OPTION],[_s.P,gs.P],[_s.PARAM,gs.PARAM],[_s.PLAINTEXT,gs.PLAINTEXT],[_s.PRE,gs.PRE],[_s.RB,gs.RB],[_s.RP,gs.RP],[_s.RT,gs.RT],[_s.RTC,gs.RTC],[_s.RUBY,gs.RUBY],[_s.S,gs.S],[_s.SCRIPT,gs.SCRIPT],[_s.SECTION,gs.SECTION],[_s.SELECT,gs.SELECT],[_s.SOURCE,gs.SOURCE],[_s.SMALL,gs.SMALL],[_s.SPAN,gs.SPAN],[_s.STRIKE,gs.STRIKE],[_s.STRONG,gs.STRONG],[_s.STYLE,gs.STYLE],[_s.SUB,gs.SUB],[_s.SUMMARY,gs.SUMMARY],[_s.SUP,gs.SUP],[_s.TABLE,gs.TABLE],[_s.TBODY,gs.TBODY],[_s.TEMPLATE,gs.TEMPLATE],[_s.TEXTAREA,gs.TEXTAREA],[_s.TFOOT,gs.TFOOT],[_s.TD,gs.TD],[_s.TH,gs.TH],[_s.THEAD,gs.THEAD],[_s.TITLE,gs.TITLE],[_s.TR,gs.TR],[_s.TRACK,gs.TRACK],[_s.TT,gs.TT],[_s.U,gs.U],[_s.UL,gs.UL],[_s.SVG,gs.SVG],[_s.VAR,gs.VAR],[_s.WBR,gs.WBR],[_s.XMP,gs.XMP]]);function Ds(e){var t;return null!==(t=Ns.get(e))&&void 0!==t?t:gs.UNKNOWN}const Ss=gs,ks={[ps.HTML]:new Set([Ss.ADDRESS,Ss.APPLET,Ss.AREA,Ss.ARTICLE,Ss.ASIDE,Ss.BASE,Ss.BASEFONT,Ss.BGSOUND,Ss.BLOCKQUOTE,Ss.BODY,Ss.BR,Ss.BUTTON,Ss.CAPTION,Ss.CENTER,Ss.COL,Ss.COLGROUP,Ss.DD,Ss.DETAILS,Ss.DIR,Ss.DIV,Ss.DL,Ss.DT,Ss.EMBED,Ss.FIELDSET,Ss.FIGCAPTION,Ss.FIGURE,Ss.FOOTER,Ss.FORM,Ss.FRAME,Ss.FRAMESET,Ss.H1,Ss.H2,Ss.H3,Ss.H4,Ss.H5,Ss.H6,Ss.HEAD,Ss.HEADER,Ss.HGROUP,Ss.HR,Ss.HTML,Ss.IFRAME,Ss.IMG,Ss.INPUT,Ss.LI,Ss.LINK,Ss.LISTING,Ss.MAIN,Ss.MARQUEE,Ss.MENU,Ss.META,Ss.NAV,Ss.NOEMBED,Ss.NOFRAMES,Ss.NOSCRIPT,Ss.OBJECT,Ss.OL,Ss.P,Ss.PARAM,Ss.PLAINTEXT,Ss.PRE,Ss.SCRIPT,Ss.SECTION,Ss.SELECT,Ss.SOURCE,Ss.STYLE,Ss.SUMMARY,Ss.TABLE,Ss.TBODY,Ss.TD,Ss.TEMPLATE,Ss.TEXTAREA,Ss.TFOOT,Ss.TH,Ss.THEAD,Ss.TITLE,Ss.TR,Ss.TRACK,Ss.UL,Ss.WBR,Ss.XMP]),[ps.MATHML]:new Set([Ss.MI,Ss.MO,Ss.MN,Ss.MS,Ss.MTEXT,Ss.ANNOTATION_XML]),[ps.SVG]:new Set([Ss.TITLE,Ss.FOREIGN_OBJECT,Ss.DESC]),[ps.XLINK]:new Set,[ps.XML]:new Set,[ps.XMLNS]:new Set};function bs(e){return e===Ss.H1||e===Ss.H2||e===Ss.H3||e===Ss.H4||e===Ss.H5||e===Ss.H6}const Os=new Set([_s.STYLE,_s.SCRIPT,_s.XMP,_s.IFRAME,_s.NOEMBED,_s.NOFRAMES,_s.PLAINTEXT]);const Rs=new Map([[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]);var Ls,ys;(ys=Ls||(Ls={}))[ys.DATA=0]="DATA",ys[ys.RCDATA=1]="RCDATA",ys[ys.RAWTEXT=2]="RAWTEXT",ys[ys.SCRIPT_DATA=3]="SCRIPT_DATA",ys[ys.PLAINTEXT=4]="PLAINTEXT",ys[ys.TAG_OPEN=5]="TAG_OPEN",ys[ys.END_TAG_OPEN=6]="END_TAG_OPEN",ys[ys.TAG_NAME=7]="TAG_NAME",ys[ys.RCDATA_LESS_THAN_SIGN=8]="RCDATA_LESS_THAN_SIGN",ys[ys.RCDATA_END_TAG_OPEN=9]="RCDATA_END_TAG_OPEN",ys[ys.RCDATA_END_TAG_NAME=10]="RCDATA_END_TAG_NAME",ys[ys.RAWTEXT_LESS_THAN_SIGN=11]="RAWTEXT_LESS_THAN_SIGN",ys[ys.RAWTEXT_END_TAG_OPEN=12]="RAWTEXT_END_TAG_OPEN",ys[ys.RAWTEXT_END_TAG_NAME=13]="RAWTEXT_END_TAG_NAME",ys[ys.SCRIPT_DATA_LESS_THAN_SIGN=14]="SCRIPT_DATA_LESS_THAN_SIGN",ys[ys.SCRIPT_DATA_END_TAG_OPEN=15]="SCRIPT_DATA_END_TAG_OPEN",ys[ys.SCRIPT_DATA_END_TAG_NAME=16]="SCRIPT_DATA_END_TAG_NAME",ys[ys.SCRIPT_DATA_ESCAPE_START=17]="SCRIPT_DATA_ESCAPE_START",ys[ys.SCRIPT_DATA_ESCAPE_START_DASH=18]="SCRIPT_DATA_ESCAPE_START_DASH",ys[ys.SCRIPT_DATA_ESCAPED=19]="SCRIPT_DATA_ESCAPED",ys[ys.SCRIPT_DATA_ESCAPED_DASH=20]="SCRIPT_DATA_ESCAPED_DASH",ys[ys.SCRIPT_DATA_ESCAPED_DASH_DASH=21]="SCRIPT_DATA_ESCAPED_DASH_DASH",ys[ys.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN=22]="SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN",ys[ys.SCRIPT_DATA_ESCAPED_END_TAG_OPEN=23]="SCRIPT_DATA_ESCAPED_END_TAG_OPEN",ys[ys.SCRIPT_DATA_ESCAPED_END_TAG_NAME=24]="SCRIPT_DATA_ESCAPED_END_TAG_NAME",ys[ys.SCRIPT_DATA_DOUBLE_ESCAPE_START=25]="SCRIPT_DATA_DOUBLE_ESCAPE_START",ys[ys.SCRIPT_DATA_DOUBLE_ESCAPED=26]="SCRIPT_DATA_DOUBLE_ESCAPED",ys[ys.SCRIPT_DATA_DOUBLE_ESCAPED_DASH=27]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH",ys[ys.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH=28]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH",ys[ys.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN=29]="SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN",ys[ys.SCRIPT_DATA_DOUBLE_ESCAPE_END=30]="SCRIPT_DATA_DOUBLE_ESCAPE_END",ys[ys.BEFORE_ATTRIBUTE_NAME=31]="BEFORE_ATTRIBUTE_NAME",ys[ys.ATTRIBUTE_NAME=32]="ATTRIBUTE_NAME",ys[ys.AFTER_ATTRIBUTE_NAME=33]="AFTER_ATTRIBUTE_NAME",ys[ys.BEFORE_ATTRIBUTE_VALUE=34]="BEFORE_ATTRIBUTE_VALUE",ys[ys.ATTRIBUTE_VALUE_DOUBLE_QUOTED=35]="ATTRIBUTE_VALUE_DOUBLE_QUOTED",ys[ys.ATTRIBUTE_VALUE_SINGLE_QUOTED=36]="ATTRIBUTE_VALUE_SINGLE_QUOTED",ys[ys.ATTRIBUTE_VALUE_UNQUOTED=37]="ATTRIBUTE_VALUE_UNQUOTED",ys[ys.AFTER_ATTRIBUTE_VALUE_QUOTED=38]="AFTER_ATTRIBUTE_VALUE_QUOTED",ys[ys.SELF_CLOSING_START_TAG=39]="SELF_CLOSING_START_TAG",ys[ys.BOGUS_COMMENT=40]="BOGUS_COMMENT",ys[ys.MARKUP_DECLARATION_OPEN=41]="MARKUP_DECLARATION_OPEN",ys[ys.COMMENT_START=42]="COMMENT_START",ys[ys.COMMENT_START_DASH=43]="COMMENT_START_DASH",ys[ys.COMMENT=44]="COMMENT",ys[ys.COMMENT_LESS_THAN_SIGN=45]="COMMENT_LESS_THAN_SIGN",ys[ys.COMMENT_LESS_THAN_SIGN_BANG=46]="COMMENT_LESS_THAN_SIGN_BANG",ys[ys.COMMENT_LESS_THAN_SIGN_BANG_DASH=47]="COMMENT_LESS_THAN_SIGN_BANG_DASH",ys[ys.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH=48]="COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH",ys[ys.COMMENT_END_DASH=49]="COMMENT_END_DASH",ys[ys.COMMENT_END=50]="COMMENT_END",ys[ys.COMMENT_END_BANG=51]="COMMENT_END_BANG",ys[ys.DOCTYPE=52]="DOCTYPE",ys[ys.BEFORE_DOCTYPE_NAME=53]="BEFORE_DOCTYPE_NAME",ys[ys.DOCTYPE_NAME=54]="DOCTYPE_NAME",ys[ys.AFTER_DOCTYPE_NAME=55]="AFTER_DOCTYPE_NAME",ys[ys.AFTER_DOCTYPE_PUBLIC_KEYWORD=56]="AFTER_DOCTYPE_PUBLIC_KEYWORD",ys[ys.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER=57]="BEFORE_DOCTYPE_PUBLIC_IDENTIFIER",ys[ys.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED=58]="DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED",ys[ys.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED=59]="DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED",ys[ys.AFTER_DOCTYPE_PUBLIC_IDENTIFIER=60]="AFTER_DOCTYPE_PUBLIC_IDENTIFIER",ys[ys.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS=61]="BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS",ys[ys.AFTER_DOCTYPE_SYSTEM_KEYWORD=62]="AFTER_DOCTYPE_SYSTEM_KEYWORD",ys[ys.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER=63]="BEFORE_DOCTYPE_SYSTEM_IDENTIFIER",ys[ys.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED=64]="DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED",ys[ys.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED=65]="DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED",ys[ys.AFTER_DOCTYPE_SYSTEM_IDENTIFIER=66]="AFTER_DOCTYPE_SYSTEM_IDENTIFIER",ys[ys.BOGUS_DOCTYPE=67]="BOGUS_DOCTYPE",ys[ys.CDATA_SECTION=68]="CDATA_SECTION",ys[ys.CDATA_SECTION_BRACKET=69]="CDATA_SECTION_BRACKET",ys[ys.CDATA_SECTION_END=70]="CDATA_SECTION_END",ys[ys.CHARACTER_REFERENCE=71]="CHARACTER_REFERENCE",ys[ys.NAMED_CHARACTER_REFERENCE=72]="NAMED_CHARACTER_REFERENCE",ys[ys.AMBIGUOUS_AMPERSAND=73]="AMBIGUOUS_AMPERSAND",ys[ys.NUMERIC_CHARACTER_REFERENCE=74]="NUMERIC_CHARACTER_REFERENCE",ys[ys.HEXADEMICAL_CHARACTER_REFERENCE_START=75]="HEXADEMICAL_CHARACTER_REFERENCE_START",ys[ys.HEXADEMICAL_CHARACTER_REFERENCE=76]="HEXADEMICAL_CHARACTER_REFERENCE",ys[ys.DECIMAL_CHARACTER_REFERENCE=77]="DECIMAL_CHARACTER_REFERENCE",ys[ys.NUMERIC_CHARACTER_REFERENCE_END=78]="NUMERIC_CHARACTER_REFERENCE_END";const Ms={DATA:Ls.DATA,RCDATA:Ls.RCDATA,RAWTEXT:Ls.RAWTEXT,SCRIPT_DATA:Ls.SCRIPT_DATA,PLAINTEXT:Ls.PLAINTEXT,CDATA_SECTION:Ls.CDATA_SECTION};function Fs(e){return e>=Xr.DIGIT_0&&e<=Xr.DIGIT_9}function xs(e){return e>=Xr.LATIN_CAPITAL_A&&e<=Xr.LATIN_CAPITAL_Z}function Ps(e){return function(e){return e>=Xr.LATIN_SMALL_A&&e<=Xr.LATIN_SMALL_Z}(e)||xs(e)}function vs(e){return Ps(e)||Fs(e)}function Bs(e){return e>=Xr.LATIN_CAPITAL_A&&e<=Xr.LATIN_CAPITAL_F}function ws(e){return e>=Xr.LATIN_SMALL_A&&e<=Xr.LATIN_SMALL_F}function Us(e){return e+32}function Hs(e){return e===Xr.SPACE||e===Xr.LINE_FEED||e===Xr.TABULATION||e===Xr.FORM_FEED}function Gs(e){return Hs(e)||e===Xr.SOLIDUS||e===Xr.GREATER_THAN_SIGN}const qs=new Set([gs.DD,gs.DT,gs.LI,gs.OPTGROUP,gs.OPTION,gs.P,gs.RB,gs.RP,gs.RT,gs.RTC]),Ys=new Set([...qs,gs.CAPTION,gs.COLGROUP,gs.TBODY,gs.TD,gs.TFOOT,gs.TH,gs.THEAD,gs.TR]),js=new Map([[gs.APPLET,ps.HTML],[gs.CAPTION,ps.HTML],[gs.HTML,ps.HTML],[gs.MARQUEE,ps.HTML],[gs.OBJECT,ps.HTML],[gs.TABLE,ps.HTML],[gs.TD,ps.HTML],[gs.TEMPLATE,ps.HTML],[gs.TH,ps.HTML],[gs.ANNOTATION_XML,ps.MATHML],[gs.MI,ps.MATHML],[gs.MN,ps.MATHML],[gs.MO,ps.MATHML],[gs.MS,ps.MATHML],[gs.MTEXT,ps.MATHML],[gs.DESC,ps.SVG],[gs.FOREIGN_OBJECT,ps.SVG],[gs.TITLE,ps.SVG]]),zs=[gs.H1,gs.H2,gs.H3,gs.H4,gs.H5,gs.H6],Vs=[gs.TR,gs.TEMPLATE,gs.HTML],Qs=[gs.TBODY,gs.TFOOT,gs.THEAD,gs.TEMPLATE,gs.HTML],$s=[gs.TABLE,gs.TEMPLATE,gs.HTML],Ws=[gs.TD,gs.TH];class Xs{get currentTmplContentOrNode(){return this._isInTemplate()?this.treeAdapter.getTemplateContent(this.current):this.current}constructor(e,t,n){this.treeAdapter=t,this.handler=n,this.items=[],this.tagIDs=[],this.stackTop=-1,this.tmplCount=0,this.currentTagId=gs.UNKNOWN,this.current=e}_indexOf(e){return this.items.lastIndexOf(e,this.stackTop)}_isInTemplate(){return this.currentTagId===gs.TEMPLATE&&this.treeAdapter.getNamespaceURI(this.current)===ps.HTML}_updateCurrentElement(){this.current=this.items[this.stackTop],this.currentTagId=this.tagIDs[this.stackTop]}push(e,t){this.stackTop++,this.items[this.stackTop]=e,this.current=e,this.tagIDs[this.stackTop]=t,this.currentTagId=t,this._isInTemplate()&&this.tmplCount++,this.handler.onItemPush(e,t,!0)}pop(){const e=this.current;this.tmplCount>0&&this._isInTemplate()&&this.tmplCount--,this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!0)}replace(e,t){const n=this._indexOf(e);this.items[n]=t,n===this.stackTop&&(this.current=t)}insertAfter(e,t,n){const r=this._indexOf(e)+1;this.items.splice(r,0,t),this.tagIDs.splice(r,0,n),this.stackTop++,r===this.stackTop&&this._updateCurrentElement(),this.handler.onItemPush(this.current,this.currentTagId,r===this.stackTop)}popUntilTagNamePopped(e){let t=this.stackTop+1;do{t=this.tagIDs.lastIndexOf(e,t-1)}while(t>0&&this.treeAdapter.getNamespaceURI(this.items[t])!==ps.HTML);this.shortenToLength(t<0?0:t)}shortenToLength(e){for(;this.stackTop>=e;){const t=this.current;this.tmplCount>0&&this._isInTemplate()&&(this.tmplCount-=1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(t,this.stackTop<e)}}popUntilElementPopped(e){const t=this._indexOf(e);this.shortenToLength(t<0?0:t)}popUntilPopped(e,t){const n=this._indexOfTagNames(e,t);this.shortenToLength(n<0?0:n)}popUntilNumberedHeaderPopped(){this.popUntilPopped(zs,ps.HTML)}popUntilTableCellPopped(){this.popUntilPopped(Ws,ps.HTML)}popAllUpToHtmlElement(){this.tmplCount=0,this.shortenToLength(1)}_indexOfTagNames(e,t){for(let n=this.stackTop;n>=0;n--)if(e.includes(this.tagIDs[n])&&this.treeAdapter.getNamespaceURI(this.items[n])===t)return n;return-1}clearBackTo(e,t){const n=this._indexOfTagNames(e,t);this.shortenToLength(n+1)}clearBackToTableContext(){this.clearBackTo($s,ps.HTML)}clearBackToTableBodyContext(){this.clearBackTo(Qs,ps.HTML)}clearBackToTableRowContext(){this.clearBackTo(Vs,ps.HTML)}remove(e){const t=this._indexOf(e);t>=0&&(t===this.stackTop?this.pop():(this.items.splice(t,1),this.tagIDs.splice(t,1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!1)))}tryPeekProperlyNestedBodyElement(){return this.stackTop>=1&&this.tagIDs[1]===gs.BODY?this.items[1]:null}contains(e){return this._indexOf(e)>-1}getCommonAncestor(e){const t=this._indexOf(e)-1;return t>=0?this.items[t]:null}isRootHtmlElementCurrent(){return 0===this.stackTop&&this.tagIDs[0]===gs.HTML}hasInScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t],r=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&r===ps.HTML)return!0;if(js.get(n)===r)return!1}return!0}hasNumberedHeaderInScope(){for(let e=this.stackTop;e>=0;e--){const t=this.tagIDs[e],n=this.treeAdapter.getNamespaceURI(this.items[e]);if(bs(t)&&n===ps.HTML)return!0;if(js.get(t)===n)return!1}return!0}hasInListItemScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t],r=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&r===ps.HTML)return!0;if((n===gs.UL||n===gs.OL)&&r===ps.HTML||js.get(n)===r)return!1}return!0}hasInButtonScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t],r=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&r===ps.HTML)return!0;if(n===gs.BUTTON&&r===ps.HTML||js.get(n)===r)return!1}return!0}hasInTableScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t];if(this.treeAdapter.getNamespaceURI(this.items[t])===ps.HTML){if(n===e)return!0;if(n===gs.TABLE||n===gs.TEMPLATE||n===gs.HTML)return!1}}return!0}hasTableBodyContextInTableScope(){for(let e=this.stackTop;e>=0;e--){const t=this.tagIDs[e];if(this.treeAdapter.getNamespaceURI(this.items[e])===ps.HTML){if(t===gs.TBODY||t===gs.THEAD||t===gs.TFOOT)return!0;if(t===gs.TABLE||t===gs.HTML)return!1}}return!0}hasInSelectScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t];if(this.treeAdapter.getNamespaceURI(this.items[t])===ps.HTML){if(n===e)return!0;if(n!==gs.OPTION&&n!==gs.OPTGROUP)return!1}}return!0}generateImpliedEndTags(){for(;qs.has(this.currentTagId);)this.pop()}generateImpliedEndTagsThoroughly(){for(;Ys.has(this.currentTagId);)this.pop()}generateImpliedEndTagsWithExclusion(e){for(;this.currentTagId!==e&&Ys.has(this.currentTagId);)this.pop()}}var Ks,Zs;(Zs=Ks=Ks||(Ks={}))[Zs.Marker=0]="Marker",Zs[Zs.Element=1]="Element";const Js={type:Ks.Marker};class ei{constructor(e){this.treeAdapter=e,this.entries=[],this.bookmark=null}_getNoahArkConditionCandidates(e,t){const n=[],r=t.length,s=this.treeAdapter.getTagName(e),i=this.treeAdapter.getNamespaceURI(e);for(let e=0;e<this.entries.length;e++){const t=this.entries[e];if(t.type===Ks.Marker)break;const{element:a}=t;if(this.treeAdapter.getTagName(a)===s&&this.treeAdapter.getNamespaceURI(a)===i){const t=this.treeAdapter.getAttrList(a);t.length===r&&n.push({idx:e,attrs:t})}}return n}_ensureNoahArkCondition(e){if(this.entries.length<3)return;const t=this.treeAdapter.getAttrList(e),n=this._getNoahArkConditionCandidates(e,t);if(n.length<3)return;const r=new Map(t.map((e=>[e.name,e.value])));let s=0;for(let e=0;e<n.length;e++){const t=n[e];t.attrs.every((e=>r.get(e.name)===e.value))&&(s+=1,s>=3&&this.entries.splice(t.idx,1))}}insertMarker(){this.entries.unshift(Js)}pushElement(e,t){this._ensureNoahArkCondition(e),this.entries.unshift({type:Ks.Element,element:e,token:t})}insertElementAfterBookmark(e,t){const n=this.entries.indexOf(this.bookmark);this.entries.splice(n,0,{type:Ks.Element,element:e,token:t})}removeEntry(e){const t=this.entries.indexOf(e);t>=0&&this.entries.splice(t,1)}clearToLastMarker(){const e=this.entries.indexOf(Js);e>=0?this.entries.splice(0,e+1):this.entries.length=0}getElementEntryInScopeWithTagName(e){const t=this.entries.find((t=>t.type===Ks.Marker||this.treeAdapter.getTagName(t.element)===e));return t&&t.type===Ks.Element?t:null}getElementEntry(e){return this.entries.find((t=>t.type===Ks.Element&&t.element===e))}}function ti(e){return{nodeName:"#text",value:e,parentNode:null}}const ni={createDocument:()=>({nodeName:"#document",mode:ms.NO_QUIRKS,childNodes:[]}),createDocumentFragment:()=>({nodeName:"#document-fragment",childNodes:[]}),createElement:(e,t,n)=>({nodeName:e,tagName:e,attrs:n,namespaceURI:t,childNodes:[],parentNode:null}),createCommentNode:e=>({nodeName:"#comment",data:e,parentNode:null}),appendChild(e,t){e.childNodes.push(t),t.parentNode=e},insertBefore(e,t,n){const r=e.childNodes.indexOf(n);e.childNodes.splice(r,0,t),t.parentNode=e},setTemplateContent(e,t){e.content=t},getTemplateContent:e=>e.content,setDocumentType(e,t,n,r){const s=e.childNodes.find((e=>"#documentType"===e.nodeName));if(s)s.name=t,s.publicId=n,s.systemId=r;else{const s={nodeName:"#documentType",name:t,publicId:n,systemId:r,parentNode:null};ni.appendChild(e,s)}},setDocumentMode(e,t){e.mode=t},getDocumentMode:e=>e.mode,detachNode(e){if(e.parentNode){const t=e.parentNode.childNodes.indexOf(e);e.parentNode.childNodes.splice(t,1),e.parentNode=null}},insertText(e,t){if(e.childNodes.length>0){const n=e.childNodes[e.childNodes.length-1];if(ni.isTextNode(n))return void(n.value+=t)}ni.appendChild(e,ti(t))},insertTextBefore(e,t,n){const r=e.childNodes[e.childNodes.indexOf(n)-1];r&&ni.isTextNode(r)?r.value+=t:ni.insertBefore(e,ti(t),n)},adoptAttributes(e,t){const n=new Set(e.attrs.map((e=>e.name)));for(let r=0;r<t.length;r++)n.has(t[r].name)||e.attrs.push(t[r])},getFirstChild:e=>e.childNodes[0],getChildNodes:e=>e.childNodes,getParentNode:e=>e.parentNode,getAttrList:e=>e.attrs,getTagName:e=>e.tagName,getNamespaceURI:e=>e.namespaceURI,getTextNodeContent:e=>e.value,getCommentNodeContent:e=>e.data,getDocumentTypeNodeName:e=>e.name,getDocumentTypeNodePublicId:e=>e.publicId,getDocumentTypeNodeSystemId:e=>e.systemId,isTextNode:e=>"#text"===e.nodeName,isCommentNode:e=>"#comment"===e.nodeName,isDocumentTypeNode:e=>"#documentType"===e.nodeName,isElementNode:e=>Object.prototype.hasOwnProperty.call(e,"tagName"),setNodeSourceCodeLocation(e,t){e.sourceCodeLocation=t},getNodeSourceCodeLocation:e=>e.sourceCodeLocation,updateNodeSourceCodeLocation(e,t){e.sourceCodeLocation={...e.sourceCodeLocation,...t}}},ri="html",si="about:legacy-compat",ii="http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd",ai=["+//silmaril//dtd html pro v0r11 19970101//","-//as//dtd html 3.0 aswedit + extensions//","-//advasoft ltd//dtd html 3.0 aswedit + extensions//","-//ietf//dtd html 2.0 level 1//","-//ietf//dtd html 2.0 level 2//","-//ietf//dtd html 2.0 strict level 1//","-//ietf//dtd html 2.0 strict level 2//","-//ietf//dtd html 2.0 strict//","-//ietf//dtd html 2.0//","-//ietf//dtd html 2.1e//","-//ietf//dtd html 3.0//","-//ietf//dtd html 3.2 final//","-//ietf//dtd html 3.2//","-//ietf//dtd html 3//","-//ietf//dtd html level 0//","-//ietf//dtd html level 1//","-//ietf//dtd html level 2//","-//ietf//dtd html level 3//","-//ietf//dtd html strict level 0//","-//ietf//dtd html strict level 1//","-//ietf//dtd html strict level 2//","-//ietf//dtd html strict level 3//","-//ietf//dtd html strict//","-//ietf//dtd html//","-//metrius//dtd metrius presentational//","-//microsoft//dtd internet explorer 2.0 html strict//","-//microsoft//dtd internet explorer 2.0 html//","-//microsoft//dtd internet explorer 2.0 tables//","-//microsoft//dtd internet explorer 3.0 html strict//","-//microsoft//dtd internet explorer 3.0 html//","-//microsoft//dtd internet explorer 3.0 tables//","-//netscape comm. corp.//dtd html//","-//netscape comm. corp.//dtd strict html//","-//o'reilly and associates//dtd html 2.0//","-//o'reilly and associates//dtd html extended 1.0//","-//o'reilly and associates//dtd html extended relaxed 1.0//","-//sq//dtd html 2.0 hotmetal + extensions//","-//softquad software//dtd hotmetal pro 6.0::19990601::extensions to html 4.0//","-//softquad//dtd hotmetal pro 4.0::19971010::extensions to html 4.0//","-//spyglass//dtd html 2.0 extended//","-//sun microsystems corp.//dtd hotjava html//","-//sun microsystems corp.//dtd hotjava strict html//","-//w3c//dtd html 3 1995-03-24//","-//w3c//dtd html 3.2 draft//","-//w3c//dtd html 3.2 final//","-//w3c//dtd html 3.2//","-//w3c//dtd html 3.2s draft//","-//w3c//dtd html 4.0 frameset//","-//w3c//dtd html 4.0 transitional//","-//w3c//dtd html experimental 19960712//","-//w3c//dtd html experimental 970421//","-//w3c//dtd w3 html//","-//w3o//dtd w3 html 3.0//","-//webtechs//dtd mozilla html 2.0//","-//webtechs//dtd mozilla html//"],oi=[...ai,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"],ci=new Set(["-//w3o//dtd w3 html strict 3.0//en//","-/w3c/dtd html 4.0 transitional/en","html"]),li=["-//w3c//dtd xhtml 1.0 frameset//","-//w3c//dtd xhtml 1.0 transitional//"],ui=[...li,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"];function hi(e,t){return t.some((t=>e.startsWith(t)))}const pi={TEXT_HTML:"text/html",APPLICATION_XML:"application/xhtml+xml"},di="definitionurl",fi="definitionURL",Ei=new Map(["attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map((e=>[e.toLowerCase(),e]))),mi=new Map([["xlink:actuate",{prefix:"xlink",name:"actuate",namespace:ps.XLINK}],["xlink:arcrole",{prefix:"xlink",name:"arcrole",namespace:ps.XLINK}],["xlink:href",{prefix:"xlink",name:"href",namespace:ps.XLINK}],["xlink:role",{prefix:"xlink",name:"role",namespace:ps.XLINK}],["xlink:show",{prefix:"xlink",name:"show",namespace:ps.XLINK}],["xlink:title",{prefix:"xlink",name:"title",namespace:ps.XLINK}],["xlink:type",{prefix:"xlink",name:"type",namespace:ps.XLINK}],["xml:base",{prefix:"xml",name:"base",namespace:ps.XML}],["xml:lang",{prefix:"xml",name:"lang",namespace:ps.XML}],["xml:space",{prefix:"xml",name:"space",namespace:ps.XML}],["xmlns",{prefix:"",name:"xmlns",namespace:ps.XMLNS}],["xmlns:xlink",{prefix:"xmlns",name:"xlink",namespace:ps.XMLNS}]]),Ti=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map((e=>[e.toLowerCase(),e]))),_i=new Set([gs.B,gs.BIG,gs.BLOCKQUOTE,gs.BODY,gs.BR,gs.CENTER,gs.CODE,gs.DD,gs.DIV,gs.DL,gs.DT,gs.EM,gs.EMBED,gs.H1,gs.H2,gs.H3,gs.H4,gs.H5,gs.H6,gs.HEAD,gs.HR,gs.I,gs.IMG,gs.LI,gs.LISTING,gs.MENU,gs.META,gs.NOBR,gs.OL,gs.P,gs.PRE,gs.RUBY,gs.S,gs.SMALL,gs.SPAN,gs.STRONG,gs.STRIKE,gs.SUB,gs.SUP,gs.TABLE,gs.TT,gs.U,gs.UL,gs.VAR]);function Ai(e){for(let t=0;t<e.attrs.length;t++)if(e.attrs[t].name===di){e.attrs[t].name=fi;break}}function gi(e){for(let t=0;t<e.attrs.length;t++){const n=Ei.get(e.attrs[t].name);null!=n&&(e.attrs[t].name=n)}}function Ci(e){for(let t=0;t<e.attrs.length;t++){const n=mi.get(e.attrs[t].name);n&&(e.attrs[t].prefix=n.prefix,e.attrs[t].name=n.name,e.attrs[t].namespace=n.namespace)}}function Ii(e,t,n,r){return(!r||r===ps.HTML)&&function(e,t,n){if(t===ps.MATHML&&e===gs.ANNOTATION_XML)for(let e=0;e<n.length;e++)if(n[e].name===fs.ENCODING){const t=n[e].value.toLowerCase();return t===pi.TEXT_HTML||t===pi.APPLICATION_XML}return t===ps.SVG&&(e===gs.FOREIGN_OBJECT||e===gs.DESC||e===gs.TITLE)}(e,t,n)||(!r||r===ps.MATHML)&&function(e,t){return t===ps.MATHML&&(e===gs.MI||e===gs.MO||e===gs.MN||e===gs.MS||e===gs.MTEXT)}(e,t)}const Ni="hidden",Di=8,Si=3;var ki,bi;(bi=ki||(ki={}))[bi.INITIAL=0]="INITIAL",bi[bi.BEFORE_HTML=1]="BEFORE_HTML",bi[bi.BEFORE_HEAD=2]="BEFORE_HEAD",bi[bi.IN_HEAD=3]="IN_HEAD",bi[bi.IN_HEAD_NO_SCRIPT=4]="IN_HEAD_NO_SCRIPT",bi[bi.AFTER_HEAD=5]="AFTER_HEAD",bi[bi.IN_BODY=6]="IN_BODY",bi[bi.TEXT=7]="TEXT",bi[bi.IN_TABLE=8]="IN_TABLE",bi[bi.IN_TABLE_TEXT=9]="IN_TABLE_TEXT",bi[bi.IN_CAPTION=10]="IN_CAPTION",bi[bi.IN_COLUMN_GROUP=11]="IN_COLUMN_GROUP",bi[bi.IN_TABLE_BODY=12]="IN_TABLE_BODY",bi[bi.IN_ROW=13]="IN_ROW",bi[bi.IN_CELL=14]="IN_CELL",bi[bi.IN_SELECT=15]="IN_SELECT",bi[bi.IN_SELECT_IN_TABLE=16]="IN_SELECT_IN_TABLE",bi[bi.IN_TEMPLATE=17]="IN_TEMPLATE",bi[bi.AFTER_BODY=18]="AFTER_BODY",bi[bi.IN_FRAMESET=19]="IN_FRAMESET",bi[bi.AFTER_FRAMESET=20]="AFTER_FRAMESET",bi[bi.AFTER_AFTER_BODY=21]="AFTER_AFTER_BODY",bi[bi.AFTER_AFTER_FRAMESET=22]="AFTER_AFTER_FRAMESET";const Oi={startLine:-1,startCol:-1,startOffset:-1,endLine:-1,endCol:-1,endOffset:-1},Ri=new Set([gs.TABLE,gs.TBODY,gs.TFOOT,gs.THEAD,gs.TR]),Li={scriptingEnabled:!0,sourceCodeLocationInfo:!1,treeAdapter:ni,onParseError:null};let yi=class{constructor(e,t,n=null,r=null){this.fragmentContext=n,this.scriptHandler=r,this.currentToken=null,this.stopped=!1,this.insertionMode=ki.INITIAL,this.originalInsertionMode=ki.INITIAL,this.headElement=null,this.formElement=null,this.currentNotInHTML=!1,this.tmplInsertionModeStack=[],this.pendingCharacterTokens=[],this.hasNonWhitespacePendingCharacterToken=!1,this.framesetOk=!0,this.skipNextNewLine=!1,this.fosterParentingEnabled=!1,this.options={...Li,...e},this.treeAdapter=this.options.treeAdapter,this.onParseError=this.options.onParseError,this.onParseError&&(this.options.sourceCodeLocationInfo=!0),this.document=null!=t?t:this.treeAdapter.createDocument(),this.tokenizer=new class{constructor(e,t){this.options=e,this.handler=t,this.paused=!1,this.inLoop=!1,this.inForeignNode=!1,this.lastStartTagName="",this.active=!1,this.state=Ls.DATA,this.returnState=Ls.DATA,this.charRefCode=-1,this.consumedAfterSnapshot=-1,this.currentCharacterToken=null,this.currentToken=null,this.currentAttr={name:"",value:""},this.preprocessor=new ls(t),this.currentLocation=this.getCurrentLocation(-1)}_err(e){var t,n;null===(n=(t=this.handler).onParseError)||void 0===n||n.call(t,this.preprocessor.getError(e))}getCurrentLocation(e){return this.options.sourceCodeLocationInfo?{startLine:this.preprocessor.line,startCol:this.preprocessor.col-e,startOffset:this.preprocessor.offset-e,endLine:-1,endCol:-1,endOffset:-1}:null}_runParsingLoop(){if(!this.inLoop){for(this.inLoop=!0;this.active&&!this.paused;){this.consumedAfterSnapshot=0;const e=this._consume();this._ensureHibernation()||this._callState(e)}this.inLoop=!1}}pause(){this.paused=!0}resume(e){if(!this.paused)throw new Error("Parser was already resumed");this.paused=!1,this.inLoop||(this._runParsingLoop(),this.paused||null==e||e())}write(e,t,n){this.active=!0,this.preprocessor.write(e,t),this._runParsingLoop(),this.paused||null==n||n()}insertHtmlAtCurrentPos(e){this.active=!0,this.preprocessor.insertHtmlAtCurrentPos(e),this._runParsingLoop()}_ensureHibernation(){return!!this.preprocessor.endOfChunkHit&&(this._unconsume(this.consumedAfterSnapshot),this.active=!1,!0)}_consume(){return this.consumedAfterSnapshot++,this.preprocessor.advance()}_unconsume(e){this.consumedAfterSnapshot-=e,this.preprocessor.retreat(e)}_reconsumeInState(e,t){this.state=e,this._callState(t)}_advanceBy(e){this.consumedAfterSnapshot+=e;for(let t=0;t<e;t++)this.preprocessor.advance()}_consumeSequenceIfMatch(e,t){return!!this.preprocessor.startsWith(e,t)&&(this._advanceBy(e.length-1),!0)}_createStartTagToken(){this.currentToken={type:us.START_TAG,tagName:"",tagID:gs.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(1)}}_createEndTagToken(){this.currentToken={type:us.END_TAG,tagName:"",tagID:gs.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(2)}}_createCommentToken(e){this.currentToken={type:us.COMMENT,data:"",location:this.getCurrentLocation(e)}}_createDoctypeToken(e){this.currentToken={type:us.DOCTYPE,name:e,forceQuirks:!1,publicId:null,systemId:null,location:this.currentLocation}}_createCharacterToken(e,t){this.currentCharacterToken={type:e,chars:t,location:this.currentLocation}}_createAttr(e){this.currentAttr={name:e,value:""},this.currentLocation=this.getCurrentLocation(0)}_leaveAttrName(){var e,t;const n=this.currentToken;null===Is(n,this.currentAttr.name)?(n.attrs.push(this.currentAttr),n.location&&this.currentLocation&&((null!==(e=(t=n.location).attrs)&&void 0!==e?e:t.attrs=Object.create(null))[this.currentAttr.name]=this.currentLocation,this._leaveAttrValue())):this._err(os.duplicateAttribute)}_leaveAttrValue(){this.currentLocation&&(this.currentLocation.endLine=this.preprocessor.line,this.currentLocation.endCol=this.preprocessor.col,this.currentLocation.endOffset=this.preprocessor.offset)}prepareToken(e){this._emitCurrentCharacterToken(e.location),this.currentToken=null,e.location&&(e.location.endLine=this.preprocessor.line,e.location.endCol=this.preprocessor.col+1,e.location.endOffset=this.preprocessor.offset+1),this.currentLocation=this.getCurrentLocation(-1)}emitCurrentTagToken(){const e=this.currentToken;this.prepareToken(e),e.tagID=Ds(e.tagName),e.type===us.START_TAG?(this.lastStartTagName=e.tagName,this.handler.onStartTag(e)):(e.attrs.length>0&&this._err(os.endTagWithAttributes),e.selfClosing&&this._err(os.endTagWithTrailingSolidus),this.handler.onEndTag(e)),this.preprocessor.dropParsedChunk()}emitCurrentComment(e){this.prepareToken(e),this.handler.onComment(e),this.preprocessor.dropParsedChunk()}emitCurrentDoctype(e){this.prepareToken(e),this.handler.onDoctype(e),this.preprocessor.dropParsedChunk()}_emitCurrentCharacterToken(e){if(this.currentCharacterToken){switch(e&&this.currentCharacterToken.location&&(this.currentCharacterToken.location.endLine=e.startLine,this.currentCharacterToken.location.endCol=e.startCol,this.currentCharacterToken.location.endOffset=e.startOffset),this.currentCharacterToken.type){case us.CHARACTER:this.handler.onCharacter(this.currentCharacterToken);break;case us.NULL_CHARACTER:this.handler.onNullCharacter(this.currentCharacterToken);break;case us.WHITESPACE_CHARACTER:this.handler.onWhitespaceCharacter(this.currentCharacterToken)}this.currentCharacterToken=null}}_emitEOFToken(){const e=this.getCurrentLocation(0);e&&(e.endLine=e.startLine,e.endCol=e.startCol,e.endOffset=e.startOffset),this._emitCurrentCharacterToken(e),this.handler.onEof({type:us.EOF,location:e}),this.active=!1}_appendCharToCurrentCharacterToken(e,t){if(this.currentCharacterToken){if(this.currentCharacterToken.type===e)return void(this.currentCharacterToken.chars+=t);this.currentLocation=this.getCurrentLocation(0),this._emitCurrentCharacterToken(this.currentLocation),this.preprocessor.dropParsedChunk()}this._createCharacterToken(e,t)}_emitCodePoint(e){const t=Hs(e)?us.WHITESPACE_CHARACTER:e===Xr.NULL?us.NULL_CHARACTER:us.CHARACTER;this._appendCharToCurrentCharacterToken(t,String.fromCodePoint(e))}_emitChars(e){this._appendCharToCurrentCharacterToken(us.CHARACTER,e)}_matchNamedCharacterReference(e){let t=null,n=0,r=!1;for(let i=0,a=le[0];i>=0&&(i=be(le,a,i+1,e),!(i<0));e=this._consume()){n+=1,a=le[i];const o=a&Te.VALUE_LENGTH;if(o){const a=(o>>14)-1;if(e!==Xr.SEMICOLON&&this._isCharacterReferenceInAttribute()&&((s=this.preprocessor.peek(1))===Xr.EQUALS_SIGN||vs(s))?(t=[Xr.AMPERSAND],i+=a):(t=0===a?[le[i]&~Te.VALUE_LENGTH]:1===a?[le[++i]]:[le[++i],le[++i]],n=0,r=e!==Xr.SEMICOLON),0===a){this._consume();break}}}var s;return this._unconsume(n),r&&!this.preprocessor.endOfChunkHit&&this._err(os.missingSemicolonAfterCharacterReference),this._unconsume(1),t}_isCharacterReferenceInAttribute(){return this.returnState===Ls.ATTRIBUTE_VALUE_DOUBLE_QUOTED||this.returnState===Ls.ATTRIBUTE_VALUE_SINGLE_QUOTED||this.returnState===Ls.ATTRIBUTE_VALUE_UNQUOTED}_flushCodePointConsumedAsCharacterReference(e){this._isCharacterReferenceInAttribute()?this.currentAttr.value+=String.fromCodePoint(e):this._emitCodePoint(e)}_callState(e){switch(this.state){case Ls.DATA:this._stateData(e);break;case Ls.RCDATA:this._stateRcdata(e);break;case Ls.RAWTEXT:this._stateRawtext(e);break;case Ls.SCRIPT_DATA:this._stateScriptData(e);break;case Ls.PLAINTEXT:this._statePlaintext(e);break;case Ls.TAG_OPEN:this._stateTagOpen(e);break;case Ls.END_TAG_OPEN:this._stateEndTagOpen(e);break;case Ls.TAG_NAME:this._stateTagName(e);break;case Ls.RCDATA_LESS_THAN_SIGN:this._stateRcdataLessThanSign(e);break;case Ls.RCDATA_END_TAG_OPEN:this._stateRcdataEndTagOpen(e);break;case Ls.RCDATA_END_TAG_NAME:this._stateRcdataEndTagName(e);break;case Ls.RAWTEXT_LESS_THAN_SIGN:this._stateRawtextLessThanSign(e);break;case Ls.RAWTEXT_END_TAG_OPEN:this._stateRawtextEndTagOpen(e);break;case Ls.RAWTEXT_END_TAG_NAME:this._stateRawtextEndTagName(e);break;case Ls.SCRIPT_DATA_LESS_THAN_SIGN:this._stateScriptDataLessThanSign(e);break;case Ls.SCRIPT_DATA_END_TAG_OPEN:this._stateScriptDataEndTagOpen(e);break;case Ls.SCRIPT_DATA_END_TAG_NAME:this._stateScriptDataEndTagName(e);break;case Ls.SCRIPT_DATA_ESCAPE_START:this._stateScriptDataEscapeStart(e);break;case Ls.SCRIPT_DATA_ESCAPE_START_DASH:this._stateScriptDataEscapeStartDash(e);break;case Ls.SCRIPT_DATA_ESCAPED:this._stateScriptDataEscaped(e);break;case Ls.SCRIPT_DATA_ESCAPED_DASH:this._stateScriptDataEscapedDash(e);break;case Ls.SCRIPT_DATA_ESCAPED_DASH_DASH:this._stateScriptDataEscapedDashDash(e);break;case Ls.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataEscapedLessThanSign(e);break;case Ls.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:this._stateScriptDataEscapedEndTagOpen(e);break;case Ls.SCRIPT_DATA_ESCAPED_END_TAG_NAME:this._stateScriptDataEscapedEndTagName(e);break;case Ls.SCRIPT_DATA_DOUBLE_ESCAPE_START:this._stateScriptDataDoubleEscapeStart(e);break;case Ls.SCRIPT_DATA_DOUBLE_ESCAPED:this._stateScriptDataDoubleEscaped(e);break;case Ls.SCRIPT_DATA_DOUBLE_ESCAPED_DASH:this._stateScriptDataDoubleEscapedDash(e);break;case Ls.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH:this._stateScriptDataDoubleEscapedDashDash(e);break;case Ls.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataDoubleEscapedLessThanSign(e);break;case Ls.SCRIPT_DATA_DOUBLE_ESCAPE_END:this._stateScriptDataDoubleEscapeEnd(e);break;case Ls.BEFORE_ATTRIBUTE_NAME:this._stateBeforeAttributeName(e);break;case Ls.ATTRIBUTE_NAME:this._stateAttributeName(e);break;case Ls.AFTER_ATTRIBUTE_NAME:this._stateAfterAttributeName(e);break;case Ls.BEFORE_ATTRIBUTE_VALUE:this._stateBeforeAttributeValue(e);break;case Ls.ATTRIBUTE_VALUE_DOUBLE_QUOTED:this._stateAttributeValueDoubleQuoted(e);break;case Ls.ATTRIBUTE_VALUE_SINGLE_QUOTED:this._stateAttributeValueSingleQuoted(e);break;case Ls.ATTRIBUTE_VALUE_UNQUOTED:this._stateAttributeValueUnquoted(e);break;case Ls.AFTER_ATTRIBUTE_VALUE_QUOTED:this._stateAfterAttributeValueQuoted(e);break;case Ls.SELF_CLOSING_START_TAG:this._stateSelfClosingStartTag(e);break;case Ls.BOGUS_COMMENT:this._stateBogusComment(e);break;case Ls.MARKUP_DECLARATION_OPEN:this._stateMarkupDeclarationOpen(e);break;case Ls.COMMENT_START:this._stateCommentStart(e);break;case Ls.COMMENT_START_DASH:this._stateCommentStartDash(e);break;case Ls.COMMENT:this._stateComment(e);break;case Ls.COMMENT_LESS_THAN_SIGN:this._stateCommentLessThanSign(e);break;case Ls.COMMENT_LESS_THAN_SIGN_BANG:this._stateCommentLessThanSignBang(e);break;case Ls.COMMENT_LESS_THAN_SIGN_BANG_DASH:this._stateCommentLessThanSignBangDash(e);break;case Ls.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:this._stateCommentLessThanSignBangDashDash(e);break;case Ls.COMMENT_END_DASH:this._stateCommentEndDash(e);break;case Ls.COMMENT_END:this._stateCommentEnd(e);break;case Ls.COMMENT_END_BANG:this._stateCommentEndBang(e);break;case Ls.DOCTYPE:this._stateDoctype(e);break;case Ls.BEFORE_DOCTYPE_NAME:this._stateBeforeDoctypeName(e);break;case Ls.DOCTYPE_NAME:this._stateDoctypeName(e);break;case Ls.AFTER_DOCTYPE_NAME:this._stateAfterDoctypeName(e);break;case Ls.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._stateAfterDoctypePublicKeyword(e);break;case Ls.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER:this._stateBeforeDoctypePublicIdentifier(e);break;case Ls.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypePublicIdentifierDoubleQuoted(e);break;case Ls.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypePublicIdentifierSingleQuoted(e);break;case Ls.AFTER_DOCTYPE_PUBLIC_IDENTIFIER:this._stateAfterDoctypePublicIdentifier(e);break;case Ls.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS:this._stateBetweenDoctypePublicAndSystemIdentifiers(e);break;case Ls.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._stateAfterDoctypeSystemKeyword(e);break;case Ls.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER:this._stateBeforeDoctypeSystemIdentifier(e);break;case Ls.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypeSystemIdentifierDoubleQuoted(e);break;case Ls.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypeSystemIdentifierSingleQuoted(e);break;case Ls.AFTER_DOCTYPE_SYSTEM_IDENTIFIER:this._stateAfterDoctypeSystemIdentifier(e);break;case Ls.BOGUS_DOCTYPE:this._stateBogusDoctype(e);break;case Ls.CDATA_SECTION:this._stateCdataSection(e);break;case Ls.CDATA_SECTION_BRACKET:this._stateCdataSectionBracket(e);break;case Ls.CDATA_SECTION_END:this._stateCdataSectionEnd(e);break;case Ls.CHARACTER_REFERENCE:this._stateCharacterReference(e);break;case Ls.NAMED_CHARACTER_REFERENCE:this._stateNamedCharacterReference(e);break;case Ls.AMBIGUOUS_AMPERSAND:this._stateAmbiguousAmpersand(e);break;case Ls.NUMERIC_CHARACTER_REFERENCE:this._stateNumericCharacterReference(e);break;case Ls.HEXADEMICAL_CHARACTER_REFERENCE_START:this._stateHexademicalCharacterReferenceStart(e);break;case Ls.HEXADEMICAL_CHARACTER_REFERENCE:this._stateHexademicalCharacterReference(e);break;case Ls.DECIMAL_CHARACTER_REFERENCE:this._stateDecimalCharacterReference(e);break;case Ls.NUMERIC_CHARACTER_REFERENCE_END:this._stateNumericCharacterReferenceEnd(e);break;default:throw new Error("Unknown state")}}_stateData(e){switch(e){case Xr.LESS_THAN_SIGN:this.state=Ls.TAG_OPEN;break;case Xr.AMPERSAND:this.returnState=Ls.DATA,this.state=Ls.CHARACTER_REFERENCE;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this._emitCodePoint(e);break;case Xr.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRcdata(e){switch(e){case Xr.AMPERSAND:this.returnState=Ls.RCDATA,this.state=Ls.CHARACTER_REFERENCE;break;case Xr.LESS_THAN_SIGN:this.state=Ls.RCDATA_LESS_THAN_SIGN;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this._emitChars(Wr);break;case Xr.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRawtext(e){switch(e){case Xr.LESS_THAN_SIGN:this.state=Ls.RAWTEXT_LESS_THAN_SIGN;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this._emitChars(Wr);break;case Xr.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptData(e){switch(e){case Xr.LESS_THAN_SIGN:this.state=Ls.SCRIPT_DATA_LESS_THAN_SIGN;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this._emitChars(Wr);break;case Xr.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_statePlaintext(e){switch(e){case Xr.NULL:this._err(os.unexpectedNullCharacter),this._emitChars(Wr);break;case Xr.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateTagOpen(e){if(Ps(e))this._createStartTagToken(),this.state=Ls.TAG_NAME,this._stateTagName(e);else switch(e){case Xr.EXCLAMATION_MARK:this.state=Ls.MARKUP_DECLARATION_OPEN;break;case Xr.SOLIDUS:this.state=Ls.END_TAG_OPEN;break;case Xr.QUESTION_MARK:this._err(os.unexpectedQuestionMarkInsteadOfTagName),this._createCommentToken(1),this.state=Ls.BOGUS_COMMENT,this._stateBogusComment(e);break;case Xr.EOF:this._err(os.eofBeforeTagName),this._emitChars("<"),this._emitEOFToken();break;default:this._err(os.invalidFirstCharacterOfTagName),this._emitChars("<"),this.state=Ls.DATA,this._stateData(e)}}_stateEndTagOpen(e){if(Ps(e))this._createEndTagToken(),this.state=Ls.TAG_NAME,this._stateTagName(e);else switch(e){case Xr.GREATER_THAN_SIGN:this._err(os.missingEndTagName),this.state=Ls.DATA;break;case Xr.EOF:this._err(os.eofBeforeTagName),this._emitChars("</"),this._emitEOFToken();break;default:this._err(os.invalidFirstCharacterOfTagName),this._createCommentToken(2),this.state=Ls.BOGUS_COMMENT,this._stateBogusComment(e)}}_stateTagName(e){const t=this.currentToken;switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:this.state=Ls.BEFORE_ATTRIBUTE_NAME;break;case Xr.SOLIDUS:this.state=Ls.SELF_CLOSING_START_TAG;break;case Xr.GREATER_THAN_SIGN:this.state=Ls.DATA,this.emitCurrentTagToken();break;case Xr.NULL:this._err(os.unexpectedNullCharacter),t.tagName+=Wr;break;case Xr.EOF:this._err(os.eofInTag),this._emitEOFToken();break;default:t.tagName+=String.fromCodePoint(xs(e)?Us(e):e)}}_stateRcdataLessThanSign(e){e===Xr.SOLIDUS?this.state=Ls.RCDATA_END_TAG_OPEN:(this._emitChars("<"),this.state=Ls.RCDATA,this._stateRcdata(e))}_stateRcdataEndTagOpen(e){Ps(e)?(this.state=Ls.RCDATA_END_TAG_NAME,this._stateRcdataEndTagName(e)):(this._emitChars("</"),this.state=Ls.RCDATA,this._stateRcdata(e))}handleSpecialEndTag(e){if(!this.preprocessor.startsWith(this.lastStartTagName,!1))return!this._ensureHibernation();switch(this._createEndTagToken(),this.currentToken.tagName=this.lastStartTagName,this.preprocessor.peek(this.lastStartTagName.length)){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:return this._advanceBy(this.lastStartTagName.length),this.state=Ls.BEFORE_ATTRIBUTE_NAME,!1;case Xr.SOLIDUS:return this._advanceBy(this.lastStartTagName.length),this.state=Ls.SELF_CLOSING_START_TAG,!1;case Xr.GREATER_THAN_SIGN:return this._advanceBy(this.lastStartTagName.length),this.emitCurrentTagToken(),this.state=Ls.DATA,!1;default:return!this._ensureHibernation()}}_stateRcdataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Ls.RCDATA,this._stateRcdata(e))}_stateRawtextLessThanSign(e){e===Xr.SOLIDUS?this.state=Ls.RAWTEXT_END_TAG_OPEN:(this._emitChars("<"),this.state=Ls.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagOpen(e){Ps(e)?(this.state=Ls.RAWTEXT_END_TAG_NAME,this._stateRawtextEndTagName(e)):(this._emitChars("</"),this.state=Ls.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Ls.RAWTEXT,this._stateRawtext(e))}_stateScriptDataLessThanSign(e){switch(e){case Xr.SOLIDUS:this.state=Ls.SCRIPT_DATA_END_TAG_OPEN;break;case Xr.EXCLAMATION_MARK:this.state=Ls.SCRIPT_DATA_ESCAPE_START,this._emitChars("<!");break;default:this._emitChars("<"),this.state=Ls.SCRIPT_DATA,this._stateScriptData(e)}}_stateScriptDataEndTagOpen(e){Ps(e)?(this.state=Ls.SCRIPT_DATA_END_TAG_NAME,this._stateScriptDataEndTagName(e)):(this._emitChars("</"),this.state=Ls.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Ls.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStart(e){e===Xr.HYPHEN_MINUS?(this.state=Ls.SCRIPT_DATA_ESCAPE_START_DASH,this._emitChars("-")):(this.state=Ls.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStartDash(e){e===Xr.HYPHEN_MINUS?(this.state=Ls.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-")):(this.state=Ls.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscaped(e){switch(e){case Xr.HYPHEN_MINUS:this.state=Ls.SCRIPT_DATA_ESCAPED_DASH,this._emitChars("-");break;case Xr.LESS_THAN_SIGN:this.state=Ls.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this._emitChars(Wr);break;case Xr.EOF:this._err(os.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataEscapedDash(e){switch(e){case Xr.HYPHEN_MINUS:this.state=Ls.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-");break;case Xr.LESS_THAN_SIGN:this.state=Ls.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this.state=Ls.SCRIPT_DATA_ESCAPED,this._emitChars(Wr);break;case Xr.EOF:this._err(os.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Ls.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedDashDash(e){switch(e){case Xr.HYPHEN_MINUS:this._emitChars("-");break;case Xr.LESS_THAN_SIGN:this.state=Ls.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case Xr.GREATER_THAN_SIGN:this.state=Ls.SCRIPT_DATA,this._emitChars(">");break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this.state=Ls.SCRIPT_DATA_ESCAPED,this._emitChars(Wr);break;case Xr.EOF:this._err(os.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Ls.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedLessThanSign(e){e===Xr.SOLIDUS?this.state=Ls.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:Ps(e)?(this._emitChars("<"),this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPE_START,this._stateScriptDataDoubleEscapeStart(e)):(this._emitChars("<"),this.state=Ls.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagOpen(e){Ps(e)?(this.state=Ls.SCRIPT_DATA_ESCAPED_END_TAG_NAME,this._stateScriptDataEscapedEndTagName(e)):(this._emitChars("</"),this.state=Ls.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Ls.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscapeStart(e){if(this.preprocessor.startsWith(ts,!1)&&Gs(this.preprocessor.peek(ts.length))){this._emitCodePoint(e);for(let e=0;e<ts.length;e++)this._emitCodePoint(this._consume());this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED}else this._ensureHibernation()||(this.state=Ls.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscaped(e){switch(e){case Xr.HYPHEN_MINUS:this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED_DASH,this._emitChars("-");break;case Xr.LESS_THAN_SIGN:this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this._emitChars(Wr);break;case Xr.EOF:this._err(os.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDash(e){switch(e){case Xr.HYPHEN_MINUS:this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH,this._emitChars("-");break;case Xr.LESS_THAN_SIGN:this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars(Wr);break;case Xr.EOF:this._err(os.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDashDash(e){switch(e){case Xr.HYPHEN_MINUS:this._emitChars("-");break;case Xr.LESS_THAN_SIGN:this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case Xr.GREATER_THAN_SIGN:this.state=Ls.SCRIPT_DATA,this._emitChars(">");break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars(Wr);break;case Xr.EOF:this._err(os.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedLessThanSign(e){e===Xr.SOLIDUS?(this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPE_END,this._emitChars("/")):(this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateScriptDataDoubleEscapeEnd(e){if(this.preprocessor.startsWith(ts,!1)&&Gs(this.preprocessor.peek(ts.length))){this._emitCodePoint(e);for(let e=0;e<ts.length;e++)this._emitCodePoint(this._consume());this.state=Ls.SCRIPT_DATA_ESCAPED}else this._ensureHibernation()||(this.state=Ls.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateBeforeAttributeName(e){switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:break;case Xr.SOLIDUS:case Xr.GREATER_THAN_SIGN:case Xr.EOF:this.state=Ls.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case Xr.EQUALS_SIGN:this._err(os.unexpectedEqualsSignBeforeAttributeName),this._createAttr("="),this.state=Ls.ATTRIBUTE_NAME;break;default:this._createAttr(""),this.state=Ls.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateAttributeName(e){switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:case Xr.SOLIDUS:case Xr.GREATER_THAN_SIGN:case Xr.EOF:this._leaveAttrName(),this.state=Ls.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case Xr.EQUALS_SIGN:this._leaveAttrName(),this.state=Ls.BEFORE_ATTRIBUTE_VALUE;break;case Xr.QUOTATION_MARK:case Xr.APOSTROPHE:case Xr.LESS_THAN_SIGN:this._err(os.unexpectedCharacterInAttributeName),this.currentAttr.name+=String.fromCodePoint(e);break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this.currentAttr.name+=Wr;break;default:this.currentAttr.name+=String.fromCodePoint(xs(e)?Us(e):e)}}_stateAfterAttributeName(e){switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:break;case Xr.SOLIDUS:this.state=Ls.SELF_CLOSING_START_TAG;break;case Xr.EQUALS_SIGN:this.state=Ls.BEFORE_ATTRIBUTE_VALUE;break;case Xr.GREATER_THAN_SIGN:this.state=Ls.DATA,this.emitCurrentTagToken();break;case Xr.EOF:this._err(os.eofInTag),this._emitEOFToken();break;default:this._createAttr(""),this.state=Ls.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateBeforeAttributeValue(e){switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:break;case Xr.QUOTATION_MARK:this.state=Ls.ATTRIBUTE_VALUE_DOUBLE_QUOTED;break;case Xr.APOSTROPHE:this.state=Ls.ATTRIBUTE_VALUE_SINGLE_QUOTED;break;case Xr.GREATER_THAN_SIGN:this._err(os.missingAttributeValue),this.state=Ls.DATA,this.emitCurrentTagToken();break;default:this.state=Ls.ATTRIBUTE_VALUE_UNQUOTED,this._stateAttributeValueUnquoted(e)}}_stateAttributeValueDoubleQuoted(e){switch(e){case Xr.QUOTATION_MARK:this.state=Ls.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case Xr.AMPERSAND:this.returnState=Ls.ATTRIBUTE_VALUE_DOUBLE_QUOTED,this.state=Ls.CHARACTER_REFERENCE;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this.currentAttr.value+=Wr;break;case Xr.EOF:this._err(os.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueSingleQuoted(e){switch(e){case Xr.APOSTROPHE:this.state=Ls.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case Xr.AMPERSAND:this.returnState=Ls.ATTRIBUTE_VALUE_SINGLE_QUOTED,this.state=Ls.CHARACTER_REFERENCE;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this.currentAttr.value+=Wr;break;case Xr.EOF:this._err(os.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueUnquoted(e){switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:this._leaveAttrValue(),this.state=Ls.BEFORE_ATTRIBUTE_NAME;break;case Xr.AMPERSAND:this.returnState=Ls.ATTRIBUTE_VALUE_UNQUOTED,this.state=Ls.CHARACTER_REFERENCE;break;case Xr.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=Ls.DATA,this.emitCurrentTagToken();break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this.currentAttr.value+=Wr;break;case Xr.QUOTATION_MARK:case Xr.APOSTROPHE:case Xr.LESS_THAN_SIGN:case Xr.EQUALS_SIGN:case Xr.GRAVE_ACCENT:this._err(os.unexpectedCharacterInUnquotedAttributeValue),this.currentAttr.value+=String.fromCodePoint(e);break;case Xr.EOF:this._err(os.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAfterAttributeValueQuoted(e){switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:this._leaveAttrValue(),this.state=Ls.BEFORE_ATTRIBUTE_NAME;break;case Xr.SOLIDUS:this._leaveAttrValue(),this.state=Ls.SELF_CLOSING_START_TAG;break;case Xr.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=Ls.DATA,this.emitCurrentTagToken();break;case Xr.EOF:this._err(os.eofInTag),this._emitEOFToken();break;default:this._err(os.missingWhitespaceBetweenAttributes),this.state=Ls.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateSelfClosingStartTag(e){switch(e){case Xr.GREATER_THAN_SIGN:this.currentToken.selfClosing=!0,this.state=Ls.DATA,this.emitCurrentTagToken();break;case Xr.EOF:this._err(os.eofInTag),this._emitEOFToken();break;default:this._err(os.unexpectedSolidusInTag),this.state=Ls.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateBogusComment(e){const t=this.currentToken;switch(e){case Xr.GREATER_THAN_SIGN:this.state=Ls.DATA,this.emitCurrentComment(t);break;case Xr.EOF:this.emitCurrentComment(t),this._emitEOFToken();break;case Xr.NULL:this._err(os.unexpectedNullCharacter),t.data+=Wr;break;default:t.data+=String.fromCodePoint(e)}}_stateMarkupDeclarationOpen(e){this._consumeSequenceIfMatch(Zr,!0)?(this._createCommentToken(Zr.length+1),this.state=Ls.COMMENT_START):this._consumeSequenceIfMatch(es,!1)?(this.currentLocation=this.getCurrentLocation(es.length+1),this.state=Ls.DOCTYPE):this._consumeSequenceIfMatch(Jr,!0)?this.inForeignNode?this.state=Ls.CDATA_SECTION:(this._err(os.cdataInHtmlContent),this._createCommentToken(Jr.length+1),this.currentToken.data="[CDATA[",this.state=Ls.BOGUS_COMMENT):this._ensureHibernation()||(this._err(os.incorrectlyOpenedComment),this._createCommentToken(2),this.state=Ls.BOGUS_COMMENT,this._stateBogusComment(e))}_stateCommentStart(e){switch(e){case Xr.HYPHEN_MINUS:this.state=Ls.COMMENT_START_DASH;break;case Xr.GREATER_THAN_SIGN:{this._err(os.abruptClosingOfEmptyComment),this.state=Ls.DATA;const e=this.currentToken;this.emitCurrentComment(e);break}default:this.state=Ls.COMMENT,this._stateComment(e)}}_stateCommentStartDash(e){const t=this.currentToken;switch(e){case Xr.HYPHEN_MINUS:this.state=Ls.COMMENT_END;break;case Xr.GREATER_THAN_SIGN:this._err(os.abruptClosingOfEmptyComment),this.state=Ls.DATA,this.emitCurrentComment(t);break;case Xr.EOF:this._err(os.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=Ls.COMMENT,this._stateComment(e)}}_stateComment(e){const t=this.currentToken;switch(e){case Xr.HYPHEN_MINUS:this.state=Ls.COMMENT_END_DASH;break;case Xr.LESS_THAN_SIGN:t.data+="<",this.state=Ls.COMMENT_LESS_THAN_SIGN;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),t.data+=Wr;break;case Xr.EOF:this._err(os.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+=String.fromCodePoint(e)}}_stateCommentLessThanSign(e){const t=this.currentToken;switch(e){case Xr.EXCLAMATION_MARK:t.data+="!",this.state=Ls.COMMENT_LESS_THAN_SIGN_BANG;break;case Xr.LESS_THAN_SIGN:t.data+="<";break;default:this.state=Ls.COMMENT,this._stateComment(e)}}_stateCommentLessThanSignBang(e){e===Xr.HYPHEN_MINUS?this.state=Ls.COMMENT_LESS_THAN_SIGN_BANG_DASH:(this.state=Ls.COMMENT,this._stateComment(e))}_stateCommentLessThanSignBangDash(e){e===Xr.HYPHEN_MINUS?this.state=Ls.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:(this.state=Ls.COMMENT_END_DASH,this._stateCommentEndDash(e))}_stateCommentLessThanSignBangDashDash(e){e!==Xr.GREATER_THAN_SIGN&&e!==Xr.EOF&&this._err(os.nestedComment),this.state=Ls.COMMENT_END,this._stateCommentEnd(e)}_stateCommentEndDash(e){const t=this.currentToken;switch(e){case Xr.HYPHEN_MINUS:this.state=Ls.COMMENT_END;break;case Xr.EOF:this._err(os.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=Ls.COMMENT,this._stateComment(e)}}_stateCommentEnd(e){const t=this.currentToken;switch(e){case Xr.GREATER_THAN_SIGN:this.state=Ls.DATA,this.emitCurrentComment(t);break;case Xr.EXCLAMATION_MARK:this.state=Ls.COMMENT_END_BANG;break;case Xr.HYPHEN_MINUS:t.data+="-";break;case Xr.EOF:this._err(os.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--",this.state=Ls.COMMENT,this._stateComment(e)}}_stateCommentEndBang(e){const t=this.currentToken;switch(e){case Xr.HYPHEN_MINUS:t.data+="--!",this.state=Ls.COMMENT_END_DASH;break;case Xr.GREATER_THAN_SIGN:this._err(os.incorrectlyClosedComment),this.state=Ls.DATA,this.emitCurrentComment(t);break;case Xr.EOF:this._err(os.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--!",this.state=Ls.COMMENT,this._stateComment(e)}}_stateDoctype(e){switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:this.state=Ls.BEFORE_DOCTYPE_NAME;break;case Xr.GREATER_THAN_SIGN:this.state=Ls.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e);break;case Xr.EOF:{this._err(os.eofInDoctype),this._createDoctypeToken(null);const e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._err(os.missingWhitespaceBeforeDoctypeName),this.state=Ls.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e)}}_stateBeforeDoctypeName(e){if(xs(e))this._createDoctypeToken(String.fromCharCode(Us(e))),this.state=Ls.DOCTYPE_NAME;else switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:break;case Xr.NULL:this._err(os.unexpectedNullCharacter),this._createDoctypeToken(Wr),this.state=Ls.DOCTYPE_NAME;break;case Xr.GREATER_THAN_SIGN:{this._err(os.missingDoctypeName),this._createDoctypeToken(null);const e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this.state=Ls.DATA;break}case Xr.EOF:{this._err(os.eofInDoctype),this._createDoctypeToken(null);const e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._createDoctypeToken(String.fromCodePoint(e)),this.state=Ls.DOCTYPE_NAME}}_stateDoctypeName(e){const t=this.currentToken;switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:this.state=Ls.AFTER_DOCTYPE_NAME;break;case Xr.GREATER_THAN_SIGN:this.state=Ls.DATA,this.emitCurrentDoctype(t);break;case Xr.NULL:this._err(os.unexpectedNullCharacter),t.name+=Wr;break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.name+=String.fromCodePoint(xs(e)?Us(e):e)}}_stateAfterDoctypeName(e){const t=this.currentToken;switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:break;case Xr.GREATER_THAN_SIGN:this.state=Ls.DATA,this.emitCurrentDoctype(t);break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._consumeSequenceIfMatch(ns,!1)?this.state=Ls.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._consumeSequenceIfMatch(rs,!1)?this.state=Ls.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._ensureHibernation()||(this._err(os.invalidCharacterSequenceAfterDoctypeName),t.forceQuirks=!0,this.state=Ls.BOGUS_DOCTYPE,this._stateBogusDoctype(e))}}_stateAfterDoctypePublicKeyword(e){const t=this.currentToken;switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:this.state=Ls.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER;break;case Xr.QUOTATION_MARK:this._err(os.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=Ls.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case Xr.APOSTROPHE:this._err(os.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=Ls.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case Xr.GREATER_THAN_SIGN:this._err(os.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Ls.DATA,this.emitCurrentDoctype(t);break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(os.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Ls.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypePublicIdentifier(e){const t=this.currentToken;switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:break;case Xr.QUOTATION_MARK:t.publicId="",this.state=Ls.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case Xr.APOSTROPHE:t.publicId="",this.state=Ls.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case Xr.GREATER_THAN_SIGN:this._err(os.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Ls.DATA,this.emitCurrentDoctype(t);break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(os.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Ls.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypePublicIdentifierDoubleQuoted(e){const t=this.currentToken;switch(e){case Xr.QUOTATION_MARK:this.state=Ls.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),t.publicId+=Wr;break;case Xr.GREATER_THAN_SIGN:this._err(os.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Ls.DATA;break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateDoctypePublicIdentifierSingleQuoted(e){const t=this.currentToken;switch(e){case Xr.APOSTROPHE:this.state=Ls.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),t.publicId+=Wr;break;case Xr.GREATER_THAN_SIGN:this._err(os.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Ls.DATA;break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateAfterDoctypePublicIdentifier(e){const t=this.currentToken;switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:this.state=Ls.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS;break;case Xr.GREATER_THAN_SIGN:this.state=Ls.DATA,this.emitCurrentDoctype(t);break;case Xr.QUOTATION_MARK:this._err(os.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=Ls.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case Xr.APOSTROPHE:this._err(os.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=Ls.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(os.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Ls.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBetweenDoctypePublicAndSystemIdentifiers(e){const t=this.currentToken;switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:break;case Xr.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=Ls.DATA;break;case Xr.QUOTATION_MARK:t.systemId="",this.state=Ls.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case Xr.APOSTROPHE:t.systemId="",this.state=Ls.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(os.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Ls.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateAfterDoctypeSystemKeyword(e){const t=this.currentToken;switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:this.state=Ls.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER;break;case Xr.QUOTATION_MARK:this._err(os.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=Ls.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case Xr.APOSTROPHE:this._err(os.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=Ls.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case Xr.GREATER_THAN_SIGN:this._err(os.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Ls.DATA,this.emitCurrentDoctype(t);break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(os.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Ls.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypeSystemIdentifier(e){const t=this.currentToken;switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:break;case Xr.QUOTATION_MARK:t.systemId="",this.state=Ls.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case Xr.APOSTROPHE:t.systemId="",this.state=Ls.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case Xr.GREATER_THAN_SIGN:this._err(os.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Ls.DATA,this.emitCurrentDoctype(t);break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(os.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Ls.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypeSystemIdentifierDoubleQuoted(e){const t=this.currentToken;switch(e){case Xr.QUOTATION_MARK:this.state=Ls.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),t.systemId+=Wr;break;case Xr.GREATER_THAN_SIGN:this._err(os.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Ls.DATA;break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateDoctypeSystemIdentifierSingleQuoted(e){const t=this.currentToken;switch(e){case Xr.APOSTROPHE:this.state=Ls.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case Xr.NULL:this._err(os.unexpectedNullCharacter),t.systemId+=Wr;break;case Xr.GREATER_THAN_SIGN:this._err(os.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Ls.DATA;break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateAfterDoctypeSystemIdentifier(e){const t=this.currentToken;switch(e){case Xr.SPACE:case Xr.LINE_FEED:case Xr.TABULATION:case Xr.FORM_FEED:break;case Xr.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=Ls.DATA;break;case Xr.EOF:this._err(os.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(os.unexpectedCharacterAfterDoctypeSystemIdentifier),this.state=Ls.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBogusDoctype(e){const t=this.currentToken;switch(e){case Xr.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=Ls.DATA;break;case Xr.NULL:this._err(os.unexpectedNullCharacter);break;case Xr.EOF:this.emitCurrentDoctype(t),this._emitEOFToken()}}_stateCdataSection(e){switch(e){case Xr.RIGHT_SQUARE_BRACKET:this.state=Ls.CDATA_SECTION_BRACKET;break;case Xr.EOF:this._err(os.eofInCdata),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateCdataSectionBracket(e){e===Xr.RIGHT_SQUARE_BRACKET?this.state=Ls.CDATA_SECTION_END:(this._emitChars("]"),this.state=Ls.CDATA_SECTION,this._stateCdataSection(e))}_stateCdataSectionEnd(e){switch(e){case Xr.GREATER_THAN_SIGN:this.state=Ls.DATA;break;case Xr.RIGHT_SQUARE_BRACKET:this._emitChars("]");break;default:this._emitChars("]]"),this.state=Ls.CDATA_SECTION,this._stateCdataSection(e)}}_stateCharacterReference(e){e===Xr.NUMBER_SIGN?this.state=Ls.NUMERIC_CHARACTER_REFERENCE:vs(e)?(this.state=Ls.NAMED_CHARACTER_REFERENCE,this._stateNamedCharacterReference(e)):(this._flushCodePointConsumedAsCharacterReference(Xr.AMPERSAND),this._reconsumeInState(this.returnState,e))}_stateNamedCharacterReference(e){const t=this._matchNamedCharacterReference(e);if(this._ensureHibernation());else if(t){for(let e=0;e<t.length;e++)this._flushCodePointConsumedAsCharacterReference(t[e]);this.state=this.returnState}else this._flushCodePointConsumedAsCharacterReference(Xr.AMPERSAND),this.state=Ls.AMBIGUOUS_AMPERSAND}_stateAmbiguousAmpersand(e){vs(e)?this._flushCodePointConsumedAsCharacterReference(e):(e===Xr.SEMICOLON&&this._err(os.unknownNamedCharacterReference),this._reconsumeInState(this.returnState,e))}_stateNumericCharacterReference(e){this.charRefCode=0,e===Xr.LATIN_SMALL_X||e===Xr.LATIN_CAPITAL_X?this.state=Ls.HEXADEMICAL_CHARACTER_REFERENCE_START:Fs(e)?(this.state=Ls.DECIMAL_CHARACTER_REFERENCE,this._stateDecimalCharacterReference(e)):(this._err(os.absenceOfDigitsInNumericCharacterReference),this._flushCodePointConsumedAsCharacterReference(Xr.AMPERSAND),this._flushCodePointConsumedAsCharacterReference(Xr.NUMBER_SIGN),this._reconsumeInState(this.returnState,e))}_stateHexademicalCharacterReferenceStart(e){!function(e){return Fs(e)||Bs(e)||ws(e)}(e)?(this._err(os.absenceOfDigitsInNumericCharacterReference),this._flushCodePointConsumedAsCharacterReference(Xr.AMPERSAND),this._flushCodePointConsumedAsCharacterReference(Xr.NUMBER_SIGN),this._unconsume(2),this.state=this.returnState):(this.state=Ls.HEXADEMICAL_CHARACTER_REFERENCE,this._stateHexademicalCharacterReference(e))}_stateHexademicalCharacterReference(e){Bs(e)?this.charRefCode=16*this.charRefCode+e-55:ws(e)?this.charRefCode=16*this.charRefCode+e-87:Fs(e)?this.charRefCode=16*this.charRefCode+e-48:e===Xr.SEMICOLON?this.state=Ls.NUMERIC_CHARACTER_REFERENCE_END:(this._err(os.missingSemicolonAfterCharacterReference),this.state=Ls.NUMERIC_CHARACTER_REFERENCE_END,this._stateNumericCharacterReferenceEnd(e))}_stateDecimalCharacterReference(e){Fs(e)?this.charRefCode=10*this.charRefCode+e-48:e===Xr.SEMICOLON?this.state=Ls.NUMERIC_CHARACTER_REFERENCE_END:(this._err(os.missingSemicolonAfterCharacterReference),this.state=Ls.NUMERIC_CHARACTER_REFERENCE_END,this._stateNumericCharacterReferenceEnd(e))}_stateNumericCharacterReferenceEnd(e){if(this.charRefCode===Xr.NULL)this._err(os.nullCharacterReference),this.charRefCode=Xr.REPLACEMENT_CHARACTER;else if(this.charRefCode>1114111)this._err(os.characterReferenceOutsideUnicodeRange),this.charRefCode=Xr.REPLACEMENT_CHARACTER;else if(ss(this.charRefCode))this._err(os.surrogateCharacterReference),this.charRefCode=Xr.REPLACEMENT_CHARACTER;else if(as(this.charRefCode))this._err(os.noncharacterCharacterReference);else if(is(this.charRefCode)||this.charRefCode===Xr.CARRIAGE_RETURN){this._err(os.controlCharacterReference);const e=Rs.get(this.charRefCode);void 0!==e&&(this.charRefCode=e)}this._flushCodePointConsumedAsCharacterReference(this.charRefCode),this._reconsumeInState(this.returnState,e)}}(this.options,this),this.activeFormattingElements=new ei(this.treeAdapter),this.fragmentContextID=n?Ds(this.treeAdapter.getTagName(n)):gs.UNKNOWN,this._setContextModes(null!=n?n:this.document,this.fragmentContextID),this.openElements=new Xs(this.document,this.treeAdapter,this)}static parse(e,t){const n=new this(t);return n.tokenizer.write(e,!0),n.document}static getFragmentParser(e,t){const n={...Li,...t};null!=e||(e=n.treeAdapter.createElement(_s.TEMPLATE,ps.HTML,[]));const r=n.treeAdapter.createElement("documentmock",ps.HTML,[]),s=new this(n,r,e);return s.fragmentContextID===gs.TEMPLATE&&s.tmplInsertionModeStack.unshift(ki.IN_TEMPLATE),s._initTokenizerForFragmentParsing(),s._insertFakeRootElement(),s._resetInsertionMode(),s._findFormInFragmentContext(),s}getFragment(){const e=this.treeAdapter.getFirstChild(this.document),t=this.treeAdapter.createDocumentFragment();return this._adoptNodes(e,t),t}_err(e,t,n){var r;if(!this.onParseError)return;const s=null!==(r=e.location)&&void 0!==r?r:Oi,i={code:t,startLine:s.startLine,startCol:s.startCol,startOffset:s.startOffset,endLine:n?s.startLine:s.endLine,endCol:n?s.startCol:s.endCol,endOffset:n?s.startOffset:s.endOffset};this.onParseError(i)}onItemPush(e,t,n){var r,s;null===(s=(r=this.treeAdapter).onItemPush)||void 0===s||s.call(r,e),n&&this.openElements.stackTop>0&&this._setContextModes(e,t)}onItemPop(e,t){var n,r;if(this.options.sourceCodeLocationInfo&&this._setEndLocation(e,this.currentToken),null===(r=(n=this.treeAdapter).onItemPop)||void 0===r||r.call(n,e,this.openElements.current),t){let e,t;0===this.openElements.stackTop&&this.fragmentContext?(e=this.fragmentContext,t=this.fragmentContextID):({current:e,currentTagId:t}=this.openElements),this._setContextModes(e,t)}}_setContextModes(e,t){const n=e===this.document||this.treeAdapter.getNamespaceURI(e)===ps.HTML;this.currentNotInHTML=!n,this.tokenizer.inForeignNode=!n&&!this._isIntegrationPoint(t,e)}_switchToTextParsing(e,t){this._insertElement(e,ps.HTML),this.tokenizer.state=t,this.originalInsertionMode=this.insertionMode,this.insertionMode=ki.TEXT}switchToPlaintextParsing(){this.insertionMode=ki.TEXT,this.originalInsertionMode=ki.IN_BODY,this.tokenizer.state=Ms.PLAINTEXT}_getAdjustedCurrentElement(){return 0===this.openElements.stackTop&&this.fragmentContext?this.fragmentContext:this.openElements.current}_findFormInFragmentContext(){let e=this.fragmentContext;for(;e;){if(this.treeAdapter.getTagName(e)===_s.FORM){this.formElement=e;break}e=this.treeAdapter.getParentNode(e)}}_initTokenizerForFragmentParsing(){if(this.fragmentContext&&this.treeAdapter.getNamespaceURI(this.fragmentContext)===ps.HTML)switch(this.fragmentContextID){case gs.TITLE:case gs.TEXTAREA:this.tokenizer.state=Ms.RCDATA;break;case gs.STYLE:case gs.XMP:case gs.IFRAME:case gs.NOEMBED:case gs.NOFRAMES:case gs.NOSCRIPT:this.tokenizer.state=Ms.RAWTEXT;break;case gs.SCRIPT:this.tokenizer.state=Ms.SCRIPT_DATA;break;case gs.PLAINTEXT:this.tokenizer.state=Ms.PLAINTEXT}}_setDocumentType(e){const t=e.name||"",n=e.publicId||"",r=e.systemId||"";if(this.treeAdapter.setDocumentType(this.document,t,n,r),e.location){const t=this.treeAdapter.getChildNodes(this.document).find((e=>this.treeAdapter.isDocumentTypeNode(e)));t&&this.treeAdapter.setNodeSourceCodeLocation(t,e.location)}}_attachElementToTree(e,t){if(this.options.sourceCodeLocationInfo){const n=t&&{...t,startTag:t};this.treeAdapter.setNodeSourceCodeLocation(e,n)}if(this._shouldFosterParentOnInsertion())this._fosterParentElement(e);else{const t=this.openElements.currentTmplContentOrNode;this.treeAdapter.appendChild(t,e)}}_appendElement(e,t){const n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location)}_insertElement(e,t){const n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location),this.openElements.push(n,e.tagID)}_insertFakeElement(e,t){const n=this.treeAdapter.createElement(e,ps.HTML,[]);this._attachElementToTree(n,null),this.openElements.push(n,t)}_insertTemplate(e){const t=this.treeAdapter.createElement(e.tagName,ps.HTML,e.attrs),n=this.treeAdapter.createDocumentFragment();this.treeAdapter.setTemplateContent(t,n),this._attachElementToTree(t,e.location),this.openElements.push(t,e.tagID),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,null)}_insertFakeRootElement(){const e=this.treeAdapter.createElement(_s.HTML,ps.HTML,[]);this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(e,null),this.treeAdapter.appendChild(this.openElements.current,e),this.openElements.push(e,gs.HTML)}_appendCommentNode(e,t){const n=this.treeAdapter.createCommentNode(e.data);this.treeAdapter.appendChild(t,n),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,e.location)}_insertCharacters(e){let t,n;if(this._shouldFosterParentOnInsertion()?(({parent:t,beforeElement:n}=this._findFosterParentingLocation()),n?this.treeAdapter.insertTextBefore(t,e.chars,n):this.treeAdapter.insertText(t,e.chars)):(t=this.openElements.currentTmplContentOrNode,this.treeAdapter.insertText(t,e.chars)),!e.location)return;const r=this.treeAdapter.getChildNodes(t),s=n?r.lastIndexOf(n):r.length,i=r[s-1];if(this.treeAdapter.getNodeSourceCodeLocation(i)){const{endLine:t,endCol:n,endOffset:r}=e.location;this.treeAdapter.updateNodeSourceCodeLocation(i,{endLine:t,endCol:n,endOffset:r})}else this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(i,e.location)}_adoptNodes(e,t){for(let n=this.treeAdapter.getFirstChild(e);n;n=this.treeAdapter.getFirstChild(e))this.treeAdapter.detachNode(n),this.treeAdapter.appendChild(t,n)}_setEndLocation(e,t){if(this.treeAdapter.getNodeSourceCodeLocation(e)&&t.location){const n=t.location,r=this.treeAdapter.getTagName(e),s=t.type===us.END_TAG&&r===t.tagName?{endTag:{...n},endLine:n.endLine,endCol:n.endCol,endOffset:n.endOffset}:{endLine:n.startLine,endCol:n.startCol,endOffset:n.startOffset};this.treeAdapter.updateNodeSourceCodeLocation(e,s)}}shouldProcessStartTagTokenInForeignContent(e){if(!this.currentNotInHTML)return!1;let t,n;return 0===this.openElements.stackTop&&this.fragmentContext?(t=this.fragmentContext,n=this.fragmentContextID):({current:t,currentTagId:n}=this.openElements),(e.tagID!==gs.SVG||this.treeAdapter.getTagName(t)!==_s.ANNOTATION_XML||this.treeAdapter.getNamespaceURI(t)!==ps.MATHML)&&(this.tokenizer.inForeignNode||(e.tagID===gs.MGLYPH||e.tagID===gs.MALIGNMARK)&&!this._isIntegrationPoint(n,t,ps.HTML))}_processToken(e){switch(e.type){case us.CHARACTER:this.onCharacter(e);break;case us.NULL_CHARACTER:this.onNullCharacter(e);break;case us.COMMENT:this.onComment(e);break;case us.DOCTYPE:this.onDoctype(e);break;case us.START_TAG:this._processStartTag(e);break;case us.END_TAG:this.onEndTag(e);break;case us.EOF:this.onEof(e);break;case us.WHITESPACE_CHARACTER:this.onWhitespaceCharacter(e)}}_isIntegrationPoint(e,t,n){return Ii(e,this.treeAdapter.getNamespaceURI(t),this.treeAdapter.getAttrList(t),n)}_reconstructActiveFormattingElements(){const e=this.activeFormattingElements.entries.length;if(e){const t=this.activeFormattingElements.entries.findIndex((e=>e.type===Ks.Marker||this.openElements.contains(e.element)));for(let n=t<0?e-1:t-1;n>=0;n--){const e=this.activeFormattingElements.entries[n];this._insertElement(e.token,this.treeAdapter.getNamespaceURI(e.element)),e.element=this.openElements.current}}}_closeTableCell(){this.openElements.generateImpliedEndTags(),this.openElements.popUntilTableCellPopped(),this.activeFormattingElements.clearToLastMarker(),this.insertionMode=ki.IN_ROW}_closePElement(){this.openElements.generateImpliedEndTagsWithExclusion(gs.P),this.openElements.popUntilTagNamePopped(gs.P)}_resetInsertionMode(){for(let e=this.openElements.stackTop;e>=0;e--)switch(0===e&&this.fragmentContext?this.fragmentContextID:this.openElements.tagIDs[e]){case gs.TR:return void(this.insertionMode=ki.IN_ROW);case gs.TBODY:case gs.THEAD:case gs.TFOOT:return void(this.insertionMode=ki.IN_TABLE_BODY);case gs.CAPTION:return void(this.insertionMode=ki.IN_CAPTION);case gs.COLGROUP:return void(this.insertionMode=ki.IN_COLUMN_GROUP);case gs.TABLE:return void(this.insertionMode=ki.IN_TABLE);case gs.BODY:return void(this.insertionMode=ki.IN_BODY);case gs.FRAMESET:return void(this.insertionMode=ki.IN_FRAMESET);case gs.SELECT:return void this._resetInsertionModeForSelect(e);case gs.TEMPLATE:return void(this.insertionMode=this.tmplInsertionModeStack[0]);case gs.HTML:return void(this.insertionMode=this.headElement?ki.AFTER_HEAD:ki.BEFORE_HEAD);case gs.TD:case gs.TH:if(e>0)return void(this.insertionMode=ki.IN_CELL);break;case gs.HEAD:if(e>0)return void(this.insertionMode=ki.IN_HEAD)}this.insertionMode=ki.IN_BODY}_resetInsertionModeForSelect(e){if(e>0)for(let t=e-1;t>0;t--){const e=this.openElements.tagIDs[t];if(e===gs.TEMPLATE)break;if(e===gs.TABLE)return void(this.insertionMode=ki.IN_SELECT_IN_TABLE)}this.insertionMode=ki.IN_SELECT}_isElementCausesFosterParenting(e){return Ri.has(e)}_shouldFosterParentOnInsertion(){return this.fosterParentingEnabled&&this._isElementCausesFosterParenting(this.openElements.currentTagId)}_findFosterParentingLocation(){for(let e=this.openElements.stackTop;e>=0;e--){const t=this.openElements.items[e];switch(this.openElements.tagIDs[e]){case gs.TEMPLATE:if(this.treeAdapter.getNamespaceURI(t)===ps.HTML)return{parent:this.treeAdapter.getTemplateContent(t),beforeElement:null};break;case gs.TABLE:{const n=this.treeAdapter.getParentNode(t);return n?{parent:n,beforeElement:t}:{parent:this.openElements.items[e-1],beforeElement:null}}}}return{parent:this.openElements.items[0],beforeElement:null}}_fosterParentElement(e){const t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertBefore(t.parent,e,t.beforeElement):this.treeAdapter.appendChild(t.parent,e)}_isSpecialElement(e,t){const n=this.treeAdapter.getNamespaceURI(e);return ks[n].has(t)}onCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode)!function(e,t){e._insertCharacters(t),e.framesetOk=!1}(this,e);else switch(this.insertionMode){case ki.INITIAL:Gi(this,e);break;case ki.BEFORE_HTML:qi(this,e);break;case ki.BEFORE_HEAD:Yi(this,e);break;case ki.IN_HEAD:Vi(this,e);break;case ki.IN_HEAD_NO_SCRIPT:Qi(this,e);break;case ki.AFTER_HEAD:$i(this,e);break;case ki.IN_BODY:case ki.IN_CAPTION:case ki.IN_CELL:case ki.IN_TEMPLATE:Ki(this,e);break;case ki.TEXT:case ki.IN_SELECT:case ki.IN_SELECT_IN_TABLE:this._insertCharacters(e);break;case ki.IN_TABLE:case ki.IN_TABLE_BODY:case ki.IN_ROW:aa(this,e);break;case ki.IN_TABLE_TEXT:ha(this,e);break;case ki.IN_COLUMN_GROUP:Ea(this,e);break;case ki.AFTER_BODY:Da(this,e);break;case ki.AFTER_AFTER_BODY:Sa(this,e)}}onNullCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode)!function(e,t){t.chars=Wr,e._insertCharacters(t)}(this,e);else switch(this.insertionMode){case ki.INITIAL:Gi(this,e);break;case ki.BEFORE_HTML:qi(this,e);break;case ki.BEFORE_HEAD:Yi(this,e);break;case ki.IN_HEAD:Vi(this,e);break;case ki.IN_HEAD_NO_SCRIPT:Qi(this,e);break;case ki.AFTER_HEAD:$i(this,e);break;case ki.TEXT:this._insertCharacters(e);break;case ki.IN_TABLE:case ki.IN_TABLE_BODY:case ki.IN_ROW:aa(this,e);break;case ki.IN_COLUMN_GROUP:Ea(this,e);break;case ki.AFTER_BODY:Da(this,e);break;case ki.AFTER_AFTER_BODY:Sa(this,e)}}onComment(e){if(this.skipNextNewLine=!1,this.currentNotInHTML)Ui(this,e);else switch(this.insertionMode){case ki.INITIAL:case ki.BEFORE_HTML:case ki.BEFORE_HEAD:case ki.IN_HEAD:case ki.IN_HEAD_NO_SCRIPT:case ki.AFTER_HEAD:case ki.IN_BODY:case ki.IN_TABLE:case ki.IN_CAPTION:case ki.IN_COLUMN_GROUP:case ki.IN_TABLE_BODY:case ki.IN_ROW:case ki.IN_CELL:case ki.IN_SELECT:case ki.IN_SELECT_IN_TABLE:case ki.IN_TEMPLATE:case ki.IN_FRAMESET:case ki.AFTER_FRAMESET:Ui(this,e);break;case ki.IN_TABLE_TEXT:pa(this,e);break;case ki.AFTER_BODY:!function(e,t){e._appendCommentNode(t,e.openElements.items[0])}(this,e);break;case ki.AFTER_AFTER_BODY:case ki.AFTER_AFTER_FRAMESET:!function(e,t){e._appendCommentNode(t,e.document)}(this,e)}}onDoctype(e){switch(this.skipNextNewLine=!1,this.insertionMode){case ki.INITIAL:!function(e,t){e._setDocumentType(t);const n=t.forceQuirks?ms.QUIRKS:function(e){if(e.name!==ri)return ms.QUIRKS;const{systemId:t}=e;if(t&&t.toLowerCase()===ii)return ms.QUIRKS;let{publicId:n}=e;if(null!==n){if(n=n.toLowerCase(),ci.has(n))return ms.QUIRKS;let e=null===t?oi:ai;if(hi(n,e))return ms.QUIRKS;if(e=null===t?li:ui,hi(n,e))return ms.LIMITED_QUIRKS}return ms.NO_QUIRKS}(t);(function(e){return e.name===ri&&null===e.publicId&&(null===e.systemId||e.systemId===si)})(t)||e._err(t,os.nonConformingDoctype);e.treeAdapter.setDocumentMode(e.document,n),e.insertionMode=ki.BEFORE_HTML}(this,e);break;case ki.BEFORE_HEAD:case ki.IN_HEAD:case ki.IN_HEAD_NO_SCRIPT:case ki.AFTER_HEAD:this._err(e,os.misplacedDoctype);break;case ki.IN_TABLE_TEXT:pa(this,e)}}onStartTag(e){this.skipNextNewLine=!1,this.currentToken=e,this._processStartTag(e),e.selfClosing&&!e.ackSelfClosing&&this._err(e,os.nonVoidHtmlElementStartTagWithTrailingSolidus)}_processStartTag(e){this.shouldProcessStartTagTokenInForeignContent(e)?function(e,t){if(function(e){const t=e.tagID;return t===gs.FONT&&e.attrs.some((({name:e})=>e===fs.COLOR||e===fs.SIZE||e===fs.FACE))||_i.has(t)}(t))ka(e),e._startTagOutsideForeignContent(t);else{const n=e._getAdjustedCurrentElement(),r=e.treeAdapter.getNamespaceURI(n);r===ps.MATHML?Ai(t):r===ps.SVG&&(!function(e){const t=Ti.get(e.tagName);null!=t&&(e.tagName=t,e.tagID=Ds(e.tagName))}(t),gi(t)),Ci(t),t.selfClosing?e._appendElement(t,r):e._insertElement(t,r),t.ackSelfClosing=!0}}(this,e):this._startTagOutsideForeignContent(e)}_startTagOutsideForeignContent(e){switch(this.insertionMode){case ki.INITIAL:Gi(this,e);break;case ki.BEFORE_HTML:!function(e,t){t.tagID===gs.HTML?(e._insertElement(t,ps.HTML),e.insertionMode=ki.BEFORE_HEAD):qi(e,t)}(this,e);break;case ki.BEFORE_HEAD:!function(e,t){switch(t.tagID){case gs.HTML:na(e,t);break;case gs.HEAD:e._insertElement(t,ps.HTML),e.headElement=e.openElements.current,e.insertionMode=ki.IN_HEAD;break;default:Yi(e,t)}}(this,e);break;case ki.IN_HEAD:ji(this,e);break;case ki.IN_HEAD_NO_SCRIPT:!function(e,t){switch(t.tagID){case gs.HTML:na(e,t);break;case gs.BASEFONT:case gs.BGSOUND:case gs.HEAD:case gs.LINK:case gs.META:case gs.NOFRAMES:case gs.STYLE:ji(e,t);break;case gs.NOSCRIPT:e._err(t,os.nestedNoscriptInHead);break;default:Qi(e,t)}}(this,e);break;case ki.AFTER_HEAD:!function(e,t){switch(t.tagID){case gs.HTML:na(e,t);break;case gs.BODY:e._insertElement(t,ps.HTML),e.framesetOk=!1,e.insertionMode=ki.IN_BODY;break;case gs.FRAMESET:e._insertElement(t,ps.HTML),e.insertionMode=ki.IN_FRAMESET;break;case gs.BASE:case gs.BASEFONT:case gs.BGSOUND:case gs.LINK:case gs.META:case gs.NOFRAMES:case gs.SCRIPT:case gs.STYLE:case gs.TEMPLATE:case gs.TITLE:e._err(t,os.abandonedHeadElementChild),e.openElements.push(e.headElement,gs.HEAD),ji(e,t),e.openElements.remove(e.headElement);break;case gs.HEAD:e._err(t,os.misplacedStartTagForHeadElement);break;default:$i(e,t)}}(this,e);break;case ki.IN_BODY:na(this,e);break;case ki.IN_TABLE:oa(this,e);break;case ki.IN_TABLE_TEXT:pa(this,e);break;case ki.IN_CAPTION:!function(e,t){const n=t.tagID;da.has(n)?e.openElements.hasInTableScope(gs.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(gs.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=ki.IN_TABLE,oa(e,t)):na(e,t)}(this,e);break;case ki.IN_COLUMN_GROUP:fa(this,e);break;case ki.IN_TABLE_BODY:ma(this,e);break;case ki.IN_ROW:_a(this,e);break;case ki.IN_CELL:!function(e,t){const n=t.tagID;da.has(n)?(e.openElements.hasInTableScope(gs.TD)||e.openElements.hasInTableScope(gs.TH))&&(e._closeTableCell(),_a(e,t)):na(e,t)}(this,e);break;case ki.IN_SELECT:ga(this,e);break;case ki.IN_SELECT_IN_TABLE:!function(e,t){const n=t.tagID;n===gs.CAPTION||n===gs.TABLE||n===gs.TBODY||n===gs.TFOOT||n===gs.THEAD||n===gs.TR||n===gs.TD||n===gs.TH?(e.openElements.popUntilTagNamePopped(gs.SELECT),e._resetInsertionMode(),e._processStartTag(t)):ga(e,t)}(this,e);break;case ki.IN_TEMPLATE:!function(e,t){switch(t.tagID){case gs.BASE:case gs.BASEFONT:case gs.BGSOUND:case gs.LINK:case gs.META:case gs.NOFRAMES:case gs.SCRIPT:case gs.STYLE:case gs.TEMPLATE:case gs.TITLE:ji(e,t);break;case gs.CAPTION:case gs.COLGROUP:case gs.TBODY:case gs.TFOOT:case gs.THEAD:e.tmplInsertionModeStack[0]=ki.IN_TABLE,e.insertionMode=ki.IN_TABLE,oa(e,t);break;case gs.COL:e.tmplInsertionModeStack[0]=ki.IN_COLUMN_GROUP,e.insertionMode=ki.IN_COLUMN_GROUP,fa(e,t);break;case gs.TR:e.tmplInsertionModeStack[0]=ki.IN_TABLE_BODY,e.insertionMode=ki.IN_TABLE_BODY,ma(e,t);break;case gs.TD:case gs.TH:e.tmplInsertionModeStack[0]=ki.IN_ROW,e.insertionMode=ki.IN_ROW,_a(e,t);break;default:e.tmplInsertionModeStack[0]=ki.IN_BODY,e.insertionMode=ki.IN_BODY,na(e,t)}}(this,e);break;case ki.AFTER_BODY:!function(e,t){t.tagID===gs.HTML?na(e,t):Da(e,t)}(this,e);break;case ki.IN_FRAMESET:!function(e,t){switch(t.tagID){case gs.HTML:na(e,t);break;case gs.FRAMESET:e._insertElement(t,ps.HTML);break;case gs.FRAME:e._appendElement(t,ps.HTML),t.ackSelfClosing=!0;break;case gs.NOFRAMES:ji(e,t)}}(this,e);break;case ki.AFTER_FRAMESET:!function(e,t){switch(t.tagID){case gs.HTML:na(e,t);break;case gs.NOFRAMES:ji(e,t)}}(this,e);break;case ki.AFTER_AFTER_BODY:!function(e,t){t.tagID===gs.HTML?na(e,t):Sa(e,t)}(this,e);break;case ki.AFTER_AFTER_FRAMESET:!function(e,t){switch(t.tagID){case gs.HTML:na(e,t);break;case gs.NOFRAMES:ji(e,t)}}(this,e)}}onEndTag(e){this.skipNextNewLine=!1,this.currentToken=e,this.currentNotInHTML?function(e,t){if(t.tagID===gs.P||t.tagID===gs.BR)return ka(e),void e._endTagOutsideForeignContent(t);for(let n=e.openElements.stackTop;n>0;n--){const r=e.openElements.items[n];if(e.treeAdapter.getNamespaceURI(r)===ps.HTML){e._endTagOutsideForeignContent(t);break}const s=e.treeAdapter.getTagName(r);if(s.toLowerCase()===t.tagName){t.tagName=s,e.openElements.shortenToLength(n);break}}}(this,e):this._endTagOutsideForeignContent(e)}_endTagOutsideForeignContent(e){switch(this.insertionMode){case ki.INITIAL:Gi(this,e);break;case ki.BEFORE_HTML:!function(e,t){const n=t.tagID;n!==gs.HTML&&n!==gs.HEAD&&n!==gs.BODY&&n!==gs.BR||qi(e,t)}(this,e);break;case ki.BEFORE_HEAD:!function(e,t){const n=t.tagID;n===gs.HEAD||n===gs.BODY||n===gs.HTML||n===gs.BR?Yi(e,t):e._err(t,os.endTagWithoutMatchingOpenElement)}(this,e);break;case ki.IN_HEAD:!function(e,t){switch(t.tagID){case gs.HEAD:e.openElements.pop(),e.insertionMode=ki.AFTER_HEAD;break;case gs.BODY:case gs.BR:case gs.HTML:Vi(e,t);break;case gs.TEMPLATE:zi(e,t);break;default:e._err(t,os.endTagWithoutMatchingOpenElement)}}(this,e);break;case ki.IN_HEAD_NO_SCRIPT:!function(e,t){switch(t.tagID){case gs.NOSCRIPT:e.openElements.pop(),e.insertionMode=ki.IN_HEAD;break;case gs.BR:Qi(e,t);break;default:e._err(t,os.endTagWithoutMatchingOpenElement)}}(this,e);break;case ki.AFTER_HEAD:!function(e,t){switch(t.tagID){case gs.BODY:case gs.HTML:case gs.BR:$i(e,t);break;case gs.TEMPLATE:zi(e,t);break;default:e._err(t,os.endTagWithoutMatchingOpenElement)}}(this,e);break;case ki.IN_BODY:sa(this,e);break;case ki.TEXT:!function(e,t){var n;t.tagID===gs.SCRIPT&&(null===(n=e.scriptHandler)||void 0===n||n.call(e,e.openElements.current));e.openElements.pop(),e.insertionMode=e.originalInsertionMode}(this,e);break;case ki.IN_TABLE:ca(this,e);break;case ki.IN_TABLE_TEXT:pa(this,e);break;case ki.IN_CAPTION:!function(e,t){const n=t.tagID;switch(n){case gs.CAPTION:case gs.TABLE:e.openElements.hasInTableScope(gs.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(gs.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=ki.IN_TABLE,n===gs.TABLE&&ca(e,t));break;case gs.BODY:case gs.COL:case gs.COLGROUP:case gs.HTML:case gs.TBODY:case gs.TD:case gs.TFOOT:case gs.TH:case gs.THEAD:case gs.TR:break;default:sa(e,t)}}(this,e);break;case ki.IN_COLUMN_GROUP:!function(e,t){switch(t.tagID){case gs.COLGROUP:e.openElements.currentTagId===gs.COLGROUP&&(e.openElements.pop(),e.insertionMode=ki.IN_TABLE);break;case gs.TEMPLATE:zi(e,t);break;case gs.COL:break;default:Ea(e,t)}}(this,e);break;case ki.IN_TABLE_BODY:Ta(this,e);break;case ki.IN_ROW:Aa(this,e);break;case ki.IN_CELL:!function(e,t){const n=t.tagID;switch(n){case gs.TD:case gs.TH:e.openElements.hasInTableScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=ki.IN_ROW);break;case gs.TABLE:case gs.TBODY:case gs.TFOOT:case gs.THEAD:case gs.TR:e.openElements.hasInTableScope(n)&&(e._closeTableCell(),Aa(e,t));break;case gs.BODY:case gs.CAPTION:case gs.COL:case gs.COLGROUP:case gs.HTML:break;default:sa(e,t)}}(this,e);break;case ki.IN_SELECT:Ca(this,e);break;case ki.IN_SELECT_IN_TABLE:!function(e,t){const n=t.tagID;n===gs.CAPTION||n===gs.TABLE||n===gs.TBODY||n===gs.TFOOT||n===gs.THEAD||n===gs.TR||n===gs.TD||n===gs.TH?e.openElements.hasInTableScope(n)&&(e.openElements.popUntilTagNamePopped(gs.SELECT),e._resetInsertionMode(),e.onEndTag(t)):Ca(e,t)}(this,e);break;case ki.IN_TEMPLATE:!function(e,t){t.tagID===gs.TEMPLATE&&zi(e,t)}(this,e);break;case ki.AFTER_BODY:Na(this,e);break;case ki.IN_FRAMESET:!function(e,t){t.tagID!==gs.FRAMESET||e.openElements.isRootHtmlElementCurrent()||(e.openElements.pop(),e.fragmentContext||e.openElements.currentTagId===gs.FRAMESET||(e.insertionMode=ki.AFTER_FRAMESET))}(this,e);break;case ki.AFTER_FRAMESET:!function(e,t){t.tagID===gs.HTML&&(e.insertionMode=ki.AFTER_AFTER_FRAMESET)}(this,e);break;case ki.AFTER_AFTER_BODY:Sa(this,e)}}onEof(e){switch(this.insertionMode){case ki.INITIAL:Gi(this,e);break;case ki.BEFORE_HTML:qi(this,e);break;case ki.BEFORE_HEAD:Yi(this,e);break;case ki.IN_HEAD:Vi(this,e);break;case ki.IN_HEAD_NO_SCRIPT:Qi(this,e);break;case ki.AFTER_HEAD:$i(this,e);break;case ki.IN_BODY:case ki.IN_TABLE:case ki.IN_CAPTION:case ki.IN_COLUMN_GROUP:case ki.IN_TABLE_BODY:case ki.IN_ROW:case ki.IN_CELL:case ki.IN_SELECT:case ki.IN_SELECT_IN_TABLE:ia(this,e);break;case ki.TEXT:!function(e,t){e._err(t,os.eofInElementThatCanContainOnlyText),e.openElements.pop(),e.insertionMode=e.originalInsertionMode,e.onEof(t)}(this,e);break;case ki.IN_TABLE_TEXT:pa(this,e);break;case ki.IN_TEMPLATE:Ia(this,e);break;case ki.AFTER_BODY:case ki.IN_FRAMESET:case ki.AFTER_FRAMESET:case ki.AFTER_AFTER_BODY:case ki.AFTER_AFTER_FRAMESET:Hi(this,e)}}onWhitespaceCharacter(e){if(this.skipNextNewLine&&(this.skipNextNewLine=!1,e.chars.charCodeAt(0)===Xr.LINE_FEED)){if(1===e.chars.length)return;e.chars=e.chars.substr(1)}if(this.tokenizer.inForeignNode)this._insertCharacters(e);else switch(this.insertionMode){case ki.IN_HEAD:case ki.IN_HEAD_NO_SCRIPT:case ki.AFTER_HEAD:case ki.TEXT:case ki.IN_COLUMN_GROUP:case ki.IN_SELECT:case ki.IN_SELECT_IN_TABLE:case ki.IN_FRAMESET:case ki.AFTER_FRAMESET:this._insertCharacters(e);break;case ki.IN_BODY:case ki.IN_CAPTION:case ki.IN_CELL:case ki.IN_TEMPLATE:case ki.AFTER_BODY:case ki.AFTER_AFTER_BODY:case ki.AFTER_AFTER_FRAMESET:Xi(this,e);break;case ki.IN_TABLE:case ki.IN_TABLE_BODY:case ki.IN_ROW:aa(this,e);break;case ki.IN_TABLE_TEXT:ua(this,e)}}};function Mi(e,t){let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(t.tagName);return n?e.openElements.contains(n.element)?e.openElements.hasInScope(t.tagID)||(n=null):(e.activeFormattingElements.removeEntry(n),n=null):ra(e,t),n}function Fi(e,t){let n=null,r=e.openElements.stackTop;for(;r>=0;r--){const s=e.openElements.items[r];if(s===t.element)break;e._isSpecialElement(s,e.openElements.tagIDs[r])&&(n=s)}return n||(e.openElements.shortenToLength(r<0?0:r),e.activeFormattingElements.removeEntry(t)),n}function xi(e,t,n){let r=t,s=e.openElements.getCommonAncestor(t);for(let i=0,a=s;a!==n;i++,a=s){s=e.openElements.getCommonAncestor(a);const n=e.activeFormattingElements.getElementEntry(a),o=n&&i>=Si;!n||o?(o&&e.activeFormattingElements.removeEntry(n),e.openElements.remove(a)):(a=Pi(e,n),r===t&&(e.activeFormattingElements.bookmark=n),e.treeAdapter.detachNode(r),e.treeAdapter.appendChild(a,r),r=a)}return r}function Pi(e,t){const n=e.treeAdapter.getNamespaceURI(t.element),r=e.treeAdapter.createElement(t.token.tagName,n,t.token.attrs);return e.openElements.replace(t.element,r),t.element=r,r}function vi(e,t,n){const r=Ds(e.treeAdapter.getTagName(t));if(e._isElementCausesFosterParenting(r))e._fosterParentElement(n);else{const s=e.treeAdapter.getNamespaceURI(t);r===gs.TEMPLATE&&s===ps.HTML&&(t=e.treeAdapter.getTemplateContent(t)),e.treeAdapter.appendChild(t,n)}}function Bi(e,t,n){const r=e.treeAdapter.getNamespaceURI(n.element),{token:s}=n,i=e.treeAdapter.createElement(s.tagName,r,s.attrs);e._adoptNodes(t,i),e.treeAdapter.appendChild(t,i),e.activeFormattingElements.insertElementAfterBookmark(i,s),e.activeFormattingElements.removeEntry(n),e.openElements.remove(n.element),e.openElements.insertAfter(t,i,s.tagID)}function wi(e,t){for(let n=0;n<Di;n++){const n=Mi(e,t);if(!n)break;const r=Fi(e,n);if(!r)break;e.activeFormattingElements.bookmark=n;const s=xi(e,r,n.element),i=e.openElements.getCommonAncestor(n.element);e.treeAdapter.detachNode(s),i&&vi(e,i,s),Bi(e,r,n)}}function Ui(e,t){e._appendCommentNode(t,e.openElements.currentTmplContentOrNode)}function Hi(e,t){if(e.stopped=!0,t.location){const n=e.fragmentContext?0:2;for(let r=e.openElements.stackTop;r>=n;r--)e._setEndLocation(e.openElements.items[r],t);if(!e.fragmentContext&&e.openElements.stackTop>=0){const n=e.openElements.items[0],r=e.treeAdapter.getNodeSourceCodeLocation(n);if(r&&!r.endTag&&(e._setEndLocation(n,t),e.openElements.stackTop>=1)){const n=e.openElements.items[1],r=e.treeAdapter.getNodeSourceCodeLocation(n);r&&!r.endTag&&e._setEndLocation(n,t)}}}}function Gi(e,t){e._err(t,os.missingDoctype,!0),e.treeAdapter.setDocumentMode(e.document,ms.QUIRKS),e.insertionMode=ki.BEFORE_HTML,e._processToken(t)}function qi(e,t){e._insertFakeRootElement(),e.insertionMode=ki.BEFORE_HEAD,e._processToken(t)}function Yi(e,t){e._insertFakeElement(_s.HEAD,gs.HEAD),e.headElement=e.openElements.current,e.insertionMode=ki.IN_HEAD,e._processToken(t)}function ji(e,t){switch(t.tagID){case gs.HTML:na(e,t);break;case gs.BASE:case gs.BASEFONT:case gs.BGSOUND:case gs.LINK:case gs.META:e._appendElement(t,ps.HTML),t.ackSelfClosing=!0;break;case gs.TITLE:e._switchToTextParsing(t,Ms.RCDATA);break;case gs.NOSCRIPT:e.options.scriptingEnabled?e._switchToTextParsing(t,Ms.RAWTEXT):(e._insertElement(t,ps.HTML),e.insertionMode=ki.IN_HEAD_NO_SCRIPT);break;case gs.NOFRAMES:case gs.STYLE:e._switchToTextParsing(t,Ms.RAWTEXT);break;case gs.SCRIPT:e._switchToTextParsing(t,Ms.SCRIPT_DATA);break;case gs.TEMPLATE:e._insertTemplate(t),e.activeFormattingElements.insertMarker(),e.framesetOk=!1,e.insertionMode=ki.IN_TEMPLATE,e.tmplInsertionModeStack.unshift(ki.IN_TEMPLATE);break;case gs.HEAD:e._err(t,os.misplacedStartTagForHeadElement);break;default:Vi(e,t)}}function zi(e,t){e.openElements.tmplCount>0?(e.openElements.generateImpliedEndTagsThoroughly(),e.openElements.currentTagId!==gs.TEMPLATE&&e._err(t,os.closingOfElementWithOpenChildElements),e.openElements.popUntilTagNamePopped(gs.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode()):e._err(t,os.endTagWithoutMatchingOpenElement)}function Vi(e,t){e.openElements.pop(),e.insertionMode=ki.AFTER_HEAD,e._processToken(t)}function Qi(e,t){const n=t.type===us.EOF?os.openElementsLeftAfterEof:os.disallowedContentInNoscriptInHead;e._err(t,n),e.openElements.pop(),e.insertionMode=ki.IN_HEAD,e._processToken(t)}function $i(e,t){e._insertFakeElement(_s.BODY,gs.BODY),e.insertionMode=ki.IN_BODY,Wi(e,t)}function Wi(e,t){switch(t.type){case us.CHARACTER:Ki(e,t);break;case us.WHITESPACE_CHARACTER:Xi(e,t);break;case us.COMMENT:Ui(e,t);break;case us.START_TAG:na(e,t);break;case us.END_TAG:sa(e,t);break;case us.EOF:ia(e,t)}}function Xi(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t)}function Ki(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t),e.framesetOk=!1}function Zi(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,ps.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}function Ji(e){const t=Is(e,fs.TYPE);return null!=t&&t.toLowerCase()===Ni}function ea(e,t){e._switchToTextParsing(t,Ms.RAWTEXT)}function ta(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,ps.HTML)}function na(e,t){switch(t.tagID){case gs.I:case gs.S:case gs.B:case gs.U:case gs.EM:case gs.TT:case gs.BIG:case gs.CODE:case gs.FONT:case gs.SMALL:case gs.STRIKE:case gs.STRONG:!function(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,ps.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case gs.A:!function(e,t){const n=e.activeFormattingElements.getElementEntryInScopeWithTagName(_s.A);n&&(wi(e,t),e.openElements.remove(n.element),e.activeFormattingElements.removeEntry(n)),e._reconstructActiveFormattingElements(),e._insertElement(t,ps.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case gs.H1:case gs.H2:case gs.H3:case gs.H4:case gs.H5:case gs.H6:!function(e,t){e.openElements.hasInButtonScope(gs.P)&&e._closePElement(),bs(e.openElements.currentTagId)&&e.openElements.pop(),e._insertElement(t,ps.HTML)}(e,t);break;case gs.P:case gs.DL:case gs.OL:case gs.UL:case gs.DIV:case gs.DIR:case gs.NAV:case gs.MAIN:case gs.MENU:case gs.ASIDE:case gs.CENTER:case gs.FIGURE:case gs.FOOTER:case gs.HEADER:case gs.HGROUP:case gs.DIALOG:case gs.DETAILS:case gs.ADDRESS:case gs.ARTICLE:case gs.SECTION:case gs.SUMMARY:case gs.FIELDSET:case gs.BLOCKQUOTE:case gs.FIGCAPTION:!function(e,t){e.openElements.hasInButtonScope(gs.P)&&e._closePElement(),e._insertElement(t,ps.HTML)}(e,t);break;case gs.LI:case gs.DD:case gs.DT:!function(e,t){e.framesetOk=!1;const n=t.tagID;for(let t=e.openElements.stackTop;t>=0;t--){const r=e.openElements.tagIDs[t];if(n===gs.LI&&r===gs.LI||(n===gs.DD||n===gs.DT)&&(r===gs.DD||r===gs.DT)){e.openElements.generateImpliedEndTagsWithExclusion(r),e.openElements.popUntilTagNamePopped(r);break}if(r!==gs.ADDRESS&&r!==gs.DIV&&r!==gs.P&&e._isSpecialElement(e.openElements.items[t],r))break}e.openElements.hasInButtonScope(gs.P)&&e._closePElement(),e._insertElement(t,ps.HTML)}(e,t);break;case gs.BR:case gs.IMG:case gs.WBR:case gs.AREA:case gs.EMBED:case gs.KEYGEN:Zi(e,t);break;case gs.HR:!function(e,t){e.openElements.hasInButtonScope(gs.P)&&e._closePElement(),e._appendElement(t,ps.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}(e,t);break;case gs.RB:case gs.RTC:!function(e,t){e.openElements.hasInScope(gs.RUBY)&&e.openElements.generateImpliedEndTags(),e._insertElement(t,ps.HTML)}(e,t);break;case gs.RT:case gs.RP:!function(e,t){e.openElements.hasInScope(gs.RUBY)&&e.openElements.generateImpliedEndTagsWithExclusion(gs.RTC),e._insertElement(t,ps.HTML)}(e,t);break;case gs.PRE:case gs.LISTING:!function(e,t){e.openElements.hasInButtonScope(gs.P)&&e._closePElement(),e._insertElement(t,ps.HTML),e.skipNextNewLine=!0,e.framesetOk=!1}(e,t);break;case gs.XMP:!function(e,t){e.openElements.hasInButtonScope(gs.P)&&e._closePElement(),e._reconstructActiveFormattingElements(),e.framesetOk=!1,e._switchToTextParsing(t,Ms.RAWTEXT)}(e,t);break;case gs.SVG:!function(e,t){e._reconstructActiveFormattingElements(),gi(t),Ci(t),t.selfClosing?e._appendElement(t,ps.SVG):e._insertElement(t,ps.SVG),t.ackSelfClosing=!0}(e,t);break;case gs.HTML:!function(e,t){0===e.openElements.tmplCount&&e.treeAdapter.adoptAttributes(e.openElements.items[0],t.attrs)}(e,t);break;case gs.BASE:case gs.LINK:case gs.META:case gs.STYLE:case gs.TITLE:case gs.SCRIPT:case gs.BGSOUND:case gs.BASEFONT:case gs.TEMPLATE:ji(e,t);break;case gs.BODY:!function(e,t){const n=e.openElements.tryPeekProperlyNestedBodyElement();n&&0===e.openElements.tmplCount&&(e.framesetOk=!1,e.treeAdapter.adoptAttributes(n,t.attrs))}(e,t);break;case gs.FORM:!function(e,t){const n=e.openElements.tmplCount>0;e.formElement&&!n||(e.openElements.hasInButtonScope(gs.P)&&e._closePElement(),e._insertElement(t,ps.HTML),n||(e.formElement=e.openElements.current))}(e,t);break;case gs.NOBR:!function(e,t){e._reconstructActiveFormattingElements(),e.openElements.hasInScope(gs.NOBR)&&(wi(e,t),e._reconstructActiveFormattingElements()),e._insertElement(t,ps.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case gs.MATH:!function(e,t){e._reconstructActiveFormattingElements(),Ai(t),Ci(t),t.selfClosing?e._appendElement(t,ps.MATHML):e._insertElement(t,ps.MATHML),t.ackSelfClosing=!0}(e,t);break;case gs.TABLE:!function(e,t){e.treeAdapter.getDocumentMode(e.document)!==ms.QUIRKS&&e.openElements.hasInButtonScope(gs.P)&&e._closePElement(),e._insertElement(t,ps.HTML),e.framesetOk=!1,e.insertionMode=ki.IN_TABLE}(e,t);break;case gs.INPUT:!function(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,ps.HTML),Ji(t)||(e.framesetOk=!1),t.ackSelfClosing=!0}(e,t);break;case gs.PARAM:case gs.TRACK:case gs.SOURCE:!function(e,t){e._appendElement(t,ps.HTML),t.ackSelfClosing=!0}(e,t);break;case gs.IMAGE:!function(e,t){t.tagName=_s.IMG,t.tagID=gs.IMG,Zi(e,t)}(e,t);break;case gs.BUTTON:!function(e,t){e.openElements.hasInScope(gs.BUTTON)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(gs.BUTTON)),e._reconstructActiveFormattingElements(),e._insertElement(t,ps.HTML),e.framesetOk=!1}(e,t);break;case gs.APPLET:case gs.OBJECT:case gs.MARQUEE:!function(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,ps.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1}(e,t);break;case gs.IFRAME:!function(e,t){e.framesetOk=!1,e._switchToTextParsing(t,Ms.RAWTEXT)}(e,t);break;case gs.SELECT:!function(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,ps.HTML),e.framesetOk=!1,e.insertionMode=e.insertionMode===ki.IN_TABLE||e.insertionMode===ki.IN_CAPTION||e.insertionMode===ki.IN_TABLE_BODY||e.insertionMode===ki.IN_ROW||e.insertionMode===ki.IN_CELL?ki.IN_SELECT_IN_TABLE:ki.IN_SELECT}(e,t);break;case gs.OPTION:case gs.OPTGROUP:!function(e,t){e.openElements.currentTagId===gs.OPTION&&e.openElements.pop(),e._reconstructActiveFormattingElements(),e._insertElement(t,ps.HTML)}(e,t);break;case gs.NOEMBED:ea(e,t);break;case gs.FRAMESET:!function(e,t){const n=e.openElements.tryPeekProperlyNestedBodyElement();e.framesetOk&&n&&(e.treeAdapter.detachNode(n),e.openElements.popAllUpToHtmlElement(),e._insertElement(t,ps.HTML),e.insertionMode=ki.IN_FRAMESET)}(e,t);break;case gs.TEXTAREA:!function(e,t){e._insertElement(t,ps.HTML),e.skipNextNewLine=!0,e.tokenizer.state=Ms.RCDATA,e.originalInsertionMode=e.insertionMode,e.framesetOk=!1,e.insertionMode=ki.TEXT}(e,t);break;case gs.NOSCRIPT:e.options.scriptingEnabled?ea(e,t):ta(e,t);break;case gs.PLAINTEXT:!function(e,t){e.openElements.hasInButtonScope(gs.P)&&e._closePElement(),e._insertElement(t,ps.HTML),e.tokenizer.state=Ms.PLAINTEXT}(e,t);break;case gs.COL:case gs.TH:case gs.TD:case gs.TR:case gs.HEAD:case gs.FRAME:case gs.TBODY:case gs.TFOOT:case gs.THEAD:case gs.CAPTION:case gs.COLGROUP:break;default:ta(e,t)}}function ra(e,t){const n=t.tagName,r=t.tagID;for(let t=e.openElements.stackTop;t>0;t--){const s=e.openElements.items[t],i=e.openElements.tagIDs[t];if(r===i&&(r!==gs.UNKNOWN||e.treeAdapter.getTagName(s)===n)){e.openElements.generateImpliedEndTagsWithExclusion(r),e.openElements.stackTop>=t&&e.openElements.shortenToLength(t);break}if(e._isSpecialElement(s,i))break}}function sa(e,t){switch(t.tagID){case gs.A:case gs.B:case gs.I:case gs.S:case gs.U:case gs.EM:case gs.TT:case gs.BIG:case gs.CODE:case gs.FONT:case gs.NOBR:case gs.SMALL:case gs.STRIKE:case gs.STRONG:wi(e,t);break;case gs.P:!function(e){e.openElements.hasInButtonScope(gs.P)||e._insertFakeElement(_s.P,gs.P),e._closePElement()}(e);break;case gs.DL:case gs.UL:case gs.OL:case gs.DIR:case gs.DIV:case gs.NAV:case gs.PRE:case gs.MAIN:case gs.MENU:case gs.ASIDE:case gs.BUTTON:case gs.CENTER:case gs.FIGURE:case gs.FOOTER:case gs.HEADER:case gs.HGROUP:case gs.DIALOG:case gs.ADDRESS:case gs.ARTICLE:case gs.DETAILS:case gs.SECTION:case gs.SUMMARY:case gs.LISTING:case gs.FIELDSET:case gs.BLOCKQUOTE:case gs.FIGCAPTION:!function(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n))}(e,t);break;case gs.LI:!function(e){e.openElements.hasInListItemScope(gs.LI)&&(e.openElements.generateImpliedEndTagsWithExclusion(gs.LI),e.openElements.popUntilTagNamePopped(gs.LI))}(e);break;case gs.DD:case gs.DT:!function(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilTagNamePopped(n))}(e,t);break;case gs.H1:case gs.H2:case gs.H3:case gs.H4:case gs.H5:case gs.H6:!function(e){e.openElements.hasNumberedHeaderInScope()&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilNumberedHeaderPopped())}(e);break;case gs.BR:!function(e){e._reconstructActiveFormattingElements(),e._insertFakeElement(_s.BR,gs.BR),e.openElements.pop(),e.framesetOk=!1}(e);break;case gs.BODY:!function(e,t){if(e.openElements.hasInScope(gs.BODY)&&(e.insertionMode=ki.AFTER_BODY,e.options.sourceCodeLocationInfo)){const n=e.openElements.tryPeekProperlyNestedBodyElement();n&&e._setEndLocation(n,t)}}(e,t);break;case gs.HTML:!function(e,t){e.openElements.hasInScope(gs.BODY)&&(e.insertionMode=ki.AFTER_BODY,Na(e,t))}(e,t);break;case gs.FORM:!function(e){const t=e.openElements.tmplCount>0,{formElement:n}=e;t||(e.formElement=null),(n||t)&&e.openElements.hasInScope(gs.FORM)&&(e.openElements.generateImpliedEndTags(),t?e.openElements.popUntilTagNamePopped(gs.FORM):n&&e.openElements.remove(n))}(e);break;case gs.APPLET:case gs.OBJECT:case gs.MARQUEE:!function(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker())}(e,t);break;case gs.TEMPLATE:zi(e,t);break;default:ra(e,t)}}function ia(e,t){e.tmplInsertionModeStack.length>0?Ia(e,t):Hi(e,t)}function aa(e,t){if(Ri.has(e.openElements.currentTagId))switch(e.pendingCharacterTokens.length=0,e.hasNonWhitespacePendingCharacterToken=!1,e.originalInsertionMode=e.insertionMode,e.insertionMode=ki.IN_TABLE_TEXT,t.type){case us.CHARACTER:ha(e,t);break;case us.WHITESPACE_CHARACTER:ua(e,t)}else la(e,t)}function oa(e,t){switch(t.tagID){case gs.TD:case gs.TH:case gs.TR:!function(e,t){e.openElements.clearBackToTableContext(),e._insertFakeElement(_s.TBODY,gs.TBODY),e.insertionMode=ki.IN_TABLE_BODY,ma(e,t)}(e,t);break;case gs.STYLE:case gs.SCRIPT:case gs.TEMPLATE:ji(e,t);break;case gs.COL:!function(e,t){e.openElements.clearBackToTableContext(),e._insertFakeElement(_s.COLGROUP,gs.COLGROUP),e.insertionMode=ki.IN_COLUMN_GROUP,fa(e,t)}(e,t);break;case gs.FORM:!function(e,t){e.formElement||0!==e.openElements.tmplCount||(e._insertElement(t,ps.HTML),e.formElement=e.openElements.current,e.openElements.pop())}(e,t);break;case gs.TABLE:!function(e,t){e.openElements.hasInTableScope(gs.TABLE)&&(e.openElements.popUntilTagNamePopped(gs.TABLE),e._resetInsertionMode(),e._processStartTag(t))}(e,t);break;case gs.TBODY:case gs.TFOOT:case gs.THEAD:!function(e,t){e.openElements.clearBackToTableContext(),e._insertElement(t,ps.HTML),e.insertionMode=ki.IN_TABLE_BODY}(e,t);break;case gs.INPUT:!function(e,t){Ji(t)?e._appendElement(t,ps.HTML):la(e,t),t.ackSelfClosing=!0}(e,t);break;case gs.CAPTION:!function(e,t){e.openElements.clearBackToTableContext(),e.activeFormattingElements.insertMarker(),e._insertElement(t,ps.HTML),e.insertionMode=ki.IN_CAPTION}(e,t);break;case gs.COLGROUP:!function(e,t){e.openElements.clearBackToTableContext(),e._insertElement(t,ps.HTML),e.insertionMode=ki.IN_COLUMN_GROUP}(e,t);break;default:la(e,t)}}function ca(e,t){switch(t.tagID){case gs.TABLE:e.openElements.hasInTableScope(gs.TABLE)&&(e.openElements.popUntilTagNamePopped(gs.TABLE),e._resetInsertionMode());break;case gs.TEMPLATE:zi(e,t);break;case gs.BODY:case gs.CAPTION:case gs.COL:case gs.COLGROUP:case gs.HTML:case gs.TBODY:case gs.TD:case gs.TFOOT:case gs.TH:case gs.THEAD:case gs.TR:break;default:la(e,t)}}function la(e,t){const n=e.fosterParentingEnabled;e.fosterParentingEnabled=!0,Wi(e,t),e.fosterParentingEnabled=n}function ua(e,t){e.pendingCharacterTokens.push(t)}function ha(e,t){e.pendingCharacterTokens.push(t),e.hasNonWhitespacePendingCharacterToken=!0}function pa(e,t){let n=0;if(e.hasNonWhitespacePendingCharacterToken)for(;n<e.pendingCharacterTokens.length;n++)la(e,e.pendingCharacterTokens[n]);else for(;n<e.pendingCharacterTokens.length;n++)e._insertCharacters(e.pendingCharacterTokens[n]);e.insertionMode=e.originalInsertionMode,e._processToken(t)}const da=new Set([gs.CAPTION,gs.COL,gs.COLGROUP,gs.TBODY,gs.TD,gs.TFOOT,gs.TH,gs.THEAD,gs.TR]);function fa(e,t){switch(t.tagID){case gs.HTML:na(e,t);break;case gs.COL:e._appendElement(t,ps.HTML),t.ackSelfClosing=!0;break;case gs.TEMPLATE:ji(e,t);break;default:Ea(e,t)}}function Ea(e,t){e.openElements.currentTagId===gs.COLGROUP&&(e.openElements.pop(),e.insertionMode=ki.IN_TABLE,e._processToken(t))}function ma(e,t){switch(t.tagID){case gs.TR:e.openElements.clearBackToTableBodyContext(),e._insertElement(t,ps.HTML),e.insertionMode=ki.IN_ROW;break;case gs.TH:case gs.TD:e.openElements.clearBackToTableBodyContext(),e._insertFakeElement(_s.TR,gs.TR),e.insertionMode=ki.IN_ROW,_a(e,t);break;case gs.CAPTION:case gs.COL:case gs.COLGROUP:case gs.TBODY:case gs.TFOOT:case gs.THEAD:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=ki.IN_TABLE,oa(e,t));break;default:oa(e,t)}}function Ta(e,t){const n=t.tagID;switch(t.tagID){case gs.TBODY:case gs.TFOOT:case gs.THEAD:e.openElements.hasInTableScope(n)&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=ki.IN_TABLE);break;case gs.TABLE:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=ki.IN_TABLE,ca(e,t));break;case gs.BODY:case gs.CAPTION:case gs.COL:case gs.COLGROUP:case gs.HTML:case gs.TD:case gs.TH:case gs.TR:break;default:ca(e,t)}}function _a(e,t){switch(t.tagID){case gs.TH:case gs.TD:e.openElements.clearBackToTableRowContext(),e._insertElement(t,ps.HTML),e.insertionMode=ki.IN_CELL,e.activeFormattingElements.insertMarker();break;case gs.CAPTION:case gs.COL:case gs.COLGROUP:case gs.TBODY:case gs.TFOOT:case gs.THEAD:case gs.TR:e.openElements.hasInTableScope(gs.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=ki.IN_TABLE_BODY,ma(e,t));break;default:oa(e,t)}}function Aa(e,t){switch(t.tagID){case gs.TR:e.openElements.hasInTableScope(gs.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=ki.IN_TABLE_BODY);break;case gs.TABLE:e.openElements.hasInTableScope(gs.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=ki.IN_TABLE_BODY,Ta(e,t));break;case gs.TBODY:case gs.TFOOT:case gs.THEAD:(e.openElements.hasInTableScope(t.tagID)||e.openElements.hasInTableScope(gs.TR))&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=ki.IN_TABLE_BODY,Ta(e,t));break;case gs.BODY:case gs.CAPTION:case gs.COL:case gs.COLGROUP:case gs.HTML:case gs.TD:case gs.TH:break;default:ca(e,t)}}function ga(e,t){switch(t.tagID){case gs.HTML:na(e,t);break;case gs.OPTION:e.openElements.currentTagId===gs.OPTION&&e.openElements.pop(),e._insertElement(t,ps.HTML);break;case gs.OPTGROUP:e.openElements.currentTagId===gs.OPTION&&e.openElements.pop(),e.openElements.currentTagId===gs.OPTGROUP&&e.openElements.pop(),e._insertElement(t,ps.HTML);break;case gs.INPUT:case gs.KEYGEN:case gs.TEXTAREA:case gs.SELECT:e.openElements.hasInSelectScope(gs.SELECT)&&(e.openElements.popUntilTagNamePopped(gs.SELECT),e._resetInsertionMode(),t.tagID!==gs.SELECT&&e._processStartTag(t));break;case gs.SCRIPT:case gs.TEMPLATE:ji(e,t)}}function Ca(e,t){switch(t.tagID){case gs.OPTGROUP:e.openElements.stackTop>0&&e.openElements.currentTagId===gs.OPTION&&e.openElements.tagIDs[e.openElements.stackTop-1]===gs.OPTGROUP&&e.openElements.pop(),e.openElements.currentTagId===gs.OPTGROUP&&e.openElements.pop();break;case gs.OPTION:e.openElements.currentTagId===gs.OPTION&&e.openElements.pop();break;case gs.SELECT:e.openElements.hasInSelectScope(gs.SELECT)&&(e.openElements.popUntilTagNamePopped(gs.SELECT),e._resetInsertionMode());break;case gs.TEMPLATE:zi(e,t)}}function Ia(e,t){e.openElements.tmplCount>0?(e.openElements.popUntilTagNamePopped(gs.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode(),e.onEof(t)):Hi(e,t)}function Na(e,t){var n;if(t.tagID===gs.HTML){if(e.fragmentContext||(e.insertionMode=ki.AFTER_AFTER_BODY),e.options.sourceCodeLocationInfo&&e.openElements.tagIDs[0]===gs.HTML){e._setEndLocation(e.openElements.items[0],t);const r=e.openElements.items[1];r&&!(null===(n=e.treeAdapter.getNodeSourceCodeLocation(r))||void 0===n?void 0:n.endTag)&&e._setEndLocation(r,t)}}else Da(e,t)}function Da(e,t){e.insertionMode=ki.IN_BODY,Wi(e,t)}function Sa(e,t){e.insertionMode=ki.IN_BODY,Wi(e,t)}function ka(e){for(;e.treeAdapter.getNamespaceURI(e.openElements.current)!==ps.HTML&&!e._isIntegrationPoint(e.openElements.currentTagId,e.openElements.current);)e.openElements.pop()}const ba=new Set([_s.AREA,_s.BASE,_s.BASEFONT,_s.BGSOUND,_s.BR,_s.COL,_s.EMBED,_s.FRAME,_s.HR,_s.IMG,_s.INPUT,_s.KEYGEN,_s.LINK,_s.META,_s.PARAM,_s.SOURCE,_s.TRACK,_s.WBR]);const Oa={treeAdapter:ni,scriptingEnabled:!0};function Ra(e,t){return La(e,{...Oa,...t})}function La(e,t){return t.treeAdapter.isElementNode(e)?function(e,t){const n=t.treeAdapter.getTagName(e);return`<${n}${function(e,{treeAdapter:t}){let n="";for(const r of t.getAttrList(e)){if(n+=" ",r.namespace)switch(r.namespace){case ps.XML:n+=`xml:${r.name}`;break;case ps.XMLNS:"xmlns"!==r.name&&(n+="xmlns:"),n+=r.name;break;case ps.XLINK:n+=`xlink:${r.name}`;break;default:n+=`${r.prefix}:${r.name}`}else n+=r.name;n+=`="${Pe(r.value)}"`}return n}(e,t)}>${function(e,t){return t.treeAdapter.isElementNode(e)&&t.treeAdapter.getNamespaceURI(e)===ps.HTML&&ba.has(t.treeAdapter.getTagName(e))}(e,t)?"":`${function(e,t){let n="";const r=t.treeAdapter.isElementNode(e)&&t.treeAdapter.getTagName(e)===_s.TEMPLATE&&t.treeAdapter.getNamespaceURI(e)===ps.HTML?t.treeAdapter.getTemplateContent(e):e,s=t.treeAdapter.getChildNodes(r);if(s)for(const e of s)n+=La(e,t);return n}(e,t)}</${n}>`}`}(e,t):t.treeAdapter.isTextNode(e)?function(e,t){const{treeAdapter:n}=t,r=n.getTextNodeContent(e),s=n.getParentNode(e),i=s&&n.isElementNode(s)&&n.getTagName(s);return i&&n.getNamespaceURI(s)===ps.HTML&&(a=i,o=t.scriptingEnabled,Os.has(a)||o&&a===_s.NOSCRIPT)?r:ve(r);var a,o}(e,t):t.treeAdapter.isCommentNode(e)?function(e,{treeAdapter:t}){return`\x3c!--${t.getCommentNodeContent(e)}--\x3e`}(e,t):t.treeAdapter.isDocumentTypeNode(e)?function(e,{treeAdapter:t}){return`<!DOCTYPE ${t.getDocumentTypeNodeName(e)}>`}(e,t):""}function ya(e){return new z(e)}function Ma(e){const t=e.includes('"')?"'":'"';return t+e+t}const Fa={isCommentNode:te,isElementNode:Z,isTextNode:ee,createDocument(){const e=new X([]);return e["x-mode"]=ms.NO_QUIRKS,e},createDocumentFragment:()=>new X([]),createElement(e,t,n){const r=Object.create(null),s=Object.create(null),i=Object.create(null);for(let e=0;e<n.length;e++){const t=n[e].name;r[t]=n[e].value,s[t]=n[e].namespace,i[t]=n[e].prefix}const a=new K(e,r,[]);return a.namespace=t,a["x-attribsNamespace"]=s,a["x-attribsPrefix"]=i,a},createCommentNode:e=>new V(e),appendChild(e,t){const n=e.children[e.children.length-1];n&&(n.next=t,t.prev=n),e.children.push(t),t.parent=e},insertBefore(e,t,n){const r=e.children.indexOf(n),{prev:s}=n;s&&(s.next=t,t.prev=s),n.prev=t,t.next=n,e.children.splice(r,0,t),t.parent=e},setTemplateContent(e,t){Fa.appendChild(e,t)},getTemplateContent:e=>e.children[0],setDocumentType(e,t,n,r){const s=function(e,t,n){let r="!DOCTYPE ";return e&&(r+=e),t?r+=` PUBLIC ${Ma(t)}`:n&&(r+=" SYSTEM"),n&&(r+=` ${Ma(n)}`),r}(t,n,r);let i=e.children.find((e=>ne(e)&&"!doctype"===e.name));i?i.data=null!=s?s:null:(i=new Q("!doctype",s),Fa.appendChild(e,i)),i["x-name"]=null!=t?t:void 0,i["x-publicId"]=null!=n?n:void 0,i["x-systemId"]=null!=r?r:void 0},setDocumentMode(e,t){e["x-mode"]=t},getDocumentMode:e=>e["x-mode"],detachNode(e){if(e.parent){const t=e.parent.children.indexOf(e),{prev:n,next:r}=e;e.prev=null,e.next=null,n&&(n.next=r),r&&(r.prev=n),e.parent.children.splice(t,1),e.parent=null}},insertText(e,t){const n=e.children[e.children.length-1];n&&ee(n)?n.data+=t:Fa.appendChild(e,ya(t))},insertTextBefore(e,t,n){const r=e.children[e.children.indexOf(n)-1];r&&ee(r)?r.data+=t:Fa.insertBefore(e,ya(t),n)},adoptAttributes(e,t){for(let n=0;n<t.length;n++){const r=t[n].name;void 0===e.attribs[r]&&(e.attribs[r]=t[n].value,e["x-attribsNamespace"][r]=t[n].namespace,e["x-attribsPrefix"][r]=t[n].prefix)}},getFirstChild:e=>e.children[0],getChildNodes:e=>e.children,getParentNode:e=>e.parent,getAttrList:e=>e.attributes,getTagName:e=>e.name,getNamespaceURI:e=>e.namespace,getTextNodeContent:e=>e.data,getCommentNodeContent:e=>e.data,getDocumentTypeNodeName(e){var t;return null!==(t=e["x-name"])&&void 0!==t?t:""},getDocumentTypeNodePublicId(e){var t;return null!==(t=e["x-publicId"])&&void 0!==t?t:""},getDocumentTypeNodeSystemId(e){var t;return null!==(t=e["x-systemId"])&&void 0!==t?t:""},isDocumentTypeNode:e=>ne(e)&&"!doctype"===e.name,setNodeSourceCodeLocation(e,t){t&&(e.startIndex=t.startOffset,e.endIndex=t.endOffset),e.sourceCodeLocation=t},getNodeSourceCodeLocation:e=>e.sourceCodeLocation,updateNodeSourceCodeLocation(e,t){null!=t.endOffset&&(e.endIndex=t.endOffset),e.sourceCodeLocation={...e.sourceCodeLocation,...t}}};function xa(e,t,n,r){const s={scriptingEnabled:"boolean"!=typeof t.scriptingEnabled||t.scriptingEnabled,treeAdapter:Fa,sourceCodeLocationInfo:t.sourceCodeLocationInfo};return n?function(e,t){return yi.parse(e,t)}(e,s):function(e,t,n){"string"==typeof e&&(n=t,t=e,e=null);const r=yi.getFragmentParser(e,n);return r.tokenizer.write(t,!0),r.getFragment()}(r,e,s)}const Pa={treeAdapter:Fa};var va,Ba,wa,Ua;function Ha(e){return e===va.Space||e===va.NewLine||e===va.Tab||e===va.FormFeed||e===va.CarriageReturn}function Ga(e){return e===va.Slash||e===va.Gt||Ha(e)}function qa(e){return e>=va.Zero&&e<=va.Nine}!function(e){e[e.Tab=9]="Tab",e[e.NewLine=10]="NewLine",e[e.FormFeed=12]="FormFeed",e[e.CarriageReturn=13]="CarriageReturn",e[e.Space=32]="Space",e[e.ExclamationMark=33]="ExclamationMark",e[e.Number=35]="Number",e[e.Amp=38]="Amp",e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote",e[e.Dash=45]="Dash",e[e.Slash=47]="Slash",e[e.Zero=48]="Zero",e[e.Nine=57]="Nine",e[e.Semi=59]="Semi",e[e.Lt=60]="Lt",e[e.Eq=61]="Eq",e[e.Gt=62]="Gt",e[e.Questionmark=63]="Questionmark",e[e.UpperA=65]="UpperA",e[e.LowerA=97]="LowerA",e[e.UpperF=70]="UpperF",e[e.LowerF=102]="LowerF",e[e.UpperZ=90]="UpperZ",e[e.LowerZ=122]="LowerZ",e[e.LowerX=120]="LowerX",e[e.OpeningSquareBracket=91]="OpeningSquareBracket"}(va||(va={})),function(e){e[e.Text=1]="Text",e[e.BeforeTagName=2]="BeforeTagName",e[e.InTagName=3]="InTagName",e[e.InSelfClosingTag=4]="InSelfClosingTag",e[e.BeforeClosingTagName=5]="BeforeClosingTagName",e[e.InClosingTagName=6]="InClosingTagName",e[e.AfterClosingTagName=7]="AfterClosingTagName",e[e.BeforeAttributeName=8]="BeforeAttributeName",e[e.InAttributeName=9]="InAttributeName",e[e.AfterAttributeName=10]="AfterAttributeName",e[e.BeforeAttributeValue=11]="BeforeAttributeValue",e[e.InAttributeValueDq=12]="InAttributeValueDq",e[e.InAttributeValueSq=13]="InAttributeValueSq",e[e.InAttributeValueNq=14]="InAttributeValueNq",e[e.BeforeDeclaration=15]="BeforeDeclaration",e[e.InDeclaration=16]="InDeclaration",e[e.InProcessingInstruction=17]="InProcessingInstruction",e[e.BeforeComment=18]="BeforeComment",e[e.CDATASequence=19]="CDATASequence",e[e.InSpecialComment=20]="InSpecialComment",e[e.InCommentLike=21]="InCommentLike",e[e.BeforeSpecialS=22]="BeforeSpecialS",e[e.SpecialStartSequence=23]="SpecialStartSequence",e[e.InSpecialTag=24]="InSpecialTag",e[e.BeforeEntity=25]="BeforeEntity",e[e.BeforeNumericEntity=26]="BeforeNumericEntity",e[e.InNamedEntity=27]="InNamedEntity",e[e.InNumericEntity=28]="InNumericEntity",e[e.InHexEntity=29]="InHexEntity"}(Ba||(Ba={})),(Ua=wa||(wa={}))[Ua.NoValue=0]="NoValue",Ua[Ua.Unquoted=1]="Unquoted",Ua[Ua.Single=2]="Single",Ua[Ua.Double=3]="Double";const Ya={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101])};class ja{constructor({xmlMode:e=!1,decodeEntities:t=!0},n){this.cbs=n,this.state=Ba.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=Ba.Text,this.isSpecial=!1,this.running=!0,this.offset=0,this.currentSequence=void 0,this.sequenceIndex=0,this.trieIndex=0,this.trieCurrent=0,this.entityResult=0,this.entityExcess=0,this.xmlMode=e,this.decodeEntities=t,this.entityTrie=e?ue:le}reset(){this.state=Ba.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=Ba.Text,this.currentSequence=void 0,this.running=!0,this.offset=0}write(e){this.offset+=this.buffer.length,this.buffer=e,this.parse()}end(){this.running&&this.finish()}pause(){this.running=!1}resume(){this.running=!0,this.index<this.buffer.length+this.offset&&this.parse()}getIndex(){return this.index}getSectionStart(){return this.sectionStart}stateText(e){e===va.Lt||!this.decodeEntities&&this.fastForwardTo(va.Lt)?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=Ba.BeforeTagName,this.sectionStart=this.index):this.decodeEntities&&e===va.Amp&&(this.state=Ba.BeforeEntity)}stateSpecialStartSequence(e){const t=this.sequenceIndex===this.currentSequence.length;if(t?Ga(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.isSpecial=!1;this.sequenceIndex=0,this.state=Ba.InTagName,this.stateInTagName(e)}stateInSpecialTag(e){if(this.sequenceIndex===this.currentSequence.length){if(e===va.Gt||Ha(e)){const t=this.index-this.currentSequence.length;if(this.sectionStart<t){const e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}return this.isSpecial=!1,this.sectionStart=t+2,void this.stateInClosingTagName(e)}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===Ya.TitleEnd?this.decodeEntities&&e===va.Amp&&(this.state=Ba.BeforeEntity):this.fastForwardTo(va.Lt)&&(this.sequenceIndex=1):this.sequenceIndex=Number(e===va.Lt)}stateCDATASequence(e){e===Ya.Cdata[this.sequenceIndex]?++this.sequenceIndex===Ya.Cdata.length&&(this.state=Ba.InCommentLike,this.currentSequence=Ya.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=Ba.InDeclaration,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length+this.offset;)if(this.buffer.charCodeAt(this.index-this.offset)===e)return!0;return this.index=this.buffer.length+this.offset-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===Ya.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index,2):this.cbs.oncomment(this.sectionStart,this.index,2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=Ba.Text):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}isTagStartChar(e){return this.xmlMode?!Ga(e):function(e){return e>=va.LowerA&&e<=va.LowerZ||e>=va.UpperA&&e<=va.UpperZ}(e)}startSpecial(e,t){this.isSpecial=!0,this.currentSequence=e,this.sequenceIndex=t,this.state=Ba.SpecialStartSequence}stateBeforeTagName(e){if(e===va.ExclamationMark)this.state=Ba.BeforeDeclaration,this.sectionStart=this.index+1;else if(e===va.Questionmark)this.state=Ba.InProcessingInstruction,this.sectionStart=this.index+1;else if(this.isTagStartChar(e)){const t=32|e;this.sectionStart=this.index,this.xmlMode||t!==Ya.TitleEnd[2]?this.state=this.xmlMode||t!==Ya.ScriptEnd[2]?Ba.InTagName:Ba.BeforeSpecialS:this.startSpecial(Ya.TitleEnd,3)}else e===va.Slash?this.state=Ba.BeforeClosingTagName:(this.state=Ba.Text,this.stateText(e))}stateInTagName(e){Ga(e)&&(this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=Ba.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateBeforeClosingTagName(e){Ha(e)||(e===va.Gt?this.state=Ba.Text:(this.state=this.isTagStartChar(e)?Ba.InClosingTagName:Ba.InSpecialComment,this.sectionStart=this.index))}stateInClosingTagName(e){(e===va.Gt||Ha(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=Ba.AfterClosingTagName,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){(e===va.Gt||this.fastForwardTo(va.Gt))&&(this.state=Ba.Text,this.baseState=Ba.Text,this.sectionStart=this.index+1)}stateBeforeAttributeName(e){e===va.Gt?(this.cbs.onopentagend(this.index),this.isSpecial?(this.state=Ba.InSpecialTag,this.sequenceIndex=0):this.state=Ba.Text,this.baseState=this.state,this.sectionStart=this.index+1):e===va.Slash?this.state=Ba.InSelfClosingTag:Ha(e)||(this.state=Ba.InAttributeName,this.sectionStart=this.index)}stateInSelfClosingTag(e){e===va.Gt?(this.cbs.onselfclosingtag(this.index),this.state=Ba.Text,this.baseState=Ba.Text,this.sectionStart=this.index+1,this.isSpecial=!1):Ha(e)||(this.state=Ba.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateInAttributeName(e){(e===va.Eq||Ga(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.sectionStart=-1,this.state=Ba.AfterAttributeName,this.stateAfterAttributeName(e))}stateAfterAttributeName(e){e===va.Eq?this.state=Ba.BeforeAttributeValue:e===va.Slash||e===va.Gt?(this.cbs.onattribend(wa.NoValue,this.index),this.state=Ba.BeforeAttributeName,this.stateBeforeAttributeName(e)):Ha(e)||(this.cbs.onattribend(wa.NoValue,this.index),this.state=Ba.InAttributeName,this.sectionStart=this.index)}stateBeforeAttributeValue(e){e===va.DoubleQuote?(this.state=Ba.InAttributeValueDq,this.sectionStart=this.index+1):e===va.SingleQuote?(this.state=Ba.InAttributeValueSq,this.sectionStart=this.index+1):Ha(e)||(this.sectionStart=this.index,this.state=Ba.InAttributeValueNq,this.stateInAttributeValueNoQuotes(e))}handleInAttributeValue(e,t){e===t||!this.decodeEntities&&this.fastForwardTo(t)?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(t===va.DoubleQuote?wa.Double:wa.Single,this.index),this.state=Ba.BeforeAttributeName):this.decodeEntities&&e===va.Amp&&(this.baseState=this.state,this.state=Ba.BeforeEntity)}stateInAttributeValueDoubleQuotes(e){this.handleInAttributeValue(e,va.DoubleQuote)}stateInAttributeValueSingleQuotes(e){this.handleInAttributeValue(e,va.SingleQuote)}stateInAttributeValueNoQuotes(e){Ha(e)||e===va.Gt?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(wa.Unquoted,this.index),this.state=Ba.BeforeAttributeName,this.stateBeforeAttributeName(e)):this.decodeEntities&&e===va.Amp&&(this.baseState=this.state,this.state=Ba.BeforeEntity)}stateBeforeDeclaration(e){e===va.OpeningSquareBracket?(this.state=Ba.CDATASequence,this.sequenceIndex=0):this.state=e===va.Dash?Ba.BeforeComment:Ba.InDeclaration}stateInDeclaration(e){(e===va.Gt||this.fastForwardTo(va.Gt))&&(this.cbs.ondeclaration(this.sectionStart,this.index),this.state=Ba.Text,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(e===va.Gt||this.fastForwardTo(va.Gt))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=Ba.Text,this.sectionStart=this.index+1)}stateBeforeComment(e){e===va.Dash?(this.state=Ba.InCommentLike,this.currentSequence=Ya.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=Ba.InDeclaration}stateInSpecialComment(e){(e===va.Gt||this.fastForwardTo(va.Gt))&&(this.cbs.oncomment(this.sectionStart,this.index,0),this.state=Ba.Text,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){const t=32|e;t===Ya.ScriptEnd[3]?this.startSpecial(Ya.ScriptEnd,4):t===Ya.StyleEnd[3]?this.startSpecial(Ya.StyleEnd,4):(this.state=Ba.InTagName,this.stateInTagName(e))}stateBeforeEntity(e){this.entityExcess=1,this.entityResult=0,e===va.Number?this.state=Ba.BeforeNumericEntity:e===va.Amp||(this.trieIndex=0,this.trieCurrent=this.entityTrie[0],this.state=Ba.InNamedEntity,this.stateInNamedEntity(e))}stateInNamedEntity(e){if(this.entityExcess+=1,this.trieIndex=be(this.entityTrie,this.trieCurrent,this.trieIndex+1,e),this.trieIndex<0)return this.emitNamedEntity(),void this.index--;this.trieCurrent=this.entityTrie[this.trieIndex];const t=this.trieCurrent&Te.VALUE_LENGTH;if(t){const n=(t>>14)-1;if(this.allowLegacyEntity()||e===va.Semi){const e=this.index-this.entityExcess+1;e>this.sectionStart&&this.emitPartial(this.sectionStart,e),this.entityResult=this.trieIndex,this.trieIndex+=n,this.entityExcess=0,this.sectionStart=this.index+1,0===n&&this.emitNamedEntity()}else this.trieIndex+=n}}emitNamedEntity(){if(this.state=this.baseState,0===this.entityResult)return;switch((this.entityTrie[this.entityResult]&Te.VALUE_LENGTH)>>14){case 1:this.emitCodePoint(this.entityTrie[this.entityResult]&~Te.VALUE_LENGTH);break;case 2:this.emitCodePoint(this.entityTrie[this.entityResult+1]);break;case 3:this.emitCodePoint(this.entityTrie[this.entityResult+1]),this.emitCodePoint(this.entityTrie[this.entityResult+2])}}stateBeforeNumericEntity(e){(32|e)===va.LowerX?(this.entityExcess++,this.state=Ba.InHexEntity):(this.state=Ba.InNumericEntity,this.stateInNumericEntity(e))}emitNumericEntity(e){const t=this.index-this.entityExcess-1;t+2+Number(this.state===Ba.InHexEntity)!==this.index&&(t>this.sectionStart&&this.emitPartial(this.sectionStart,t),this.sectionStart=this.index+Number(e),this.emitCodePoint(fe(this.entityResult))),this.state=this.baseState}stateInNumericEntity(e){e===va.Semi?this.emitNumericEntity(!0):qa(e)?(this.entityResult=10*this.entityResult+(e-va.Zero),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)}stateInHexEntity(e){e===va.Semi?this.emitNumericEntity(!0):qa(e)?(this.entityResult=16*this.entityResult+(e-va.Zero),this.entityExcess++):!function(e){return e>=va.UpperA&&e<=va.UpperF||e>=va.LowerA&&e<=va.LowerF}(e)?(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--):(this.entityResult=16*this.entityResult+((32|e)-va.LowerA+10),this.entityExcess++)}allowLegacyEntity(){return!this.xmlMode&&(this.baseState===Ba.Text||this.baseState===Ba.InSpecialTag)}cleanup(){this.running&&this.sectionStart!==this.index&&(this.state===Ba.Text||this.state===Ba.InSpecialTag&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):this.state!==Ba.InAttributeValueDq&&this.state!==Ba.InAttributeValueSq&&this.state!==Ba.InAttributeValueNq||(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}shouldContinue(){return this.index<this.buffer.length+this.offset&&this.running}parse(){for(;this.shouldContinue();){const e=this.buffer.charCodeAt(this.index-this.offset);switch(this.state){case Ba.Text:this.stateText(e);break;case Ba.SpecialStartSequence:this.stateSpecialStartSequence(e);break;case Ba.InSpecialTag:this.stateInSpecialTag(e);break;case Ba.CDATASequence:this.stateCDATASequence(e);break;case Ba.InAttributeValueDq:this.stateInAttributeValueDoubleQuotes(e);break;case Ba.InAttributeName:this.stateInAttributeName(e);break;case Ba.InCommentLike:this.stateInCommentLike(e);break;case Ba.InSpecialComment:this.stateInSpecialComment(e);break;case Ba.BeforeAttributeName:this.stateBeforeAttributeName(e);break;case Ba.InTagName:this.stateInTagName(e);break;case Ba.InClosingTagName:this.stateInClosingTagName(e);break;case Ba.BeforeTagName:this.stateBeforeTagName(e);break;case Ba.AfterAttributeName:this.stateAfterAttributeName(e);break;case Ba.InAttributeValueSq:this.stateInAttributeValueSingleQuotes(e);break;case Ba.BeforeAttributeValue:this.stateBeforeAttributeValue(e);break;case Ba.BeforeClosingTagName:this.stateBeforeClosingTagName(e);break;case Ba.AfterClosingTagName:this.stateAfterClosingTagName(e);break;case Ba.BeforeSpecialS:this.stateBeforeSpecialS(e);break;case Ba.InAttributeValueNq:this.stateInAttributeValueNoQuotes(e);break;case Ba.InSelfClosingTag:this.stateInSelfClosingTag(e);break;case Ba.InDeclaration:this.stateInDeclaration(e);break;case Ba.BeforeDeclaration:this.stateBeforeDeclaration(e);break;case Ba.BeforeComment:this.stateBeforeComment(e);break;case Ba.InProcessingInstruction:this.stateInProcessingInstruction(e);break;case Ba.InNamedEntity:this.stateInNamedEntity(e);break;case Ba.BeforeEntity:this.stateBeforeEntity(e);break;case Ba.InHexEntity:this.stateInHexEntity(e);break;case Ba.InNumericEntity:this.stateInNumericEntity(e);break;default:this.stateBeforeNumericEntity(e)}this.index++}this.cleanup()}finish(){this.state===Ba.InNamedEntity&&this.emitNamedEntity(),this.sectionStart<this.index&&this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){const e=this.buffer.length+this.offset;this.state===Ba.InCommentLike?this.currentSequence===Ya.CdataEnd?this.cbs.oncdata(this.sectionStart,e,0):this.cbs.oncomment(this.sectionStart,e,0):this.state===Ba.InNumericEntity&&this.allowLegacyEntity()||this.state===Ba.InHexEntity&&this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state===Ba.InTagName||this.state===Ba.BeforeAttributeName||this.state===Ba.BeforeAttributeValue||this.state===Ba.AfterAttributeName||this.state===Ba.InAttributeName||this.state===Ba.InAttributeValueSq||this.state===Ba.InAttributeValueDq||this.state===Ba.InAttributeValueNq||this.state===Ba.InClosingTagName||this.cbs.ontext(this.sectionStart,e)}emitPartial(e,t){this.baseState!==Ba.Text&&this.baseState!==Ba.InSpecialTag?this.cbs.onattribdata(e,t):this.cbs.ontext(e,t)}emitCodePoint(e){this.baseState!==Ba.Text&&this.baseState!==Ba.InSpecialTag?this.cbs.onattribentity(e):this.cbs.ontextentity(e)}}const za=new Set(["input","option","optgroup","select","button","datalist","textarea"]),Va=new Set(["p"]),Qa=new Set(["thead","tbody"]),$a=new Set(["dd","dt"]),Wa=new Set(["rt","rp"]),Xa=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",Va],["h1",Va],["h2",Va],["h3",Va],["h4",Va],["h5",Va],["h6",Va],["select",za],["input",za],["output",za],["button",za],["datalist",za],["textarea",za],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",$a],["dt",$a],["address",Va],["article",Va],["aside",Va],["blockquote",Va],["details",Va],["div",Va],["dl",Va],["fieldset",Va],["figcaption",Va],["figure",Va],["footer",Va],["form",Va],["header",Va],["hr",Va],["main",Va],["nav",Va],["ol",Va],["pre",Va],["section",Va],["table",Va],["ul",Va],["rt",Wa],["rp",Wa],["tbody",Qa],["tfoot",Qa]]),Ka=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),Za=new Set(["math","svg"]),Ja=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),eo=/\s|\//;class to{constructor(e,t={}){var n,r,s,i,a;this.options=t,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.foreignContext=[],this.buffers=[],this.bufferOffset=0,this.writeIndex=0,this.ended=!1,this.cbs=null!=e?e:{},this.lowerCaseTagNames=null!==(n=t.lowerCaseTags)&&void 0!==n?n:!t.xmlMode,this.lowerCaseAttributeNames=null!==(r=t.lowerCaseAttributeNames)&&void 0!==r?r:!t.xmlMode,this.tokenizer=new(null!==(s=t.Tokenizer)&&void 0!==s?s:ja)(this.options,this),null===(a=(i=this.cbs).onparserinit)||void 0===a||a.call(i,this)}ontext(e,t){var n,r;const s=this.getSlice(e,t);this.endIndex=t-1,null===(r=(n=this.cbs).ontext)||void 0===r||r.call(n,s),this.startIndex=t}ontextentity(e){var t,n;const r=this.tokenizer.getSectionStart();this.endIndex=r-1,null===(n=(t=this.cbs).ontext)||void 0===n||n.call(t,de(e)),this.startIndex=r}isVoidElement(e){return!this.options.xmlMode&&Ka.has(e)}onopentagname(e,t){this.endIndex=t;let n=this.getSlice(e,t);this.lowerCaseTagNames&&(n=n.toLowerCase()),this.emitOpenTag(n)}emitOpenTag(e){var t,n,r,s;this.openTagStart=this.startIndex,this.tagname=e;const i=!this.options.xmlMode&&Xa.get(e);if(i)for(;this.stack.length>0&&i.has(this.stack[this.stack.length-1]);){const e=this.stack.pop();null===(n=(t=this.cbs).onclosetag)||void 0===n||n.call(t,e,!0)}this.isVoidElement(e)||(this.stack.push(e),Za.has(e)?this.foreignContext.push(!0):Ja.has(e)&&this.foreignContext.push(!1)),null===(s=(r=this.cbs).onopentagname)||void 0===s||s.call(r,e),this.cbs.onopentag&&(this.attribs={})}endOpenTag(e){var t,n;this.startIndex=this.openTagStart,this.attribs&&(null===(n=(t=this.cbs).onopentag)||void 0===n||n.call(t,this.tagname,this.attribs,e),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""}onopentagend(e){this.endIndex=e,this.endOpenTag(!1),this.startIndex=e+1}onclosetag(e,t){var n,r,s,i,a,o;this.endIndex=t;let c=this.getSlice(e,t);if(this.lowerCaseTagNames&&(c=c.toLowerCase()),(Za.has(c)||Ja.has(c))&&this.foreignContext.pop(),this.isVoidElement(c))this.options.xmlMode||"br"!==c||(null===(r=(n=this.cbs).onopentagname)||void 0===r||r.call(n,"br"),null===(i=(s=this.cbs).onopentag)||void 0===i||i.call(s,"br",{},!0),null===(o=(a=this.cbs).onclosetag)||void 0===o||o.call(a,"br",!1));else{const e=this.stack.lastIndexOf(c);if(-1!==e)if(this.cbs.onclosetag){let t=this.stack.length-e;for(;t--;)this.cbs.onclosetag(this.stack.pop(),0!==t)}else this.stack.length=e;else this.options.xmlMode||"p"!==c||(this.emitOpenTag("p"),this.closeCurrentTag(!0))}this.startIndex=t+1}onselfclosingtag(e){this.endIndex=e,this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]?(this.closeCurrentTag(!1),this.startIndex=e+1):this.onopentagend(e)}closeCurrentTag(e){var t,n;const r=this.tagname;this.endOpenTag(e),this.stack[this.stack.length-1]===r&&(null===(n=(t=this.cbs).onclosetag)||void 0===n||n.call(t,r,!e),this.stack.pop())}onattribname(e,t){this.startIndex=e;const n=this.getSlice(e,t);this.attribname=this.lowerCaseAttributeNames?n.toLowerCase():n}onattribdata(e,t){this.attribvalue+=this.getSlice(e,t)}onattribentity(e){this.attribvalue+=de(e)}onattribend(e,t){var n,r;this.endIndex=t,null===(r=(n=this.cbs).onattribute)||void 0===r||r.call(n,this.attribname,this.attribvalue,e===wa.Double?'"':e===wa.Single?"'":e===wa.NoValue?void 0:null),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribvalue=""}getInstructionName(e){const t=e.search(eo);let n=t<0?e:e.substr(0,t);return this.lowerCaseTagNames&&(n=n.toLowerCase()),n}ondeclaration(e,t){this.endIndex=t;const n=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){const e=this.getInstructionName(n);this.cbs.onprocessinginstruction(`!${e}`,`!${n}`)}this.startIndex=t+1}onprocessinginstruction(e,t){this.endIndex=t;const n=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){const e=this.getInstructionName(n);this.cbs.onprocessinginstruction(`?${e}`,`?${n}`)}this.startIndex=t+1}oncomment(e,t,n){var r,s,i,a;this.endIndex=t,null===(s=(r=this.cbs).oncomment)||void 0===s||s.call(r,this.getSlice(e,t-n)),null===(a=(i=this.cbs).oncommentend)||void 0===a||a.call(i),this.startIndex=t+1}oncdata(e,t,n){var r,s,i,a,o,c,l,u,h,p;this.endIndex=t;const d=this.getSlice(e,t-n);this.options.xmlMode||this.options.recognizeCDATA?(null===(s=(r=this.cbs).oncdatastart)||void 0===s||s.call(r),null===(a=(i=this.cbs).ontext)||void 0===a||a.call(i,d),null===(c=(o=this.cbs).oncdataend)||void 0===c||c.call(o)):(null===(u=(l=this.cbs).oncomment)||void 0===u||u.call(l,`[CDATA[${d}]]`),null===(p=(h=this.cbs).oncommentend)||void 0===p||p.call(h)),this.startIndex=t+1}onend(){var e,t;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(let e=this.stack.length;e>0;this.cbs.onclosetag(this.stack[--e],!0));}null===(t=(e=this.cbs).onend)||void 0===t||t.call(e)}reset(){var e,t,n,r;null===(t=(e=this.cbs).onreset)||void 0===t||t.call(e),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack.length=0,this.startIndex=0,this.endIndex=0,null===(r=(n=this.cbs).onparserinit)||void 0===r||r.call(n,this),this.buffers.length=0,this.bufferOffset=0,this.writeIndex=0,this.ended=!1}parseComplete(e){this.reset(),this.end(e)}getSlice(e,t){for(;e-this.bufferOffset>=this.buffers[0].length;)this.shiftBuffer();let n=this.buffers[0].slice(e-this.bufferOffset,t-this.bufferOffset);for(;t-this.bufferOffset>this.buffers[0].length;)this.shiftBuffer(),n+=this.buffers[0].slice(0,t-this.bufferOffset);return n}shiftBuffer(){this.bufferOffset+=this.buffers[0].length,this.writeIndex--,this.buffers.shift()}write(e){var t,n;this.ended?null===(n=(t=this.cbs).onerror)||void 0===n||n.call(t,new Error(".write() after done!")):(this.buffers.push(e),this.tokenizer.running&&(this.tokenizer.write(e),this.writeIndex++))}end(e){var t,n;this.ended?null===(n=(t=this.cbs).onerror)||void 0===n||n.call(t,new Error(".end() after done!")):(e&&this.write(e),this.ended=!0,this.tokenizer.end())}pause(){this.tokenizer.pause()}resume(){for(this.tokenizer.resume();this.tokenizer.running&&this.writeIndex<this.buffers.length;)this.tokenizer.write(this.buffers[this.writeIndex++]);this.ended&&this.tokenizer.end()}parseChunk(e){this.write(e)}done(e){this.end(e)}}const no=(ro=(e,t,n,r)=>t.xmlMode||t._useHtmlParser2?function(e,t){const n=new ce(void 0,t);return new to(n,t).end(e),n.root}(e,t):xa(e,t,n,r),function(e,t,n,r){if("undefined"!=typeof Buffer&&Buffer.isBuffer(e)&&(e=e.toString()),"string"==typeof e)return ro(e,t,n,r);const s=e;if(!Array.isArray(s)&&re(s))return s;const i=new X([]);return yr(s,i),i});var ro;const so=(io=no,ao=(e,t)=>t.xmlMode||t._useHtmlParser2?qe(e,t):function(e){const t="length"in e?e:[e];for(let e=0;e<t.length;e+=1){const n=t[e];re(n)&&Array.prototype.splice.call(t,e,1,...n.children)}let n="";for(let e=0;e<t.length;e+=1)n+=Ra(t[e],Pa);return n}(e),function e(t,n,r=!0){if(null==t)throw new Error("cheerio.load() expects a string");const s={...R,...y(n)},i=io(t,s,r,null);class a extends Qr{_make(e,t){const n=o(e,t);return n.prevObject=this,n}_parse(e,t,n,r){return io(e,t,n,r)}_render(e){return ao(e,this.options)}}function o(e,t,n=i,r){if(e&&kt(e))return e;const o={...s,...y(r)},c="string"==typeof n?[io(n,o,!1,null)]:"length"in n?n:[n],l=kt(c)?c:new a(c,null,o);if(l._root=l,!e)return new a(void 0,l,o);const u="string"==typeof e&&yt(e)?io(e,o,!1,null).children:(h=e).name||"root"===h.type||"text"===h.type||"comment"===h.type?[e]:Array.isArray(e)?e:void 0;var h;const p=new a(u,l,o);if(u)return p;if("string"!=typeof e)throw new Error("Unexpected type of selector");let d=e;const f=t?"string"==typeof t?yt(t)?new a([io(t,o,!1,null)],l,o):(d=`${t} ${d}`,l):kt(t)?t:new a(Array.isArray(t)?t:[t],l,o):l;return f?f.find(d):p}return Object.assign(o,St,{load:e,_root:i,_options:s,fn:a.prototype,prototype:a.prototype}),o});var io,ao;so([]);const oo={selector:"h1,h2,h3,h4,h5,h6,ul,ol,li,table,pre,p>img:only-child",selectorRules:{"div,p":({$node:e})=>({queue:e.children()}),"h1,h2,h3,h4,h5,h6":({$node:e,getContent:t})=>({...t(e.contents())}),"ul,ol":({$node:e})=>({queue:e.children(),nesting:!0}),li:({$node:e,getContent:t})=>{const n=e.children().filter("ul,ol");let r;if(e.contents().first().is("div,p"))r=t(e.children().first());else{let s=e.contents();const i=s.index(n);i>=0&&(s=s.slice(0,i)),r=t(s)}return{queue:n,nesting:!0,...r}},"table,pre,p>img:only-child":({$node:e,getContent:t})=>({...t(e)})}},co="markmap: ",lo=/^h[1-6]$/,uo=/^[uo]l$/,ho=/^li$/;function po(e,t){const n={...oo,...t},r=so(e),s=r("body");let i=0;const a={id:i,tag:"",html:"",level:0,parent:0,childrenLevel:0,children:[]},o=[];let c=0;return function e(t,s){t.each(((t,a)=>{var h;const p=r(a),d=null==(h=Object.entries(n.selectorRules).find((([e])=>p.is(e))))?void 0:h[1],f=null==d?void 0:d({$node:p,$:r,getContent:u});if((null==f?void 0:f.queue)&&!f.nesting)return void e(f.queue,s);const E=(m=a.tagName,lo.test(m)?+m[1]:uo.test(m)?8:ho.test(m)?9:7);var m;if(!f)return void(E<=6&&(c=E));if(c>0&&E>c)return;if(!p.is(n.selector))return;c=0;const T=E<=6;let _=p.data();p.children("code:only-child").length&&(_={..._,...p.children().data()});const A=function(e){var t;const{parent:n}=e,r={id:++i,tag:e.tagName,level:e.level,html:e.html,childrenLevel:0,children:e.nesting?[]:void 0,parent:n.id};(null==(t=e.comments)?void 0:t.length)&&(r.comments=e.comments);Object.keys(e.data||{}).length&&(r.data=e.data);n.children&&((0===n.childrenLevel||n.childrenLevel>r.level)&&(n.children=[],n.childrenLevel=r.level),n.childrenLevel===r.level&&n.children.push(r));return r}({parent:s||l(E),nesting:!!f.queue||T,tagName:a.tagName,level:E,html:f.html||"",comments:f.comments,data:_});T&&o.push(A),f.queue&&e(f.queue,A)}))}(s.children()),a;function l(e){let t;for(;(t=o.at(-1))&&t.level>=e;)o.pop();return t||a}function u(e){var t;const n=function(e){const t=[];return e=e.filter(((e,n)=>{if("comment"===n.type){const e=n.data.trim();if(e.startsWith(co))return t.push(e.slice(co.length).trim()),!1}return!0})),{$node:e,comments:t}}(e),s=null==(t=r.html(n.$node))?void 0:t.trimEnd();return{comments:n.comments,html:s}}}const fo={};function Eo(e,t){"string"!=typeof t&&(t=Eo.defaultChars);const n=function(e){let t=fo[e];if(t)return t;t=fo[e]=[];for(let e=0;e<128;e++){const n=String.fromCharCode(e);t.push(n)}for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);t[r]="%"+("0"+r.toString(16).toUpperCase()).slice(-2)}return t}(t);return e.replace(/(%[a-f0-9]{2})+/gi,(function(e){let t="";for(let r=0,s=e.length;r<s;r+=3){const i=parseInt(e.slice(r+1,r+3),16);if(i<128)t+=n[i];else{if(192==(224&i)&&r+3<s){const n=parseInt(e.slice(r+4,r+6),16);if(128==(192&n)){const e=i<<6&1984|63&n;t+=e<128?"��":String.fromCharCode(e),r+=3;continue}}if(224==(240&i)&&r+6<s){const n=parseInt(e.slice(r+4,r+6),16),s=parseInt(e.slice(r+7,r+9),16);if(128==(192&n)&&128==(192&s)){const e=i<<12&61440|n<<6&4032|63&s;t+=e<2048||e>=55296&&e<=57343?"���":String.fromCharCode(e),r+=6;continue}}if(240==(248&i)&&r+9<s){const n=parseInt(e.slice(r+4,r+6),16),s=parseInt(e.slice(r+7,r+9),16),a=parseInt(e.slice(r+10,r+12),16);if(128==(192&n)&&128==(192&s)&&128==(192&a)){let e=i<<18&1835008|n<<12&258048|s<<6&4032|63&a;e<65536||e>1114111?t+="����":(e-=65536,t+=String.fromCharCode(55296+(e>>10),56320+(1023&e))),r+=9;continue}}t+="�"}}return t}))}Eo.defaultChars=";/?:@&=+$,#",Eo.componentChars="";const mo={};function To(e,t,n){"string"!=typeof t&&(n=t,t=To.defaultChars),void 0===n&&(n=!0);const r=function(e){let t=mo[e];if(t)return t;t=mo[e]=[];for(let e=0;e<128;e++){const n=String.fromCharCode(e);/^[0-9a-z]$/i.test(n)?t.push(n):t.push("%"+("0"+e.toString(16).toUpperCase()).slice(-2))}for(let n=0;n<e.length;n++)t[e.charCodeAt(n)]=e[n];return t}(t);let s="";for(let t=0,i=e.length;t<i;t++){const a=e.charCodeAt(t);if(n&&37===a&&t+2<i&&/^[0-9a-f]{2}$/i.test(e.slice(t+1,t+3)))s+=e.slice(t,t+3),t+=2;else if(a<128)s+=r[a];else if(a>=55296&&a<=57343){if(a>=55296&&a<=56319&&t+1<i){const n=e.charCodeAt(t+1);if(n>=56320&&n<=57343){s+=encodeURIComponent(e[t]+e[t+1]),t++;continue}}s+="%EF%BF%BD"}else s+=encodeURIComponent(e[t])}return s}function _o(e){let t="";return t+=e.protocol||"",t+=e.slashes?"//":"",t+=e.auth?e.auth+"@":"",e.hostname&&-1!==e.hostname.indexOf(":")?t+="["+e.hostname+"]":t+=e.hostname||"",t+=e.port?":"+e.port:"",t+=e.pathname||"",t+=e.search||"",t+=e.hash||"",t}function Ao(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}To.defaultChars=";/?:@&=+$,-_.!~*'()#",To.componentChars="-_.!~*'()";const go=/^([a-z0-9.+-]+:)/i,Co=/:[0-9]*$/,Io=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,No=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),Do=["'"].concat(No),So=["%","/","?",";","#"].concat(Do),ko=["/","?","#"],bo=/^[+a-z0-9A-Z_-]{0,63}$/,Oo=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,Ro={javascript:!0,"javascript:":!0},Lo={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function yo(e,t){if(e&&e instanceof Ao)return e;const n=new Ao;return n.parse(e,t),n}Ao.prototype.parse=function(e,t){let n,r,s,i=e;if(i=i.trim(),!t&&1===e.split("#").length){const e=Io.exec(i);if(e)return this.pathname=e[1],e[2]&&(this.search=e[2]),this}let a=go.exec(i);if(a&&(a=a[0],n=a.toLowerCase(),this.protocol=a,i=i.substr(a.length)),(t||a||i.match(/^\/\/[^@\/]+@[^@\/]+/))&&(s="//"===i.substr(0,2),!s||a&&Ro[a]||(i=i.substr(2),this.slashes=!0)),!Ro[a]&&(s||a&&!Lo[a])){let e,t,n=-1;for(let e=0;e<ko.length;e++)r=i.indexOf(ko[e]),-1!==r&&(-1===n||r<n)&&(n=r);t=-1===n?i.lastIndexOf("@"):i.lastIndexOf("@",n),-1!==t&&(e=i.slice(0,t),i=i.slice(t+1),this.auth=e),n=-1;for(let e=0;e<So.length;e++)r=i.indexOf(So[e]),-1!==r&&(-1===n||r<n)&&(n=r);-1===n&&(n=i.length),":"===i[n-1]&&n--;const s=i.slice(0,n);i=i.slice(n),this.parseHost(s),this.hostname=this.hostname||"";const a="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!a){const e=this.hostname.split(/\./);for(let t=0,n=e.length;t<n;t++){const n=e[t];if(n&&!n.match(bo)){let r="";for(let e=0,t=n.length;e<t;e++)n.charCodeAt(e)>127?r+="x":r+=n[e];if(!r.match(bo)){const r=e.slice(0,t),s=e.slice(t+1),a=n.match(Oo);a&&(r.push(a[1]),s.unshift(a[2])),s.length&&(i=s.join(".")+i),this.hostname=r.join(".");break}}}}this.hostname.length>255&&(this.hostname=""),a&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}const o=i.indexOf("#");-1!==o&&(this.hash=i.substr(o),i=i.slice(0,o));const c=i.indexOf("?");return-1!==c&&(this.search=i.substr(c),i=i.slice(0,c)),i&&(this.pathname=i),Lo[n]&&this.hostname&&!this.pathname&&(this.pathname=""),this},Ao.prototype.parseHost=function(e){let t=Co.exec(e);t&&(t=t[0],":"!==t&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)};const Mo=Object.freeze(Object.defineProperty({__proto__:null,decode:Eo,encode:To,format:_o,parse:yo},Symbol.toStringTag,{value:"Module"})),Fo=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,xo=/[\0-\x1F\x7F-\x9F]/,Po=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,vo=/[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/,Bo=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,wo=Object.freeze(Object.defineProperty({__proto__:null,Any:Fo,Cc:xo,Cf:/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/,P:Po,S:vo,Z:Bo},Symbol.toStringTag,{value:"Module"}));function Uo(e){return"[object String]"===function(e){return Object.prototype.toString.call(e)}(e)}const Ho=Object.prototype.hasOwnProperty;function Go(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach((function(n){e[n]=t[n]}))}})),e}function qo(e,t,n){return[].concat(e.slice(0,t),n,e.slice(t+1))}function Yo(e){return!(e>=55296&&e<=57343)&&(!(e>=64976&&e<=65007)&&(65535!=(65535&e)&&65534!=(65535&e)&&(!(e>=0&&e<=8)&&(11!==e&&(!(e>=14&&e<=31)&&(!(e>=127&&e<=159)&&!(e>1114111)))))))}function jo(e){if(e>65535){const t=55296+((e-=65536)>>10),n=56320+(1023&e);return String.fromCharCode(t,n)}return String.fromCharCode(e)}const zo=/\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,Vo=new RegExp(zo.source+"|"+/&([a-z#][a-z0-9]{1,31});/gi.source,"gi"),Qo=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;function $o(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(Vo,(function(e,t,n){return t||function(e,t){if(35===t.charCodeAt(0)&&Qo.test(t)){const n="x"===t[1].toLowerCase()?parseInt(t.slice(2),16):parseInt(t.slice(1),10);return Yo(n)?jo(n):e}const n=Re(e);return n!==e?n:e}(e,n)}))}const Wo=/[&<>"]/,Xo=/[&<>"]/g,Ko={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function Zo(e){return Ko[e]}function Jo(e){return Wo.test(e)?e.replace(Xo,Zo):e}const ec=/[.?*+^$[\]\\(){}|-]/g;function tc(e){switch(e){case 9:case 32:return!0}return!1}function nc(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}function rc(e){return Po.test(e)||vo.test(e)}function sc(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function ic(e){return e=e.trim().replace(/\s+/g," "),"Ṿ"==="ẞ".toLowerCase()&&(e=e.replace(/ẞ/g,"ß")),e.toLowerCase().toUpperCase()}const ac={mdurl:Mo,ucmicro:wo},oc=Object.freeze(Object.defineProperty({__proto__:null,arrayReplaceAt:qo,assign:Go,escapeHtml:Jo,escapeRE:function(e){return e.replace(ec,"\\$&")},fromCodePoint:jo,has:function(e,t){return Ho.call(e,t)},isMdAsciiPunct:sc,isPunctChar:rc,isSpace:tc,isString:Uo,isValidEntityCode:Yo,isWhiteSpace:nc,lib:ac,normalizeReference:ic,unescapeAll:$o,unescapeMd:function(e){return e.indexOf("\\")<0?e:e.replace(zo,"$1")}},Symbol.toStringTag,{value:"Module"}));const cc=Object.freeze(Object.defineProperty({__proto__:null,parseLinkDestination:function(e,t,n){let r,s=t;const i={ok:!1,pos:0,str:""};if(60===e.charCodeAt(s)){for(s++;s<n;){if(r=e.charCodeAt(s),10===r)return i;if(60===r)return i;if(62===r)return i.pos=s+1,i.str=$o(e.slice(t+1,s)),i.ok=!0,i;92===r&&s+1<n?s+=2:s++}return i}let a=0;for(;s<n&&(r=e.charCodeAt(s),32!==r)&&!(r<32||127===r);)if(92===r&&s+1<n){if(32===e.charCodeAt(s+1))break;s+=2}else{if(40===r&&(a++,a>32))return i;if(41===r){if(0===a)break;a--}s++}return t===s||0!==a||(i.str=$o(e.slice(t,s)),i.pos=s,i.ok=!0),i},parseLinkLabel:function(e,t,n){let r,s,i,a;const o=e.posMax,c=e.pos;for(e.pos=t+1,r=1;e.pos<o;){if(i=e.src.charCodeAt(e.pos),93===i&&(r--,0===r)){s=!0;break}if(a=e.pos,e.md.inline.skipToken(e),91===i)if(a===e.pos-1)r++;else if(n)return e.pos=c,-1}let l=-1;return s&&(l=e.pos),e.pos=c,l},parseLinkTitle:function(e,t,n,r){let s,i=t;const a={ok:!1,can_continue:!1,pos:0,str:"",marker:0};if(r)a.str=r.str,a.marker=r.marker;else{if(i>=n)return a;let r=e.charCodeAt(i);if(34!==r&&39!==r&&40!==r)return a;t++,i++,40===r&&(r=41),a.marker=r}for(;i<n;){if(s=e.charCodeAt(i),s===a.marker)return a.pos=i+1,a.str+=$o(e.slice(t,i)),a.ok=!0,a;if(40===s&&41===a.marker)return a;92===s&&i+1<n&&i++,i++}return a.can_continue=!0,a.str+=$o(e.slice(t,i)),a}},Symbol.toStringTag,{value:"Module"})),lc={};function uc(){this.rules=Go({},lc)}function hc(){this.__rules__=[],this.__cache__=null}function pc(e,t,n){this.type=e,this.tag=t,this.attrs=null,this.map=null,this.nesting=n,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}function dc(e,t,n){this.src=e,this.env=n,this.tokens=[],this.inlineMode=!1,this.md=t}lc.code_inline=function(e,t,n,r,s){const i=e[t];return"<code"+s.renderAttrs(i)+">"+Jo(i.content)+"</code>"},lc.code_block=function(e,t,n,r,s){const i=e[t];return"<pre"+s.renderAttrs(i)+"><code>"+Jo(e[t].content)+"</code></pre>\n"},lc.fence=function(e,t,n,r,s){const i=e[t],a=i.info?$o(i.info).trim():"";let o,c="",l="";if(a){const e=a.split(/(\s+)/g);c=e[0],l=e.slice(2).join("")}if(o=n.highlight&&n.highlight(i.content,c,l)||Jo(i.content),0===o.indexOf("<pre"))return o+"\n";if(a){const e=i.attrIndex("class"),t=i.attrs?i.attrs.slice():[];e<0?t.push(["class",n.langPrefix+c]):(t[e]=t[e].slice(),t[e][1]+=" "+n.langPrefix+c);const r={attrs:t};return`<pre><code${s.renderAttrs(r)}>${o}</code></pre>\n`}return`<pre><code${s.renderAttrs(i)}>${o}</code></pre>\n`},lc.image=function(e,t,n,r,s){const i=e[t];return i.attrs[i.attrIndex("alt")][1]=s.renderInlineAsText(i.children,n,r),s.renderToken(e,t,n)},lc.hardbreak=function(e,t,n){return n.xhtmlOut?"<br />\n":"<br>\n"},lc.softbreak=function(e,t,n){return n.breaks?n.xhtmlOut?"<br />\n":"<br>\n":"\n"},lc.text=function(e,t){return Jo(e[t].content)},lc.html_block=function(e,t){return e[t].content},lc.html_inline=function(e,t){return e[t].content},uc.prototype.renderAttrs=function(e){let t,n,r;if(!e.attrs)return"";for(r="",t=0,n=e.attrs.length;t<n;t++)r+=" "+Jo(e.attrs[t][0])+'="'+Jo(e.attrs[t][1])+'"';return r},uc.prototype.renderToken=function(e,t,n){const r=e[t];let s="";if(r.hidden)return"";r.block&&-1!==r.nesting&&t&&e[t-1].hidden&&(s+="\n"),s+=(-1===r.nesting?"</":"<")+r.tag,s+=this.renderAttrs(r),0===r.nesting&&n.xhtmlOut&&(s+=" /");let i=!1;if(r.block&&(i=!0,1===r.nesting&&t+1<e.length)){const n=e[t+1];("inline"===n.type||n.hidden||-1===n.nesting&&n.tag===r.tag)&&(i=!1)}return s+=i?">\n":">",s},uc.prototype.renderInline=function(e,t,n){let r="";const s=this.rules;for(let i=0,a=e.length;i<a;i++){const a=e[i].type;void 0!==s[a]?r+=s[a](e,i,t,n,this):r+=this.renderToken(e,i,t)}return r},uc.prototype.renderInlineAsText=function(e,t,n){let r="";for(let s=0,i=e.length;s<i;s++)switch(e[s].type){case"text":case"html_inline":case"html_block":r+=e[s].content;break;case"image":r+=this.renderInlineAsText(e[s].children,t,n);break;case"softbreak":case"hardbreak":r+="\n"}return r},uc.prototype.render=function(e,t,n){let r="";const s=this.rules;for(let i=0,a=e.length;i<a;i++){const a=e[i].type;"inline"===a?r+=this.renderInline(e[i].children,t,n):void 0!==s[a]?r+=s[a](e,i,t,n,this):r+=this.renderToken(e,i,t,n)}return r},hc.prototype.__find__=function(e){for(let t=0;t<this.__rules__.length;t++)if(this.__rules__[t].name===e)return t;return-1},hc.prototype.__compile__=function(){const e=this,t=[""];e.__rules__.forEach((function(e){e.enabled&&e.alt.forEach((function(e){t.indexOf(e)<0&&t.push(e)}))})),e.__cache__={},t.forEach((function(t){e.__cache__[t]=[],e.__rules__.forEach((function(n){n.enabled&&(t&&n.alt.indexOf(t)<0||e.__cache__[t].push(n.fn))}))}))},hc.prototype.at=function(e,t,n){const r=this.__find__(e),s=n||{};if(-1===r)throw new Error("Parser rule not found: "+e);this.__rules__[r].fn=t,this.__rules__[r].alt=s.alt||[],this.__cache__=null},hc.prototype.before=function(e,t,n,r){const s=this.__find__(e),i=r||{};if(-1===s)throw new Error("Parser rule not found: "+e);this.__rules__.splice(s,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},hc.prototype.after=function(e,t,n,r){const s=this.__find__(e),i=r||{};if(-1===s)throw new Error("Parser rule not found: "+e);this.__rules__.splice(s+1,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},hc.prototype.push=function(e,t,n){const r=n||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:r.alt||[]}),this.__cache__=null},hc.prototype.enable=function(e,t){Array.isArray(e)||(e=[e]);const n=[];return e.forEach((function(e){const r=this.__find__(e);if(r<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[r].enabled=!0,n.push(e)}),this),this.__cache__=null,n},hc.prototype.enableOnly=function(e,t){Array.isArray(e)||(e=[e]),this.__rules__.forEach((function(e){e.enabled=!1})),this.enable(e,t)},hc.prototype.disable=function(e,t){Array.isArray(e)||(e=[e]);const n=[];return e.forEach((function(e){const r=this.__find__(e);if(r<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[r].enabled=!1,n.push(e)}),this),this.__cache__=null,n},hc.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},pc.prototype.attrIndex=function(e){if(!this.attrs)return-1;const t=this.attrs;for(let n=0,r=t.length;n<r;n++)if(t[n][0]===e)return n;return-1},pc.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},pc.prototype.attrSet=function(e,t){const n=this.attrIndex(e),r=[e,t];n<0?this.attrPush(r):this.attrs[n]=r},pc.prototype.attrGet=function(e){const t=this.attrIndex(e);let n=null;return t>=0&&(n=this.attrs[t][1]),n},pc.prototype.attrJoin=function(e,t){const n=this.attrIndex(e);n<0?this.attrPush([e,t]):this.attrs[n][1]=this.attrs[n][1]+" "+t},dc.prototype.Token=pc;const fc=/\r\n?|\n/g,Ec=/\0/g;function mc(e){return/^<\/a\s*>/i.test(e)}const Tc=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,_c=/\((c|tm|r)\)/i,Ac=/\((c|tm|r)\)/gi,gc={c:"©",r:"®",tm:"™"};function Cc(e,t){return gc[t.toLowerCase()]}function Ic(e){let t=0;for(let n=e.length-1;n>=0;n--){const r=e[n];"text"!==r.type||t||(r.content=r.content.replace(Ac,Cc)),"link_open"===r.type&&"auto"===r.info&&t--,"link_close"===r.type&&"auto"===r.info&&t++}}function Nc(e){let t=0;for(let n=e.length-1;n>=0;n--){const r=e[n];"text"!==r.type||t||Tc.test(r.content)&&(r.content=r.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/gm,"$1—").replace(/(^|\s)--(?=\s|$)/gm,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm,"$1–")),"link_open"===r.type&&"auto"===r.info&&t--,"link_close"===r.type&&"auto"===r.info&&t++}}const Dc=/['"]/,Sc=/['"]/g,kc="’";function bc(e,t,n){return e.slice(0,t)+n+e.slice(t+1)}function Oc(e,t){let n;const r=[];for(let s=0;s<e.length;s++){const i=e[s],a=e[s].level;for(n=r.length-1;n>=0&&!(r[n].level<=a);n--);if(r.length=n+1,"text"!==i.type)continue;let o=i.content,c=0,l=o.length;e:for(;c<l;){Sc.lastIndex=c;const u=Sc.exec(o);if(!u)break;let h=!0,p=!0;c=u.index+1;const d="'"===u[0];let f=32;if(u.index-1>=0)f=o.charCodeAt(u.index-1);else for(n=s-1;n>=0&&("softbreak"!==e[n].type&&"hardbreak"!==e[n].type);n--)if(e[n].content){f=e[n].content.charCodeAt(e[n].content.length-1);break}let E=32;if(c<l)E=o.charCodeAt(c);else for(n=s+1;n<e.length&&("softbreak"!==e[n].type&&"hardbreak"!==e[n].type);n++)if(e[n].content){E=e[n].content.charCodeAt(0);break}const m=sc(f)||rc(String.fromCharCode(f)),T=sc(E)||rc(String.fromCharCode(E)),_=nc(f),A=nc(E);if(A?h=!1:T&&(_||m||(h=!1)),_?p=!1:m&&(A||T||(p=!1)),34===E&&'"'===u[0]&&f>=48&&f<=57&&(p=h=!1),h&&p&&(h=m,p=T),h||p){if(p)for(n=r.length-1;n>=0;n--){let h=r[n];if(r[n].level<a)break;if(h.single===d&&r[n].level===a){let a,p;h=r[n],d?(a=t.md.options.quotes[2],p=t.md.options.quotes[3]):(a=t.md.options.quotes[0],p=t.md.options.quotes[1]),i.content=bc(i.content,u.index,p),e[h.token].content=bc(e[h.token].content,h.pos,a),c+=p.length-1,h.token===s&&(c+=a.length-1),o=i.content,l=o.length,r.length=n;continue e}}h?r.push({token:s,pos:u.index,single:d,level:a}):p&&d&&(i.content=bc(i.content,u.index,kc))}else d&&(i.content=bc(i.content,u.index,kc))}}}const Rc=[["normalize",function(e){let t;t=e.src.replace(fc,"\n"),t=t.replace(Ec,"�"),e.src=t}],["block",function(e){let t;e.inlineMode?(t=new e.Token("inline","",0),t.content=e.src,t.map=[0,1],t.children=[],e.tokens.push(t)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}],["inline",function(e){const t=e.tokens;for(let n=0,r=t.length;n<r;n++){const r=t[n];"inline"===r.type&&e.md.inline.parse(r.content,e.md,e.env,r.children)}}],["linkify",function(e){const t=e.tokens;var n;if(e.md.options.linkify)for(let r=0,s=t.length;r<s;r++){if("inline"!==t[r].type||!e.md.linkify.pretest(t[r].content))continue;let s=t[r].children,i=0;for(let a=s.length-1;a>=0;a--){const o=s[a];if("link_close"!==o.type){if("html_inline"===o.type&&(n=o.content,/^<a[>\s]/i.test(n)&&i>0&&i--,mc(o.content)&&i++),!(i>0)&&"text"===o.type&&e.md.linkify.test(o.content)){const n=o.content;let i=e.md.linkify.match(n);const c=[];let l=o.level,u=0;i.length>0&&0===i[0].index&&a>0&&"text_special"===s[a-1].type&&(i=i.slice(1));for(let t=0;t<i.length;t++){const r=i[t].url,s=e.md.normalizeLink(r);if(!e.md.validateLink(s))continue;let a=i[t].text;a=i[t].schema?"mailto:"!==i[t].schema||/^mailto:/i.test(a)?e.md.normalizeLinkText(a):e.md.normalizeLinkText("mailto:"+a).replace(/^mailto:/,""):e.md.normalizeLinkText("http://"+a).replace(/^http:\/\//,"");const o=i[t].index;if(o>u){const t=new e.Token("text","",0);t.content=n.slice(u,o),t.level=l,c.push(t)}const h=new e.Token("link_open","a",1);h.attrs=[["href",s]],h.level=l++,h.markup="linkify",h.info="auto",c.push(h);const p=new e.Token("text","",0);p.content=a,p.level=l,c.push(p);const d=new e.Token("link_close","a",-1);d.level=--l,d.markup="linkify",d.info="auto",c.push(d),u=i[t].lastIndex}if(u<n.length){const t=new e.Token("text","",0);t.content=n.slice(u),t.level=l,c.push(t)}t[r].children=s=qo(s,a,c)}}else for(a--;s[a].level!==o.level&&"link_open"!==s[a].type;)a--}}}],["replacements",function(e){let t;if(e.md.options.typographer)for(t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&(_c.test(e.tokens[t].content)&&Ic(e.tokens[t].children),Tc.test(e.tokens[t].content)&&Nc(e.tokens[t].children))}],["smartquotes",function(e){if(e.md.options.typographer)for(let t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&Dc.test(e.tokens[t].content)&&Oc(e.tokens[t].children,e)}],["text_join",function(e){let t,n;const r=e.tokens,s=r.length;for(let e=0;e<s;e++){if("inline"!==r[e].type)continue;const s=r[e].children,i=s.length;for(t=0;t<i;t++)"text_special"===s[t].type&&(s[t].type="text");for(t=n=0;t<i;t++)"text"===s[t].type&&t+1<i&&"text"===s[t+1].type?s[t+1].content=s[t].content+s[t+1].content:(t!==n&&(s[n]=s[t]),n++);t!==n&&(s.length=n)}}]];function Lc(){this.ruler=new hc;for(let e=0;e<Rc.length;e++)this.ruler.push(Rc[e][0],Rc[e][1])}function yc(e,t,n,r){this.src=e,this.md=t,this.env=n,this.tokens=r,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0;const s=this.src;for(let e=0,t=0,n=0,r=0,i=s.length,a=!1;t<i;t++){const o=s.charCodeAt(t);if(!a){if(tc(o)){n++,9===o?r+=4-r%4:r++;continue}a=!0}10!==o&&t!==i-1||(10!==o&&t++,this.bMarks.push(e),this.eMarks.push(t),this.tShift.push(n),this.sCount.push(r),this.bsCount.push(0),a=!1,n=0,r=0,e=t+1)}this.bMarks.push(s.length),this.eMarks.push(s.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}Lc.prototype.process=function(e){const t=this.ruler.getRules("");for(let n=0,r=t.length;n<r;n++)t[n](e)},Lc.prototype.State=dc,yc.prototype.push=function(e,t,n){const r=new pc(e,t,n);return r.block=!0,n<0&&this.level--,r.level=this.level,n>0&&this.level++,this.tokens.push(r),r},yc.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},yc.prototype.skipEmptyLines=function(e){for(let t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},yc.prototype.skipSpaces=function(e){for(let t=this.src.length;e<t;e++){if(!tc(this.src.charCodeAt(e)))break}return e},yc.prototype.skipSpacesBack=function(e,t){if(e<=t)return e;for(;e>t;)if(!tc(this.src.charCodeAt(--e)))return e+1;return e},yc.prototype.skipChars=function(e,t){for(let n=this.src.length;e<n&&this.src.charCodeAt(e)===t;e++);return e},yc.prototype.skipCharsBack=function(e,t,n){if(e<=n)return e;for(;e>n;)if(t!==this.src.charCodeAt(--e))return e+1;return e},yc.prototype.getLines=function(e,t,n,r){if(e>=t)return"";const s=new Array(t-e);for(let i=0,a=e;a<t;a++,i++){let e=0;const o=this.bMarks[a];let c,l=o;for(c=a+1<t||r?this.eMarks[a]+1:this.eMarks[a];l<c&&e<n;){const t=this.src.charCodeAt(l);if(tc(t))9===t?e+=4-(e+this.bsCount[a])%4:e++;else{if(!(l-o<this.tShift[a]))break;e++}l++}s[i]=e>n?new Array(e-n+1).join(" ")+this.src.slice(l,c):this.src.slice(l,c)}return s.join("")},yc.prototype.Token=pc;function Mc(e,t){const n=e.bMarks[t]+e.tShift[t],r=e.eMarks[t];return e.src.slice(n,r)}function Fc(e){const t=[],n=e.length;let r=0,s=e.charCodeAt(r),i=!1,a=0,o="";for(;r<n;)124===s&&(i?(o+=e.substring(a,r-1),a=r):(t.push(o+e.substring(a,r)),o="",a=r+1)),i=92===s,r++,s=e.charCodeAt(r);return t.push(o+e.substring(a)),t}function xc(e,t){const n=e.eMarks[t];let r=e.bMarks[t]+e.tShift[t];const s=e.src.charCodeAt(r++);if(42!==s&&45!==s&&43!==s)return-1;if(r<n){if(!tc(e.src.charCodeAt(r)))return-1}return r}function Pc(e,t){const n=e.bMarks[t]+e.tShift[t],r=e.eMarks[t];let s=n;if(s+1>=r)return-1;let i=e.src.charCodeAt(s++);if(i<48||i>57)return-1;for(;;){if(s>=r)return-1;if(i=e.src.charCodeAt(s++),!(i>=48&&i<=57)){if(41===i||46===i)break;return-1}if(s-n>=10)return-1}return s<r&&(i=e.src.charCodeAt(s),!tc(i))?-1:s}const vc="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",Bc="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",wc=new RegExp("^(?:"+vc+"|"+Bc+"|\x3c!---?>|\x3c!--(?:[^-]|-[^-]|--[^>])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Za-z][^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"),Uc=new RegExp("^(?:"+vc+"|"+Bc+")"),Hc=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"].join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(Uc.source+"\\s*$"),/^$/,!1]];const Gc=[["table",function(e,t,n,r){if(t+2>n)return!1;let s=t+1;if(e.sCount[s]<e.blkIndent)return!1;if(e.sCount[s]-e.blkIndent>=4)return!1;let i=e.bMarks[s]+e.tShift[s];if(i>=e.eMarks[s])return!1;const a=e.src.charCodeAt(i++);if(124!==a&&45!==a&&58!==a)return!1;if(i>=e.eMarks[s])return!1;const o=e.src.charCodeAt(i++);if(124!==o&&45!==o&&58!==o&&!tc(o))return!1;if(45===a&&tc(o))return!1;for(;i<e.eMarks[s];){const t=e.src.charCodeAt(i);if(124!==t&&45!==t&&58!==t&&!tc(t))return!1;i++}let c=Mc(e,t+1),l=c.split("|");const u=[];for(let e=0;e<l.length;e++){const t=l[e].trim();if(!t){if(0===e||e===l.length-1)continue;return!1}if(!/^:?-+:?$/.test(t))return!1;58===t.charCodeAt(t.length-1)?u.push(58===t.charCodeAt(0)?"center":"right"):58===t.charCodeAt(0)?u.push("left"):u.push("")}if(c=Mc(e,t).trim(),-1===c.indexOf("|"))return!1;if(e.sCount[t]-e.blkIndent>=4)return!1;l=Fc(c),l.length&&""===l[0]&&l.shift(),l.length&&""===l[l.length-1]&&l.pop();const h=l.length;if(0===h||h!==u.length)return!1;if(r)return!0;const p=e.parentType;e.parentType="table";const d=e.md.block.ruler.getRules("blockquote"),f=[t,0];e.push("table_open","table",1).map=f,e.push("thead_open","thead",1).map=[t,t+1],e.push("tr_open","tr",1).map=[t,t+1];for(let t=0;t<l.length;t++){const n=e.push("th_open","th",1);u[t]&&(n.attrs=[["style","text-align:"+u[t]]]);const r=e.push("inline","",0);r.content=l[t].trim(),r.children=[],e.push("th_close","th",-1)}let E;e.push("tr_close","tr",-1),e.push("thead_close","thead",-1);let m=0;for(s=t+2;s<n&&!(e.sCount[s]<e.blkIndent);s++){let r=!1;for(let t=0,i=d.length;t<i;t++)if(d[t](e,s,n,!0)){r=!0;break}if(r)break;if(c=Mc(e,s).trim(),!c)break;if(e.sCount[s]-e.blkIndent>=4)break;if(l=Fc(c),l.length&&""===l[0]&&l.shift(),l.length&&""===l[l.length-1]&&l.pop(),m+=h-l.length,m>65536)break;if(s===t+2){e.push("tbody_open","tbody",1).map=E=[t+2,0]}e.push("tr_open","tr",1).map=[s,s+1];for(let t=0;t<h;t++){const n=e.push("td_open","td",1);u[t]&&(n.attrs=[["style","text-align:"+u[t]]]);const r=e.push("inline","",0);r.content=l[t]?l[t].trim():"",r.children=[],e.push("td_close","td",-1)}e.push("tr_close","tr",-1)}return E&&(e.push("tbody_close","tbody",-1),E[1]=s),e.push("table_close","table",-1),f[1]=s,e.parentType=p,e.line=s,!0},["paragraph","reference"]],["code",function(e,t,n){if(e.sCount[t]-e.blkIndent<4)return!1;let r=t+1,s=r;for(;r<n;)if(e.isEmpty(r))r++;else{if(!(e.sCount[r]-e.blkIndent>=4))break;r++,s=r}e.line=s;const i=e.push("code_block","code",0);return i.content=e.getLines(t,s,4+e.blkIndent,!1)+"\n",i.map=[t,e.line],!0}],["fence",function(e,t,n,r){let s=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(s+3>i)return!1;const a=e.src.charCodeAt(s);if(126!==a&&96!==a)return!1;let o=s;s=e.skipChars(s,a);let c=s-o;if(c<3)return!1;const l=e.src.slice(o,s),u=e.src.slice(s,i);if(96===a&&u.indexOf(String.fromCharCode(a))>=0)return!1;if(r)return!0;let h=t,p=!1;for(;(h++,!(h>=n))&&(s=o=e.bMarks[h]+e.tShift[h],i=e.eMarks[h],!(s<i&&e.sCount[h]<e.blkIndent));)if(e.src.charCodeAt(s)===a&&!(e.sCount[h]-e.blkIndent>=4||(s=e.skipChars(s,a),s-o<c||(s=e.skipSpaces(s),s<i)))){p=!0;break}c=e.sCount[t],e.line=h+(p?1:0);const d=e.push("fence","code",0);return d.info=u,d.content=e.getLines(t+1,h,c,!0),d.markup=l,d.map=[t,e.line],!0},["paragraph","reference","blockquote","list"]],["blockquote",function(e,t,n,r){let s=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];const a=e.lineMax;if(e.sCount[t]-e.blkIndent>=4)return!1;if(62!==e.src.charCodeAt(s))return!1;if(r)return!0;const o=[],c=[],l=[],u=[],h=e.md.block.ruler.getRules("blockquote"),p=e.parentType;e.parentType="blockquote";let d,f=!1;for(d=t;d<n;d++){const t=e.sCount[d]<e.blkIndent;if(s=e.bMarks[d]+e.tShift[d],i=e.eMarks[d],s>=i)break;if(62===e.src.charCodeAt(s++)&&!t){let t,n,r=e.sCount[d]+1;32===e.src.charCodeAt(s)?(s++,r++,n=!1,t=!0):9===e.src.charCodeAt(s)?(t=!0,(e.bsCount[d]+r)%4==3?(s++,r++,n=!1):n=!0):t=!1;let a=r;for(o.push(e.bMarks[d]),e.bMarks[d]=s;s<i;){const t=e.src.charCodeAt(s);if(!tc(t))break;9===t?a+=4-(a+e.bsCount[d]+(n?1:0))%4:a++,s++}f=s>=i,c.push(e.bsCount[d]),e.bsCount[d]=e.sCount[d]+1+(t?1:0),l.push(e.sCount[d]),e.sCount[d]=a-r,u.push(e.tShift[d]),e.tShift[d]=s-e.bMarks[d];continue}if(f)break;let r=!1;for(let t=0,s=h.length;t<s;t++)if(h[t](e,d,n,!0)){r=!0;break}if(r){e.lineMax=d,0!==e.blkIndent&&(o.push(e.bMarks[d]),c.push(e.bsCount[d]),u.push(e.tShift[d]),l.push(e.sCount[d]),e.sCount[d]-=e.blkIndent);break}o.push(e.bMarks[d]),c.push(e.bsCount[d]),u.push(e.tShift[d]),l.push(e.sCount[d]),e.sCount[d]=-1}const E=e.blkIndent;e.blkIndent=0;const m=e.push("blockquote_open","blockquote",1);m.markup=">";const T=[t,0];m.map=T,e.md.block.tokenize(e,t,d),e.push("blockquote_close","blockquote",-1).markup=">",e.lineMax=a,e.parentType=p,T[1]=e.line;for(let n=0;n<u.length;n++)e.bMarks[n+t]=o[n],e.tShift[n+t]=u[n],e.sCount[n+t]=l[n],e.bsCount[n+t]=c[n];return e.blkIndent=E,!0},["paragraph","reference","blockquote","list"]],["hr",function(e,t,n,r){const s=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;let i=e.bMarks[t]+e.tShift[t];const a=e.src.charCodeAt(i++);if(42!==a&&45!==a&&95!==a)return!1;let o=1;for(;i<s;){const t=e.src.charCodeAt(i++);if(t!==a&&!tc(t))return!1;t===a&&o++}if(o<3)return!1;if(r)return!0;e.line=t+1;const c=e.push("hr","hr",0);return c.map=[t,e.line],c.markup=Array(o+1).join(String.fromCharCode(a)),!0},["paragraph","reference","blockquote","list"]],["list",function(e,t,n,r){let s,i,a,o,c=t,l=!0;if(e.sCount[c]-e.blkIndent>=4)return!1;if(e.listIndent>=0&&e.sCount[c]-e.listIndent>=4&&e.sCount[c]<e.blkIndent)return!1;let u,h,p,d=!1;if(r&&"paragraph"===e.parentType&&e.sCount[c]>=e.blkIndent&&(d=!0),(p=Pc(e,c))>=0){if(u=!0,a=e.bMarks[c]+e.tShift[c],h=Number(e.src.slice(a,p-1)),d&&1!==h)return!1}else{if(!((p=xc(e,c))>=0))return!1;u=!1}if(d&&e.skipSpaces(p)>=e.eMarks[c])return!1;if(r)return!0;const f=e.src.charCodeAt(p-1),E=e.tokens.length;u?(o=e.push("ordered_list_open","ol",1),1!==h&&(o.attrs=[["start",h]])):o=e.push("bullet_list_open","ul",1);const m=[c,0];o.map=m,o.markup=String.fromCharCode(f);let T=!1;const _=e.md.block.ruler.getRules("list"),A=e.parentType;for(e.parentType="list";c<n;){i=p,s=e.eMarks[c];const t=e.sCount[c]+p-(e.bMarks[c]+e.tShift[c]);let r=t;for(;i<s;){const t=e.src.charCodeAt(i);if(9===t)r+=4-(r+e.bsCount[c])%4;else{if(32!==t)break;r++}i++}const h=i;let d;d=h>=s?1:r-t,d>4&&(d=1);const E=t+d;o=e.push("list_item_open","li",1),o.markup=String.fromCharCode(f);const m=[c,0];o.map=m,u&&(o.info=e.src.slice(a,p-1));const A=e.tight,g=e.tShift[c],C=e.sCount[c],I=e.listIndent;if(e.listIndent=e.blkIndent,e.blkIndent=E,e.tight=!0,e.tShift[c]=h-e.bMarks[c],e.sCount[c]=r,h>=s&&e.isEmpty(c+1)?e.line=Math.min(e.line+2,n):e.md.block.tokenize(e,c,n,!0),e.tight&&!T||(l=!1),T=e.line-c>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=I,e.tShift[c]=g,e.sCount[c]=C,e.tight=A,o=e.push("list_item_close","li",-1),o.markup=String.fromCharCode(f),c=e.line,m[1]=c,c>=n)break;if(e.sCount[c]<e.blkIndent)break;if(e.sCount[c]-e.blkIndent>=4)break;let N=!1;for(let t=0,r=_.length;t<r;t++)if(_[t](e,c,n,!0)){N=!0;break}if(N)break;if(u){if(p=Pc(e,c),p<0)break;a=e.bMarks[c]+e.tShift[c]}else if(p=xc(e,c),p<0)break;if(f!==e.src.charCodeAt(p-1))break}return o=u?e.push("ordered_list_close","ol",-1):e.push("bullet_list_close","ul",-1),o.markup=String.fromCharCode(f),m[1]=c,e.line=c,e.parentType=A,l&&function(e,t){const n=e.level+2;for(let r=t+2,s=e.tokens.length-2;r<s;r++)e.tokens[r].level===n&&"paragraph_open"===e.tokens[r].type&&(e.tokens[r+2].hidden=!0,e.tokens[r].hidden=!0,r+=2)}(e,E),!0},["paragraph","reference","blockquote"]],["reference",function(e,t,n,r){let s=e.bMarks[t]+e.tShift[t],i=e.eMarks[t],a=t+1;if(e.sCount[t]-e.blkIndent>=4)return!1;if(91!==e.src.charCodeAt(s))return!1;function o(t){const n=e.lineMax;if(t>=n||e.isEmpty(t))return null;let r=!1;if(e.sCount[t]-e.blkIndent>3&&(r=!0),e.sCount[t]<0&&(r=!0),!r){const r=e.md.block.ruler.getRules("reference"),s=e.parentType;e.parentType="reference";let i=!1;for(let s=0,a=r.length;s<a;s++)if(r[s](e,t,n,!0)){i=!0;break}if(e.parentType=s,i)return null}const s=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];return e.src.slice(s,i+1)}let c=e.src.slice(s,i+1);i=c.length;let l=-1;for(s=1;s<i;s++){const e=c.charCodeAt(s);if(91===e)return!1;if(93===e){l=s;break}if(10===e){const e=o(a);null!==e&&(c+=e,i=c.length,a++)}else if(92===e&&(s++,s<i&&10===c.charCodeAt(s))){const e=o(a);null!==e&&(c+=e,i=c.length,a++)}}if(l<0||58!==c.charCodeAt(l+1))return!1;for(s=l+2;s<i;s++){const e=c.charCodeAt(s);if(10===e){const e=o(a);null!==e&&(c+=e,i=c.length,a++)}else if(!tc(e))break}const u=e.md.helpers.parseLinkDestination(c,s,i);if(!u.ok)return!1;const h=e.md.normalizeLink(u.str);if(!e.md.validateLink(h))return!1;s=u.pos;const p=s,d=a,f=s;for(;s<i;s++){const e=c.charCodeAt(s);if(10===e){const e=o(a);null!==e&&(c+=e,i=c.length,a++)}else if(!tc(e))break}let E,m=e.md.helpers.parseLinkTitle(c,s,i);for(;m.can_continue;){const t=o(a);if(null===t)break;c+=t,s=i,i=c.length,a++,m=e.md.helpers.parseLinkTitle(c,s,i,m)}for(s<i&&f!==s&&m.ok?(E=m.str,s=m.pos):(E="",s=p,a=d);s<i;){if(!tc(c.charCodeAt(s)))break;s++}if(s<i&&10!==c.charCodeAt(s)&&E)for(E="",s=p,a=d;s<i;){if(!tc(c.charCodeAt(s)))break;s++}if(s<i&&10!==c.charCodeAt(s))return!1;const T=ic(c.slice(1,l));return!!T&&(r||(void 0===e.env.references&&(e.env.references={}),void 0===e.env.references[T]&&(e.env.references[T]={title:E,href:h}),e.line=a),!0)}],["html_block",function(e,t,n,r){let s=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(!e.md.options.html)return!1;if(60!==e.src.charCodeAt(s))return!1;let a=e.src.slice(s,i),o=0;for(;o<Hc.length&&!Hc[o][0].test(a);o++);if(o===Hc.length)return!1;if(r)return Hc[o][2];let c=t+1;if(!Hc[o][1].test(a))for(;c<n&&!(e.sCount[c]<e.blkIndent);c++)if(s=e.bMarks[c]+e.tShift[c],i=e.eMarks[c],a=e.src.slice(s,i),Hc[o][1].test(a)){0!==a.length&&c++;break}e.line=c;const l=e.push("html_block","",0);return l.map=[t,c],l.content=e.getLines(t,c,e.blkIndent,!0),!0},["paragraph","reference","blockquote"]],["heading",function(e,t,n,r){let s=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;let a=e.src.charCodeAt(s);if(35!==a||s>=i)return!1;let o=1;for(a=e.src.charCodeAt(++s);35===a&&s<i&&o<=6;)o++,a=e.src.charCodeAt(++s);if(o>6||s<i&&!tc(a))return!1;if(r)return!0;i=e.skipSpacesBack(i,s);const c=e.skipCharsBack(i,35,s);c>s&&tc(e.src.charCodeAt(c-1))&&(i=c),e.line=t+1;const l=e.push("heading_open","h"+String(o),1);l.markup="########".slice(0,o),l.map=[t,e.line];const u=e.push("inline","",0);return u.content=e.src.slice(s,i).trim(),u.map=[t,e.line],u.children=[],e.push("heading_close","h"+String(o),-1).markup="########".slice(0,o),!0},["paragraph","reference","blockquote"]],["lheading",function(e,t,n){const r=e.md.block.ruler.getRules("paragraph");if(e.sCount[t]-e.blkIndent>=4)return!1;const s=e.parentType;e.parentType="paragraph";let i,a=0,o=t+1;for(;o<n&&!e.isEmpty(o);o++){if(e.sCount[o]-e.blkIndent>3)continue;if(e.sCount[o]>=e.blkIndent){let t=e.bMarks[o]+e.tShift[o];const n=e.eMarks[o];if(t<n&&(i=e.src.charCodeAt(t),(45===i||61===i)&&(t=e.skipChars(t,i),t=e.skipSpaces(t),t>=n))){a=61===i?1:2;break}}if(e.sCount[o]<0)continue;let t=!1;for(let s=0,i=r.length;s<i;s++)if(r[s](e,o,n,!0)){t=!0;break}if(t)break}if(!a)return!1;const c=e.getLines(t,o,e.blkIndent,!1).trim();e.line=o+1;const l=e.push("heading_open","h"+String(a),1);l.markup=String.fromCharCode(i),l.map=[t,e.line];const u=e.push("inline","",0);return u.content=c,u.map=[t,e.line-1],u.children=[],e.push("heading_close","h"+String(a),-1).markup=String.fromCharCode(i),e.parentType=s,!0}],["paragraph",function(e,t,n){const r=e.md.block.ruler.getRules("paragraph"),s=e.parentType;let i=t+1;for(e.parentType="paragraph";i<n&&!e.isEmpty(i);i++){if(e.sCount[i]-e.blkIndent>3)continue;if(e.sCount[i]<0)continue;let t=!1;for(let s=0,a=r.length;s<a;s++)if(r[s](e,i,n,!0)){t=!0;break}if(t)break}const a=e.getLines(t,i,e.blkIndent,!1).trim();e.line=i,e.push("paragraph_open","p",1).map=[t,e.line];const o=e.push("inline","",0);return o.content=a,o.map=[t,e.line],o.children=[],e.push("paragraph_close","p",-1),e.parentType=s,!0}]];function qc(){this.ruler=new hc;for(let e=0;e<Gc.length;e++)this.ruler.push(Gc[e][0],Gc[e][1],{alt:(Gc[e][2]||[]).slice()})}function Yc(e,t,n,r){this.src=e,this.env=n,this.md=t,this.tokens=r,this.tokens_meta=Array(r.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}function jc(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}qc.prototype.tokenize=function(e,t,n){const r=this.ruler.getRules(""),s=r.length,i=e.md.options.maxNesting;let a=t,o=!1;for(;a<n&&(e.line=a=e.skipEmptyLines(a),!(a>=n))&&!(e.sCount[a]<e.blkIndent);){if(e.level>=i){e.line=n;break}const t=e.line;let c=!1;for(let i=0;i<s;i++)if(c=r[i](e,a,n,!1),c){if(t>=e.line)throw new Error("block rule didn't increment state.line");break}if(!c)throw new Error("none of the block rules matched");e.tight=!o,e.isEmpty(e.line-1)&&(o=!0),a=e.line,a<n&&e.isEmpty(a)&&(o=!0,a++,e.line=a)}},qc.prototype.parse=function(e,t,n,r){if(!e)return;const s=new this.State(e,t,n,r);this.tokenize(s,s.line,s.lineMax)},qc.prototype.State=yc,Yc.prototype.pushPending=function(){const e=new pc("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e},Yc.prototype.push=function(e,t,n){this.pending&&this.pushPending();const r=new pc(e,t,n);let s=null;return n<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),r.level=this.level,n>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],s={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(r),this.tokens_meta.push(s),r},Yc.prototype.scanDelims=function(e,t){const n=this.posMax,r=this.src.charCodeAt(e),s=e>0?this.src.charCodeAt(e-1):32;let i=e;for(;i<n&&this.src.charCodeAt(i)===r;)i++;const a=i-e,o=i<n?this.src.charCodeAt(i):32,c=sc(s)||rc(String.fromCharCode(s)),l=sc(o)||rc(String.fromCharCode(o)),u=nc(s),h=nc(o),p=!h&&(!l||u||c),d=!u&&(!c||h||l);return{can_open:p&&(t||!d||c),can_close:d&&(t||!p||l),length:a}},Yc.prototype.Token=pc;const zc=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;const Vc=[];for(let e=0;e<256;e++)Vc.push(0);function Qc(e,t){let n;const r=[],s=t.length;for(let i=0;i<s;i++){const s=t[i];if(126!==s.marker)continue;if(-1===s.end)continue;const a=t[s.end];n=e.tokens[s.token],n.type="s_open",n.tag="s",n.nesting=1,n.markup="~~",n.content="",n=e.tokens[a.token],n.type="s_close",n.tag="s",n.nesting=-1,n.markup="~~",n.content="","text"===e.tokens[a.token-1].type&&"~"===e.tokens[a.token-1].content&&r.push(a.token-1)}for(;r.length;){const t=r.pop();let s=t+1;for(;s<e.tokens.length&&"s_close"===e.tokens[s].type;)s++;s--,t!==s&&(n=e.tokens[s],e.tokens[s]=e.tokens[t],e.tokens[t]=n)}}"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(e){Vc[e.charCodeAt(0)]=1}));const $c={tokenize:function(e,t){const n=e.pos,r=e.src.charCodeAt(n);if(t)return!1;if(126!==r)return!1;const s=e.scanDelims(e.pos,!0);let i=s.length;const a=String.fromCharCode(r);if(i<2)return!1;let o;i%2&&(o=e.push("text","",0),o.content=a,i--);for(let t=0;t<i;t+=2)o=e.push("text","",0),o.content=a+a,e.delimiters.push({marker:r,length:0,token:e.tokens.length-1,end:-1,open:s.can_open,close:s.can_close});return e.pos+=s.length,!0},postProcess:function(e){const t=e.tokens_meta,n=e.tokens_meta.length;Qc(e,e.delimiters);for(let r=0;r<n;r++)t[r]&&t[r].delimiters&&Qc(e,t[r].delimiters)}};function Wc(e,t){for(let n=t.length-1;n>=0;n--){const r=t[n];if(95!==r.marker&&42!==r.marker)continue;if(-1===r.end)continue;const s=t[r.end],i=n>0&&t[n-1].end===r.end+1&&t[n-1].marker===r.marker&&t[n-1].token===r.token-1&&t[r.end+1].token===s.token+1,a=String.fromCharCode(r.marker),o=e.tokens[r.token];o.type=i?"strong_open":"em_open",o.tag=i?"strong":"em",o.nesting=1,o.markup=i?a+a:a,o.content="";const c=e.tokens[s.token];c.type=i?"strong_close":"em_close",c.tag=i?"strong":"em",c.nesting=-1,c.markup=i?a+a:a,c.content="",i&&(e.tokens[t[n-1].token].content="",e.tokens[t[r.end+1].token].content="",n--)}}const Xc={tokenize:function(e,t){const n=e.pos,r=e.src.charCodeAt(n);if(t)return!1;if(95!==r&&42!==r)return!1;const s=e.scanDelims(e.pos,42===r);for(let t=0;t<s.length;t++){e.push("text","",0).content=String.fromCharCode(r),e.delimiters.push({marker:r,length:s.length,token:e.tokens.length-1,end:-1,open:s.can_open,close:s.can_close})}return e.pos+=s.length,!0},postProcess:function(e){const t=e.tokens_meta,n=e.tokens_meta.length;Wc(e,e.delimiters);for(let r=0;r<n;r++)t[r]&&t[r].delimiters&&Wc(e,t[r].delimiters)}};const Kc=/^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,Zc=/^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/;const Jc=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,el=/^&([a-z][a-z0-9]{1,31});/i;function tl(e){const t={},n=e.length;if(!n)return;let r=0,s=-2;const i=[];for(let a=0;a<n;a++){const n=e[a];if(i.push(0),e[r].marker===n.marker&&s===n.token-1||(r=a),s=n.token,n.length=n.length||0,!n.close)continue;t.hasOwnProperty(n.marker)||(t[n.marker]=[-1,-1,-1,-1,-1,-1]);const o=t[n.marker][(n.open?3:0)+n.length%3];let c=r-i[r]-1,l=c;for(;c>o;c-=i[c]+1){const t=e[c];if(t.marker===n.marker&&(t.open&&t.end<0)){let r=!1;if((t.close||n.open)&&(t.length+n.length)%3==0&&(t.length%3==0&&n.length%3==0||(r=!0)),!r){const r=c>0&&!e[c-1].open?i[c-1]+1:0;i[a]=a-c+r,i[c]=r,n.open=!1,t.end=a,t.close=!1,l=-1,s=-2;break}}}-1!==l&&(t[n.marker][(n.open?3:0)+(n.length||0)%3]=l)}}const nl=[["text",function(e,t){let n=e.pos;for(;n<e.posMax&&!jc(e.src.charCodeAt(n));)n++;return n!==e.pos&&(t||(e.pending+=e.src.slice(e.pos,n)),e.pos=n,!0)}],["linkify",function(e,t){if(!e.md.options.linkify)return!1;if(e.linkLevel>0)return!1;const n=e.pos;if(n+3>e.posMax)return!1;if(58!==e.src.charCodeAt(n))return!1;if(47!==e.src.charCodeAt(n+1))return!1;if(47!==e.src.charCodeAt(n+2))return!1;const r=e.pending.match(zc);if(!r)return!1;const s=r[1],i=e.md.linkify.matchAtStart(e.src.slice(n-s.length));if(!i)return!1;let a=i.url;if(a.length<=s.length)return!1;a=a.replace(/\*+$/,"");const o=e.md.normalizeLink(a);if(!e.md.validateLink(o))return!1;if(!t){e.pending=e.pending.slice(0,-s.length);const t=e.push("link_open","a",1);t.attrs=[["href",o]],t.markup="linkify",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(a);const n=e.push("link_close","a",-1);n.markup="linkify",n.info="auto"}return e.pos+=a.length-s.length,!0}],["newline",function(e,t){let n=e.pos;if(10!==e.src.charCodeAt(n))return!1;const r=e.pending.length-1,s=e.posMax;if(!t)if(r>=0&&32===e.pending.charCodeAt(r))if(r>=1&&32===e.pending.charCodeAt(r-1)){let t=r-1;for(;t>=1&&32===e.pending.charCodeAt(t-1);)t--;e.pending=e.pending.slice(0,t),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(n++;n<s&&tc(e.src.charCodeAt(n));)n++;return e.pos=n,!0}],["escape",function(e,t){let n=e.pos;const r=e.posMax;if(92!==e.src.charCodeAt(n))return!1;if(n++,n>=r)return!1;let s=e.src.charCodeAt(n);if(10===s){for(t||e.push("hardbreak","br",0),n++;n<r&&(s=e.src.charCodeAt(n),tc(s));)n++;return e.pos=n,!0}let i=e.src[n];if(s>=55296&&s<=56319&&n+1<r){const t=e.src.charCodeAt(n+1);t>=56320&&t<=57343&&(i+=e.src[n+1],n++)}const a="\\"+i;if(!t){const t=e.push("text_special","",0);s<256&&0!==Vc[s]?t.content=i:t.content=a,t.markup=a,t.info="escape"}return e.pos=n+1,!0}],["backticks",function(e,t){let n=e.pos;if(96!==e.src.charCodeAt(n))return!1;const r=n;n++;const s=e.posMax;for(;n<s&&96===e.src.charCodeAt(n);)n++;const i=e.src.slice(r,n),a=i.length;if(e.backticksScanned&&(e.backticks[a]||0)<=r)return t||(e.pending+=i),e.pos+=a,!0;let o,c=n;for(;-1!==(o=e.src.indexOf("`",c));){for(c=o+1;c<s&&96===e.src.charCodeAt(c);)c++;const r=c-o;if(r===a){if(!t){const t=e.push("code_inline","code",0);t.markup=i,t.content=e.src.slice(n,o).replace(/\n/g," ").replace(/^ (.+) $/,"$1")}return e.pos=c,!0}e.backticks[r]=o}return e.backticksScanned=!0,t||(e.pending+=i),e.pos+=a,!0}],["strikethrough",$c.tokenize],["emphasis",Xc.tokenize],["link",function(e,t){let n,r,s,i,a="",o="",c=e.pos,l=!0;if(91!==e.src.charCodeAt(e.pos))return!1;const u=e.pos,h=e.posMax,p=e.pos+1,d=e.md.helpers.parseLinkLabel(e,e.pos,!0);if(d<0)return!1;let f=d+1;if(f<h&&40===e.src.charCodeAt(f)){for(l=!1,f++;f<h&&(n=e.src.charCodeAt(f),tc(n)||10===n);f++);if(f>=h)return!1;if(c=f,s=e.md.helpers.parseLinkDestination(e.src,f,e.posMax),s.ok){for(a=e.md.normalizeLink(s.str),e.md.validateLink(a)?f=s.pos:a="",c=f;f<h&&(n=e.src.charCodeAt(f),tc(n)||10===n);f++);if(s=e.md.helpers.parseLinkTitle(e.src,f,e.posMax),f<h&&c!==f&&s.ok)for(o=s.str,f=s.pos;f<h&&(n=e.src.charCodeAt(f),tc(n)||10===n);f++);}(f>=h||41!==e.src.charCodeAt(f))&&(l=!0),f++}if(l){if(void 0===e.env.references)return!1;if(f<h&&91===e.src.charCodeAt(f)?(c=f+1,f=e.md.helpers.parseLinkLabel(e,f),f>=0?r=e.src.slice(c,f++):f=d+1):f=d+1,r||(r=e.src.slice(p,d)),i=e.env.references[ic(r)],!i)return e.pos=u,!1;a=i.href,o=i.title}if(!t){e.pos=p,e.posMax=d;const t=[["href",a]];e.push("link_open","a",1).attrs=t,o&&t.push(["title",o]),e.linkLevel++,e.md.inline.tokenize(e),e.linkLevel--,e.push("link_close","a",-1)}return e.pos=f,e.posMax=h,!0}],["image",function(e,t){let n,r,s,i,a,o,c,l,u="";const h=e.pos,p=e.posMax;if(33!==e.src.charCodeAt(e.pos))return!1;if(91!==e.src.charCodeAt(e.pos+1))return!1;const d=e.pos+2,f=e.md.helpers.parseLinkLabel(e,e.pos+1,!1);if(f<0)return!1;if(i=f+1,i<p&&40===e.src.charCodeAt(i)){for(i++;i<p&&(n=e.src.charCodeAt(i),tc(n)||10===n);i++);if(i>=p)return!1;for(l=i,o=e.md.helpers.parseLinkDestination(e.src,i,e.posMax),o.ok&&(u=e.md.normalizeLink(o.str),e.md.validateLink(u)?i=o.pos:u=""),l=i;i<p&&(n=e.src.charCodeAt(i),tc(n)||10===n);i++);if(o=e.md.helpers.parseLinkTitle(e.src,i,e.posMax),i<p&&l!==i&&o.ok)for(c=o.str,i=o.pos;i<p&&(n=e.src.charCodeAt(i),tc(n)||10===n);i++);else c="";if(i>=p||41!==e.src.charCodeAt(i))return e.pos=h,!1;i++}else{if(void 0===e.env.references)return!1;if(i<p&&91===e.src.charCodeAt(i)?(l=i+1,i=e.md.helpers.parseLinkLabel(e,i),i>=0?s=e.src.slice(l,i++):i=f+1):i=f+1,s||(s=e.src.slice(d,f)),a=e.env.references[ic(s)],!a)return e.pos=h,!1;u=a.href,c=a.title}if(!t){r=e.src.slice(d,f);const t=[];e.md.inline.parse(r,e.md,e.env,t);const n=e.push("image","img",0),s=[["src",u],["alt",""]];n.attrs=s,n.children=t,n.content=r,c&&s.push(["title",c])}return e.pos=i,e.posMax=p,!0}],["autolink",function(e,t){let n=e.pos;if(60!==e.src.charCodeAt(n))return!1;const r=e.pos,s=e.posMax;for(;;){if(++n>=s)return!1;const t=e.src.charCodeAt(n);if(60===t)return!1;if(62===t)break}const i=e.src.slice(r+1,n);if(Zc.test(i)){const n=e.md.normalizeLink(i);if(!e.md.validateLink(n))return!1;if(!t){const t=e.push("link_open","a",1);t.attrs=[["href",n]],t.markup="autolink",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(i);const r=e.push("link_close","a",-1);r.markup="autolink",r.info="auto"}return e.pos+=i.length+2,!0}if(Kc.test(i)){const n=e.md.normalizeLink("mailto:"+i);if(!e.md.validateLink(n))return!1;if(!t){const t=e.push("link_open","a",1);t.attrs=[["href",n]],t.markup="autolink",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(i);const r=e.push("link_close","a",-1);r.markup="autolink",r.info="auto"}return e.pos+=i.length+2,!0}return!1}],["html_inline",function(e,t){if(!e.md.options.html)return!1;const n=e.posMax,r=e.pos;if(60!==e.src.charCodeAt(r)||r+2>=n)return!1;const s=e.src.charCodeAt(r+1);if(33!==s&&63!==s&&47!==s&&!function(e){const t=32|e;return t>=97&&t<=122}(s))return!1;const i=e.src.slice(r).match(wc);if(!i)return!1;if(!t){const t=e.push("html_inline","",0);t.content=i[0],a=t.content,/^<a[>\s]/i.test(a)&&e.linkLevel++,function(e){return/^<\/a\s*>/i.test(e)}(t.content)&&e.linkLevel--}var a;return e.pos+=i[0].length,!0}],["entity",function(e,t){const n=e.pos,r=e.posMax;if(38!==e.src.charCodeAt(n))return!1;if(n+1>=r)return!1;if(35===e.src.charCodeAt(n+1)){const r=e.src.slice(n).match(Jc);if(r){if(!t){const t="x"===r[1][0].toLowerCase()?parseInt(r[1].slice(1),16):parseInt(r[1],10),n=e.push("text_special","",0);n.content=Yo(t)?jo(t):jo(65533),n.markup=r[0],n.info="entity"}return e.pos+=r[0].length,!0}}else{const r=e.src.slice(n).match(el);if(r){const n=Re(r[0]);if(n!==r[0]){if(!t){const t=e.push("text_special","",0);t.content=n,t.markup=r[0],t.info="entity"}return e.pos+=r[0].length,!0}}}return!1}]],rl=[["balance_pairs",function(e){const t=e.tokens_meta,n=e.tokens_meta.length;tl(e.delimiters);for(let e=0;e<n;e++)t[e]&&t[e].delimiters&&tl(t[e].delimiters)}],["strikethrough",$c.postProcess],["emphasis",Xc.postProcess],["fragments_join",function(e){let t,n,r=0;const s=e.tokens,i=e.tokens.length;for(t=n=0;t<i;t++)s[t].nesting<0&&r--,s[t].level=r,s[t].nesting>0&&r++,"text"===s[t].type&&t+1<i&&"text"===s[t+1].type?s[t+1].content=s[t].content+s[t+1].content:(t!==n&&(s[n]=s[t]),n++);t!==n&&(s.length=n)}]];function sl(){this.ruler=new hc;for(let e=0;e<nl.length;e++)this.ruler.push(nl[e][0],nl[e][1]);this.ruler2=new hc;for(let e=0;e<rl.length;e++)this.ruler2.push(rl[e][0],rl[e][1])}function il(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){t&&Object.keys(t).forEach((function(n){e[n]=t[n]}))})),e}function al(e){return Object.prototype.toString.call(e)}function ol(e){return"[object Function]"===al(e)}function cl(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}sl.prototype.skipToken=function(e){const t=e.pos,n=this.ruler.getRules(""),r=n.length,s=e.md.options.maxNesting,i=e.cache;if(void 0!==i[t])return void(e.pos=i[t]);let a=!1;if(e.level<s){for(let s=0;s<r;s++)if(e.level++,a=n[s](e,!0),e.level--,a){if(t>=e.pos)throw new Error("inline rule didn't increment state.pos");break}}else e.pos=e.posMax;a||e.pos++,i[t]=e.pos},sl.prototype.tokenize=function(e){const t=this.ruler.getRules(""),n=t.length,r=e.posMax,s=e.md.options.maxNesting;for(;e.pos<r;){const i=e.pos;let a=!1;if(e.level<s)for(let r=0;r<n;r++)if(a=t[r](e,!1),a){if(i>=e.pos)throw new Error("inline rule didn't increment state.pos");break}if(a){if(e.pos>=r)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},sl.prototype.parse=function(e,t,n,r){const s=new this.State(e,t,n,r);this.tokenize(s);const i=this.ruler2.getRules(""),a=i.length;for(let e=0;e<a;e++)i[e](s)},sl.prototype.State=Yc;const ll={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};const ul={"http:":{validate:function(e,t,n){const r=e.slice(t);return n.re.http||(n.re.http=new RegExp("^\\/\\/"+n.re.src_auth+n.re.src_host_port_strict+n.re.src_path,"i")),n.re.http.test(r)?r.match(n.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,t,n){const r=e.slice(t);return n.re.no_http||(n.re.no_http=new RegExp("^"+n.re.src_auth+"(?:localhost|(?:(?:"+n.re.src_domain+")\\.)+"+n.re.src_domain_root+")"+n.re.src_port+n.re.src_host_terminator+n.re.src_path,"i")),n.re.no_http.test(r)?t>=3&&":"===e[t-3]||t>=3&&"/"===e[t-3]?0:r.match(n.re.no_http)[0].length:0}},"mailto:":{validate:function(e,t,n){const r=e.slice(t);return n.re.mailto||(n.re.mailto=new RegExp("^"+n.re.src_email_name+"@"+n.re.src_host_strict,"i")),n.re.mailto.test(r)?r.match(n.re.mailto)[0].length:0}}},hl="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",pl="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function dl(e){const t=e.re=function(e){const t={};e=e||{},t.src_Any=Fo.source,t.src_Cc=xo.source,t.src_Z=Bo.source,t.src_P=Po.source,t.src_ZPCc=[t.src_Z,t.src_P,t.src_Cc].join("|"),t.src_ZCc=[t.src_Z,t.src_Cc].join("|");const n="[><｜]";return t.src_pseudo_letter="(?:(?![><｜]|"+t.src_ZPCc+")"+t.src_Any+")",t.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",t.src_auth="(?:(?:(?!"+t.src_ZCc+"|[@/\\[\\]()]).)+@)?",t.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",t.src_host_terminator="(?=$|[><｜]|"+t.src_ZPCc+")(?!"+(e["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+t.src_ZPCc+"))",t.src_path="(?:[/?#](?:(?!"+t.src_ZCc+"|"+n+"|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!"+t.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+t.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+t.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+t.src_ZCc+'|["]).)+\\"|\\\'(?:(?!'+t.src_ZCc+"|[']).)+\\'|\\'(?="+t.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+t.src_ZCc+"|[.]|$)|"+(e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+t.src_ZCc+"|$)|;(?!"+t.src_ZCc+"|$)|\\!+(?!"+t.src_ZCc+"|[!]|$)|\\?(?!"+t.src_ZCc+"|[?]|$))+|\\/)?",t.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',t.src_xn="xn--[a-z0-9\\-]{1,59}",t.src_domain_root="(?:"+t.src_xn+"|"+t.src_pseudo_letter+"{1,63})",t.src_domain="(?:"+t.src_xn+"|(?:"+t.src_pseudo_letter+")|(?:"+t.src_pseudo_letter+"(?:-|"+t.src_pseudo_letter+"){0,61}"+t.src_pseudo_letter+"))",t.src_host="(?:(?:(?:(?:"+t.src_domain+")\\.)*"+t.src_domain+"))",t.tpl_host_fuzzy="(?:"+t.src_ip4+"|(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%)))",t.tpl_host_no_ip_fuzzy="(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%))",t.src_host_strict=t.src_host+t.src_host_terminator,t.tpl_host_fuzzy_strict=t.tpl_host_fuzzy+t.src_host_terminator,t.src_host_port_strict=t.src_host+t.src_port+t.src_host_terminator,t.tpl_host_port_fuzzy_strict=t.tpl_host_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_port_no_ip_fuzzy_strict=t.tpl_host_no_ip_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+t.src_ZPCc+"|>|$))",t.tpl_email_fuzzy='(^|[><｜]|"|\\(|'+t.src_ZCc+")("+t.src_email_name+"@"+t.tpl_host_fuzzy_strict+")",t.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_fuzzy_strict+t.src_path+")",t.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_no_ip_fuzzy_strict+t.src_path+")",t}(e.__opts__),n=e.__tlds__.slice();function r(e){return e.replace("%TLDS%",t.src_tlds)}e.onCompile(),e.__tlds_replaced__||n.push(hl),n.push(t.src_xn),t.src_tlds=n.join("|"),t.email_fuzzy=RegExp(r(t.tpl_email_fuzzy),"i"),t.link_fuzzy=RegExp(r(t.tpl_link_fuzzy),"i"),t.link_no_ip_fuzzy=RegExp(r(t.tpl_link_no_ip_fuzzy),"i"),t.host_fuzzy_test=RegExp(r(t.tpl_host_fuzzy_test),"i");const s=[];function i(e,t){throw new Error('(LinkifyIt) Invalid schema "'+e+'": '+t)}e.__compiled__={},Object.keys(e.__schemas__).forEach((function(t){const n=e.__schemas__[t];if(null===n)return;const r={validate:null,link:null};if(e.__compiled__[t]=r,"[object Object]"===al(n))return!function(e){return"[object RegExp]"===al(e)}(n.validate)?ol(n.validate)?r.validate=n.validate:i(t,n):r.validate=function(e){return function(t,n){const r=t.slice(n);return e.test(r)?r.match(e)[0].length:0}}(n.validate),void(ol(n.normalize)?r.normalize=n.normalize:n.normalize?i(t,n):r.normalize=function(e,t){t.normalize(e)});!function(e){return"[object String]"===al(e)}(n)?i(t,n):s.push(t)})),s.forEach((function(t){e.__compiled__[e.__schemas__[t]]&&(e.__compiled__[t].validate=e.__compiled__[e.__schemas__[t]].validate,e.__compiled__[t].normalize=e.__compiled__[e.__schemas__[t]].normalize)})),e.__compiled__[""]={validate:null,normalize:function(e,t){t.normalize(e)}};const a=Object.keys(e.__compiled__).filter((function(t){return t.length>0&&e.__compiled__[t]})).map(cl).join("|");e.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+a+")","i"),e.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+a+")","ig"),e.re.schema_at_start=RegExp("^"+e.re.schema_search.source,"i"),e.re.pretest=RegExp("("+e.re.schema_test.source+")|("+e.re.host_fuzzy_test.source+")|@","i"),function(e){e.__index__=-1,e.__text_cache__=""}(e)}function fl(e,t){const n=e.__index__,r=e.__last_index__,s=e.__text_cache__.slice(n,r);this.schema=e.__schema__.toLowerCase(),this.index=n+t,this.lastIndex=r+t,this.raw=s,this.text=s,this.url=s}function El(e,t){const n=new fl(e,t);return e.__compiled__[n.schema].normalize(n,e),n}function ml(e,t){if(!(this instanceof ml))return new ml(e,t);var n;t||(n=e,Object.keys(n||{}).reduce((function(e,t){return e||ll.hasOwnProperty(t)}),!1)&&(t=e,e={})),this.__opts__=il({},ll,t),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=il({},ul,e),this.__compiled__={},this.__tlds__=pl,this.__tlds_replaced__=!1,this.re={},dl(this)}ml.prototype.add=function(e,t){return this.__schemas__[e]=t,dl(this),this},ml.prototype.set=function(e){return this.__opts__=il(this.__opts__,e),this},ml.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;let t,n,r,s,i,a,o,c,l;if(this.re.schema_test.test(e))for(o=this.re.schema_search,o.lastIndex=0;null!==(t=o.exec(e));)if(s=this.testSchemaAt(e,t[2],o.lastIndex),s){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+s;break}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(c=e.search(this.re.host_fuzzy_test),c>=0&&(this.__index__<0||c<this.__index__)&&null!==(n=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(i=n.index+n[1].length,(this.__index__<0||i<this.__index__)&&(this.__schema__="",this.__index__=i,this.__last_index__=n.index+n[0].length))),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&(l=e.indexOf("@"),l>=0&&null!==(r=e.match(this.re.email_fuzzy))&&(i=r.index+r[1].length,a=r.index+r[0].length,(this.__index__<0||i<this.__index__||i===this.__index__&&a>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=i,this.__last_index__=a))),this.__index__>=0},ml.prototype.pretest=function(e){return this.re.pretest.test(e)},ml.prototype.testSchemaAt=function(e,t,n){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,n,this):0},ml.prototype.match=function(e){const t=[];let n=0;this.__index__>=0&&this.__text_cache__===e&&(t.push(El(this,n)),n=this.__last_index__);let r=n?e.slice(n):e;for(;this.test(r);)t.push(El(this,n)),r=r.slice(this.__last_index__),n+=this.__last_index__;return t.length?t:null},ml.prototype.matchAtStart=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return null;const t=this.re.schema_at_start.exec(e);if(!t)return null;const n=this.testSchemaAt(e,t[2],t[0].length);return n?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+n,El(this,0)):null},ml.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?(this.__tlds__=this.__tlds__.concat(e).sort().filter((function(e,t,n){return e!==n[t-1]})).reverse(),dl(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,dl(this),this)},ml.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),"mailto:"!==e.schema||/^mailto:/i.test(e.url)||(e.url="mailto:"+e.url)},ml.prototype.onCompile=function(){};const Tl=2147483647,_l=36,Al=/^xn--/,gl=/[^\0-\x7F]/,Cl=/[\x2E\u3002\uFF0E\uFF61]/g,Il={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},Nl=Math.floor,Dl=String.fromCharCode;function Sl(e){throw new RangeError(Il[e])}function kl(e,t){const n=e.split("@");let r="";n.length>1&&(r=n[0]+"@",e=n[1]);const s=function(e,t){const n=[];let r=e.length;for(;r--;)n[r]=t(e[r]);return n}((e=e.replace(Cl,".")).split("."),t).join(".");return r+s}function bl(e){const t=[];let n=0;const r=e.length;for(;n<r;){const s=e.charCodeAt(n++);if(s>=55296&&s<=56319&&n<r){const r=e.charCodeAt(n++);56320==(64512&r)?t.push(((1023&s)<<10)+(1023&r)+65536):(t.push(s),n--)}else t.push(s)}return t}const Ol=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},Rl=function(e,t,n){let r=0;for(e=n?Nl(e/700):e>>1,e+=Nl(e/t);e>455;r+=_l)e=Nl(e/35);return Nl(r+36*e/(e+38))},Ll=function(e){const t=[],n=e.length;let r=0,s=128,i=72,a=e.lastIndexOf("-");a<0&&(a=0);for(let n=0;n<a;++n)e.charCodeAt(n)>=128&&Sl("not-basic"),t.push(e.charCodeAt(n));for(let c=a>0?a+1:0;c<n;){const a=r;for(let t=1,s=_l;;s+=_l){c>=n&&Sl("invalid-input");const a=(o=e.charCodeAt(c++))>=48&&o<58?o-48+26:o>=65&&o<91?o-65:o>=97&&o<123?o-97:_l;a>=_l&&Sl("invalid-input"),a>Nl((Tl-r)/t)&&Sl("overflow"),r+=a*t;const l=s<=i?1:s>=i+26?26:s-i;if(a<l)break;const u=_l-l;t>Nl(Tl/u)&&Sl("overflow"),t*=u}const l=t.length+1;i=Rl(r-a,l,0==a),Nl(r/l)>Tl-s&&Sl("overflow"),s+=Nl(r/l),r%=l,t.splice(r++,0,s)}var o;return String.fromCodePoint(...t)},yl=function(e){const t=[],n=(e=bl(e)).length;let r=128,s=0,i=72;for(const n of e)n<128&&t.push(Dl(n));const a=t.length;let o=a;for(a&&t.push("-");o<n;){let n=Tl;for(const t of e)t>=r&&t<n&&(n=t);const c=o+1;n-r>Nl((Tl-s)/c)&&Sl("overflow"),s+=(n-r)*c,r=n;for(const n of e)if(n<r&&++s>Tl&&Sl("overflow"),n===r){let e=s;for(let n=_l;;n+=_l){const r=n<=i?1:n>=i+26?26:n-i;if(e<r)break;const s=e-r,a=_l-r;t.push(Dl(Ol(r+s%a,0))),e=Nl(s/a)}t.push(Dl(Ol(e,0))),i=Rl(s,c,o===a),s=0,++o}++s,++r}return t.join("")},Ml={version:"2.3.1",ucs2:{decode:bl,encode:e=>String.fromCodePoint(...e)},decode:Ll,encode:yl,toASCII:function(e){return kl(e,(function(e){return gl.test(e)?"xn--"+yl(e):e}))},toUnicode:function(e){return kl(e,(function(e){return Al.test(e)?Ll(e.slice(4).toLowerCase()):e}))}},Fl={default:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}},zero:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}},commonmark:{options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}}},xl=/^(vbscript|javascript|file|data):/,Pl=/^data:image\/(gif|png|jpeg|webp);/;function vl(e){const t=e.trim().toLowerCase();return!xl.test(t)||Pl.test(t)}const Bl=["http:","https:","mailto:"];function wl(e){const t=yo(e,!0);if(t.hostname&&(!t.protocol||Bl.indexOf(t.protocol)>=0))try{t.hostname=Ml.toASCII(t.hostname)}catch(e){}return To(_o(t))}function Ul(e){const t=yo(e,!0);if(t.hostname&&(!t.protocol||Bl.indexOf(t.protocol)>=0))try{t.hostname=Ml.toUnicode(t.hostname)}catch(e){}return Eo(_o(t),Eo.defaultChars+"%")}function Hl(e,t){if(!(this instanceof Hl))return new Hl(e,t);t||Uo(e)||(t=e||{},e="default"),this.inline=new sl,this.block=new qc,this.core=new Lc,this.renderer=new uc,this.linkify=new ml,this.validateLink=vl,this.normalizeLink=wl,this.normalizeLinkText=Ul,this.utils=oc,this.helpers=Go({},cc),this.options={},this.configure(e),t&&this.set(t)}function Gl(e){function t(e,t){let n;const r=[],s=t.length;for(let i=0;i<s;i++){const s=t[i];if(43!==s.marker)continue;if(-1===s.end)continue;const a=t[s.end];n=e.tokens[s.token],n.type="ins_open",n.tag="ins",n.nesting=1,n.markup="++",n.content="",n=e.tokens[a.token],n.type="ins_close",n.tag="ins",n.nesting=-1,n.markup="++",n.content="","text"===e.tokens[a.token-1].type&&"+"===e.tokens[a.token-1].content&&r.push(a.token-1)}for(;r.length;){const t=r.pop();let s=t+1;for(;s<e.tokens.length&&"ins_close"===e.tokens[s].type;)s++;s--,t!==s&&(n=e.tokens[s],e.tokens[s]=e.tokens[t],e.tokens[t]=n)}}e.inline.ruler.before("emphasis","ins",(function(e,t){const n=e.pos,r=e.src.charCodeAt(n);if(t)return!1;if(43!==r)return!1;const s=e.scanDelims(e.pos,!0);let i=s.length;const a=String.fromCharCode(r);if(i<2)return!1;if(i%2){e.push("text","",0).content=a,i--}for(let t=0;t<i;t+=2){e.push("text","",0).content=a+a,(s.can_open||s.can_close)&&e.delimiters.push({marker:r,length:0,jump:t/2,token:e.tokens.length-1,end:-1,open:s.can_open,close:s.can_close})}return e.pos+=s.length,!0})),e.inline.ruler2.before("emphasis","ins",(function(e){const n=e.tokens_meta,r=(e.tokens_meta||[]).length;t(e,e.delimiters);for(let s=0;s<r;s++)n[s]&&n[s].delimiters&&t(e,n[s].delimiters)}))}function ql(e){function t(e,t){const n=[],r=t.length;for(let s=0;s<r;s++){const r=t[s];if(61!==r.marker)continue;if(-1===r.end)continue;const i=t[r.end],a=e.tokens[r.token];a.type="mark_open",a.tag="mark",a.nesting=1,a.markup="==",a.content="";const o=e.tokens[i.token];o.type="mark_close",o.tag="mark",o.nesting=-1,o.markup="==",o.content="","text"===e.tokens[i.token-1].type&&"="===e.tokens[i.token-1].content&&n.push(i.token-1)}for(;n.length;){const t=n.pop();let r=t+1;for(;r<e.tokens.length&&"mark_close"===e.tokens[r].type;)r++;if(r--,t!==r){const n=e.tokens[r];e.tokens[r]=e.tokens[t],e.tokens[t]=n}}}e.inline.ruler.before("emphasis","mark",(function(e,t){const n=e.pos,r=e.src.charCodeAt(n);if(t)return!1;if(61!==r)return!1;const s=e.scanDelims(e.pos,!0);let i=s.length;const a=String.fromCharCode(r);if(i<2)return!1;if(i%2){e.push("text","",0).content=a,i--}for(let t=0;t<i;t+=2){e.push("text","",0).content=a+a,(s.can_open||s.can_close)&&e.delimiters.push({marker:r,length:0,jump:t/2,token:e.tokens.length-1,end:-1,open:s.can_open,close:s.can_close})}return e.pos+=s.length,!0})),e.inline.ruler2.before("emphasis","mark",(function(e){let n;const r=e.tokens_meta,s=(e.tokens_meta||[]).length;for(t(e,e.delimiters),n=0;n<s;n++)r[n]&&r[n].delimiters&&t(e,r[n].delimiters)}))}Hl.prototype.set=function(e){return Go(this.options,e),this},Hl.prototype.configure=function(e){const t=this;if(Uo(e)){const t=e;if(!(e=Fl[t]))throw new Error('Wrong `markdown-it` preset "'+t+'", check name')}if(!e)throw new Error("Wrong `markdown-it` preset, can't be empty");return e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach((function(n){e.components[n].rules&&t[n].ruler.enableOnly(e.components[n].rules),e.components[n].rules2&&t[n].ruler2.enableOnly(e.components[n].rules2)})),this},Hl.prototype.enable=function(e,t){let n=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){n=n.concat(this[t].ruler.enable(e,!0))}),this),n=n.concat(this.inline.ruler2.enable(e,!0));const r=e.filter((function(e){return n.indexOf(e)<0}));if(r.length&&!t)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+r);return this},Hl.prototype.disable=function(e,t){let n=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){n=n.concat(this[t].ruler.disable(e,!0))}),this),n=n.concat(this.inline.ruler2.disable(e,!0));const r=e.filter((function(e){return n.indexOf(e)<0}));if(r.length&&!t)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+r);return this},Hl.prototype.use=function(e){const t=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,t),this},Hl.prototype.parse=function(e,t){if("string"!=typeof e)throw new Error("Input data should be a String");const n=new this.core.State(e,this,t);return this.core.process(n),n.tokens},Hl.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},Hl.prototype.parseInline=function(e,t){const n=new this.core.State(e,this,t);return n.inlineMode=!0,this.core.process(n),n.tokens},Hl.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)};const Yl=/\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;function jl(e,t){const n=e.posMax,r=e.pos;if(126!==e.src.charCodeAt(r))return!1;if(t)return!1;if(r+2>=n)return!1;e.pos=r+1;let s=!1;for(;e.pos<n;){if(126===e.src.charCodeAt(e.pos)){s=!0;break}e.md.inline.skipToken(e)}if(!s||r+1===e.pos)return e.pos=r,!1;const i=e.src.slice(r+1,e.pos);if(i.match(/(^|[^\\])(\\\\)*\s/))return e.pos=r,!1;e.posMax=e.pos,e.pos=r+1;e.push("sub_open","sub",1).markup="~";e.push("text","",0).content=i.replace(Yl,"$1");return e.push("sub_close","sub",-1).markup="~",e.pos=e.posMax+1,e.posMax=n,!0}function zl(e){e.inline.ruler.after("emphasis","sub",jl)}const Vl=/\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;function Ql(e,t){const n=e.posMax,r=e.pos;if(94!==e.src.charCodeAt(r))return!1;if(t)return!1;if(r+2>=n)return!1;e.pos=r+1;let s=!1;for(;e.pos<n;){if(94===e.src.charCodeAt(e.pos)){s=!0;break}e.md.inline.skipToken(e)}if(!s||r+1===e.pos)return e.pos=r,!1;const i=e.src.slice(r+1,e.pos);if(i.match(/(^|[^\\])(\\\\)*\s/))return e.pos=r,!1;e.posMax=e.pos,e.pos=r+1;e.push("sup_open","sup",1).markup="^";e.push("text","",0).content=i.replace(Vl,"$1");return e.push("sup_close","sup",-1).markup="^",e.pos=e.posMax+1,e.posMax=n,!0}function $l(e){e.inline.ruler.after("emphasis","sup",Ql)}const Wl={" ":'<svg width="16" height="16" viewBox="0 -3 24 24"><path fill-rule="evenodd" d="M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z" clip-rule="evenodd"/></svg>\n'.trim(),x:'<svg width="16" height="16" viewBox="0 -3 24 24"><path d="M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"/></svg>\n'.trim()},Xl={name:"checkbox",transform:e=>(e.parser.tap((e=>{e.core.ruler.before("inline","checkbox",(e=>{for(let t=2;t<e.tokens.length;t+=1){const n=e.tokens[t];if("inline"===n.type&&n.content){const r=e.tokens[t-1].type,s=e.tokens[t-2].type;("heading_open"===r||"paragraph_open"===r&&"list_item_open"===s)&&(n.content=n.content.replace(/^\[(.)\] /,((e,t)=>Wl[t]?`${Wl[t]} `:e)))}}return!1}))})),{})};
/*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT */
function Kl(e){return null==e}var Zl={isNothing:Kl,isObject:function(e){return"object"==typeof e&&null!==e},toArray:function(e){return Array.isArray(e)?e:Kl(e)?[]:[e]},repeat:function(e,t){var n,r="";for(n=0;n<t;n+=1)r+=e;return r},isNegativeZero:function(e){return 0===e&&Number.NEGATIVE_INFINITY===1/e},extend:function(e,t){var n,r,s,i;if(t)for(n=0,r=(i=Object.keys(t)).length;n<r;n+=1)e[s=i[n]]=t[s];return e}};function Jl(e,t){var n="",r=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(n+='in "'+e.mark.name+'" '),n+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!t&&e.mark.snippet&&(n+="\n\n"+e.mark.snippet),r+" "+n):r}function eu(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=Jl(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack||""}eu.prototype=Object.create(Error.prototype),eu.prototype.constructor=eu,eu.prototype.toString=function(e){return this.name+": "+Jl(this,e)};var tu=eu;function nu(e,t,n,r,s){var i="",a="",o=Math.floor(s/2)-1;return r-t>o&&(t=r-o+(i=" ... ").length),n-r>o&&(n=r+o-(a=" ...").length),{str:i+e.slice(t,n).replace(/\t/g,"→")+a,pos:r-t+i.length}}function ru(e,t){return Zl.repeat(" ",t-e.length)+e}var su=function(e,t){if(t=Object.create(t||null),!e.buffer)return null;t.maxLength||(t.maxLength=79),"number"!=typeof t.indent&&(t.indent=1),"number"!=typeof t.linesBefore&&(t.linesBefore=3),"number"!=typeof t.linesAfter&&(t.linesAfter=2);for(var n,r=/\r?\n|\r|\0/g,s=[0],i=[],a=-1;n=r.exec(e.buffer);)i.push(n.index),s.push(n.index+n[0].length),e.position<=n.index&&a<0&&(a=s.length-2);a<0&&(a=s.length-1);var o,c,l="",u=Math.min(e.line+t.linesAfter,i.length).toString().length,h=t.maxLength-(t.indent+u+3);for(o=1;o<=t.linesBefore&&!(a-o<0);o++)c=nu(e.buffer,s[a-o],i[a-o],e.position-(s[a]-s[a-o]),h),l=Zl.repeat(" ",t.indent)+ru((e.line-o+1).toString(),u)+" | "+c.str+"\n"+l;for(c=nu(e.buffer,s[a],i[a],e.position,h),l+=Zl.repeat(" ",t.indent)+ru((e.line+1).toString(),u)+" | "+c.str+"\n",l+=Zl.repeat("-",t.indent+u+3+c.pos)+"^\n",o=1;o<=t.linesAfter&&!(a+o>=i.length);o++)c=nu(e.buffer,s[a+o],i[a+o],e.position-(s[a]-s[a+o]),h),l+=Zl.repeat(" ",t.indent)+ru((e.line+o+1).toString(),u)+" | "+c.str+"\n";return l.replace(/\n$/,"")},iu=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],au=["scalar","sequence","mapping"];var ou=function(e,t){var n,r;if(t=t||{},Object.keys(t).forEach((function(t){if(-1===iu.indexOf(t))throw new tu('Unknown option "'+t+'" is met in definition of "'+e+'" YAML type.')})),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(e){return e},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=(n=t.styleAliases||null,r={},null!==n&&Object.keys(n).forEach((function(e){n[e].forEach((function(t){r[String(t)]=e}))})),r),-1===au.indexOf(this.kind))throw new tu('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')};function cu(e,t){var n=[];return e[t].forEach((function(e){var t=n.length;n.forEach((function(n,r){n.tag===e.tag&&n.kind===e.kind&&n.multi===e.multi&&(t=r)})),n[t]=e})),n}function lu(e){return this.extend(e)}lu.prototype.extend=function(e){var t=[],n=[];if(e instanceof ou)n.push(e);else if(Array.isArray(e))n=n.concat(e);else{if(!e||!Array.isArray(e.implicit)&&!Array.isArray(e.explicit))throw new tu("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");e.implicit&&(t=t.concat(e.implicit)),e.explicit&&(n=n.concat(e.explicit))}t.forEach((function(e){if(!(e instanceof ou))throw new tu("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(e.loadKind&&"scalar"!==e.loadKind)throw new tu("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(e.multi)throw new tu("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")})),n.forEach((function(e){if(!(e instanceof ou))throw new tu("Specified list of YAML types (or a single Type object) contains a non-Type object.")}));var r=Object.create(lu.prototype);return r.implicit=(this.implicit||[]).concat(t),r.explicit=(this.explicit||[]).concat(n),r.compiledImplicit=cu(r,"implicit"),r.compiledExplicit=cu(r,"explicit"),r.compiledTypeMap=function(){var e,t,n={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}};function r(e){e.multi?(n.multi[e.kind].push(e),n.multi.fallback.push(e)):n[e.kind][e.tag]=n.fallback[e.tag]=e}for(e=0,t=arguments.length;e<t;e+=1)arguments[e].forEach(r);return n}(r.compiledImplicit,r.compiledExplicit),r};var uu=lu,hu=new ou("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return null!==e?e:""}}),pu=new ou("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return null!==e?e:[]}}),du=new ou("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return null!==e?e:{}}}),fu=new uu({explicit:[hu,pu,du]});var Eu=new ou("tag:yaml.org,2002:null",{kind:"scalar",resolve:function(e){if(null===e)return!0;var t=e.length;return 1===t&&"~"===e||4===t&&("null"===e||"Null"===e||"NULL"===e)},construct:function(){return null},predicate:function(e){return null===e},represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"},empty:function(){return""}},defaultStyle:"lowercase"});var mu=new ou("tag:yaml.org,2002:bool",{kind:"scalar",resolve:function(e){if(null===e)return!1;var t=e.length;return 4===t&&("true"===e||"True"===e||"TRUE"===e)||5===t&&("false"===e||"False"===e||"FALSE"===e)},construct:function(e){return"true"===e||"True"===e||"TRUE"===e},predicate:function(e){return"[object Boolean]"===Object.prototype.toString.call(e)},represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"});function Tu(e){return 48<=e&&e<=55}function _u(e){return 48<=e&&e<=57}var Au=new ou("tag:yaml.org,2002:int",{kind:"scalar",resolve:function(e){if(null===e)return!1;var t,n,r=e.length,s=0,i=!1;if(!r)return!1;if("-"!==(t=e[s])&&"+"!==t||(t=e[++s]),"0"===t){if(s+1===r)return!0;if("b"===(t=e[++s])){for(s++;s<r;s++)if("_"!==(t=e[s])){if("0"!==t&&"1"!==t)return!1;i=!0}return i&&"_"!==t}if("x"===t){for(s++;s<r;s++)if("_"!==(t=e[s])){if(!(48<=(n=e.charCodeAt(s))&&n<=57||65<=n&&n<=70||97<=n&&n<=102))return!1;i=!0}return i&&"_"!==t}if("o"===t){for(s++;s<r;s++)if("_"!==(t=e[s])){if(!Tu(e.charCodeAt(s)))return!1;i=!0}return i&&"_"!==t}}if("_"===t)return!1;for(;s<r;s++)if("_"!==(t=e[s])){if(!_u(e.charCodeAt(s)))return!1;i=!0}return!(!i||"_"===t)},construct:function(e){var t,n=e,r=1;if(-1!==n.indexOf("_")&&(n=n.replace(/_/g,"")),"-"!==(t=n[0])&&"+"!==t||("-"===t&&(r=-1),t=(n=n.slice(1))[0]),"0"===n)return 0;if("0"===t){if("b"===n[1])return r*parseInt(n.slice(2),2);if("x"===n[1])return r*parseInt(n.slice(2),16);if("o"===n[1])return r*parseInt(n.slice(2),8)}return r*parseInt(n,10)},predicate:function(e){return"[object Number]"===Object.prototype.toString.call(e)&&e%1==0&&!Zl.isNegativeZero(e)},represent:{binary:function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},octal:function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},decimal:function(e){return e.toString(10)},hexadecimal:function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),gu=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");var Cu=/^[-+]?[0-9]+e/;var Iu=new ou("tag:yaml.org,2002:float",{kind:"scalar",resolve:function(e){return null!==e&&!(!gu.test(e)||"_"===e[e.length-1])},construct:function(e){var t,n;return n="-"===(t=e.replace(/_/g,"").toLowerCase())[0]?-1:1,"+-".indexOf(t[0])>=0&&(t=t.slice(1)),".inf"===t?1===n?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:".nan"===t?NaN:n*parseFloat(t,10)},predicate:function(e){return"[object Number]"===Object.prototype.toString.call(e)&&(e%1!=0||Zl.isNegativeZero(e))},represent:function(e,t){var n;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(Zl.isNegativeZero(e))return"-0.0";return n=e.toString(10),Cu.test(n)?n.replace("e",".e"):n},defaultStyle:"lowercase"}),Nu=fu.extend({implicit:[Eu,mu,Au,Iu]}),Du=Nu,Su=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),ku=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");var bu=new ou("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:function(e){return null!==e&&(null!==Su.exec(e)||null!==ku.exec(e))},construct:function(e){var t,n,r,s,i,a,o,c,l=0,u=null;if(null===(t=Su.exec(e))&&(t=ku.exec(e)),null===t)throw new Error("Date resolve error");if(n=+t[1],r=+t[2]-1,s=+t[3],!t[4])return new Date(Date.UTC(n,r,s));if(i=+t[4],a=+t[5],o=+t[6],t[7]){for(l=t[7].slice(0,3);l.length<3;)l+="0";l=+l}return t[9]&&(u=6e4*(60*+t[10]+ +(t[11]||0)),"-"===t[9]&&(u=-u)),c=new Date(Date.UTC(n,r,s,i,a,o,l)),u&&c.setTime(c.getTime()-u),c},instanceOf:Date,represent:function(e){return e.toISOString()}});var Ou=new ou("tag:yaml.org,2002:merge",{kind:"scalar",resolve:function(e){return"<<"===e||null===e}}),Ru="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r";var Lu=new ou("tag:yaml.org,2002:binary",{kind:"scalar",resolve:function(e){if(null===e)return!1;var t,n,r=0,s=e.length,i=Ru;for(n=0;n<s;n++)if(!((t=i.indexOf(e.charAt(n)))>64)){if(t<0)return!1;r+=6}return r%8==0},construct:function(e){var t,n,r=e.replace(/[\r\n=]/g,""),s=r.length,i=Ru,a=0,o=[];for(t=0;t<s;t++)t%4==0&&t&&(o.push(a>>16&255),o.push(a>>8&255),o.push(255&a)),a=a<<6|i.indexOf(r.charAt(t));return 0===(n=s%4*6)?(o.push(a>>16&255),o.push(a>>8&255),o.push(255&a)):18===n?(o.push(a>>10&255),o.push(a>>2&255)):12===n&&o.push(a>>4&255),new Uint8Array(o)},predicate:function(e){return"[object Uint8Array]"===Object.prototype.toString.call(e)},represent:function(e){var t,n,r="",s=0,i=e.length,a=Ru;for(t=0;t<i;t++)t%3==0&&t&&(r+=a[s>>18&63],r+=a[s>>12&63],r+=a[s>>6&63],r+=a[63&s]),s=(s<<8)+e[t];return 0===(n=i%3)?(r+=a[s>>18&63],r+=a[s>>12&63],r+=a[s>>6&63],r+=a[63&s]):2===n?(r+=a[s>>10&63],r+=a[s>>4&63],r+=a[s<<2&63],r+=a[64]):1===n&&(r+=a[s>>2&63],r+=a[s<<4&63],r+=a[64],r+=a[64]),r}}),yu=Object.prototype.hasOwnProperty,Mu=Object.prototype.toString;var Fu=new ou("tag:yaml.org,2002:omap",{kind:"sequence",resolve:function(e){if(null===e)return!0;var t,n,r,s,i,a=[],o=e;for(t=0,n=o.length;t<n;t+=1){if(r=o[t],i=!1,"[object Object]"!==Mu.call(r))return!1;for(s in r)if(yu.call(r,s)){if(i)return!1;i=!0}if(!i)return!1;if(-1!==a.indexOf(s))return!1;a.push(s)}return!0},construct:function(e){return null!==e?e:[]}}),xu=Object.prototype.toString;var Pu=new ou("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:function(e){if(null===e)return!0;var t,n,r,s,i,a=e;for(i=new Array(a.length),t=0,n=a.length;t<n;t+=1){if(r=a[t],"[object Object]"!==xu.call(r))return!1;if(1!==(s=Object.keys(r)).length)return!1;i[t]=[s[0],r[s[0]]]}return!0},construct:function(e){if(null===e)return[];var t,n,r,s,i,a=e;for(i=new Array(a.length),t=0,n=a.length;t<n;t+=1)r=a[t],s=Object.keys(r),i[t]=[s[0],r[s[0]]];return i}}),vu=Object.prototype.hasOwnProperty;var Bu=new ou("tag:yaml.org,2002:set",{kind:"mapping",resolve:function(e){if(null===e)return!0;var t,n=e;for(t in n)if(vu.call(n,t)&&null!==n[t])return!1;return!0},construct:function(e){return null!==e?e:{}}}),wu=Du.extend({implicit:[bu,Ou],explicit:[Lu,Fu,Pu,Bu]}),Uu=Object.prototype.hasOwnProperty,Hu=1,Gu=2,qu=3,Yu=4,ju=1,zu=2,Vu=3,Qu=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,$u=/[\x85\u2028\u2029]/,Wu=/[,\[\]\{\}]/,Xu=/^(?:!|!!|![a-z\-]+!)$/i,Ku=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Zu(e){return Object.prototype.toString.call(e)}function Ju(e){return 10===e||13===e}function eh(e){return 9===e||32===e}function th(e){return 9===e||32===e||10===e||13===e}function nh(e){return 44===e||91===e||93===e||123===e||125===e}function rh(e){var t;return 48<=e&&e<=57?e-48:97<=(t=32|e)&&t<=102?t-97+10:-1}function sh(e){return 48===e?"\0":97===e?"":98===e?"\b":116===e||9===e?"\t":110===e?"\n":118===e?"\v":102===e?"\f":114===e?"\r":101===e?"":32===e?" ":34===e?'"':47===e?"/":92===e?"\\":78===e?"":95===e?" ":76===e?"\u2028":80===e?"\u2029":""}function ih(e){return e<=65535?String.fromCharCode(e):String.fromCharCode(55296+(e-65536>>10),56320+(e-65536&1023))}for(var ah=new Array(256),oh=new Array(256),ch=0;ch<256;ch++)ah[ch]=sh(ch)?1:0,oh[ch]=sh(ch);function lh(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||wu,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}function uh(e,t){var n={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return n.snippet=su(n),new tu(t,n)}function hh(e,t){throw uh(e,t)}function ph(e,t){e.onWarning&&e.onWarning.call(null,uh(e,t))}var dh={YAML:function(e,t,n){var r,s,i;null!==e.version&&hh(e,"duplication of %YAML directive"),1!==n.length&&hh(e,"YAML directive accepts exactly one argument"),null===(r=/^([0-9]+)\.([0-9]+)$/.exec(n[0]))&&hh(e,"ill-formed argument of the YAML directive"),s=parseInt(r[1],10),i=parseInt(r[2],10),1!==s&&hh(e,"unacceptable YAML version of the document"),e.version=n[0],e.checkLineBreaks=i<2,1!==i&&2!==i&&ph(e,"unsupported YAML version of the document")},TAG:function(e,t,n){var r,s;2!==n.length&&hh(e,"TAG directive accepts exactly two arguments"),r=n[0],s=n[1],Xu.test(r)||hh(e,"ill-formed tag handle (first argument) of the TAG directive"),Uu.call(e.tagMap,r)&&hh(e,'there is a previously declared suffix for "'+r+'" tag handle'),Ku.test(s)||hh(e,"ill-formed tag prefix (second argument) of the TAG directive");try{s=decodeURIComponent(s)}catch(t){hh(e,"tag prefix is malformed: "+s)}e.tagMap[r]=s}};function fh(e,t,n,r){var s,i,a,o;if(t<n){if(o=e.input.slice(t,n),r)for(s=0,i=o.length;s<i;s+=1)9===(a=o.charCodeAt(s))||32<=a&&a<=1114111||hh(e,"expected valid JSON character");else Qu.test(o)&&hh(e,"the stream contains non-printable characters");e.result+=o}}function Eh(e,t,n,r){var s,i,a,o;for(Zl.isObject(n)||hh(e,"cannot merge mappings; the provided source object is unacceptable"),a=0,o=(s=Object.keys(n)).length;a<o;a+=1)i=s[a],Uu.call(t,i)||(t[i]=n[i],r[i]=!0)}function mh(e,t,n,r,s,i,a,o,c){var l,u;if(Array.isArray(s))for(l=0,u=(s=Array.prototype.slice.call(s)).length;l<u;l+=1)Array.isArray(s[l])&&hh(e,"nested arrays are not supported inside keys"),"object"==typeof s&&"[object Object]"===Zu(s[l])&&(s[l]="[object Object]");if("object"==typeof s&&"[object Object]"===Zu(s)&&(s="[object Object]"),s=String(s),null===t&&(t={}),"tag:yaml.org,2002:merge"===r)if(Array.isArray(i))for(l=0,u=i.length;l<u;l+=1)Eh(e,t,i[l],n);else Eh(e,t,i,n);else e.json||Uu.call(n,s)||!Uu.call(t,s)||(e.line=a||e.line,e.lineStart=o||e.lineStart,e.position=c||e.position,hh(e,"duplicated mapping key")),"__proto__"===s?Object.defineProperty(t,s,{configurable:!0,enumerable:!0,writable:!0,value:i}):t[s]=i,delete n[s];return t}function Th(e){var t;10===(t=e.input.charCodeAt(e.position))?e.position++:13===t?(e.position++,10===e.input.charCodeAt(e.position)&&e.position++):hh(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}function _h(e,t,n){for(var r=0,s=e.input.charCodeAt(e.position);0!==s;){for(;eh(s);)9===s&&-1===e.firstTabInLine&&(e.firstTabInLine=e.position),s=e.input.charCodeAt(++e.position);if(t&&35===s)do{s=e.input.charCodeAt(++e.position)}while(10!==s&&13!==s&&0!==s);if(!Ju(s))break;for(Th(e),s=e.input.charCodeAt(e.position),r++,e.lineIndent=0;32===s;)e.lineIndent++,s=e.input.charCodeAt(++e.position)}return-1!==n&&0!==r&&e.lineIndent<n&&ph(e,"deficient indentation"),r}function Ah(e){var t,n=e.position;return!(45!==(t=e.input.charCodeAt(n))&&46!==t||t!==e.input.charCodeAt(n+1)||t!==e.input.charCodeAt(n+2)||(n+=3,0!==(t=e.input.charCodeAt(n))&&!th(t)))}function gh(e,t){1===t?e.result+=" ":t>1&&(e.result+=Zl.repeat("\n",t-1))}function Ch(e,t){var n,r,s=e.tag,i=e.anchor,a=[],o=!1;if(-1!==e.firstTabInLine)return!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=a),r=e.input.charCodeAt(e.position);0!==r&&(-1!==e.firstTabInLine&&(e.position=e.firstTabInLine,hh(e,"tab characters must not be used in indentation")),45===r)&&th(e.input.charCodeAt(e.position+1));)if(o=!0,e.position++,_h(e,!0,-1)&&e.lineIndent<=t)a.push(null),r=e.input.charCodeAt(e.position);else if(n=e.line,Dh(e,t,qu,!1,!0),a.push(e.result),_h(e,!0,-1),r=e.input.charCodeAt(e.position),(e.line===n||e.lineIndent>t)&&0!==r)hh(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break;return!!o&&(e.tag=s,e.anchor=i,e.kind="sequence",e.result=a,!0)}function Ih(e){var t,n,r,s,i=!1,a=!1;if(33!==(s=e.input.charCodeAt(e.position)))return!1;if(null!==e.tag&&hh(e,"duplication of a tag property"),60===(s=e.input.charCodeAt(++e.position))?(i=!0,s=e.input.charCodeAt(++e.position)):33===s?(a=!0,n="!!",s=e.input.charCodeAt(++e.position)):n="!",t=e.position,i){do{s=e.input.charCodeAt(++e.position)}while(0!==s&&62!==s);e.position<e.length?(r=e.input.slice(t,e.position),s=e.input.charCodeAt(++e.position)):hh(e,"unexpected end of the stream within a verbatim tag")}else{for(;0!==s&&!th(s);)33===s&&(a?hh(e,"tag suffix cannot contain exclamation marks"):(n=e.input.slice(t-1,e.position+1),Xu.test(n)||hh(e,"named tag handle cannot contain such characters"),a=!0,t=e.position+1)),s=e.input.charCodeAt(++e.position);r=e.input.slice(t,e.position),Wu.test(r)&&hh(e,"tag suffix cannot contain flow indicator characters")}r&&!Ku.test(r)&&hh(e,"tag name cannot contain such characters: "+r);try{r=decodeURIComponent(r)}catch(t){hh(e,"tag name is malformed: "+r)}return i?e.tag=r:Uu.call(e.tagMap,n)?e.tag=e.tagMap[n]+r:"!"===n?e.tag="!"+r:"!!"===n?e.tag="tag:yaml.org,2002:"+r:hh(e,'undeclared tag handle "'+n+'"'),!0}function Nh(e){var t,n;if(38!==(n=e.input.charCodeAt(e.position)))return!1;for(null!==e.anchor&&hh(e,"duplication of an anchor property"),n=e.input.charCodeAt(++e.position),t=e.position;0!==n&&!th(n)&&!nh(n);)n=e.input.charCodeAt(++e.position);return e.position===t&&hh(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}function Dh(e,t,n,r,s){var i,a,o,c,l,u,h,p,d,f=1,E=!1,m=!1;if(null!==e.listener&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,i=a=o=Yu===n||qu===n,r&&_h(e,!0,-1)&&(E=!0,e.lineIndent>t?f=1:e.lineIndent===t?f=0:e.lineIndent<t&&(f=-1)),1===f)for(;Ih(e)||Nh(e);)_h(e,!0,-1)?(E=!0,o=i,e.lineIndent>t?f=1:e.lineIndent===t?f=0:e.lineIndent<t&&(f=-1)):o=!1;if(o&&(o=E||s),1!==f&&Yu!==n||(p=Hu===n||Gu===n?t:t+1,d=e.position-e.lineStart,1===f?o&&(Ch(e,d)||function(e,t,n){var r,s,i,a,o,c,l,u=e.tag,h=e.anchor,p={},d=Object.create(null),f=null,E=null,m=null,T=!1,_=!1;if(-1!==e.firstTabInLine)return!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=p),l=e.input.charCodeAt(e.position);0!==l;){if(T||-1===e.firstTabInLine||(e.position=e.firstTabInLine,hh(e,"tab characters must not be used in indentation")),r=e.input.charCodeAt(e.position+1),i=e.line,63!==l&&58!==l||!th(r)){if(a=e.line,o=e.lineStart,c=e.position,!Dh(e,n,Gu,!1,!0))break;if(e.line===i){for(l=e.input.charCodeAt(e.position);eh(l);)l=e.input.charCodeAt(++e.position);if(58===l)th(l=e.input.charCodeAt(++e.position))||hh(e,"a whitespace character is expected after the key-value separator within a block mapping"),T&&(mh(e,p,d,f,E,null,a,o,c),f=E=m=null),_=!0,T=!1,s=!1,f=e.tag,E=e.result;else{if(!_)return e.tag=u,e.anchor=h,!0;hh(e,"can not read an implicit mapping pair; a colon is missed")}}else{if(!_)return e.tag=u,e.anchor=h,!0;hh(e,"can not read a block mapping entry; a multiline key may not be an implicit key")}}else 63===l?(T&&(mh(e,p,d,f,E,null,a,o,c),f=E=m=null),_=!0,T=!0,s=!0):T?(T=!1,s=!0):hh(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,l=r;if((e.line===i||e.lineIndent>t)&&(T&&(a=e.line,o=e.lineStart,c=e.position),Dh(e,t,Yu,!0,s)&&(T?E=e.result:m=e.result),T||(mh(e,p,d,f,E,m,a,o,c),f=E=m=null),_h(e,!0,-1),l=e.input.charCodeAt(e.position)),(e.line===i||e.lineIndent>t)&&0!==l)hh(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return T&&mh(e,p,d,f,E,null,a,o,c),_&&(e.tag=u,e.anchor=h,e.kind="mapping",e.result=p),_}(e,d,p))||function(e,t){var n,r,s,i,a,o,c,l,u,h,p,d,f=!0,E=e.tag,m=e.anchor,T=Object.create(null);if(91===(d=e.input.charCodeAt(e.position)))a=93,l=!1,i=[];else{if(123!==d)return!1;a=125,l=!0,i={}}for(null!==e.anchor&&(e.anchorMap[e.anchor]=i),d=e.input.charCodeAt(++e.position);0!==d;){if(_h(e,!0,t),(d=e.input.charCodeAt(e.position))===a)return e.position++,e.tag=E,e.anchor=m,e.kind=l?"mapping":"sequence",e.result=i,!0;f?44===d&&hh(e,"expected the node content, but found ','"):hh(e,"missed comma between flow collection entries"),p=null,o=c=!1,63===d&&th(e.input.charCodeAt(e.position+1))&&(o=c=!0,e.position++,_h(e,!0,t)),n=e.line,r=e.lineStart,s=e.position,Dh(e,t,Hu,!1,!0),h=e.tag,u=e.result,_h(e,!0,t),d=e.input.charCodeAt(e.position),!c&&e.line!==n||58!==d||(o=!0,d=e.input.charCodeAt(++e.position),_h(e,!0,t),Dh(e,t,Hu,!1,!0),p=e.result),l?mh(e,i,T,h,u,p,n,r,s):o?i.push(mh(e,null,T,h,u,p,n,r,s)):i.push(u),_h(e,!0,t),44===(d=e.input.charCodeAt(e.position))?(f=!0,d=e.input.charCodeAt(++e.position)):f=!1}hh(e,"unexpected end of the stream within a flow collection")}(e,p)?m=!0:(a&&function(e,t){var n,r,s,i,a,o=ju,c=!1,l=!1,u=t,h=0,p=!1;if(124===(i=e.input.charCodeAt(e.position)))r=!1;else{if(62!==i)return!1;r=!0}for(e.kind="scalar",e.result="";0!==i;)if(43===(i=e.input.charCodeAt(++e.position))||45===i)ju===o?o=43===i?Vu:zu:hh(e,"repeat of a chomping mode identifier");else{if(!((s=48<=(a=i)&&a<=57?a-48:-1)>=0))break;0===s?hh(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):l?hh(e,"repeat of an indentation width identifier"):(u=t+s-1,l=!0)}if(eh(i)){do{i=e.input.charCodeAt(++e.position)}while(eh(i));if(35===i)do{i=e.input.charCodeAt(++e.position)}while(!Ju(i)&&0!==i)}for(;0!==i;){for(Th(e),e.lineIndent=0,i=e.input.charCodeAt(e.position);(!l||e.lineIndent<u)&&32===i;)e.lineIndent++,i=e.input.charCodeAt(++e.position);if(!l&&e.lineIndent>u&&(u=e.lineIndent),Ju(i))h++;else{if(e.lineIndent<u){o===Vu?e.result+=Zl.repeat("\n",c?1+h:h):o===ju&&c&&(e.result+="\n");break}for(r?eh(i)?(p=!0,e.result+=Zl.repeat("\n",c?1+h:h)):p?(p=!1,e.result+=Zl.repeat("\n",h+1)):0===h?c&&(e.result+=" "):e.result+=Zl.repeat("\n",h):e.result+=Zl.repeat("\n",c?1+h:h),c=!0,l=!0,h=0,n=e.position;!Ju(i)&&0!==i;)i=e.input.charCodeAt(++e.position);fh(e,n,e.position,!1)}}return!0}(e,p)||function(e,t){var n,r,s;if(39!==(n=e.input.charCodeAt(e.position)))return!1;for(e.kind="scalar",e.result="",e.position++,r=s=e.position;0!==(n=e.input.charCodeAt(e.position));)if(39===n){if(fh(e,r,e.position,!0),39!==(n=e.input.charCodeAt(++e.position)))return!0;r=e.position,e.position++,s=e.position}else Ju(n)?(fh(e,r,s,!0),gh(e,_h(e,!1,t)),r=s=e.position):e.position===e.lineStart&&Ah(e)?hh(e,"unexpected end of the document within a single quoted scalar"):(e.position++,s=e.position);hh(e,"unexpected end of the stream within a single quoted scalar")}(e,p)||function(e,t){var n,r,s,i,a,o,c;if(34!==(o=e.input.charCodeAt(e.position)))return!1;for(e.kind="scalar",e.result="",e.position++,n=r=e.position;0!==(o=e.input.charCodeAt(e.position));){if(34===o)return fh(e,n,e.position,!0),e.position++,!0;if(92===o){if(fh(e,n,e.position,!0),Ju(o=e.input.charCodeAt(++e.position)))_h(e,!1,t);else if(o<256&&ah[o])e.result+=oh[o],e.position++;else if((a=120===(c=o)?2:117===c?4:85===c?8:0)>0){for(s=a,i=0;s>0;s--)(a=rh(o=e.input.charCodeAt(++e.position)))>=0?i=(i<<4)+a:hh(e,"expected hexadecimal character");e.result+=ih(i),e.position++}else hh(e,"unknown escape sequence");n=r=e.position}else Ju(o)?(fh(e,n,r,!0),gh(e,_h(e,!1,t)),n=r=e.position):e.position===e.lineStart&&Ah(e)?hh(e,"unexpected end of the document within a double quoted scalar"):(e.position++,r=e.position)}hh(e,"unexpected end of the stream within a double quoted scalar")}(e,p)?m=!0:!function(e){var t,n,r;if(42!==(r=e.input.charCodeAt(e.position)))return!1;for(r=e.input.charCodeAt(++e.position),t=e.position;0!==r&&!th(r)&&!nh(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&hh(e,"name of an alias node must contain at least one character"),n=e.input.slice(t,e.position),Uu.call(e.anchorMap,n)||hh(e,'unidentified alias "'+n+'"'),e.result=e.anchorMap[n],_h(e,!0,-1),!0}(e)?function(e,t,n){var r,s,i,a,o,c,l,u,h=e.kind,p=e.result;if(th(u=e.input.charCodeAt(e.position))||nh(u)||35===u||38===u||42===u||33===u||124===u||62===u||39===u||34===u||37===u||64===u||96===u)return!1;if((63===u||45===u)&&(th(r=e.input.charCodeAt(e.position+1))||n&&nh(r)))return!1;for(e.kind="scalar",e.result="",s=i=e.position,a=!1;0!==u;){if(58===u){if(th(r=e.input.charCodeAt(e.position+1))||n&&nh(r))break}else if(35===u){if(th(e.input.charCodeAt(e.position-1)))break}else{if(e.position===e.lineStart&&Ah(e)||n&&nh(u))break;if(Ju(u)){if(o=e.line,c=e.lineStart,l=e.lineIndent,_h(e,!1,-1),e.lineIndent>=t){a=!0,u=e.input.charCodeAt(e.position);continue}e.position=i,e.line=o,e.lineStart=c,e.lineIndent=l;break}}a&&(fh(e,s,i,!1),gh(e,e.line-o),s=i=e.position,a=!1),eh(u)||(i=e.position+1),u=e.input.charCodeAt(++e.position)}return fh(e,s,i,!1),!!e.result||(e.kind=h,e.result=p,!1)}(e,p,Hu===n)&&(m=!0,null===e.tag&&(e.tag="?")):(m=!0,null===e.tag&&null===e.anchor||hh(e,"alias node should not have any properties")),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):0===f&&(m=o&&Ch(e,d))),null===e.tag)null!==e.anchor&&(e.anchorMap[e.anchor]=e.result);else if("?"===e.tag){for(null!==e.result&&"scalar"!==e.kind&&hh(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),c=0,l=e.implicitTypes.length;c<l;c+=1)if((h=e.implicitTypes[c]).resolve(e.result)){e.result=h.construct(e.result),e.tag=h.tag,null!==e.anchor&&(e.anchorMap[e.anchor]=e.result);break}}else if("!"!==e.tag){if(Uu.call(e.typeMap[e.kind||"fallback"],e.tag))h=e.typeMap[e.kind||"fallback"][e.tag];else for(h=null,c=0,l=(u=e.typeMap.multi[e.kind||"fallback"]).length;c<l;c+=1)if(e.tag.slice(0,u[c].tag.length)===u[c].tag){h=u[c];break}h||hh(e,"unknown tag !<"+e.tag+">"),null!==e.result&&h.kind!==e.kind&&hh(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+h.kind+'", not "'+e.kind+'"'),h.resolve(e.result,e.tag)?(e.result=h.construct(e.result,e.tag),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):hh(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return null!==e.listener&&e.listener("close",e),null!==e.tag||null!==e.anchor||m}function Sh(e){var t,n,r,s,i=e.position,a=!1;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);0!==(s=e.input.charCodeAt(e.position))&&(_h(e,!0,-1),s=e.input.charCodeAt(e.position),!(e.lineIndent>0||37!==s));){for(a=!0,s=e.input.charCodeAt(++e.position),t=e.position;0!==s&&!th(s);)s=e.input.charCodeAt(++e.position);for(r=[],(n=e.input.slice(t,e.position)).length<1&&hh(e,"directive name must not be less than one character in length");0!==s;){for(;eh(s);)s=e.input.charCodeAt(++e.position);if(35===s){do{s=e.input.charCodeAt(++e.position)}while(0!==s&&!Ju(s));break}if(Ju(s))break;for(t=e.position;0!==s&&!th(s);)s=e.input.charCodeAt(++e.position);r.push(e.input.slice(t,e.position))}0!==s&&Th(e),Uu.call(dh,n)?dh[n](e,n,r):ph(e,'unknown document directive "'+n+'"')}_h(e,!0,-1),0===e.lineIndent&&45===e.input.charCodeAt(e.position)&&45===e.input.charCodeAt(e.position+1)&&45===e.input.charCodeAt(e.position+2)?(e.position+=3,_h(e,!0,-1)):a&&hh(e,"directives end mark is expected"),Dh(e,e.lineIndent-1,Yu,!1,!0),_h(e,!0,-1),e.checkLineBreaks&&$u.test(e.input.slice(i,e.position))&&ph(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&Ah(e)?46===e.input.charCodeAt(e.position)&&(e.position+=3,_h(e,!0,-1)):e.position<e.length-1&&hh(e,"end of the stream or a document separator is expected")}function kh(e,t){t=t||{},0!==(e=String(e)).length&&(10!==e.charCodeAt(e.length-1)&&13!==e.charCodeAt(e.length-1)&&(e+="\n"),65279===e.charCodeAt(0)&&(e=e.slice(1)));var n=new lh(e,t),r=e.indexOf("\0");for(-1!==r&&(n.position=r,hh(n,"null byte is not allowed in input")),n.input+="\0";32===n.input.charCodeAt(n.position);)n.lineIndent+=1,n.position+=1;for(;n.position<n.length-1;)Sh(n);return n.documents}var bh={loadAll:function(e,t,n){null!==t&&"object"==typeof t&&void 0===n&&(n=t,t=null);var r=kh(e,n);if("function"!=typeof t)return r;for(var s=0,i=r.length;s<i;s+=1)t(r[s])},load:function(e,t){var n=kh(e,t);if(0!==n.length){if(1===n.length)return n[0];throw new tu("expected a single document in the stream, but found more")}}},Oh=Object.prototype.toString,Rh=Object.prototype.hasOwnProperty,Lh=65279,yh=9,Mh=10,Fh=13,xh=32,Ph=33,vh=34,Bh=35,wh=37,Uh=38,Hh=39,Gh=42,qh=44,Yh=45,jh=58,zh=61,Vh=62,Qh=63,$h=64,Wh=91,Xh=93,Kh=96,Zh=123,Jh=124,ep=125,tp={0:"\\0",7:"\\a",8:"\\b",9:"\\t",10:"\\n",11:"\\v",12:"\\f",13:"\\r",27:"\\e",34:'\\"',92:"\\\\",133:"\\N",160:"\\_",8232:"\\L",8233:"\\P"},np=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],rp=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function sp(e){var t,n,r;if(t=e.toString(16).toUpperCase(),e<=255)n="x",r=2;else if(e<=65535)n="u",r=4;else{if(!(e<=4294967295))throw new tu("code point within a string may not be greater than 0xFFFFFFFF");n="U",r=8}return"\\"+n+Zl.repeat("0",r-t.length)+t}var ip=1,ap=2;function op(e){this.schema=e.schema||wu,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=Zl.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=function(e,t){var n,r,s,i,a,o,c;if(null===t)return{};for(n={},s=0,i=(r=Object.keys(t)).length;s<i;s+=1)a=r[s],o=String(t[a]),"!!"===a.slice(0,2)&&(a="tag:yaml.org,2002:"+a.slice(2)),(c=e.compiledTypeMap.fallback[a])&&Rh.call(c.styleAliases,o)&&(o=c.styleAliases[o]),n[a]=o;return n}(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType='"'===e.quotingType?ap:ip,this.forceQuotes=e.forceQuotes||!1,this.replacer="function"==typeof e.replacer?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function cp(e,t){for(var n,r=Zl.repeat(" ",t),s=0,i=-1,a="",o=e.length;s<o;)-1===(i=e.indexOf("\n",s))?(n=e.slice(s),s=o):(n=e.slice(s,i+1),s=i+1),n.length&&"\n"!==n&&(a+=r),a+=n;return a}function lp(e,t){return"\n"+Zl.repeat(" ",e.indent*t)}function up(e){return e===xh||e===yh}function hp(e){return 32<=e&&e<=126||161<=e&&e<=55295&&8232!==e&&8233!==e||57344<=e&&e<=65533&&e!==Lh||65536<=e&&e<=1114111}function pp(e){return hp(e)&&e!==Lh&&e!==Fh&&e!==Mh}function dp(e,t,n){var r=pp(e),s=r&&!up(e);return(n?r:r&&e!==qh&&e!==Wh&&e!==Xh&&e!==Zh&&e!==ep)&&e!==Bh&&!(t===jh&&!s)||pp(t)&&!up(t)&&e===Bh||t===jh&&s}function fp(e,t){var n,r=e.charCodeAt(t);return r>=55296&&r<=56319&&t+1<e.length&&(n=e.charCodeAt(t+1))>=56320&&n<=57343?1024*(r-55296)+n-56320+65536:r}function Ep(e){return/^\n* /.test(e)}var mp=1,Tp=2,_p=3,Ap=4,gp=5;function Cp(e,t,n,r,s,i,a,o){var c,l,u=0,h=null,p=!1,d=!1,f=-1!==r,E=-1,m=hp(l=fp(e,0))&&l!==Lh&&!up(l)&&l!==Yh&&l!==Qh&&l!==jh&&l!==qh&&l!==Wh&&l!==Xh&&l!==Zh&&l!==ep&&l!==Bh&&l!==Uh&&l!==Gh&&l!==Ph&&l!==Jh&&l!==zh&&l!==Vh&&l!==Hh&&l!==vh&&l!==wh&&l!==$h&&l!==Kh&&function(e){return!up(e)&&e!==jh}(fp(e,e.length-1));if(t||a)for(c=0;c<e.length;u>=65536?c+=2:c++){if(!hp(u=fp(e,c)))return gp;m=m&&dp(u,h,o),h=u}else{for(c=0;c<e.length;u>=65536?c+=2:c++){if((u=fp(e,c))===Mh)p=!0,f&&(d=d||c-E-1>r&&" "!==e[E+1],E=c);else if(!hp(u))return gp;m=m&&dp(u,h,o),h=u}d=d||f&&c-E-1>r&&" "!==e[E+1]}return p||d?n>9&&Ep(e)?gp:a?i===ap?gp:Tp:d?Ap:_p:!m||a||s(e)?i===ap?gp:Tp:mp}function Ip(e,t,n,r,s){e.dump=function(){if(0===t.length)return e.quotingType===ap?'""':"''";if(!e.noCompatMode&&(-1!==np.indexOf(t)||rp.test(t)))return e.quotingType===ap?'"'+t+'"':"'"+t+"'";var i=e.indent*Math.max(1,n),a=-1===e.lineWidth?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-i),o=r||e.flowLevel>-1&&n>=e.flowLevel;switch(Cp(t,o,e.indent,a,(function(t){return function(e,t){var n,r;for(n=0,r=e.implicitTypes.length;n<r;n+=1)if(e.implicitTypes[n].resolve(t))return!0;return!1}(e,t)}),e.quotingType,e.forceQuotes&&!r,s)){case mp:return t;case Tp:return"'"+t.replace(/'/g,"''")+"'";case _p:return"|"+Np(t,e.indent)+Dp(cp(t,i));case Ap:return">"+Np(t,e.indent)+Dp(cp(function(e,t){var n,r,s=/(\n+)([^\n]*)/g,i=(o=e.indexOf("\n"),o=-1!==o?o:e.length,s.lastIndex=o,Sp(e.slice(0,o),t)),a="\n"===e[0]||" "===e[0];var o;for(;r=s.exec(e);){var c=r[1],l=r[2];n=" "===l[0],i+=c+(a||n||""===l?"":"\n")+Sp(l,t),a=n}return i}(t,a),i));case gp:return'"'+function(e){for(var t,n="",r=0,s=0;s<e.length;r>=65536?s+=2:s++)r=fp(e,s),!(t=tp[r])&&hp(r)?(n+=e[s],r>=65536&&(n+=e[s+1])):n+=t||sp(r);return n}(t)+'"';default:throw new tu("impossible error: invalid scalar style")}}()}function Np(e,t){var n=Ep(e)?String(t):"",r="\n"===e[e.length-1];return n+(r&&("\n"===e[e.length-2]||"\n"===e)?"+":r?"":"-")+"\n"}function Dp(e){return"\n"===e[e.length-1]?e.slice(0,-1):e}function Sp(e,t){if(""===e||" "===e[0])return e;for(var n,r,s=/ [^ ]/g,i=0,a=0,o=0,c="";n=s.exec(e);)(o=n.index)-i>t&&(r=a>i?a:o,c+="\n"+e.slice(i,r),i=r+1),a=o;return c+="\n",e.length-i>t&&a>i?c+=e.slice(i,a)+"\n"+e.slice(a+1):c+=e.slice(i),c.slice(1)}function kp(e,t,n,r){var s,i,a,o="",c=e.tag;for(s=0,i=n.length;s<i;s+=1)a=n[s],e.replacer&&(a=e.replacer.call(n,String(s),a)),(Op(e,t+1,a,!0,!0,!1,!0)||void 0===a&&Op(e,t+1,null,!0,!0,!1,!0))&&(r&&""===o||(o+=lp(e,t)),e.dump&&Mh===e.dump.charCodeAt(0)?o+="-":o+="- ",o+=e.dump);e.tag=c,e.dump=o||"[]"}function bp(e,t,n){var r,s,i,a,o,c;for(i=0,a=(s=n?e.explicitTypes:e.implicitTypes).length;i<a;i+=1)if(((o=s[i]).instanceOf||o.predicate)&&(!o.instanceOf||"object"==typeof t&&t instanceof o.instanceOf)&&(!o.predicate||o.predicate(t))){if(n?o.multi&&o.representName?e.tag=o.representName(t):e.tag=o.tag:e.tag="?",o.represent){if(c=e.styleMap[o.tag]||o.defaultStyle,"[object Function]"===Oh.call(o.represent))r=o.represent(t,c);else{if(!Rh.call(o.represent,c))throw new tu("!<"+o.tag+'> tag resolver accepts not "'+c+'" style');r=o.represent[c](t,c)}e.dump=r}return!0}return!1}function Op(e,t,n,r,s,i,a){e.tag=null,e.dump=n,bp(e,n,!1)||bp(e,n,!0);var o,c=Oh.call(e.dump),l=r;r&&(r=e.flowLevel<0||e.flowLevel>t);var u,h,p="[object Object]"===c||"[object Array]"===c;if(p&&(h=-1!==(u=e.duplicates.indexOf(n))),(null!==e.tag&&"?"!==e.tag||h||2!==e.indent&&t>0)&&(s=!1),h&&e.usedDuplicates[u])e.dump="*ref_"+u;else{if(p&&h&&!e.usedDuplicates[u]&&(e.usedDuplicates[u]=!0),"[object Object]"===c)r&&0!==Object.keys(e.dump).length?(!function(e,t,n,r){var s,i,a,o,c,l,u="",h=e.tag,p=Object.keys(n);if(!0===e.sortKeys)p.sort();else if("function"==typeof e.sortKeys)p.sort(e.sortKeys);else if(e.sortKeys)throw new tu("sortKeys must be a boolean or a function");for(s=0,i=p.length;s<i;s+=1)l="",r&&""===u||(l+=lp(e,t)),o=n[a=p[s]],e.replacer&&(o=e.replacer.call(n,a,o)),Op(e,t+1,a,!0,!0,!0)&&((c=null!==e.tag&&"?"!==e.tag||e.dump&&e.dump.length>1024)&&(e.dump&&Mh===e.dump.charCodeAt(0)?l+="?":l+="? "),l+=e.dump,c&&(l+=lp(e,t)),Op(e,t+1,o,!0,c)&&(e.dump&&Mh===e.dump.charCodeAt(0)?l+=":":l+=": ",u+=l+=e.dump));e.tag=h,e.dump=u||"{}"}(e,t,e.dump,s),h&&(e.dump="&ref_"+u+e.dump)):(!function(e,t,n){var r,s,i,a,o,c="",l=e.tag,u=Object.keys(n);for(r=0,s=u.length;r<s;r+=1)o="",""!==c&&(o+=", "),e.condenseFlow&&(o+='"'),a=n[i=u[r]],e.replacer&&(a=e.replacer.call(n,i,a)),Op(e,t,i,!1,!1)&&(e.dump.length>1024&&(o+="? "),o+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),Op(e,t,a,!1,!1)&&(c+=o+=e.dump));e.tag=l,e.dump="{"+c+"}"}(e,t,e.dump),h&&(e.dump="&ref_"+u+" "+e.dump));else if("[object Array]"===c)r&&0!==e.dump.length?(e.noArrayIndent&&!a&&t>0?kp(e,t-1,e.dump,s):kp(e,t,e.dump,s),h&&(e.dump="&ref_"+u+e.dump)):(!function(e,t,n){var r,s,i,a="",o=e.tag;for(r=0,s=n.length;r<s;r+=1)i=n[r],e.replacer&&(i=e.replacer.call(n,String(r),i)),(Op(e,t,i,!1,!1)||void 0===i&&Op(e,t,null,!1,!1))&&(""!==a&&(a+=","+(e.condenseFlow?"":" ")),a+=e.dump);e.tag=o,e.dump="["+a+"]"}(e,t,e.dump),h&&(e.dump="&ref_"+u+" "+e.dump));else{if("[object String]"!==c){if("[object Undefined]"===c)return!1;if(e.skipInvalid)return!1;throw new tu("unacceptable kind of an object to dump "+c)}"?"!==e.tag&&Ip(e,e.dump,t,i,l)}null!==e.tag&&"?"!==e.tag&&(o=encodeURI("!"===e.tag[0]?e.tag.slice(1):e.tag).replace(/!/g,"%21"),o="!"===e.tag[0]?"!"+o:"tag:yaml.org,2002:"===o.slice(0,18)?"!!"+o.slice(18):"!<"+o+">",e.dump=o+" "+e.dump)}return!0}function Rp(e,t){var n,r,s=[],i=[];for(Lp(e,s,i),n=0,r=i.length;n<r;n+=1)t.duplicates.push(s[i[n]]);t.usedDuplicates=new Array(r)}function Lp(e,t,n){var r,s,i;if(null!==e&&"object"==typeof e)if(-1!==(s=t.indexOf(e)))-1===n.indexOf(s)&&n.push(s);else if(t.push(e),Array.isArray(e))for(s=0,i=e.length;s<i;s+=1)Lp(e[s],t,n);else for(s=0,i=(r=Object.keys(e)).length;s<i;s+=1)Lp(e[r[s]],t,n)}function yp(e,t){return function(){throw new Error("Function yaml."+e+" is removed in js-yaml 4. Use yaml."+t+" instead, which is now safe by default.")}}const Mp={Type:ou,Schema:uu,FAILSAFE_SCHEMA:fu,JSON_SCHEMA:Nu,CORE_SCHEMA:Du,DEFAULT_SCHEMA:wu,load:bh.load,loadAll:bh.loadAll,dump:{dump:function(e,t){var n=new op(t=t||{});n.noRefs||Rp(e,n);var r=e;return n.replacer&&(r=n.replacer.call({"":r},"",r)),Op(n,0,r,!0,!0)?n.dump+"\n":""}}.dump,YAMLException:tu,types:{binary:Lu,float:Iu,map:du,null:Eu,pairs:Pu,set:Bu,timestamp:bu,bool:mu,int:Au,merge:Ou,omap:Fu,seq:pu,str:hu},safeLoad:yp("safeLoad","load"),safeLoadAll:yp("safeLoadAll","loadAll"),safeDump:yp("safeDump","dump")},Fp={name:"frontmatter",transform:e=>(e.beforeParse.tap(((e,t)=>{const{content:n}=t;if(!/^---\r?\n/.test(n))return;const r=/\n---\r?\n/.exec(n);if(!r)return;const s=n.slice(4,r.index);let i;try{i=Mp.load(s),(null==i?void 0:i.markmap)&&(i.markmap=function(e){if(!e)return;return["color","extraJs","extraCss"].forEach((t=>{null!=e[t]&&(e[t]=function(e){let t;"string"==typeof e?t=[e]:Array.isArray(e)&&(t=e.filter((e=>e&&"string"==typeof e)));return(null==t?void 0:t.length)?t:void 0}(e[t]))})),["duration","maxWidth","initialExpandLevel"].forEach((t=>{null!=e[t]&&(e[t]=function(e){if(isNaN(+e))return;return+e}(e[t]))})),e}(i.markmap))}catch{return}t.frontmatter=i,t.content=n.slice(r.index+r[0].length),t.contentLineOffset=n.slice(0,r.index).split("\n").length+1})),{})};function xp(e,t){return"script"===t.type&&t.data.src?{...t,data:{...t.data,src:e.getFullUrl(t.data.src)}}:t}const Pp="hljs",vp=["@highlightjs/cdn-assets@11.8.0/highlight.min.js"].map((e=>b(e))),Bp=["@highlightjs/cdn-assets@11.8.0/styles/default.min.css"].map((e=>O(e))),wp={name:Pp,config:{versions:{hljs:"11.8.0"},preloadScripts:vp,styles:Bp},transform(e){var t,n,r;let s;const a=(null==(n=null==(t=wp.config)?void 0:t.preloadScripts)?void 0:n.map((t=>xp(e.transformer.urlBuilder,t))))||[];let o=i;return e.parser.tap((t=>{t.set({highlight:(t,n)=>{o();const{hljs:r}=window;return r?r.highlightAuto(t,n?[n]:void 0).value:((s||(s=k(a)),s).then((()=>{e.retransform.call()})),t)}})})),e.beforeParse.tap(((e,t)=>{o=()=>{t.features[Pp]=!0}})),{styles:null==(r=wp.config)?void 0:r.styles}}},Up=wp;var Hp=t;function Gp(e,t){var n,r,s=e.posMax,i=!0,a=!0;return n=t>0?e.src.charCodeAt(t-1):-1,r=t+1<=s?e.src.charCodeAt(t+1):-1,(32===n||9===n||r>=48&&r<=57)&&(a=!1),32!==r&&9!==r||(i=!1),{can_open:i,can_close:a}}function qp(e,t){var n,r,s,i;if("$"!==e.src[e.pos])return!1;if(!Gp(e,e.pos).can_open)return t||(e.pending+="$"),e.pos+=1,!0;for(r=n=e.pos+1;-1!==(r=e.src.indexOf("$",r));){for(i=r-1;"\\"===e.src[i];)i-=1;if((r-i)%2==1)break;r+=1}return-1===r?(t||(e.pending+="$"),e.pos=n,!0):r-n==0?(t||(e.pending+="$$"),e.pos=n+1,!0):Gp(e,r).can_close?(t||((s=e.push("math_inline","math",0)).markup="$",s.content=e.src.slice(n,r)),e.pos=r+1,!0):(t||(e.pending+="$"),e.pos=n,!0)}function Yp(e,t,n,r){var s,i,a,o,c,l=!1,u=e.bMarks[t]+e.tShift[t],h=e.eMarks[t];if(u+2>h)return!1;if("$$"!==e.src.slice(u,u+2))return!1;if(u+=2,s=e.src.slice(u,h),r)return!0;for("$$"===s.trim().slice(-2)&&(s=s.trim().slice(0,-2),l=!0),a=t;!l&&!(++a>=n)&&!((u=e.bMarks[a]+e.tShift[a])<(h=e.eMarks[a])&&e.tShift[a]<e.blkIndent);)"$$"===e.src.slice(u,h).trim().slice(-2)&&(o=e.src.slice(0,h).lastIndexOf("$$"),i=e.src.slice(u,o),l=!0);return e.line=a+1,(c=e.push("math_block","math",0)).block=!0,c.content=(s&&s.trim()?s+"\n":"")+e.getLines(t+1,a,e.tShift[t],!0)+(i&&i.trim()?i:""),c.map=[t,e.line],c.markup="$$",!0}function jp(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")}const zp=un((function(e,t){t=t||{};e.inline.ruler.after("escape","math_inline",qp),e.block.ruler.after("blockquote","math_block",Yp,{alt:["paragraph","reference","blockquote","list"]}),e.renderer.rules.math_inline=function(e,n){return function(e){t.displayMode=!1;try{return Hp.renderToString(e,t)}catch(n){return t.throwOnError&&console.log(n),`<span class='katex-error' title='${jp(n.toString())}'>${jp(e)}</span>`}}(e[n].content)},e.renderer.rules.math_block=function(e,n){return function(e){t.displayMode=!0;try{return"<p class='katex-block'>"+Hp.renderToString(e,t)+"</p>"}catch(n){return t.throwOnError&&console.log(n),`<p class='katex-block katex-error' title='${jp(n.toString())}'>${jp(e)}</p>`}}(e[n].content)+"\n"}}));const Vp="katex",Qp=["katex@0.16.8/dist/katex.min.js"].map((e=>b(e))),$p=b("webfontloader@1.6.28/webfontloader.js");$p.data.defer=!0;const Wp=["katex@0.16.8/dist/katex.min.css"].map((e=>O(e))),Xp={name:Vp,config:{versions:{katex:"0.16.8",webfontloader:"1.6.28"},preloadScripts:Qp,scripts:[{type:"iife",data:{fn:e=>{window.WebFontConfig={custom:{families:["KaTeX_AMS","KaTeX_Caligraphic:n4,n7","KaTeX_Fraktur:n4,n7","KaTeX_Main:n4,n7,i4,i7","KaTeX_Math:i4,i7","KaTeX_Script","KaTeX_SansSerif:n4,n7,i4","KaTeX_Size1","KaTeX_Size2","KaTeX_Size3","KaTeX_Size4","KaTeX_Typewriter"]},active:()=>{e().refreshHook.call()}}},getParams:({getMarkmap:e})=>[e]}},$p],styles:Wp},transform(e){var t,n,r,s;let a;const o=(null==(n=null==(t=Xp.config)?void 0:t.preloadScripts)?void 0:n.map((t=>xp(e.transformer.urlBuilder,t))))||[],c=(t,n)=>{const{katex:r}=window;return r?r.renderToString(t,{displayMode:n,throwOnError:!1}):((a||(a=k(o)),a).then((()=>{e.retransform.call()})),t)};let l=i;return e.parser.tap((e=>{e.use(zp),["math_block","math_inline"].forEach((t=>{e.renderer.rules[t]=(e,t)=>{l();return c(e[t].content,!!e[t].block)}}))})),e.beforeParse.tap(((e,t)=>{l=()=>{t.features[Vp]=!0}})),e.afterParse.tap(((e,t)=>{var n;const r=null==(n=t.frontmatter)?void 0:n.markmap;r&&["extraJs","extraCss"].forEach((e=>{var t,n;const s=r[e];var i,a,o;s&&(r[e]=(i=s,a=Vp,o=(null==(n=null==(t=Xp.config)?void 0:t.versions)?void 0:n.katex)||"",i.map((e=>{if("string"==typeof e&&!e.includes("://")){e.startsWith("npm:")||(e=`npm:${e}`);const t=4+a.length;e.startsWith(`npm:${a}/`)&&(e=`${e.slice(0,t)}@${o}${e.slice(t)}`)}return e}))))}))})),{styles:null==(r=Xp.config)?void 0:r.styles,scripts:null==(s=Xp.config)?void 0:s.scripts}}},Kp=[Fp,Xp,Up,{name:"npmUrl",transform:e=>(e.afterParse.tap(((t,n)=>{const{frontmatter:r}=n,s=null==r?void 0:r.markmap;s&&["extraJs","extraCss"].forEach((t=>{const n=s[t];n&&(s[t]=n.map((t=>t.startsWith("npm:")?e.transformer.urlBuilder.getFullUrl(t.slice(4)):t)))}))})),{})},Xl,{name:"sourceLines",transform:e=>(e.parser.tap((e=>{var t,n;e.renderer.renderAttrs=(t=e.renderer.renderAttrs,n=(e,t)=>{let n=e(t);return t.block&&t.map&&(n+=` data-lines=${t.map.join(",")}`),n},(...e)=>n(t,...e))})),{})}];function Zp(e){for(;!e.content&&1===e.children.length;)e=e.children[0];for(;1===e.children.length&&!e.children[0].content;)e={...e,children:e.children[0].children};return{...e,children:e.children.map(Zp)}}e.Transformer=class{constructor(e=Kp){this.assetsMap={},this.urlBuilder=new r,this.hooks={transformer:this,parser:new s,beforeParse:new s,afterParse:new s,retransform:new s},this.plugins=e.map((e=>"function"==typeof e?e():e));const t={};for(const{name:e,transform:n}of this.plugins)t[e]=n(this.hooks);this.assetsMap=t;const n=function(){const e=Hl({html:!0,breaks:!0});return e.use(Gl).use(ql).use(zl).use($l),e}();this.md=n,this.hooks.parser.call(n)}transform(e,t){var n,r,s;const i={content:e,features:{},contentLineOffset:0};this.hooks.beforeParse.call(this.md,i);const a=this.md.render(i.content,{});this.hooks.afterParse.call(this.md,i);const o=Zp(function(e,t){return n=po(e,t),function(e,t){const n=(e,r)=>t(e,(()=>{var t;return null==(t=e.children)?void 0:t.map((t=>n(t,e)))}),r);return n(e)}(n,((e,t)=>{const n={content:e.html,children:t()||[]};return e.data&&(n.payload={...e.data}),e.comments&&(e.comments.includes("foldAll")?n.payload={...n.payload,fold:2}:e.comments.includes("fold")&&(n.payload={...n.payload,fold:1})),n}));var n}(a,{...null==(r=null==(n=i.frontmatter)?void 0:n.markmap)?void 0:r.htmlParser,...t}));return o.content||(o.content=`${(null==(s=i.frontmatter)?void 0:s.title)||""}`),{...i,root:o}}getAssets(e){const t=[],n=[];e??(e=this.plugins.map((e=>e.name)));for(const r of e.map((e=>this.assetsMap[e])))r&&(r.styles&&t.push(...r.styles),r.scripts&&n.push(...r.scripts));return{styles:t.map((e=>function(e,t){return"stylesheet"===t.type&&t.data.href?{...t,data:{...t.data,href:e.getFullUrl(t.data.href)}}:t}(this.urlBuilder,e))),scripts:n.map((e=>xp(this.urlBuilder,e)))}}getUsedAssets(e){const t=this.plugins.map((e=>e.name)).filter((t=>e[t]));return this.getAssets(t)}},e.builtInPlugins=Kp,e.transformerVersions={"markmap-lib":"0.17.0"},Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}(this.markmap=this.markmap||{},window.katex);
//# sourceMappingURL=/sm/29aa396d0082427fc47aa463886afb1bec570b74826fb7820799a2df2252cab9.map