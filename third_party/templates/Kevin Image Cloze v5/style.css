<style>
/* --- Global & Variables --- */
:root {
  --main-color: #FF5656;
  --secondary-color: #FFEBA2;
  --bg-color: #f8f9fa;
  --text-color: #212529;
  --border-color: #dee2e6;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --border-radius: 12px;
}

.card {
    font-family: -apple-system-font, BlinkMacSystemFont, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial, sans-serif;
    font-size: 20px;
    text-align: left;
    color: var(--pico-color);
    background-color: var(--pico-background-color);
}


html,
body,
#content,
#qa,
.container {
    height: 100%;
    margin: 0;
    padding: 0;
}

article{
    color: var(--pico-color);
    background-color: var(--pico-background-color);
}
#deck_container,
#type_container,
#tag_container,
#source_container,
#time_container {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 0.8em;
    color: var(--pico-muted-color);
    margin-top: 0.5em;
    padding: 4px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
}

#deck_container::before {
    content: "牌组:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}

#tag_container span.tag {
    display: inline-block;
    /* 让 span 可以设置 padding 和 margin */
    padding: 2px 6px;
    background-color: #007bff;
    /* 主要的品牌蓝色 */
    color: white;
    border-radius: 4px;
    /* 圆角 */
    font-size: 0.8em;
    font-weight: 500;
    /* 中等粗细的字体 */
    transition: background-color 0.3s ease, transform 0.2s ease;
    /* 添加过渡效果 */
    cursor: default;
    /* 默认光标，如果你希望点击可以做些什么，可以改成 pointer */
    white-space: nowrap;
    /* 防止标签内的文本换行 */
}

/* 鼠标悬停时的样式 */
#tag_container span.tag:hover {
    background-color: #0056b3;
    /* 深一点的蓝色 */
    transform: translateY(-1px);
    /* 轻微向上移动 */
}

#time_container::before {
    content: "用时:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}

/* 第一个标签前的 "标签:" 文本样式 */
#tag_container::before {
    content: "标签:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}

#source_container::before {
    content: "出处:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}

.heading {
    text-align: left;
    padding-left: 0.5em;
    border-left: 4px solid #4891e7;
    color: var(--pico-color);
    font-weight: bold;
}


.row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}


.image-container {
  position: relative;
  margin: 10px auto;
  overflow: hidden;
  border-radius: 4px;
  width: -moz-fit-content; /* Firefox compatibility */
  width: fit-content;      /* Standard */
  max-width: 100%;         /* Prevent overflow on very large images */
}

.image-container.is-scrollable {
  max-height: 85vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

.image-container.is-scrollable::-webkit-scrollbar { width: 8px; }
.image-container.is-scrollable::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 8px; }
.image-container.is-scrollable::-webkit-scrollbar-thumb { background: #888; border-radius: 8px; }
.image-container.is-scrollable::-webkit-scrollbar-thumb:hover { background: #555; }

.main-image img {
  display: block;
  max-width: 100%;
  /* ANKIDROID FIX: Removed `max-height: 85vh` to allow the image to scale correctly with Anki's card zoom feature. */
  /* The `.is-scrollable` class will handle excessively tall images. */
  width: 100% !important;
  height: auto;
  margin: 0 auto;
  object-fit: contain;
}

.is-scrollable .main-image img {
  width: 100%;
  max-height: none;
}

#masksContainer, #scratchCanvasContainer {
  position: absolute;
  top: 0; left: 0;
  pointer-events: none;
  z-index: 5;
}

#scratchCanvasContainer { z-index: 10; }

.mask {
  position: absolute;
  cursor: pointer;
  transition: opacity 0.2s ease-in-out;
  pointer-events: auto;
}

.mask.is-main { background-color: var(--main-color); }
.mask.is-secondary { background-color: var(--secondary-color); }
.mask.is-revealed { opacity: 0.15; }

.scratch-canvas {
  position: absolute;
  cursor: pointer;
  pointer-events: auto;
  border-radius: 4px;
}

/* --- Navigation --- */
.navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: clamp(0.5rem, 2.5vw, 1rem);
  margin: 16px auto;
}

.nav-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: clamp(2.1rem, 7vw, 2.4rem);
  height: clamp(2.1rem, 7vw, 2.4rem);
  background-color: #4a86e8;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  text-decoration: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.nav-btn:hover { background-color: #3a76d8; transform: scale(1.05); }
.nav-btn:active { transform: scale(0.95); }

.nav-btn svg {
  stroke: white;
  width: 55%;
  height: 55%;
}

/* --- Utilities --- */
.scroll-indicator {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  z-index: 20;
  pointer-events: none;
  opacity: 1;
  transition: opacity 0.5s ease-out;
}
.scroll-indicator.is-hidden { opacity: 0; }

</style>

<script>
    // 定义配置项的默认值，方便管理
    var configDefaults = {
        "dark_mode": false,
        "show_deck": true,
        "show_tag": true,
        "show_source": true,
        "show_time": true,
    };

    // 监听器用于处理所有复选框的变更
    function handleCheckboxChange(event) {
        console.log(event);
        const checkbox = event.target;
        const configKey = checkbox.dataset.configKey;
        const isChecked = checkbox.checked;
        localStorage.setItem(configKey, isChecked.toString());

        console.log({
            configKey, isChecked
        });

        if (configKey === "dark_mode") {
            console.log("dark mode");
            let ele = document.querySelector("html");

            if (isChecked) {
                ele.setAttribute("data-theme", "dark");
            }

            else {
                ele.setAttribute("data-theme", "light");
            }
        }

        else if (configKey === "show_deck") {

            if (isChecked) {
                document.getElementById("deck_container").style.display = "flex";
                document.getElementById("deck_container_fallback").style.display = "none";
            }

            else {
                document.getElementById("deck_container").style.display = "none";
                document.getElementById("deck_container_fallback").style.display = "flex";
            }
        }

        else if (configKey === "show_tag") {
            if (isChecked) {
                document.getElementById("tag_container").style.display = "flex";
            }

            else {
                document.getElementById("tag_container").style.display = "none";
            }
        }
        else if (configKey === "show_source") {
            if (isChecked) {
                document.getElementById("source_container").style.display = "flex";
            }

            else {
                document.getElementById("source_container").style.display = "none";
            }
        }
        else if (configKey === "show_time") {
            if (isChecked) {
                document.getElementById("time_container").style.display = "flex";
            }

            else {
                document.getElementById("time_container").style.display = "none";
            }
        }
    }

    function checkAndSetTheme() {
        if (localStorage.getItem("dark_mode") === "false" || localStorage.getItem("dark_mode") === "true") {
            return;
        }

        const htmlElement = document.querySelector("html");
        const bodyElement = document.querySelector("body");

        if (!htmlElement || !bodyElement) {
            console.error("HTML or Body element not found!");
            return;
        }

        const nightModeClasses = ["night-mode", "night_mode", "nightMode"];

        // 检查 html 元素
        const isHtmlNightMode = nightModeClasses.some(className => htmlElement.classList.contains(className));

        // 检查 body 元素
        const isBodyNightMode = nightModeClasses.some(className => bodyElement.classList.contains(className));

        // 如果 html 或 body 中任何一个包含了夜间模式类，则认为是夜间模式
        const isDarkMode = isHtmlNightMode || isBodyNightMode;

        if (isDarkMode) {
            console.log("dark");
            htmlElement.setAttribute("data-theme", "dark");
            localStorage.setItem("dark_mode", true);
        }

        else {
            console.log("light");
            htmlElement.setAttribute("data-theme", "light");
            localStorage.setItem("dark_mode", false);
        }
    }

    // 初始化配置
    function initializeConfig() {
        checkAndSetTheme();
        const configCheckboxes = document.querySelectorAll('input[type="checkbox"][role="switch"][data-config-key]');

        configCheckboxes.forEach(checkbox => {
            const configKey = checkbox.dataset.configKey;
            let storedValue = localStorage.getItem(configKey);

            console.log({
                configKey
            });

            // 根据不同的配置项，解析 localStorage 中的值
            if (storedValue === null) {
                // 如果 localStorage 中没有该项，使用默认值
                checkbox.checked = configDefaults[configKey];
                localStorage.setItem(configKey, configDefaults[configKey]);
            }

            else {
                checkbox.checked = storedValue === "true";
            }

            // 添加事件监听器
            checkbox.addEventListener("change", handleCheckboxChange);

            if (configKey === "show_deck") {
                if (checkbox.checked) {
                    document.getElementById("deck_container").style.display = "flex";
                }

                else {
                    document.getElementById("deck_container").style.display = "none";
                }
            }

            else if (configKey === "show_tag") {
                if (checkbox.checked) {
                    document.getElementById("tag_container").style.display = "flex";
                }

                else {
                    document.getElementById("tag_container").style.display = "none";
                }
            }

            else if (configKey === "show_time") {
                if (checkbox.checked) {
                    let interval_id = Number.parseInt(localStorage.getItem("timer_interval") || "0");

                    if (interval_id > 0) {
                        clearInterval(interval_id);
                    }

                    document.getElementById("time_container").style.display = "flex";
                }

                else {
                    document.getElementById("time_container").style.display = "none";
                }
            }
        });

        // 绑定关闭按钮事件
        const closeBtn = document.getElementById("close-config-btn");

        if (closeBtn) {
            closeBtn.addEventListener("click", () => {
                const dialog = document.getElementById("config-dialog");

                if (dialog) {
                    dialog.close();
                }
            });
        }
    }


    function toggleConfig() {
        console.log("toggleConfig");
        const configDialog = document.getElementById('config-dialog');

        if (configDialog.open) {
            configDialog.close();
        }

        else {
            configDialog.showModal();
        }
    }

</script>


<script id="shared-controller-script">
// --- CONFIGURATION ---
var SCRATCH_REVEAL_PERCENTAGE = 0.5; // Reveal when 50% is scratched. Adjust from 0.0 to 1.0.
// ---------------------

if (!window.ImageClozeController) {
    window.ImageClozeController = class ImageClozeController {
        constructor(config) {
            this.config = config;
            this.state = {
                masks: this.config.masks || [],
                currentMaskIndex: 0,
                isCurrentMaskRevealed: false,
                isResizing: null
            };
            this.elements = {
                imageContainer: document.getElementById('imageContainer'),
                mainImage: document.getElementById('mainImage'),
                img: document.querySelector('#mainImage img'),
                masksContainer: document.getElementById('masksContainer'),
                scratchCanvasContainer: document.getElementById('scratchCanvasContainer'),
                navigationContainer: document.getElementById('navigationContainer')
            };
        }

        init() {
            if (!this.elements.img || this.state.masks.length === 0) { return; }
            this._applyColors();
            if (this.config.isReversed) {
                this.config.mode = 'free_guess';
                if (this.config.isFront) return;
            }
            if (this.elements.img.complete) {
                this._onImageReady();
            } else {
                this.elements.img.onload = () => this._onImageReady();
            }
        }

        _onImageReady() {
            this._setupObservers();
            this._createMaskElements();
            this._checkImageAspectRatio();
        }
        
        _createMaskElements() {
            this._updateMaskPositions();
            window.addEventListener('resize', () => {
                clearTimeout(this.state.isResizing);
                this.state.isResizing = setTimeout(() => this._updateMaskPositions(), 250);
            });
        }

        _updateMaskPositions() {
            const tempRevealed = new Set(Array.from(this.elements.masksContainer.querySelectorAll('.is-revealed')).map(el => el.dataset.groupIndex));
            this.elements.masksContainer.innerHTML = '';
            if (this.elements.scratchCanvasContainer) this.elements.scratchCanvasContainer.innerHTML = '';

            const imgRect = this.elements.img.getBoundingClientRect();
            const containerRect = this.elements.imageContainer.getBoundingClientRect();

            // *** ANKIDROID FIX 1: MASK MISALIGNMENT ***
            // Use `imgRect.width/height` from getBoundingClientRect() instead of `offsetWidth/Height`.
            // This correctly accounts for CSS transforms (like AnkiDroid's pinch-zoom),
            // ensuring masks stay aligned with the visually rendered image.
            const { width: imgWidth, height: imgHeight } = imgRect;

            const offsetLeft = imgRect.left - containerRect.left + this.elements.imageContainer.scrollLeft;
            const offsetTop = imgRect.top - containerRect.top + this.elements.imageContainer.scrollTop;
            
            [this.elements.masksContainer, this.elements.scratchCanvasContainer].forEach(el => {
                if (el) Object.assign(el.style, { width: `${imgWidth}px`, height: `${imgHeight}px`, left: `${offsetLeft}px`, top: `${offsetTop}px` });
            });
            
            this.state.masks.forEach((maskGroup, groupIndex) => {
                maskGroup.forEach(coords => {
                    const maskEl = this._createSingleMask(coords, groupIndex, imgWidth, imgHeight);
                    this.elements.masksContainer.appendChild(maskEl);
                    if (tempRevealed.has(String(groupIndex))) {
                        maskEl.classList.add('is-revealed');
                    }
                    if (this.config.mode === 'scratch_guess' && this.config.isFront) {
                        maskEl.style.opacity = '0';
                        maskEl.style.pointerEvents = 'none';
                        this._setupScratchCanvas(maskEl);
                    }
                });
            });

            this._updateView(true);
            this._setupNavigation();
            this._setupKeyboardShortcuts();
        }

        _updateView(isInitialSetup = false) {
            // This robustly prevents state from a previous card from "leaking" on AnkiMobile.
            if (this.config.isFront && isInitialSetup) {
                // Force-reset all masks to their default hidden state on initial render.
                document.querySelectorAll('.mask.is-revealed').forEach(m => m.classList.remove('is-revealed'));
                this.state.isCurrentMaskRevealed = false;
            }
            
            if (isInitialSetup) {
                this.state.currentMaskIndex = this._getTargetIndex();
            }
            
            if (!this.config.isFront) {
                if (this.config.mode === 'mask_one_guess_one_multi') {
                    this._showOnlyCurrentMask();
                    this._toggleMaskGroup(this.state.currentMaskIndex, true);
                } else {
                    this._revealAllMasks();
                    this._updateMaskHighlight();
                }
                return;
            }
            
            const mode = this.config.mode;
            if (mode === 'mask_one_guess_one' || mode === 'mask_one_guess_one_multi') {
                this._showOnlyCurrentMask();
            } else if (mode !== 'scratcƒfh_guess') {
                this._updateMaskHighlight();
            }
        }

        _createSingleMask(coords, groupIndex, imgWidth, imgHeight) {
            const el = document.createElement('div');
            el.className = 'mask';
            el.dataset.groupIndex = groupIndex;
            Object.assign(el.style, {
                left: `${coords[0] * imgWidth}px`,
                top: `${coords[1] * imgHeight}px`,
                width: `${coords[2] * imgWidth}px`,
                height: `${coords[3] * imgHeight}px`
            });
            el.onclick = () => this.handleMaskClick(groupIndex);
            return el;
        }

        _setupScratchCanvas(maskEl) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d', { willReadFrequently: true });
            canvas.width = maskEl.offsetWidth;
            canvas.height = maskEl.offsetHeight;
            canvas.className = 'scratch-canvas';
            canvas.style.left = maskEl.style.left;
            canvas.style.top = maskEl.style.top;
            this.elements.scratchCanvasContainer.appendChild(canvas);

            const mainColor = getComputedStyle(document.documentElement).getPropertyValue('--main-color').trim();
            ctx.fillStyle = mainColor || '#FF5656';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            let isDrawing = false;
            const scratchRadius = Math.max(8, Math.min(canvas.width, canvas.height) / 10);
            
            const getCoords = (e) => {
                const canvasRect = canvas.getBoundingClientRect();
                const event = e.touches ? e.touches[0] : e;
                return { x: event.clientX - canvasRect.left, y: event.clientY - canvasRect.top };
            };
            
            const stopScratching = () => {
                isDrawing = false;
                checkRevealPercentage();
                document.removeEventListener('mousemove', doScratch);
                document.removeEventListener('mouseup', stopScratching);
                document.removeEventListener('touchmove', doScratch, { passive: false });
                document.removeEventListener('touchend', stopScratching);
            };

            let lastCheck = 0;
            const checkRevealPercentage = () => {
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;
                let transparentPixels = 0;
                for (let i = 3; i < data.length; i += 4) {
                    if (data[i] < 128) {
                        transparentPixels++;
                    }
                }
                const percentage = transparentPixels / (canvas.width * canvas.height);

                if (percentage > SCRATCH_REVEAL_PERCENTAGE) {
                    stopScratching();
                    canvas.style.transition = 'opacity 0.3s ease-out';
                    canvas.style.opacity = '0';
                    setTimeout(() => canvas.remove(), 300);
                }
            };
            
            const doScratch = (e) => {
                if (!isDrawing) return;
                e.preventDefault();
                const { x, y } = getCoords(e);
                ctx.globalCompositeOperation = 'destination-out';
                ctx.beginPath();
                ctx.arc(x, y, scratchRadius, 0, 2 * Math.PI);
                ctx.fill();
                
                const now = Date.now();
                if (now - lastCheck > 250) {
                    lastCheck = now;
                    checkRevealPercentage();
                }
            };

            const startScratching = (e) => {
                e.preventDefault();
                isDrawing = true;
                document.addEventListener('mousemove', doScratch);
                document.addEventListener('mouseup', stopScratching);
                document.addEventListener('touchmove', doScratch, { passive: false });
                document.addEventListener('touchend', stopScratching);
                doScratch(e);
            };

            canvas.addEventListener('mousedown', startScratching);
            canvas.addEventListener('touchstart', startScratching, { passive: false });
        }
        
        handleMaskClick(groupIndex) {
            if (!this.config.isFront) {
                this._toggleMaskGroup(groupIndex);
                return;
            }
            
            const targetIndex = this.state.currentMaskIndex;
            switch(this.config.mode) {
                case 'mask_one_guess_one':
                case 'mask_one_guess_one_multi':
                    if (groupIndex === targetIndex) this._toggleMaskGroup(groupIndex);
                    break;
                case 'mask_all_guess_all':
                    this.toggleAllMasks();
                    break;
                default:
                    this._toggleMaskGroup(groupIndex);
                    break;
            }
        }

        _toggleMaskGroup(groupIndex, forceState) {
            const masks = document.querySelectorAll(`.mask[data-group-index="${groupIndex}"]`);
            if (masks.length === 0) return;
            const isRevealed = (forceState !== undefined) ? !forceState : masks[0].classList.contains('is-revealed');
            masks.forEach(m => m.classList.toggle('is-revealed', !isRevealed));
            if (groupIndex === this.state.currentMaskIndex) this.state.isCurrentMaskRevealed = !isRevealed;
            this._updateToggleAllIcon();
        }

        toggleAllMasks() {
            const allMasks = document.querySelectorAll('.mask');
            if (allMasks.length === 0) return;
            const allRevealed = Array.from(allMasks).every(m => m.classList.contains('is-revealed'));
            allMasks.forEach(m => m.classList.toggle('is-revealed', !allRevealed));
            this.state.isCurrentMaskRevealed = !allRevealed;
            this._updateToggleAllIcon(!allRevealed);
        }

        _revealAllMasks() {
            document.querySelectorAll('.mask').forEach(m => m.classList.add('is-revealed'));
            this.state.isCurrentMaskRevealed = true;
            this._updateToggleAllIcon(true);
        }

        _showOnlyCurrentMask() {
            document.querySelectorAll('.mask').forEach(m => {
                const groupIndex = parseInt(m.dataset.groupIndex, 10);
                m.style.display = groupIndex === this.state.currentMaskIndex ? 'block' : 'none';
                this._applyMaskColor(m, groupIndex);
            });
            this._checkCurrentMaskState();
        }
        
        _updateMaskHighlight() {
            document.querySelectorAll('.mask').forEach(m => {
                m.style.display = 'block';
                this._applyMaskColor(m, parseInt(m.dataset.groupIndex, 10));
            });
            this._checkCurrentMaskState();
        }

        nextMask() {
            const total = this.state.masks.length;
            if (total <= 1) {
                if (this.config.isFront) this._toggleMaskGroup(0);
                return;
            }

            if (!this.config.isFront) {
                this.state.currentMaskIndex = (this.state.currentMaskIndex + 1) % total;
                this._updateView();
                return;
            }

            if (this.config.mode === 'free_guess') {
                this._toggleMaskGroup(this.state.currentMaskIndex, true);
                this.state.currentMaskIndex = (this.state.currentMaskIndex + 1) % total;
                return;
            }
            if (this.config.mode === 'mask_all_guess_all') { this._revealAllMasks(); return; }
            
            if (!this.state.isCurrentMaskRevealed) {
                this._toggleMaskGroup(this.state.currentMaskIndex);
            } else {
                this._toggleMaskGroup(this.state.currentMaskIndex, false);
                this.state.currentMaskIndex = (this.state.currentMaskIndex + 1) % total;
                this._updateView();
            }
        }

        prevMask() {
            const total = this.state.masks.length;
            if (total <= 1) {
                if (this.config.isFront) this._toggleMaskGroup(0);
                return;
            }

            if (!this.config.isFront) {
                this.state.currentMaskIndex = (this.state.currentMaskIndex - 1 + total) % total;
                this._updateView();
                return;
            }

            if (this.config.mode === 'free_guess') {
                this._toggleMaskGroup(this.state.currentMaskIndex, false);
                this.state.currentMaskIndex = (this.state.currentMaskIndex - 1 + total) % total;
                return;
            }
            if (this.config.mode === 'mask_all_guess_all') {
                document.querySelectorAll('.mask').forEach(m => m.classList.remove('is-revealed'));
                this.state.isCurrentMaskRevealed = false;
                this._updateToggleAllIcon(false);
                return;
            }

            if (!this.state.isCurrentMaskRevealed) {
                this._toggleMaskGroup(this.state.currentMaskIndex);
            } else {
                this._toggleMaskGroup(this.state.currentMaskIndex, false);
                this.state.currentMaskIndex = (this.state.currentMaskIndex - 1 + total) % total;
                this._updateView();
            }
        }
        
        _setupNavigation() {
            if (!this.elements.navigationContainer) return;
            this.elements.navigationContainer.innerHTML = ''; 

            const hasMultipleMasks = this.state.masks.length > 1;
            
            if (!hasMultipleMasks || (this.config.mode === 'scratch_guess' && this.config.isFront)) return;
            if (!this.config.isFront && this.config.mode === 'mask_one_guess_one_multi') return;

            this.elements.navigationContainer.innerHTML = `
                <div class="navigation">
                    <a href="javascript:void(0);" class="nav-btn" id="prevMaskBtn" title="Previous (J)"><svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg></a>
                    <a href="javascript:void(0);" class="nav-btn" id="toggleAllBtn" title="Toggle All"><svg id="eyeOpen" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg><svg id="eyeClosed" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display:none;"><path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line></svg></a>
                    <a href="javascript:void(0);" class="nav-btn" id="nextMaskBtn" title="Next (K)"><svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg></a>
                </div>`;
            
            const prevBtn = this.elements.navigationContainer.querySelector('#prevMaskBtn');
            const nextBtn = this.elements.navigationContainer.querySelector('#nextMaskBtn');
            const toggleBtn = this.elements.navigationContainer.querySelector('#toggleAllBtn');

            if (prevBtn) prevBtn.onclick = () => this.prevMask();
            if (nextBtn) nextBtn.onclick = () => this.nextMask();
            if (toggleBtn) toggleBtn.onclick = () => this.toggleAllMasks();
            
            this._updateToggleAllIcon();
        }
        
        _updateToggleAllIcon() {
            const eyeOpen = document.getElementById('eyeOpen'), eyeClosed = document.getElementById('eyeClosed');
            if (!eyeOpen || !eyeClosed) return;
            const allMasks = document.querySelectorAll('.mask');
            if (allMasks.length === 0) return;
            const allRevealed = Array.from(allMasks).every(m => m.classList.contains('is-revealed'));
            eyeOpen.style.display = allRevealed ? 'none' : 'block';
            eyeClosed.style.display = allRevealed ? 'block' : 'none';
            eyeOpen.parentElement.title = allRevealed ? 'Hide All Masks' : 'Show All Masks';
        }

        _setupKeyboardShortcuts() {
            document.removeEventListener('keydown', window.ankiImageClozeKeyHandler);
            window.ankiImageClozeKeyHandler = (e) => {
                if (e.target.closest('input, textarea, [contenteditable]')) return;
                if (e.key === 'j') this.prevMask();
                else if (e.key === 'k') this.nextMask();
            };
            document.addEventListener('keydown', window.ankiImageClozeKeyHandler);
        }

        _applyColors() {
            const [main, sec] = this.config.colors.split(',');
            document.documentElement.style.setProperty('--main-color', main || '#FF5656');
            document.documentElement.style.setProperty('--secondary-color', sec || '#FFEBA2');
        }
        _applyMaskColor(el, groupIndex) {
            let isMain = !this.config.mode.includes('mask_all_guess_one') || (groupIndex === this.state.currentMaskIndex);
            el.classList.toggle('is-main', isMain);
            el.classList.toggle('is-secondary', !isMain);
        }
        _checkCurrentMaskState() {
            const masks = document.querySelectorAll(`.mask[data-group-index="${this.state.currentMaskIndex}"]`);
            this.state.isCurrentMaskRevealed = masks.length > 0 && Array.from(masks).every(m => m.classList.contains('is-revealed'));
        }
        _getTargetIndex() {
            if (this.config.mode.includes('multi') && this.config.index) {
                const i = parseInt(this.config.index.replace('c', ''), 10) - 1;
                return isNaN(i) || i < 0 ? 0 : i;
            }
            return 0;
        }
        _setupObservers() {
            // AnkiDroid's pinch zoom is often a transform. ResizeObserver handles this well.
            new ResizeObserver(() => this._updateMaskPositions()).observe(this.elements.img);
            new MutationObserver(() => this._updateMaskPositions()).observe(this.elements.imageContainer, { attributes: true, attributeFilter: ['class', 'style'] });
        }
        _checkImageAspectRatio() {
            const { naturalWidth, naturalHeight } = this.elements.img, container = this.elements.imageContainer;
            if (naturalHeight > naturalWidth * 2) {
                container.classList.add('is-scrollable');
                if (!container.querySelector('.scroll-indicator')) {
                    const ind = document.createElement('div');
                    ind.className = 'scroll-indicator';
                    ind.textContent = '↓ Scroll to see more ↓';
                    container.appendChild(ind);
                    setTimeout(() => ind.classList.add('is-hidden'), 2500);
                }
                container.onscroll = () => this._updateMaskPositions();
            } else container.classList.remove('is-scrollable');
        }
    }
}
</script>