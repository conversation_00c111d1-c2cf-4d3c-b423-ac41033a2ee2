<style>
:root {
}

[data-theme=light] {
    color-scheme: light;
    --container-bg-color: var(--pico-background-color);
    --option-border-color: #e7eaf0;
    --option-hover-bg-color: #e9ecef;
    --option-hover-border-color: #adb5bd;
    --feedback-chosen-color: #ba9e83;
    --feedback-chosen-border-color: #ba9e83;
    --feedback-correct-bg: #edf8f0;
    --feedback-correct-border: #64bd7b;
    --feedback-correct-text: #64bd7b;
    --feedback-incorrect-bg: #fbebeb;
    --feedback-incorrect-border: #df4a48;
    --feedback-incorrect-text: #df4a48;
    --feedback-skipped-bg: #f8eedb;
    --feedback-skipped-border: #ffc245;
    --feedback-skipped-text: #ff9d22;
    --icon-reset: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" height="1em" fill="currentColor" viewBox="0 0 512 512"> <path d="M 469.7142857142857 204.57142857142858 Q 450.2857142857143 130.28571428571428 393.14285714285717 84.57142857142857 L 393.14285714285717 84.57142857142857 L 393.14285714285717 84.57142857142857 Q 334.85714285714283 37.714285714285715 256 36.57142857142857 Q 194.28571428571428 37.714285714285715 145.14285714285714 66.28571428571429 Q 94.85714285714286 96 66.28571428571429 146.28571428571428 L 128 146.28571428571428 L 128 146.28571428571428 Q 145.14285714285714 147.************** 146.28571428571428 164.57142857142858 Q 145.14285714285714 181.71428571428572 128 182.85714285714286 L 35.************** 182.85714285714286 L 18.285714285714285 182.85714285714286 Q 1.1**************8 181.71428571428572 0 164.57142857142858 L 0 54.857142857142854 L 0 54.857142857142854 Q 1.1**************8 37.714285714285715 18.285714285714285 36.57142857142857 Q 35.************** 37.714285714285715 36.57142857142857 54.857142857142854 L 36.57142857142857 124.57142857142857 L 36.57142857142857 124.57142857142857 Q 70.85714285714286 67.************** 128 34.285714285714285 Q 185.14285714285714 1.1**************8 256 0 Q 348.57142857142856 2.2857142857142856 417.14285714285717 57.142857142857146 Q 484.57142857142856 112 505.14285714285717 198.85714285714286 Q 507.************** 217.14285714285714 488 219.************** Q 473.14285714285717 218.28571428571428 469.7142857142857 204.57142857142858 L 469.7142857142857 204.57142857142858 Z M 42.285714285714285 307.************** Q 61.714285714285715 381.7142857142857 118.85714285714286 427.************** L 118.85714285714286 427.************** L 118.85714285714286 427.************** Q 177.14285714285714 474.2857142857143 256 475.************** Q 317.7142857142857 474.2857142857143 366.85714285714283 445.7142857142857 Q 417.14285714285717 416 445.7142857142857 365.7142857142857 L 384 365.7142857142857 L 384 365.7142857142857 Q 366.85714285714283 364.57142857142856 365.7142857142857 347.************** Q 366.85714285714283 330.2857142857143 384 329.14285714285717 L 476.57142857142856 329.14285714285717 L 493.7142857142857 329.14285714285717 Q 509.7142857142857 330.2857142857143 512 347.************** L 512 457.14285714285717 L 512 457.14285714285717 Q 509.7142857142857 474.2857142857143 493.7142857142857 475.************** Q 476.57142857142856 474.2857142857143 475.************** 457.14285714285717 L 475.************** 388.57142857142856 L 475.************** 388.57142857142856 Q 441.14285714285717 444.57142857142856 384 477.7142857142857 Q 326.85714285714283 510.85714285714283 256 512 Q 163.************** 509.7142857142857 96 454.85714285714283 Q 27.**************7 400 6.857142857142857 313.14285714285717 Q 4.571428571428571 294.85714285714283 24 292.57142857142856 Q 38.857142857142854 293.7142857142857 43.************** 307.************** L 42.285714285714285 307.************** Z" /> </svg>');
}

[data-theme=dark] {
    color-scheme: dark;
    --container-bg-color: #181c25;
    --option-border-color: #202632;
    --option-hover-bg-color: #404a5a;
    --option-hover-border-color: #5a6778;
    --feedback-chosen-color: #ba9e83;
    --feedback-chosen-border-color: #ba9e83;
    --feedback-correct-bg: #1c3c2c;
    --feedback-correct-border: #28a745;
    --feedback-correct-text: #d4edda;
    --feedback-incorrect-bg: #4a252c;
    --feedback-incorrect-border: #dc3545;
    --feedback-incorrect-text: #f8d7da;
    --feedback-skipped-bg: #594d2e;
    --feedback-skipped-border: #ffc107;
    --feedback-skipped-text: #fff3cd;
    --icon-reset: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" height="1em" fill="grey" viewBox="0 0 512 512"> <path d="M 469.7142857142857 204.57142857142858 Q 450.2857142857143 130.28571428571428 393.14285714285717 84.57142857142857 L 393.14285714285717 84.57142857142857 L 393.14285714285717 84.57142857142857 Q 334.85714285714283 37.714285714285715 256 36.57142857142857 Q 194.28571428571428 37.714285714285715 145.14285714285714 66.28571428571429 Q 94.85714285714286 96 66.28571428571429 146.28571428571428 L 128 146.28571428571428 L 128 146.28571428571428 Q 145.14285714285714 147.************** 146.28571428571428 164.57142857142858 Q 145.14285714285714 181.71428571428572 128 182.85714285714286 L 35.************** 182.85714285714286 L 18.285714285714285 182.85714285714286 Q 1.1**************8 181.71428571428572 0 164.57142857142858 L 0 54.857142857142854 L 0 54.857142857142854 Q 1.1**************8 37.714285714285715 18.285714285714285 36.57142857142857 Q 35.************** 37.714285714285715 36.57142857142857 54.857142857142854 L 36.57142857142857 124.57142857142857 L 36.57142857142857 124.57142857142857 Q 70.85714285714286 67.************** 128 34.285714285714285 Q 185.14285714285714 1.1**************8 256 0 Q 348.57142857142856 2.2857142857142856 417.14285714285717 57.142857142857146 Q 484.57142857142856 112 505.14285714285717 198.85714285714286 Q 507.************** 217.14285714285714 488 219.************** Q 473.14285714285717 218.28571428571428 469.7142857142857 204.57142857142858 L 469.7142857142857 204.57142857142858 Z M 42.285714285714285 307.************** Q 61.714285714285715 381.7142857142857 118.85714285714286 427.************** L 118.85714285714286 427.************** L 118.85714285714286 427.************** Q 177.14285714285714 474.2857142857143 256 475.************** Q 317.7142857142857 474.2857142857143 366.85714285714283 445.7142857142857 Q 417.14285714285717 416 445.7142857142857 365.7142857142857 L 384 365.7142857142857 L 384 365.7142857142857 Q 366.85714285714283 364.57142857142856 365.7142857142857 347.************** Q 366.85714285714283 330.2857142857143 384 329.14285714285717 L 476.57142857142856 329.14285714285717 L 493.7142857142857 329.14285714285717 Q 509.7142857142857 330.2857142857143 512 347.************** L 512 457.14285714285717 L 512 457.14285714285717 Q 509.7142857142857 474.2857142857143 493.7142857142857 475.************** Q 476.57142857142856 474.2857142857143 475.************** 457.14285714285717 L 475.************** 388.57142857142856 L 475.************** 388.57142857142856 Q 441.14285714285717 444.57142857142856 384 477.7142857142857 Q 326.85714285714283 510.85714285714283 256 512 Q 163.************** 509.7142857142857 96 454.85714285714283 Q 27.**************7 400 6.857142857142857 313.14285714285717 Q 4.571428571428571 294.85714285714283 24 292.57142857142856 Q 38.857142857142854 293.7142857142857 43.************** 307.************** L 42.285714285714285 307.************** Z" /> </svg>');
}

.card {
    font-family: -apple-system-font, BlinkMacSystemFont, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial, sans-serif;
    text-align: left;
    color: var(--pico-color);
    background-color: var(--pico-background-color);
}

html,
body,
#content,
#qa,
.container {
    height: 100%;
    margin: 0;
    padding: 0;
}

article{
    color: var(--pico-color);
    background-color: var(--pico-background-color);
}

.reset_btn {
    background-image: var(--icon-reset);
    /* background-image: var(--pico-icon-close); */
    width: 24px;
    height: 24px;

}

#deck_container,
#type_container,
#tag_container,
#source_container,
#time_container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 0.8em;
    color: var(--pico-muted-color);
    margin-top: 0.5em;
    padding: 4px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
}

#tag_container span.tag {
    display: inline-block;
    /* 让 span 可以设置 padding 和 margin */
    padding: 2px 6px;
    background-color: #007bff;
    /* 主要的品牌蓝色 */
    color: white;
    border-radius: 4px;
    /* 圆角 */
    font-size: 0.8em;
    font-weight: 500;
    /* 中等粗细的字体 */
    transition: background-color 0.3s ease, transform 0.2s ease;
    /* 添加过渡效果 */
    cursor: default;
    /* 默认光标，如果你希望点击可以做些什么，可以改成 pointer */
    white-space: nowrap;
    /* 防止标签内的文本换行 */
}

/* 鼠标悬停时的样式 */
#tag_container span.tag:hover {
    background-color: #0056b3;
    /* 深一点的蓝色 */
    transform: translateY(-1px);
    /* 轻微向上移动 */
}

#deck_container::before {
    content: "牌组:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}
#time_container::before {
    content: "用时:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}
#source_container::before {
    content: "出处:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}

/* 第一个标签前的 "标签:" 文本样式 */
#tag_container::before {
    content: "标签:";
    font-weight: bold;
    color: var(--pico-color);
    margin-right: 8px;
    font-size: 1em;
}

.heading {
    text-align: left;
    padding-left: 0.5em;
    border-left: 4px solid #4891e7;
    color: var(--pico-color);
    font-weight: bold;
}


.row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

span.cloze {
    position: relative;
    display: inline-block;
    cursor: pointer;
    transition: color 0.3s ease;
    font-weight: 600;
    border-bottom: 1px solid var(--pico-primary-underline);
}

span.cloze.activated {
    color: transparent;
    background-color: var(--pico-background-color);
}

span.cloze.activated::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    height: 1em;
    opacity: 0.85;
    transition: all 0.3s ease;
    z-index: 9;
}

span.cloze.activated:hover::after {
    opacity: 1;
}

.question .q-body {
    color: var(--pico-color);
    line-height: 28px;
    font: 1.1em/1.5 "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    font-weight: bold;
    word-break: break-word;
    word-wrap: break-word;
}


.question .q-answer {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    line-height: 1.5;
    margin-top: 16px;
    margin-bottom: 16px;
    padding: 0.5em 1em;
    border-radius: 5px;
    cursor: pointer;
    word-break: break-word;
    word-wrap: break-word;
    white-space: normal;
    min-height: 40px;
}

/* 动画定义 */
@keyframes reorder {
    0% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0;
        transform: scale(0.9);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.choice_div {
    color: var(--pico-color);
    background-color: var(--pico-background-color);
}

#recover_order {
    border: none;
    padding: 0.4em 1em;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    margin: 1em 0;
    cursor: pointer;
    border-radius: 0.8em;
    color: var(--pico-primary-inverse);
    background-color: var(--pico-primary-background);
}

.normal {
    background-color: var(--container-bg-color);
    border: 1px solid var(--option-border-color);
    color: var(--pico-color);
}



.normal:hover {
    border: 1px solid #ba9e83;
}

.chosen {
    color: #ba9e83 !important;
    border: 1px solid #ba9e83;
}

.correct {
    background-color: var(--feedback-correct-bg);
    color: var(--feedback-correct-text);
    border: 1px solid var(--feedback-correct-border);
    font-weight: bold;
}


.should-select {
    background-color: var(--feedback-skipped-bg);
    color: var(--feedback-skipped-text);
    border: 1px solid var(--feedback-skipped-border);
    font-weight: bold;
}

.incorrect {
    background-color: var(--feedback-incorrect-bg);
    color: var(--feedback-incorrect-text);
    border: 1px solid var(--feedback-incorrect-border);
    font-weight: bold;
}


.question .q-answer .icon {
    width: 1em;
    height: 1em;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    position: relative;
    left: 12px;
    align-self: center;
    margin: 0 8px;
}

.choosable {
    align-items: flex-start;
    border-radius: 10px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    font-variant-numeric: tabular-nums;
    justify-content: space-between;
    margin-bottom: 16px;
    margin-top: 16px;
    padding: 10px 24px;
    text-size-adjust: 100%;
    white-space: normal;
    word-break: break-word;
    word-wrap: break-word;
    line-height: 1.5;
    min-height: 40px;
}

#q-clozes {
    margin-bottom: 1em;
}

.cloze-hide {
    font-weight: bold;
    color: vvar(--pico-primary-background);
    border-bottom: 1px solid var(--pico-primary-background);
    cursor: pointer;
    /* filter: blur(0.25em); */
}

.cloze-show {
    font-weight: bold;
    color: var(--pico-primary-background);
    border-bottom: 1px solid var(--pico-primary-background);
    cursor: pointer;
}

.highlight{
    color: #fda31d;
}
</style>
<script>
    var configDefaults = {
        "dark_mode": false,   // 暗黑模式
        "show_deck": true,    // 显示牌组
        "show_tag": true,    // 显示标签
        "show_time": true,    // 显示用时
        "show_type": true,    // 显示题型
        "random_option": false,   // 随机选项
        "auto_flip": true,    // 自动翻转
        "statistics": true,    // 显示统计信息
        "play_sound": true,    // 播放音效
        "cloze_mode": false,   // 填空模式
        "show_source": true,   // 显示出处
    };
    function handleCheckboxChange(event) {
        console.log(event);
        const checkbox = event.target;
        const configKey = checkbox.dataset.configKey;
        const isChecked = checkbox.checked;
        localStorage.setItem(configKey, isChecked.toString());

        console.log({
            configKey, isChecked
        });

        if (configKey === "dark_mode") {
            console.log("dark mode");
            let ele = document.querySelector("html");

            if (isChecked) {
                ele.setAttribute("data-theme", "dark");
            }

            else {
                ele.setAttribute("data-theme", "light");
            }
        }

        else if (configKey === "show_deck") {

            if (isChecked) {
                document.getElementById("deck_container").style.display = "flex";
                document.getElementById("deck_container_fallback").style.display = "none";
            }

            else {
                document.getElementById("deck_container").style.display = "none";
                document.getElementById("deck_container_fallback").style.display = "flex";
            }
        }

        else if (configKey === "show_tag") {
            if (isChecked) {
                document.getElementById("tag_container").style.display = "flex";
            }

            else {
                document.getElementById("tag_container").style.display = "none";
            }
        }
        else if (configKey === "show_source") {
            if (isChecked) {
                document.getElementById("source_container").style.display = "flex";
            }

            else {
                document.getElementById("source_container").style.display = "none";
            }
        }
        else if (configKey === "show_time") {
            if (isChecked) {
                document.getElementById("time_container").style.display = "flex";
            }

            else {
                document.getElementById("time_container").style.display = "none";
            }
        }
    }

    function checkAndSetTheme() {
        if (localStorage.getItem("dark_mode") === "false" || localStorage.getItem("dark_mode") === "true") {
            return;
        }

        const htmlElement = document.querySelector("html");
        const bodyElement = document.querySelector("body");

        if (!htmlElement || !bodyElement) {
            console.error("HTML or Body element not found!");
            return;
        }

        const nightModeClasses = ["night-mode",
            "night_mode",
            "nightMode"];

        // 检查 html 元素
        const isHtmlNightMode = nightModeClasses.some(className => htmlElement.classList.contains(className));

        // 检查 body 元素
        const isBodyNightMode = nightModeClasses.some(className => bodyElement.classList.contains(className));

        // 如果 html 或 body 中任何一个包含了夜间模式类，则认为是夜间模式
        const isDarkMode = isHtmlNightMode || isBodyNightMode;

        if (isDarkMode) {
            console.log("dark");
            htmlElement.setAttribute("data-theme", "dark");
            localStorage.setItem("dark_mode", true);
        }

        else {
            console.log("light");
            htmlElement.setAttribute("data-theme", "light");
            localStorage.setItem("dark_mode", false);
        }
    }

    // 初始化配置
    function initializeConfig() {
        checkAndSetTheme();
        const configCheckboxes = document.querySelectorAll('input[type="checkbox"][role="switch"][data-config-key]');
        if(localStorage.getItem("show_footer") == null){
            localStorage.setItem("show_footer", true);
        }
        console.log("111", localStorage.getItem("show_footer"));
        configCheckboxes.forEach(checkbox => {
            const configKey = checkbox.dataset.configKey;
            let storedValue = localStorage.getItem(configKey);

            // 根据不同的配置项，解析 localStorage 中的值
            if (storedValue === null) {
                // 如果 localStorage 中没有该项，使用默认值
                checkbox.checked = configDefaults[configKey];
                localStorage.setItem(configKey, configDefaults[configKey]);
            }

            else {
                checkbox.checked = storedValue === "true";
            }

            console.log(configKey, checkbox.checked) // 添加事件监听器
            checkbox.addEventListener("change", handleCheckboxChange);

            if (configKey === "show_deck") {
                if (checkbox.checked) {
                    document.getElementById("deck_container").style.display = "flex";
                }

                else {
                    document.getElementById("deck_container").style.display = "none";
                }
            }

            else if (configKey === "show_tag") {
                if (checkbox.checked) {
                    document.getElementById("tag_container").style.display = "flex";
                }

                else {
                    document.getElementById("tag_container").style.display = "none";
                }
            }

            else if (configKey === "show_time") {
                if (checkbox.checked) {
                    let interval_id = Number.parseInt(localStorage.getItem("timer_interval") || "0");

                    if (interval_id > 0) {
                        clearInterval(interval_id);
                    }

                    document.getElementById("time_container").style.display = "flex";
                }

                else {
                    document.getElementById("time_container").style.display = "none";
                }
            }
        });

        // 绑定关闭按钮事件
        const closeBtn = document.getElementById("close-config-btn");

        if (closeBtn) {
            closeBtn.addEventListener("click", () => {
                const dialog = document.getElementById("config-dialog");

                if (dialog) {
                    dialog.close();
                }
            });
        }
    }


    function toggleConfig() {
        console.log("toggleConfig");
        const configDialog = document.getElementById('config-dialog');

        if (configDialog.open) {
            configDialog.close();
        }

        else {
            configDialog.showModal();
        }
    }

</script>
<script>function autoFlip() {
        document.getElementById("qa").style.display = "none";

        if (window.pycmd) {
            pycmd("ans");
        }

        else if (window.showAnswer) {
            showAnswer();
        }

        document.getElementById("qa").style.display = "block";
    }

    function shuffleArray(array) {
        var currentIndex = array.length,
            randomIndex;

        while (0 !== currentIndex) {
            randomIndex = Math.floor(Math.random() * currentIndex);
            currentIndex -= 1;
            temporaryValue = array[currentIndex];
            array[currentIndex] = array[randomIndex];
            array[randomIndex] = temporaryValue;
        }

        return array;
    }

    function updateClozes() {
        var mask = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";

        var correctAnswers = `{{Answers}}`.split("||");
        var q_cloze_div = document.getElementById("q-clozes");
        q_cloze_div.style.display = "block";
        var q_options_div = document.getElementById("q-options");
        q_options_div.style.display = "none";

        var options = `{{Options}}`.split("||");
        let final_html_content = "";

        for (let i = 0; i < correctAnswers.length; i++) {
            let option_content = options[parseInt(correctAnswers[i]) - 1];
            let span = document.createElement("span");

            span.setAttribute("cloze_id", `c${i}`);

            span.setAttribute("cloze_text", `${option_content}`);
            span.innerHTML = mask;
            span.className = "cloze-hide";

            final_html_content += `${span.outerHTML}`;
            if (i != correctAnswers.length - 1) final_html_content += "，";
        }

        q_cloze_div.innerHTML = final_html_content;

        document.querySelectorAll(".cloze-hide, .cloze-show").forEach(ele => {
            let cloze_id = ele.getAttribute("cloze_id");

            ele.onclick = () => {
                [].forEach.call(q_cloze_div.querySelectorAll(`span[cloze_id=${cloze_id}]`),
                    (ele) => {
                        if (ele.className === 'cloze-hide') {
                            ele.innerHTML = ele.getAttribute("cloze_text");
                            ele.className = "cloze-show";
                        }

                        else {
                            ele.innerHTML = mask;
                            ele.className = "cloze-hide";
                        }∑
                    })
            }
        });
    }

</script>