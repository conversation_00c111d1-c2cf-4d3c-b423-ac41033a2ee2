#
# Generated file, do not edit.
#

list(APPEND FLUTTER_PLUGIN_LIST
  audioplayers_linux
  desktop_drop
  file_selector_linux
  flutter_js
  flutter_localization
  flutter_machineid
  flutter_udid
  gtk
  hotkey_manager_linux
  irondash_engine_context
  isar_flutter_libs
  local_notifier
  media_kit_libs_linux
  media_kit_video
  open_file_linux
  pasteboard
  screen_capturer_linux
  super_native_extensions
  url_launcher_linux
  volume_controller
  window_size
)

list(APPEND FLUTTER_FFI_PLUGIN_LIST
  rinf
)

set(PLUGIN_BUNDLED_LIBRARIES)

foreach(plugin ${FLUTTER_PLUGIN_LIST})
  add_subdirectory(flutter/ephemeral/.plugin_symlinks/${plugin}/linux plugins/${plugin})
  target_link_libraries(${BINARY_NAME} PRIVATE ${plugin}_plugin)
  list(APPEND PLUGIN_BUNDLED_LIBRARIES $<TARGET_FILE:${plugin}_plugin>)
  list(APPEND PLUGIN_BUNDLED_LIBRARIES ${${plugin}_bundled_libraries})
endforeach(plugin)

foreach(ffi_plugin ${FLUTTER_FFI_PLUGIN_LIST})
  add_subdirectory(flutter/ephemeral/.plugin_symlinks/${ffi_plugin}/linux plugins/${ffi_plugin})
  list(APPEND PLUGIN_BUNDLED_LIBRARIES ${${ffi_plugin}_bundled_libraries})
endforeach(ffi_plugin)
