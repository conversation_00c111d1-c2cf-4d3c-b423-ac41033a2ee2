package top.kevin2li.guru

import android.content.Context
import com.ichi2.anki.api.AddContentApi
import org.json.JSONArray
import android.util.Log
import android.net.Uri

class Anki<PERSON><PERSON>per(val context: Context) {
    private var api: AddContentApi = AddContentApi(context)

    public fun isApiAvailable(): <PERSON><PERSON><PERSON> {
        return AddContentApi.getAnkiDroidPackageName(context) != null
    }
    
    public fun getDeckId(name: String): Long? {
        val deckList = api.deckList
        if (deckList != null) {
            for (entry in deckList.entries) {
                if (entry.value == name) {
                    return entry.key
                }
            }
        }
        return api.addNewDeck(name)
    }

    public fun getModelId(name: String): Long? {
        val modelList = api.modelList
        if (modelList != null) {
            for (entry in modelList.entries) {
                if (entry.value == name) {
                    return entry.key
                }
            }
        }
        if(name == InternalModelNames.KEVIN_TEXT_QA_CARD){
            return api.addNewCustomModel(
                KevinTextQACardModel.NAME,
                KevinTextQACardModel.FIELDS,
                KevinTextQACardModel.CARD_NAMES,
                KevinTextQACardModel.QFMT,
                KevinTextQACardModel.AFMT,
                KevinTextQACardModel.CSS,
                null,
                null
            )
        }else if(name == InternalModelNames.KEVIN_TEXT_CLOZE){
        }else if(name == InternalModelNames.KEVIN_IMAGE_CLOZE){
            return api.addNewCustomModel(
                KevinImageClozeModel.NAME,
                KevinImageClozeModel.FIELDS,
                KevinImageClozeModel.CARD_NAMES,
                KevinImageClozeModel.QFMT,
                KevinImageClozeModel.AFMT,
                KevinImageClozeModel.CSS,
                null,
                null
            )
        }else if(name == InternalModelNames.KEVIN_IMAGE_QA_CARD){
            
        }
        return null 
    }

    public fun getDeckList(): List<String> {
        val deckList = api.deckList
        return deckList?.values?.toList() ?: emptyList()
    }

    public fun getModelList(): List<String> {
        val modelList = api.modelList
        return modelList?.values?.toList() ?: emptyList()
    }

    public fun addNotes(
        deckName: String,
        modelName: String,
        fieldsList: List<Array<String>>,
        tagsList: List<Set<String>?>?
    ): Int {
        val did = getDeckId(deckName)
        val mid = getModelId(modelName)
        Log.d("AnkiGuru", "did: $did")
        Log.d("AnkiGuru", "mid: $mid")
        if (did != null && mid != null) {
            return api.addNotes(mid, did, fieldsList, tagsList)
        }
        return -1
    }

    public fun addMedia(
        fileUri: Uri,
        preferredName: String,
        mimeType: String
    ): String? {
        return api.addMediaFromUri(fileUri, preferredName, mimeType)
    }
}