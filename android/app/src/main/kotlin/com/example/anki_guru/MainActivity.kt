package top.kevin2li.guru

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.BatteryManager
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.provider.MediaStore
import android.util.Base64
import android.util.Log
import androidx.annotation.NonNull
import androidx.core.content.FileProvider
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import net.i2p.crypto.eddsa.EdDSAEngine
import net.i2p.crypto.eddsa.EdDSAPublicKey
import net.i2p.crypto.eddsa.spec.EdDSANamedCurveTable
import net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec

class MainActivity : FlutterActivity() {
  private val CHANNEL = "samples.flutter.dev/battery"
  private val mApi by lazy { AnkiHelper(this) }

  override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
    super.configureFlutterEngine(flutterEngine)
    MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
            call,
            result ->
      if (call.method == MethodNames.GET_BATTERY_LEVEL) {
        // 获取电池电量(调试用)
        val batteryLevel = getBatteryLevel()
        if (batteryLevel != -1) {
          result.success(batteryLevel)
        } else {
          result.error("UNAVAILABLE", "Battery level not available.", null)
        }
      } else if (call.method == MethodNames.IS_API_AVAILABLE) {
        // 判断API是否可用
        val isApiAvailable = mApi.isApiAvailable()
        Log.d("AnkiGuru", "isApiAvailable: $isApiAvailable")
        result.success(isApiAvailable)
      } else if (call.method == MethodNames.GET_DECK_LIST) {
        // 获取牌组列表
        val deckList = mApi.getDeckList()
        result.success(deckList)
      } else if (call.method == MethodNames.GET_MODEL_LIST) {
        // 获取模版列表
        val modelList = mApi.getModelList()
        result.success(modelList)
      } else if (call.method == MethodNames.ADD_NOTES) {
        // 添加卡片
        val deckName: String? = call.argument("deckName")
        val modelName: String? = call.argument("modelName")
        val rawFieldsList: List<List<String>>? = call.argument("fieldsList")
        val rawTagsList: List<List<String>>? = call.argument("tagsList")

        Log.d("AnkiGuru", "deckName: $deckName")
        Log.d("AnkiGuru", "modelName: $modelName")
        Log.d("AnkiGuru", "fieldsList: $rawFieldsList")
        Log.d("AnkiGuru", "tagsList: $rawTagsList")

        if (deckName != null && modelName != null && rawFieldsList != null) {
          // 转换 fieldsList 从 List<List<String>> 到 List<Array<String>>
          val fieldsList = rawFieldsList.map { it.toTypedArray() }
          // 转换 tagsList 从 List<List<String>> 到 List<Set<String>?>
          val tagsList = rawTagsList?.map { it.toSet() }

          val res = mApi.addNotes(deckName, modelName, fieldsList, tagsList)
          result.success(res)
        } else {
          result.error("INVALID_ARGUMENTS", "Required parameters cannot be null", null)
        }
      } else if (call.method == MethodNames.ADD_MEDIA) {
        // 添加媒体
        val fileUri: String? = call.argument("fileUri")
        val preferredName: String? = call.argument("preferredName")
        val mimeType: String? = call.argument("mimeType")

        Log.d("AnkiGuru", "fileUri: $fileUri")
        Log.d("AnkiGuru", "preferredName: $preferredName")
        Log.d("AnkiGuru", "mimeType: $mimeType")

        if (fileUri != null && preferredName != null && mimeType != null) {
          val uri =
                  if (fileUri.startsWith("content://")) {
                    Uri.parse(fileUri)
                  } else {
                    getContentUri(fileUri) ?: throw IllegalArgumentException("Invalid file path")
                  }
          try {
            // 添加权限授予
            context.grantUriPermission(
                    "com.ichi2.anki", // AnkiDroid 的包名
                    uri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
            val res = mApi.addMedia(uri, preferredName, mimeType)
            Log.d("AnkiGuru", "Final result: $res")
            if (res != null) {
              result.success(res)
            } else {
              result.error("MEDIA_ERROR", "Failed to add media", null)
            }
          } catch (e: Exception) {
            Log.e("AnkiGuru", "Error processing media: ${e.message}")
            e.printStackTrace()
            result.error("MEDIA_ERROR", e.message, null)
          } finally {
            // 操作完成后撤销权限
            context.revokeUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
          }
        } else {
          result.error("INVALID_ARGUMENTS", "Required parameters cannot be null", null)
        }
      } else if (call.method == MethodNames.VERIFY_SIGNATURE) {
        // 验证签名
        try {
          val message: String? = call.argument("message")
          val signature: String? = call.argument("signature")
          Log.d("AnkiGuru", "message: $message, signature: $signature")

          if (message == null || signature == null) {
            throw Exception("Message or signature is null")
          }

          val publicKeyStr: String =
                  "88e4d55c1b2b7d5d9121a0fadb7ae17aac2cbb926e284de42adcb10bff749530"
          val publicKeyBytes = publicKeyStr.chunked(2).map { it.toInt(16).toByte() }.toByteArray()
          val messageBytes = message.chunked(2).map { it.toInt(16).toByte() }.toByteArray()
          val signatureBytes = signature.chunked(2).map { it.toInt(16).toByte() }.toByteArray()

          val spec = EdDSANamedCurveTable.getByName(EdDSANamedCurveTable.ED_25519)
          val pubKeySpec = EdDSAPublicKeySpec(publicKeyBytes, spec)
          val pubKey = EdDSAPublicKey(pubKeySpec)
          val sgr = EdDSAEngine()
          sgr.initVerify(pubKey)
          sgr.update(messageBytes)
          val verified = sgr.verify(signatureBytes)
          result.success(verified)
        } catch (err: Exception) {
          result.error("VERIFY_FAILED", err.message, null)
        }
      } else if (call.method == MethodNames.GET_NATIVE_LIBRARY_DIR) {
        try {
          val nativeLibraryDir = context?.applicationInfo?.nativeLibraryDir
          result.success(nativeLibraryDir)
        } catch (err: Exception) {
          result.error("GET_NATIVE_LIBRARY_DIR_FAILED", err.message, null)
        }
      } else if (call.method == "startForegroundService") {
        val intent = Intent(this, AnkiSyncService::class.java)
        startForegroundService(intent)
        result.success(null)
      } else if (call.method == "stopForegroundService") {
        val intent = Intent(this, AnkiSyncService::class.java)
        stopService(intent)
        result.success(null)
      } else if (call.method == MethodNames.GET_CLIPBOARD_DATA) {
        try {
          // 获取剪贴板管理器
          val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
          val clip = clipboard.primaryClip

          if (clip != null && clip.itemCount > 0) {
            val item = clip.getItemAt(0)

            // 尝试获取文本内容
            val text = item.text?.toString()
            if (text != null) {
              result.success(mapOf("type" to "text", "content" to text))
              return@setMethodCallHandler
            }

            // 尝试获取URI内容
            val uri = item.uri
            if (uri != null) {
              val mimeType = contentResolver.getType(uri)
              if (mimeType?.startsWith("image/") == true) {
                try {
                  val bitmap = MediaStore.Images.Media.getBitmap(contentResolver, uri)
                  // 将bitmap转换为base64
                  val byteArrayOutputStream = ByteArrayOutputStream()
                  bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream)
                  val byteArray = byteArrayOutputStream.toByteArray()
                  val base64String = Base64.encodeToString(byteArray, Base64.DEFAULT)

                  result.success(
                          mapOf(
                                  "type" to "image",
                                  "mimeType" to mimeType,
                                  "content" to base64String
                          )
                  )
                  return@setMethodCallHandler
                } catch (e: Exception) {
                  Log.e("AnkiGuru", "Error reading image from clipboard: ${e.message}")
                }
              }
            }
          }

          // 剪贴板为空或不支持的内容类型
          result.success(null)
        } catch (e: Exception) {
          result.error("CLIPBOARD_ERROR", "Failed to get clipboard data: ${e.message}", null)
        }
      } else if (call.method == MethodNames.SET_CLIPBOARD_DATA) {
        try {
          val type: String? = call.argument("type")
          val content: String? = call.argument("content")

          if (type == null || content == null) {
            result.error("INVALID_ARGUMENTS", "Type and content must not be null", null)
            return@setMethodCallHandler
          }

          val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
          val clip =
                  when (type) {
                    "text" -> ClipData.newPlainText("Text", content)
                    "image" -> {
                      try {
                        // 将base64转换为bitmap
                        val imageBytes = Base64.decode(content, Base64.DEFAULT)
                        val bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)

                        // 使用UUID代替时间戳作为文件名
                        val tempFile =
                                File(cacheDir, "clipboard_image_${java.util.UUID.randomUUID()}.png")
                        FileOutputStream(tempFile).use { out ->
                          bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
                        }

                        // 创建ContentUri
                        val contentUri =
                                FileProvider.getUriForFile(
                                        this,
                                        "${applicationContext.packageName}.fileprovider",
                                        tempFile
                                )

                        // 授予临时权限
                        grantUriPermission(
                                "com.android.clipboard",
                                contentUri,
                                Intent.FLAG_GRANT_READ_URI_PERMISSION
                        )

                        ClipData.newUri(contentResolver, "Image", contentUri)
                      } catch (e: Exception) {
                        Log.e("AnkiGuru", "Error setting image to clipboard: ${e.message}")
                        result.error(
                                "IMAGE_PROCESSING_ERROR",
                                "Failed to process image: ${e.message}",
                                null
                        )
                        return@setMethodCallHandler
                      }
                    }
                    "uri" -> ClipData.newUri(contentResolver, "URI", Uri.parse(content))
                    else -> {
                      result.error("INVALID_TYPE", "Unsupported clipboard data type: $type", null)
                      return@setMethodCallHandler
                    }
                  }

          clipboard.setPrimaryClip(clip)
          result.success(true)
        } catch (e: Exception) {
          result.error("CLIPBOARD_ERROR", "Failed to set clipboard data: ${e.message}", null)
        }
      } else {
        // 其他方法未实现
        result.notImplemented()
      }
    }
  }

  private fun getContentUri(filePath: String): Uri? {
    try {
      val file = File(filePath)
      val contentUri =
              FileProvider.getUriForFile(context, "${context.packageName}.fileprovider", file)
      Log.d("AnkiGuru", "Content URI: $contentUri")
      return contentUri
    } catch (e: Exception) {
      Log.e("AnkiGuru", "Error getting content URI: ${e.message}")
      return null
    }
  }
  private fun getBatteryLevel(): Int {
    val batteryLevel: Int
    if (VERSION.SDK_INT >= VERSION_CODES.LOLLIPOP) {
      val batteryManager = getSystemService(Context.BATTERY_SERVICE) as BatteryManager
      batteryLevel = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
    } else {
      val intent =
              ContextWrapper(applicationContext)
                      .registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
      batteryLevel =
              intent!!.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) * 100 /
                      intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
    }

    return batteryLevel
  }
}
