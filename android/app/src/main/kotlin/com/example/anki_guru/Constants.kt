package top.kevin2li.guru

object InternalModelNames {
    const val KEVIN_IMAGE_QA_CARD = "Kevin Image QA Card v2"
    const val KEVIN_IMAGE_CLOZE = "Kevin Image Cloze v5"
    const val KEVIN_TEXT_QA_CARD = "Kevin Text QA Card v2"
    const val KEVIN_TEXT_CLOZE = "Kevin Text Cloze v3"
}

object MethodNames {
    const val BATTERY_CHANNEL = "samples.flutter.dev/battery"
    const val GET_BATTERY_LEVEL = "getBatteryLevel"
    const val IS_API_AVAILABLE = "isApiAvailable"
    const val GET_DECK_LIST = "getDeckList"
    const val GET_MODEL_LIST = "getModelList"
    const val ADD_NOTES = "addNotes"
    const val ADD_MEDIA = "addMedia"
    const val VERIFY_SIGNATURE = "verifySignature"
    const val GET_NATIVE_LIBRARY_DIR = "getNativeLibraryDir"
    const val GET_CLIPBOARD_DATA = "getClipboardData"
    const val SET_CLIPBOARD_DATA = "setClipboardData"
}
