/**
 * Minified by jsDelivr using Terser v5.37.0.
 * Original file: /npm/markmap-lib@0.18.8/dist/browser/index.iife.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(e,t){"use strict";const n={jsdelivr:e=>`https://cdn.jsdelivr.net/npm/${e}`,unpkg:e=>`https://unpkg.com/${e}`};class s{constructor(){this.providers={...n},this.provider="jsdelivr"}async getFastestProvider(e=5e3,t="npm2url/dist/index.cjs"){const n=new AbortController;let s=0;try{return await new Promise(((r,i)=>{Promise.all(Object.entries(this.providers).map((async([e,s])=>{try{await async function(e,t){const n=await fetch(e,{signal:t});if(!n.ok)throw n;await n.text()}(s(t),n.signal),r(e)}catch{}}))).then((()=>i(new Error("All providers failed")))),s=setTimeout(i,e,new Error("Timed out"))}))}finally{n.abort(),clearTimeout(s)}}async findFastestProvider(e,t){return this.provider=await this.getFastestProvider(e,t),this.provider}setProvider(e,t){t?this.providers[e]=t:delete this.providers[e]}getFullUrl(e,t=this.provider){if(e.includes("://"))return e;const n=this.providers[t];if(!n)throw new Error(`Provider ${t} not found`);return n(e)}}class r{constructor(){this.listeners=[]}tap(e){return this.listeners.push(e),()=>this.revoke(e)}revoke(e){const t=this.listeners.indexOf(e);t>=0&&this.listeners.splice(t,1)}revokeAll(){this.listeners.splice(0)}call(...e){for(const t of this.listeners)t(...e)}}function i(){}function o(e,t){return(...n)=>t(e,...n)}Math.random().toString(36).slice(2,8);
/*! @gera2ld/jsx-dom v2.2.2 | ISC License */
const a="http://www.w3.org/1999/xlink",c={show:a,actuate:a,href:a};function l(e,t,...n){return function(e,t){let n;if("string"==typeof e)n=1;else{if("function"!=typeof e)throw new Error("Invalid VNode type");n=2}return{vtype:n,type:e,props:t}}(e,t=Object.assign({},t,{children:1===n.length?n[0]:n}))}function u(e){return e.children}const h={isSvg:!1};function d(e,t){Array.isArray(t)||(t=[t]),(t=t.filter(Boolean)).length&&e.append(...t)}const p={className:"class",labelFor:"for"};function f(e,t,n,s){if(t=p[t]||t,!0===n)e.setAttribute(t,"");else if(!1===n)e.removeAttribute(t);else{const r=s?c[t]:void 0;void 0!==r?e.setAttributeNS(r,t,n):e.setAttribute(t,n)}}function m(e,t){return Array.isArray(e)?e.map((e=>m(e,t))).reduce(((e,t)=>e.concat(t)),[]):E(e,t)}function E(e,t=h){if(null==e||"boolean"==typeof e)return null;if(e instanceof Node)return e;if(2===(null==(n=e)?void 0:n.vtype)){const{type:n,props:s}=e;if(n===u){const e=document.createDocumentFragment();if(s.children){d(e,m(s.children,t))}return e}return E(n(s),t)}var n;if((e=>"string"==typeof e||"number"==typeof e)(e))return document.createTextNode(`${e}`);if((e=>1===(null==e?void 0:e.vtype))(e)){let n;const{type:s,props:r}=e;if(t.isSvg||"svg"!==s||(t=Object.assign({},t,{isSvg:!0})),n=t.isSvg?document.createElementNS("http://www.w3.org/2000/svg",s):document.createElement(s),function(e,t,n){for(const s in t)if("key"!==s&&"children"!==s&&"ref"!==s)if("dangerouslySetInnerHTML"===s)e.innerHTML=t[s].__html;else if("innerHTML"===s||"textContent"===s||"innerText"===s||"value"===s&&["textarea","select"].includes(e.tagName)){const n=t[s];null!=n&&(e[s]=n)}else s.startsWith("on")?e[s.toLowerCase()]=t[s]:f(e,s,t[s],n.isSvg)}(n,r,t),r.children){let e=t;t.isSvg&&"foreignObject"===s&&(e=Object.assign({},e,{isSvg:!1}));const i=m(r.children,e);null!=i&&d(n,i)}const{ref:i}=r;return"function"==typeof i&&i(n),n}throw new Error("mount: Invalid Vnode!")}function T(...e){return E(l(...e))}const _=function(e){const t={};return function(...n){const s=`${n[0]}`;let r=t[s];return r||(r={value:e(...n)},t[s]=r),r.value}}((e=>{document.head.append(T("link",{rel:"preload",as:"script",href:e}))})),A={};async function g(e,t){var n;const s="script"===e.type&&(null==(n=e.data)?void 0:n.src)||"";if(e.loaded||(e.loaded=A[s]),!e.loaded){const n=function(){const e={};return e.promise=new Promise(((t,n)=>{e.resolve=t,e.reject=n})),e}();if(e.loaded=n.promise,"script"===e.type&&(document.head.append(T("script",{...e.data,onLoad:()=>n.resolve(),onError:n.reject})),s?A[s]=e.loaded:n.resolve()),"iife"===e.type){const{fn:s,getParams:r}=e.data;s(...(null==r?void 0:r(t))||[]),n.resolve()}}await e.loaded}async function C(e,t){e.forEach((e=>{var t;"script"===e.type&&(null==(t=e.data)?void 0:t.src)&&_(e.data.src)})),t={getMarkmap:()=>window.markmap,...t};for(const n of e)await g(n,t)}function b(e){return{type:"script",data:{src:e}}}function k(e){return{type:"stylesheet",data:{href:e}}}var S,N;(N=S||(S={})).Root="root",N.Text="text",N.Directive="directive",N.Comment="comment",N.Script="script",N.Style="style",N.Tag="tag",N.CDATA="cdata",N.Doctype="doctype";const I=S.Root,D=S.Text,y=S.Directive,O=S.Comment,L=S.Script,R=S.Style,M=S.Tag,v=S.CDATA,x=S.Doctype;let w=class{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(e){this.parent=e}get previousSibling(){return this.prev}set previousSibling(e){this.prev=e}get nextSibling(){return this.next}set nextSibling(e){this.next=e}cloneNode(e=!1){return X(this,e)}};class F extends w{constructor(e){super(),this.data=e}get nodeValue(){return this.data}set nodeValue(e){this.data=e}}class P extends F{constructor(){super(...arguments),this.type=S.Text}get nodeType(){return 3}}class B extends F{constructor(){super(...arguments),this.type=S.Comment}get nodeType(){return 8}}class U extends F{constructor(e,t){super(t),this.name=e,this.type=S.Directive}get nodeType(){return 1}}class H extends w{constructor(e){super(),this.children=e}get firstChild(){var e;return null!==(e=this.children[0])&&void 0!==e?e:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(e){this.children=e}}class G extends H{constructor(){super(...arguments),this.type=S.CDATA}get nodeType(){return 4}}let $=class extends H{constructor(){super(...arguments),this.type=S.Root}get nodeType(){return 9}};class q extends H{constructor(e,t,n=[],s=("script"===e?S.Script:"style"===e?S.Style:S.Tag)){super(n),this.name=e,this.attribs=t,this.type=s}get nodeType(){return 1}get tagName(){return this.name}set tagName(e){this.name=e}get attributes(){return Object.keys(this.attribs).map((e=>{var t,n;return{name:e,value:this.attribs[e],namespace:null===(t=this["x-attribsNamespace"])||void 0===t?void 0:t[e],prefix:null===(n=this["x-attribsPrefix"])||void 0===n?void 0:n[e]}}))}}function Y(e){return(t=e).type===S.Tag||t.type===S.Script||t.type===S.Style;var t}function j(e){return e.type===S.CDATA}function K(e){return e.type===S.Text}function V(e){return e.type===S.Comment}function z(e){return e.type===S.Directive}function Q(e){return e.type===S.Root}function W(e){return Object.prototype.hasOwnProperty.call(e,"children")}function X(e,t=!1){let n;if(K(e))n=new P(e.data);else if(V(e))n=new B(e.data);else if(Y(e)){const s=t?Z(e.children):[],r=new q(e.name,{...e.attribs},s);s.forEach((e=>e.parent=r)),null!=e.namespace&&(r.namespace=e.namespace),e["x-attribsNamespace"]&&(r["x-attribsNamespace"]={...e["x-attribsNamespace"]}),e["x-attribsPrefix"]&&(r["x-attribsPrefix"]={...e["x-attribsPrefix"]}),n=r}else if(j(e)){const s=t?Z(e.children):[],r=new G(s);s.forEach((e=>e.parent=r)),n=r}else if(Q(e)){const s=t?Z(e.children):[],r=new $(s);s.forEach((e=>e.parent=r)),e["x-mode"]&&(r["x-mode"]=e["x-mode"]),n=r}else{if(!z(e))throw new Error(`Not implemented yet: ${e.type}`);{const t=new U(e.name,e.data);null!=e["x-name"]&&(t["x-name"]=e["x-name"],t["x-publicId"]=e["x-publicId"],t["x-systemId"]=e["x-systemId"]),n=t}}return n.startIndex=e.startIndex,n.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(n.sourceCodeLocation=e.sourceCodeLocation),n}function Z(e){const t=e.map((e=>X(e,!0)));for(let e=1;e<t.length;e++)t[e].prev=t[e-1],t[e-1].next=t[e];return t}const J={withStartIndices:!1,withEndIndices:!1,xmlMode:!1};class ee{constructor(e,t,n){this.dom=[],this.root=new $(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(n=t,t=J),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:J,this.elementCB=null!=n?n:null}onparserinit(e){this.parser=e}onreset(){this.dom=[],this.root=new $(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(e){this.handleCallback(e)}onclosetag(){this.lastNode=null;const e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)}onopentag(e,t){const n=this.options.xmlMode?S.Tag:void 0,s=new q(e,t,void 0,n);this.addNode(s),this.tagStack.push(s)}ontext(e){const{lastNode:t}=this;if(t&&t.type===S.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{const t=new P(e);this.addNode(t),this.lastNode=t}}oncomment(e){if(this.lastNode&&this.lastNode.type===S.Comment)return void(this.lastNode.data+=e);const t=new B(e);this.addNode(t),this.lastNode=t}oncommentend(){this.lastNode=null}oncdatastart(){const e=new P(""),t=new G([e]);this.addNode(t),e.parent=t,this.lastNode=e}oncdataend(){this.lastNode=null}onprocessinginstruction(e,t){const n=new U(e,t);this.addNode(n)}handleCallback(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e}addNode(e){const t=this.tagStack[this.tagStack.length-1],n=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),n&&(e.prev=n,n.next=e),e.parent=t,this.lastNode=null}}const te=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map((e=>e.charCodeAt(0)))),ne=new Uint16Array("Ȁaglq\tɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map((e=>e.charCodeAt(0))));var se;const re=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),ie=null!==(se=String.fromCodePoint)&&void 0!==se?se:function(e){let t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e),t};var oe,ae;(ae=oe||(oe={}))[ae.NUM=35]="NUM",ae[ae.SEMI=59]="SEMI",ae[ae.EQUALS=61]="EQUALS",ae[ae.ZERO=48]="ZERO",ae[ae.NINE=57]="NINE",ae[ae.LOWER_A=97]="LOWER_A",ae[ae.LOWER_F=102]="LOWER_F",ae[ae.LOWER_X=120]="LOWER_X",ae[ae.LOWER_Z=122]="LOWER_Z",ae[ae.UPPER_A=65]="UPPER_A",ae[ae.UPPER_F=70]="UPPER_F",ae[ae.UPPER_Z=90]="UPPER_Z";var ce,le,ue,he,de,pe;function fe(e){return e>=oe.ZERO&&e<=oe.NINE}function me(e){return e===oe.EQUALS||function(e){return e>=oe.UPPER_A&&e<=oe.UPPER_Z||e>=oe.LOWER_A&&e<=oe.LOWER_Z||fe(e)}(e)}(le=ce||(ce={}))[le.VALUE_LENGTH=49152]="VALUE_LENGTH",le[le.BRANCH_LENGTH=16256]="BRANCH_LENGTH",le[le.JUMP_TABLE=127]="JUMP_TABLE",(he=ue||(ue={}))[he.EntityStart=0]="EntityStart",he[he.NumericStart=1]="NumericStart",he[he.NumericDecimal=2]="NumericDecimal",he[he.NumericHex=3]="NumericHex",he[he.NamedEntity=4]="NamedEntity",(pe=de||(de={}))[pe.Legacy=0]="Legacy",pe[pe.Strict=1]="Strict",pe[pe.Attribute=2]="Attribute";class Ee{constructor(e,t,n){this.decodeTree=e,this.emitCodePoint=t,this.errors=n,this.state=ue.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=de.Strict}startEntity(e){this.decodeMode=e,this.state=ue.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case ue.EntityStart:return e.charCodeAt(t)===oe.NUM?(this.state=ue.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=ue.NamedEntity,this.stateNamedEntity(e,t));case ue.NumericStart:return this.stateNumericStart(e,t);case ue.NumericDecimal:return this.stateNumericDecimal(e,t);case ue.NumericHex:return this.stateNumericHex(e,t);case ue.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===oe.LOWER_X?(this.state=ue.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=ue.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,n,s){if(t!==n){const r=n-t;this.result=this.result*Math.pow(s,r)+parseInt(e.substr(t,r),s),this.consumed+=r}}stateNumericHex(e,t){const n=t;for(;t<e.length;){const r=e.charCodeAt(t);if(!(fe(r)||(s=r,s>=oe.UPPER_A&&s<=oe.UPPER_F||s>=oe.LOWER_A&&s<=oe.LOWER_F)))return this.addToNumericResult(e,n,t,16),this.emitNumericEntity(r,3);t+=1}var s;return this.addToNumericResult(e,n,t,16),-1}stateNumericDecimal(e,t){const n=t;for(;t<e.length;){const s=e.charCodeAt(t);if(!fe(s))return this.addToNumericResult(e,n,t,10),this.emitNumericEntity(s,2);t+=1}return this.addToNumericResult(e,n,t,10),-1}emitNumericEntity(e,t){var n;if(this.consumed<=t)return null===(n=this.errors)||void 0===n||n.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===oe.SEMI)this.consumed+=1;else if(this.decodeMode===de.Strict)return 0;return this.emitCodePoint(function(e){var t;return e>=55296&&e<=57343||e>1114111?65533:null!==(t=re.get(e))&&void 0!==t?t:e}(this.result),this.consumed),this.errors&&(e!==oe.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){const{decodeTree:n}=this;let s=n[this.treeIndex],r=(s&ce.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){const i=e.charCodeAt(t);if(this.treeIndex=_e(n,s,this.treeIndex+Math.max(1,r),i),this.treeIndex<0)return 0===this.result||this.decodeMode===de.Attribute&&(0===r||me(i))?0:this.emitNotTerminatedNamedEntity();if(s=n[this.treeIndex],r=(s&ce.VALUE_LENGTH)>>14,0!==r){if(i===oe.SEMI)return this.emitNamedEntityData(this.treeIndex,r,this.consumed+this.excess);this.decodeMode!==de.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;const{result:t,decodeTree:n}=this,s=(n[t]&ce.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,s,this.consumed),null===(e=this.errors)||void 0===e||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,n){const{decodeTree:s}=this;return this.emitCodePoint(1===t?s[e]&~ce.VALUE_LENGTH:s[e+1],n),3===t&&this.emitCodePoint(s[e+2],n),n}end(){var e;switch(this.state){case ue.NamedEntity:return 0===this.result||this.decodeMode===de.Attribute&&this.result!==this.treeIndex?0:this.emitNotTerminatedNamedEntity();case ue.NumericDecimal:return this.emitNumericEntity(0,2);case ue.NumericHex:return this.emitNumericEntity(0,3);case ue.NumericStart:return null===(e=this.errors)||void 0===e||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case ue.EntityStart:return 0}}}function Te(e){let t="";const n=new Ee(e,(e=>t+=ie(e)));return function(e,s){let r=0,i=0;for(;(i=e.indexOf("&",i))>=0;){t+=e.slice(r,i),n.startEntity(s);const o=n.write(e,i+1);if(o<0){r=i+n.end();break}r=i+o,i=0===o?r+1:r}const o=t+e.slice(r);return t="",o}}function _e(e,t,n,s){const r=(t&ce.BRANCH_LENGTH)>>7,i=t&ce.JUMP_TABLE;if(0===r)return 0!==i&&s===i?n:-1;if(i){const t=s-i;return t<0||t>=r?-1:e[n+t]-1}let o=n,a=o+r-1;for(;o<=a;){const t=o+a>>>1,n=e[t];if(n<s)o=t+1;else{if(!(n>s))return e[t+r];a=t-1}}return-1}const Ae=Te(te);function ge(e,t=de.Legacy){return Ae(e,t)}Te(ne);const Ce=/["&'<>$\x80-\uFFFF]/g,be=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]),ke=null!=String.prototype.codePointAt?(e,t)=>e.codePointAt(t):(e,t)=>55296==(64512&e.charCodeAt(t))?1024*(e.charCodeAt(t)-55296)+e.charCodeAt(t+1)-56320+65536:e.charCodeAt(t);function Se(e){let t,n="",s=0;for(;null!==(t=Ce.exec(e));){const r=t.index,i=e.charCodeAt(r),o=be.get(i);void 0!==o?(n+=e.substring(s,r)+o,s=r+1):(n+=`${e.substring(s,r)}&#x${ke(e,r).toString(16)};`,s=Ce.lastIndex+=Number(55296==(64512&i)))}return n+e.substr(s)}function Ne(e,t){return function(n){let s,r=0,i="";for(;s=e.exec(n);)r!==s.index&&(i+=n.substring(r,s.index)),i+=t.get(s[0].charCodeAt(0)),r=s.index+1;return i+n.substring(r)}}const Ie=Ne(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),De=Ne(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]])),ye=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map((e=>[e.toLowerCase(),e]))),Oe=new Map(["definitionURL","attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map((e=>[e.toLowerCase(),e]))),Le=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function Re(e){return e.replace(/"/g,"&quot;")}const Me=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function ve(e,t={}){const n="length"in e?e:[e];let s="";for(let e=0;e<n.length;e++)s+=xe(n[e],t);return s}function xe(e,t){switch(e.type){case I:return ve(e.children,t);case x:case y:return`<${e.data}>`;case O:return function(e){return`\x3c!--${e.data}--\x3e`}(e);case v:return function(e){return`<![CDATA[${e.children[0].data}]]>`}(e);case L:case R:case M:return function(e,t){var n;"foreign"===t.xmlMode&&(e.name=null!==(n=ye.get(e.name))&&void 0!==n?n:e.name,e.parent&&we.has(e.parent.name)&&(t={...t,xmlMode:!1}));!t.xmlMode&&Fe.has(e.name)&&(t={...t,xmlMode:"foreign"});let s=`<${e.name}`;const r=function(e,t){var n;if(!e)return;const s=!1===(null!==(n=t.encodeEntities)&&void 0!==n?n:t.decodeEntities)?Re:t.xmlMode||"utf8"!==t.encodeEntities?Se:Ie;return Object.keys(e).map((n=>{var r,i;const o=null!==(r=e[n])&&void 0!==r?r:"";return"foreign"===t.xmlMode&&(n=null!==(i=Oe.get(n))&&void 0!==i?i:n),t.emptyAttrs||t.xmlMode||""!==o?`${n}="${s(o)}"`:n})).join(" ")}(e.attribs,t);r&&(s+=` ${r}`);0===e.children.length&&(t.xmlMode?!1!==t.selfClosingTags:t.selfClosingTags&&Me.has(e.name))?(t.xmlMode||(s+=" "),s+="/>"):(s+=">",e.children.length>0&&(s+=ve(e.children,t)),!t.xmlMode&&Me.has(e.name)||(s+=`</${e.name}>`));return s}(e,t);case D:return function(e,t){var n;let s=e.data||"";!1===(null!==(n=t.encodeEntities)&&void 0!==n?n:t.decodeEntities)||!t.xmlMode&&e.parent&&Le.has(e.parent.name)||(s=t.xmlMode||"utf8"!==t.encodeEntities?Se(s):De(s));return s}(e,t)}}const we=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),Fe=new Set(["svg","math"]);function Pe(e,t){return ve(e,t)}function Be(e){return Array.isArray(e)?e.map(Be).join(""):W(e)&&!V(e)?Be(e.children):K(e)?e.data:""}function Ue(e){return Array.isArray(e)?e.map(Ue).join(""):W(e)&&(e.type===S.Tag||j(e))?Ue(e.children):K(e)?e.data:""}function He(e){return W(e)?e.children:[]}function Ge(e){return e.parent||null}function $e(e){const t=Ge(e);if(null!=t)return He(t);const n=[e];let{prev:s,next:r}=e;for(;null!=s;)n.unshift(s),({prev:s}=s);for(;null!=r;)n.push(r),({next:r}=r);return n}function qe(e){let{next:t}=e;for(;null!==t&&!Y(t);)({next:t}=t);return t}function Ye(e){let{prev:t}=e;for(;null!==t&&!Y(t);)({prev:t}=t);return t}function je(e){if(e.prev&&(e.prev.next=e.next),e.next&&(e.next.prev=e.prev),e.parent){const t=e.parent.children,n=t.lastIndexOf(e);n>=0&&t.splice(n,1)}e.next=null,e.prev=null,e.parent=null}function Ke(e,t,n=!0,s=1/0){return Ve(e,Array.isArray(t)?t:[t],n,s)}function Ve(e,t,n,s){const r=[],i=[t],o=[0];for(;;){if(o[0]>=i[0].length){if(1===o.length)return r;i.shift(),o.shift();continue}const t=i[0][o[0]++];if(e(t)&&(r.push(t),--s<=0))return r;n&&W(t)&&t.children.length>0&&(o.unshift(0),i.unshift(t.children))}}function ze(e,t,n=!0){let s=null;for(let r=0;r<t.length&&!s;r++){const i=t[r];Y(i)&&(e(i)?s=i:n&&i.children.length>0&&(s=ze(e,i.children,!0)))}return s}const Qe={tag_name:e=>"function"==typeof e?t=>Y(t)&&e(t.name):"*"===e?Y:t=>Y(t)&&t.name===e,tag_type:e=>"function"==typeof e?t=>e(t.type):t=>t.type===e,tag_contains:e=>"function"==typeof e?t=>K(t)&&e(t.data):t=>K(t)&&t.data===e};function We(e,t){return"function"==typeof t?n=>Y(n)&&t(n.attribs[e]):n=>Y(n)&&n.attribs[e]===t}function Xe(e,t){return n=>e(n)||t(n)}function Ze(e){const t=Object.keys(e).map((t=>{const n=e[t];return Object.prototype.hasOwnProperty.call(Qe,t)?Qe[t](n):We(t,n)}));return 0===t.length?null:t.reduce(Xe)}function Je(e,t,n=!0,s=1/0){return Ke(Qe.tag_name(e),t,n,s)}var et,tt;function nt(e,t){const n=[],s=[];if(e===t)return 0;let r=W(e)?e:e.parent;for(;r;)n.unshift(r),r=r.parent;for(r=W(t)?t:t.parent;r;)s.unshift(r),r=r.parent;const i=Math.min(n.length,s.length);let o=0;for(;o<i&&n[o]===s[o];)o++;if(0===o)return et.DISCONNECTED;const a=n[o-1],c=a.children,l=n[o],u=s[o];return c.indexOf(l)>c.indexOf(u)?a===t?et.FOLLOWING|et.CONTAINED_BY:et.FOLLOWING:a===e?et.PRECEDING|et.CONTAINS:et.PRECEDING}function st(e){return(e=e.filter(((e,t,n)=>!n.includes(e,t+1)))).sort(((e,t)=>{const n=nt(e,t);return n&et.PRECEDING?-1:n&et.FOLLOWING?1:0})),e}(tt=et||(et={}))[tt.DISCONNECTED=1]="DISCONNECTED",tt[tt.PRECEDING=2]="PRECEDING",tt[tt.FOLLOWING=4]="FOLLOWING",tt[tt.CONTAINS=8]="CONTAINS",tt[tt.CONTAINED_BY=16]="CONTAINED_BY";const rt=["url","type","lang"],it=["fileSize","bitrate","framerate","samplingrate","channels","duration","height","width"];function ot(e){return Je("media:content",e).map((e=>{const{attribs:t}=e,n={medium:t.medium,isDefault:!!t.isDefault};for(const e of rt)t[e]&&(n[e]=t[e]);for(const e of it)t[e]&&(n[e]=parseInt(t[e],10));return t.expression&&(n.expression=t.expression),n}))}function at(e,t){return Je(e,t,!0,1)[0]}function ct(e,t,n=!1){return Be(Je(e,t,n,1)).trim()}function lt(e,t,n,s,r=!1){const i=ct(n,s,r);i&&(e[t]=i)}function ut(e){return"rss"===e||"feed"===e||"rdf:RDF"===e}const ht=Object.freeze(Object.defineProperty({__proto__:null,get DocumentPosition(){return et},append:function(e,t){je(t);const{parent:n}=e,s=e.next;if(t.next=s,t.prev=e,e.next=t,t.parent=n,s){if(s.prev=t,n){const e=n.children;e.splice(e.lastIndexOf(s),0,t)}}else n&&n.children.push(t)},appendChild:function(e,t){if(je(t),t.next=null,t.parent=e,e.children.push(t)>1){const n=e.children[e.children.length-2];n.next=t,t.prev=n}else t.prev=null},compareDocumentPosition:nt,existsOne:function e(t,n){return n.some((n=>Y(n)&&(t(n)||e(t,n.children))))},filter:Ke,find:Ve,findAll:function(e,t){const n=[],s=[t],r=[0];for(;;){if(r[0]>=s[0].length){if(1===s.length)return n;s.shift(),r.shift();continue}const t=s[0][r[0]++];Y(t)&&(e(t)&&n.push(t),t.children.length>0&&(r.unshift(0),s.unshift(t.children)))}},findOne:ze,findOneChild:function(e,t){return t.find(e)},getAttributeValue:function(e,t){var n;return null===(n=e.attribs)||void 0===n?void 0:n[t]},getChildren:He,getElementById:function(e,t,n=!0){return Array.isArray(t)||(t=[t]),ze(We("id",e),t,n)},getElements:function(e,t,n,s=1/0){const r=Ze(e);return r?Ke(r,t,n,s):[]},getElementsByTagName:Je,getElementsByTagType:function(e,t,n=!0,s=1/0){return Ke(Qe.tag_type(e),t,n,s)},getFeed:function(e){const t=at(ut,e);return t?"feed"===t.name?function(e){var t;const n=e.children,s={type:"atom",items:Je("entry",n).map((e=>{var t;const{children:n}=e,s={media:ot(n)};lt(s,"id","id",n),lt(s,"title","title",n);const r=null===(t=at("link",n))||void 0===t?void 0:t.attribs.href;r&&(s.link=r);const i=ct("summary",n)||ct("content",n);i&&(s.description=i);const o=ct("updated",n);return o&&(s.pubDate=new Date(o)),s}))};lt(s,"id","id",n),lt(s,"title","title",n);const r=null===(t=at("link",n))||void 0===t?void 0:t.attribs.href;r&&(s.link=r);lt(s,"description","subtitle",n);const i=ct("updated",n);i&&(s.updated=new Date(i));return lt(s,"author","email",n,!0),s}(t):function(e){var t,n;const s=null!==(n=null===(t=at("channel",e.children))||void 0===t?void 0:t.children)&&void 0!==n?n:[],r={type:e.name.substr(0,3),id:"",items:Je("item",e.children).map((e=>{const{children:t}=e,n={media:ot(t)};lt(n,"id","guid",t),lt(n,"title","title",t),lt(n,"link","link",t),lt(n,"description","description",t);const s=ct("pubDate",t)||ct("dc:date",t);return s&&(n.pubDate=new Date(s)),n}))};lt(r,"title","title",s),lt(r,"link","link",s),lt(r,"description","description",s);const i=ct("lastBuildDate",s);i&&(r.updated=new Date(i));return lt(r,"author","managingEditor",s,!0),r}(t):null},getInnerHTML:function(e,t){return W(e)?e.children.map((e=>Pe(e,t))).join(""):""},getName:function(e){return e.name},getOuterHTML:Pe,getParent:Ge,getSiblings:$e,getText:function e(t){return Array.isArray(t)?t.map(e).join(""):Y(t)?"br"===t.name?"\n":e(t.children):j(t)?e(t.children):K(t)?t.data:""},hasAttrib:function(e,t){return null!=e.attribs&&Object.prototype.hasOwnProperty.call(e.attribs,t)&&null!=e.attribs[t]},hasChildren:W,innerText:Ue,isCDATA:j,isComment:V,isDocument:Q,isTag:Y,isText:K,nextElementSibling:qe,prepend:function(e,t){je(t);const{parent:n}=e;if(n){const s=n.children;s.splice(s.indexOf(e),0,t)}e.prev&&(e.prev.next=t),t.parent=n,t.prev=e.prev,t.next=e,e.prev=t},prependChild:function(e,t){if(je(t),t.parent=e,t.prev=null,1!==e.children.unshift(t)){const n=e.children[1];n.prev=t,t.next=n}else t.next=null},prevElementSibling:Ye,removeElement:je,removeSubsets:function(e){let t=e.length;for(;--t>=0;){const n=e[t];if(t>0&&e.lastIndexOf(n,t-1)>=0)e.splice(t,1);else for(let s=n.parent;s;s=s.parent)if(e.includes(s)){e.splice(t,1);break}}return e},replaceElement:function(e,t){const n=t.prev=e.prev;n&&(n.next=t);const s=t.next=e.next;s&&(s.prev=t);const r=t.parent=e.parent;if(r){const n=r.children;n[n.lastIndexOf(e)]=t,e.parent=null}},testElement:function(e,t){const n=Ze(e);return!n||n(t)},textContent:Be,uniqueSort:st},Symbol.toStringTag,{value:"Module"})),dt={_useHtmlParser2:!1};function pt(e,t){if(!e)return null!=t?t:dt;const n={_useHtmlParser2:!!e.xmlMode,...t,...e};return e.xml?(n._useHtmlParser2=!0,n.xmlMode=!0,!0!==e.xml&&Object.assign(n,e.xml)):e.xmlMode&&(n._useHtmlParser2=!0),n}function ft(e,t,n){return e?e(null!=t?t:e._root.children,null,void 0,n).toString():""}function mt(e){const t=null!=e?e:this?this.root():[];let n="";for(let e=0;e<t.length;e++)n+=Be(t[e]);return n}function Et(e,t){if(t===e)return!1;let n=t;for(;n&&n!==n.parent;)if(n=n.parent,n===e)return!0;return!1}function Tt(e){if(Array.isArray(e))return!0;if("object"!=typeof e||null===e||!("length"in e)||"number"!=typeof e.length||e.length<0)return!1;for(let t=0;t<e.length;t++)if(!(t in e))return!1;return!0}const _t=Object.freeze(Object.defineProperty({__proto__:null,contains:Et,extract:function(e){return this.root().extract(e)},html:function(e,t){return ft(this,function(e){return"object"==typeof e&&null!=e&&!("length"in e)&&!("type"in e)}(e)?void(t=e):e,{...null==this?void 0:this._options,...pt(t)})},merge:function(e,t){if(!Tt(e)||!Tt(t))return;let n=e.length;const s=+t.length;for(let r=0;r<s;r++)e[n++]=t[r];return e.length=n,e},parseHTML:function(e,t,n="boolean"==typeof t&&t){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t);const s=this.load(e,this._options,!1);return n||s("script").remove(),[...s.root()[0].children]},root:function(){return this(this._root)},text:mt,xml:function(e){return ft(this,e,{...this._options,xmlMode:!0})}},Symbol.toStringTag,{value:"Module"}));function At(e){return null!=e.cheerio}function gt(e,t){const n=e.length;for(let s=0;s<n;s++)t(e[s],s);return e}var Ct,bt;function kt(e){const t=e.indexOf("<");if(t<0||t>e.length-3)return!1;const n=e.charCodeAt(t+1);return(n>=Ct.LowerA&&n<=Ct.LowerZ||n>=Ct.UpperA&&n<=Ct.UpperZ||n===Ct.Exclamation)&&e.includes(">",t+2)}(bt=Ct||(Ct={}))[bt.LowerA=97]="LowerA",bt[bt.LowerZ=122]="LowerZ",bt[bt.UpperA=65]="UpperA",bt[bt.UpperZ=90]="UpperZ",bt[bt.Exclamation=33]="Exclamation";const St=Object.prototype.hasOwnProperty,Nt=/\s+/,It="data-",Dt=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,yt=/^{[^]*}$|^\[[^]*]$/;function Ot(e,t,n){var s;if(e&&Y(e))return null!==(s=e.attribs)&&void 0!==s||(e.attribs={}),t?St.call(e.attribs,t)?!n&&Dt.test(t)?t:e.attribs[t]:"option"===e.name&&"value"===t?mt(e.children):"input"!==e.name||"radio"!==e.attribs.type&&"checkbox"!==e.attribs.type||"value"!==t?void 0:"on":e.attribs}function Lt(e,t,n){null===n?wt(e,t):e.attribs[t]=`${n}`}function Rt(e,t,n){return t in e?e[t]:!n&&Dt.test(t)?void 0!==Ot(e,t,!1):Ot(e,t,n)}function Mt(e,t,n,s){t in e?e[t]=n:Lt(e,t,!s&&Dt.test(t)?n?"":null:`${n}`)}function vt(e,t,n){var s;null!==(s=e.data)&&void 0!==s||(e.data={}),"object"==typeof t?Object.assign(e.data,t):"string"==typeof t&&void 0!==n&&(e.data[t]=n)}function xt(e){if("null"===e)return null;if("true"===e)return!0;if("false"===e)return!1;const t=Number(e);if(e===String(t))return t;if(yt.test(e))try{return JSON.parse(e)}catch{}return e}function wt(e,t){e.attribs&&St.call(e.attribs,t)&&delete e.attribs[t]}function Ft(e){return e?e.trim().split(Nt):[]}const Pt=Object.freeze(Object.defineProperty({__proto__:null,addClass:function e(t){if("function"==typeof t)return gt(this,((n,s)=>{if(Y(n)){const r=n.attribs.class||"";e.call([n],t.call(n,s,r))}}));if(!t||"string"!=typeof t)return this;const n=t.split(Nt),s=this.length;for(let e=0;e<s;e++){const t=this[e];if(!Y(t))continue;const s=Ot(t,"class",!1);if(s){let e=` ${s} `;for(const t of n){const n=`${t} `;e.includes(` ${n}`)||(e+=n)}Lt(t,"class",e.trim())}else Lt(t,"class",n.join(" ").trim())}return this},attr:function(e,t){if("object"==typeof e||void 0!==t){if("function"==typeof t){if("string"!=typeof e)throw new Error("Bad combination of arguments.");return gt(this,((n,s)=>{Y(n)&&Lt(n,e,t.call(n,s,n.attribs[e]))}))}return gt(this,(n=>{if(Y(n))if("object"==typeof e)for(const t of Object.keys(e)){Lt(n,t,e[t])}else Lt(n,e,t)}))}return arguments.length>1?this:Ot(this[0],e,this.options.xmlMode)},data:function(e,t){var n;const s=this[0];if(!s||!Y(s))return;const r=s;return null!==(n=r.data)&&void 0!==n||(r.data={}),null==e?function(e){for(const t of Object.keys(e.attribs)){if(!t.startsWith(It))continue;const n=t.slice(5).replace(/[._-](\w|$)/g,((e,t)=>t.toUpperCase()));St.call(e.data,n)||(e.data[n]=xt(e.attribs[t]))}return e.data}(r):"object"==typeof e||void 0!==t?(gt(this,(n=>{Y(n)&&("object"==typeof e?vt(n,e):vt(n,e,t))})),this):function(e,t){const n=It+t.replace(/[A-Z]/g,"-$&").toLowerCase(),s=e.data;return St.call(s,t)?s[t]:St.call(e.attribs,n)?s[t]=xt(e.attribs[n]):void 0}(r,e)},hasClass:function(e){return this.toArray().some((t=>{const n=Y(t)&&t.attribs.class;let s=-1;if(n&&e.length>0)for(;(s=n.indexOf(e,s+1))>-1;){const t=s+e.length;if((0===s||Nt.test(n[s-1]))&&(t===n.length||Nt.test(n[t])))return!0}return!1}))},prop:function(e,t){var n;if("string"==typeof e&&void 0===t){const t=this[0];if(!t||!Y(t))return;switch(e){case"style":{const e=this.css(),t=Object.keys(e);for(let n=0;n<t.length;n++)e[n]=t[n];return e.length=t.length,e}case"tagName":case"nodeName":return t.name.toUpperCase();case"href":case"src":{const s=null===(n=t.attribs)||void 0===n?void 0:n[e];return"undefined"==typeof URL||("href"!==e||"a"!==t.tagName&&"link"!==t.tagName)&&("src"!==e||"img"!==t.tagName&&"iframe"!==t.tagName&&"audio"!==t.tagName&&"video"!==t.tagName&&"source"!==t.tagName)||void 0===s||!this.options.baseURI?s:new URL(s,this.options.baseURI).href}case"innerText":return Ue(t);case"textContent":return Be(t);case"outerHTML":return this.clone().wrap("<container />").parent().html();case"innerHTML":return this.html();default:return Rt(t,e,this.options.xmlMode)}}if("object"==typeof e||void 0!==t){if("function"==typeof t){if("object"==typeof e)throw new TypeError("Bad combination of arguments.");return gt(this,((n,s)=>{Y(n)&&Mt(n,e,t.call(n,s,Rt(n,e,this.options.xmlMode)),this.options.xmlMode)}))}return gt(this,(n=>{if(Y(n))if("object"==typeof e)for(const t of Object.keys(e)){Mt(n,t,e[t],this.options.xmlMode)}else Mt(n,e,t,this.options.xmlMode)}))}},removeAttr:function(e){const t=Ft(e);for(const e of t)gt(this,(t=>{Y(t)&&wt(t,e)}));return this},removeClass:function e(t){if("function"==typeof t)return gt(this,((n,s)=>{Y(n)&&e.call([n],t.call(n,s,n.attribs.class||""))}));const n=Ft(t),s=n.length,r=0===arguments.length;return gt(this,(e=>{if(Y(e))if(r)e.attribs.class="";else{const t=Ft(e.attribs.class);let r=!1;for(let e=0;e<s;e++){const s=t.indexOf(n[e]);s>=0&&(t.splice(s,1),r=!0,e--)}r&&(e.attribs.class=t.join(" "))}}))},toggleClass:function e(t,n){if("function"==typeof t)return gt(this,((s,r)=>{Y(s)&&e.call([s],t.call(s,r,s.attribs.class||"",n),n)}));if(!t||"string"!=typeof t)return this;const s=t.split(Nt),r=s.length,i="boolean"==typeof n?n?1:-1:0,o=this.length;for(let e=0;e<o;e++){const t=this[e];if(!Y(t))continue;const n=Ft(t.attribs.class);for(let e=0;e<r;e++){const t=n.indexOf(s[e]);i>=0&&t<0?n.push(s[e]):i<=0&&t>=0&&n.splice(t,1)}t.attribs.class=n.join(" ")}return this},val:function(e){const t=0===arguments.length,n=this[0];if(!n||!Y(n))return t?void 0:this;switch(n.name){case"textarea":return this.text(e);case"select":{const n=this.find("option:selected");if(!t){if(null==this.attr("multiple")&&"object"==typeof e)return this;this.find("option").removeAttr("selected");const t="object"==typeof e?e:[e];for(const e of t)this.find(`option[value="${e}"]`).attr("selected","");return this}return this.attr("multiple")?n.toArray().map((e=>mt(e.children))):n.attr("value")}case"input":case"option":return t?this.attr("value"):this.attr("value",e)}}},Symbol.toStringTag,{value:"Module"}));var Bt,Ut,Ht,Gt;(Ut=Bt||(Bt={})).Attribute="attribute",Ut.Pseudo="pseudo",Ut.PseudoElement="pseudo-element",Ut.Tag="tag",Ut.Universal="universal",Ut.Adjacent="adjacent",Ut.Child="child",Ut.Descendant="descendant",Ut.Parent="parent",Ut.Sibling="sibling",Ut.ColumnCombinator="column-combinator",(Gt=Ht||(Ht={})).Any="any",Gt.Element="element",Gt.End="end",Gt.Equals="equals",Gt.Exists="exists",Gt.Hyphen="hyphen",Gt.Not="not",Gt.Start="start";const $t=/^[^\\#]?(?:\\(?:[\da-f]{1,6}\s?|.)|[\w\-\u00b0-\uFFFF])+/,qt=/\\([\da-f]{1,6}\s?|(\s)|.)/gi,Yt=new Map([[126,Ht.Element],[94,Ht.Start],[36,Ht.End],[42,Ht.Any],[33,Ht.Not],[124,Ht.Hyphen]]),jt=new Set(["has","not","matches","is","where","host","host-context"]);function Kt(e){switch(e.type){case Bt.Adjacent:case Bt.Child:case Bt.Descendant:case Bt.Parent:case Bt.Sibling:case Bt.ColumnCombinator:return!0;default:return!1}}const Vt=new Set(["contains","icontains"]);function zt(e,t,n){const s=parseInt(t,16)-65536;return s!=s||n?t:s<0?String.fromCharCode(s+65536):String.fromCharCode(s>>10|55296,1023&s|56320)}function Qt(e){return e.replace(qt,zt)}function Wt(e){return 39===e||34===e}function Xt(e){return 32===e||9===e||10===e||12===e||13===e}function Zt(e){const t=[],n=Jt(t,`${e}`,0);if(n<e.length)throw new Error(`Unmatched selector: ${e.slice(n)}`);return t}function Jt(e,t,n){let s=[];function r(e){const s=t.slice(n+e).match($t);if(!s)throw new Error(`Expected name, found ${t.slice(n)}`);const[r]=s;return n+=e+r.length,Qt(r)}function i(e){for(n+=e;n<t.length&&Xt(t.charCodeAt(n));)n++}function o(){const e=n+=1;let s=1;for(;s>0&&n<t.length;n++)40!==t.charCodeAt(n)||a(n)?41!==t.charCodeAt(n)||a(n)||s--:s++;if(s)throw new Error("Parenthesis not matched");return Qt(t.slice(e,n-1))}function a(e){let n=0;for(;92===t.charCodeAt(--e);)n++;return!(1&~n)}function c(){if(s.length>0&&Kt(s[s.length-1]))throw new Error("Did not expect successive traversals.")}function l(e){s.length>0&&s[s.length-1].type===Bt.Descendant?s[s.length-1].type=e:(c(),s.push({type:e}))}function u(e,t){s.push({type:Bt.Attribute,name:e,action:t,value:r(1),namespace:null,ignoreCase:"quirks"})}function h(){if(s.length&&s[s.length-1].type===Bt.Descendant&&s.pop(),0===s.length)throw new Error("Empty sub-selector");e.push(s)}if(i(0),t.length===n)return n;e:for(;n<t.length;){const e=t.charCodeAt(n);switch(e){case 32:case 9:case 10:case 12:case 13:0!==s.length&&s[0].type===Bt.Descendant||(c(),s.push({type:Bt.Descendant})),i(1);break;case 62:l(Bt.Child),i(1);break;case 60:l(Bt.Parent),i(1);break;case 126:l(Bt.Sibling),i(1);break;case 43:l(Bt.Adjacent),i(1);break;case 46:u("class",Ht.Element);break;case 35:u("id",Ht.Equals);break;case 91:{let e;i(1);let o=null;124===t.charCodeAt(n)?e=r(1):t.startsWith("*|",n)?(o="*",e=r(2)):(e=r(0),124===t.charCodeAt(n)&&61!==t.charCodeAt(n+1)&&(o=e,e=r(1))),i(0);let c=Ht.Exists;const l=Yt.get(t.charCodeAt(n));if(l){if(c=l,61!==t.charCodeAt(n+1))throw new Error("Expected `=`");i(2)}else 61===t.charCodeAt(n)&&(c=Ht.Equals,i(1));let u="",h=null;if("exists"!==c){if(Wt(t.charCodeAt(n))){const e=t.charCodeAt(n);let s=n+1;for(;s<t.length&&(t.charCodeAt(s)!==e||a(s));)s+=1;if(t.charCodeAt(s)!==e)throw new Error("Attribute value didn't end");u=Qt(t.slice(n+1,s)),n=s+1}else{const e=n;for(;n<t.length&&(!Xt(t.charCodeAt(n))&&93!==t.charCodeAt(n)||a(n));)n+=1;u=Qt(t.slice(e,n))}i(0);const e=32|t.charCodeAt(n);115===e?(h=!1,i(1)):105===e&&(h=!0,i(1))}if(93!==t.charCodeAt(n))throw new Error("Attribute selector didn't terminate");n+=1;const d={type:Bt.Attribute,name:e,action:c,value:u,namespace:o,ignoreCase:h};s.push(d);break}case 58:{if(58===t.charCodeAt(n+1)){s.push({type:Bt.PseudoElement,name:r(2).toLowerCase(),data:40===t.charCodeAt(n)?o():null});continue}const e=r(1).toLowerCase();let i=null;if(40===t.charCodeAt(n))if(jt.has(e)){if(Wt(t.charCodeAt(n+1)))throw new Error(`Pseudo-selector ${e} cannot be quoted`);if(i=[],n=Jt(i,t,n+1),41!==t.charCodeAt(n))throw new Error(`Missing closing parenthesis in :${e} (${t})`);n+=1}else{if(i=o(),Vt.has(e)){const e=i.charCodeAt(0);e===i.charCodeAt(i.length-1)&&Wt(e)&&(i=i.slice(1,-1))}i=Qt(i)}s.push({type:Bt.Pseudo,name:e,data:i});break}case 44:h(),s=[],i(1);break;default:{if(t.startsWith("/*",n)){const e=t.indexOf("*/",n+2);if(e<0)throw new Error("Comment was not terminated");n=e+2,0===s.length&&i(0);break}let o,a=null;if(42===e)n+=1,o="*";else if(124===e){if(o="",124===t.charCodeAt(n+1)){l(Bt.ColumnCombinator),i(2);break}}else{if(!$t.test(t.slice(n)))break e;o=r(0)}124===t.charCodeAt(n)&&124!==t.charCodeAt(n+1)&&(a=o,42===t.charCodeAt(n+1)?(o="*",n+=2):o=r(1)),s.push("*"===o?{type:Bt.Universal,namespace:a}:{type:Bt.Tag,name:o,namespace:a})}}}return h(),n}function en(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var tn,nn;var sn=nn?tn:(nn=1,tn={trueFunc:function(){return!0},falseFunc:function(){return!1}});const rn=en(sn),on=new Map([[Bt.Universal,50],[Bt.Tag,30],[Bt.Attribute,1],[Bt.Pseudo,0]]);function an(e){return!on.has(e.type)}const cn=new Map([[Ht.Exists,10],[Ht.Equals,8],[Ht.Not,7],[Ht.Start,6],[Ht.End,6],[Ht.Any,5]]);function ln(e){const t=e.map(un);for(let n=1;n<e.length;n++){const s=t[n];if(!(s<0))for(let r=n-1;r>=0&&s<t[r];r--){const n=e[r+1];e[r+1]=e[r],e[r]=n,t[r+1]=t[r],t[r]=s}}}function un(e){var t,n;let s=null!==(t=on.get(e.type))&&void 0!==t?t:-1;return e.type===Bt.Attribute?(s=null!==(n=cn.get(e.action))&&void 0!==n?n:4,e.action===Ht.Equals&&"id"===e.name&&(s=9),e.ignoreCase&&(s>>=1)):e.type===Bt.Pseudo&&(e.data?"has"===e.name||"contains"===e.name?s=0:Array.isArray(e.data)?(s=Math.min(...e.data.map((e=>Math.min(...e.map(un))))),s<0&&(s=0)):s=2:s=3),s}const hn=/[-[\]{}()*+?.,\\^$|#\s]/g;function dn(e){return e.replace(hn,"\\$&")}const pn=new Set(["accept","accept-charset","align","alink","axis","bgcolor","charset","checked","clear","codetype","color","compact","declare","defer","dir","direction","disabled","enctype","face","frame","hreflang","http-equiv","lang","language","link","media","method","multiple","nohref","noresize","noshade","nowrap","readonly","rel","rev","rules","scope","scrolling","selected","shape","target","text","type","valign","valuetype","vlink"]);function fn(e,t){return"boolean"==typeof e.ignoreCase?e.ignoreCase:"quirks"===e.ignoreCase?!!t.quirksMode:!t.xmlMode&&pn.has(e.name)}const mn={equals(e,t,n){const{adapter:s}=n,{name:r}=t;let{value:i}=t;return fn(t,n)?(i=i.toLowerCase(),t=>{const n=s.getAttributeValue(t,r);return null!=n&&n.length===i.length&&n.toLowerCase()===i&&e(t)}):t=>s.getAttributeValue(t,r)===i&&e(t)},hyphen(e,t,n){const{adapter:s}=n,{name:r}=t;let{value:i}=t;const o=i.length;return fn(t,n)?(i=i.toLowerCase(),function(t){const n=s.getAttributeValue(t,r);return null!=n&&(n.length===o||"-"===n.charAt(o))&&n.substr(0,o).toLowerCase()===i&&e(t)}):function(t){const n=s.getAttributeValue(t,r);return null!=n&&(n.length===o||"-"===n.charAt(o))&&n.substr(0,o)===i&&e(t)}},element(e,t,n){const{adapter:s}=n,{name:r,value:i}=t;if(/\s/.test(i))return rn.falseFunc;const o=new RegExp(`(?:^|\\s)${dn(i)}(?:$|\\s)`,fn(t,n)?"i":"");return function(t){const n=s.getAttributeValue(t,r);return null!=n&&n.length>=i.length&&o.test(n)&&e(t)}},exists:(e,{name:t},{adapter:n})=>s=>n.hasAttrib(s,t)&&e(s),start(e,t,n){const{adapter:s}=n,{name:r}=t;let{value:i}=t;const o=i.length;return 0===o?rn.falseFunc:fn(t,n)?(i=i.toLowerCase(),t=>{const n=s.getAttributeValue(t,r);return null!=n&&n.length>=o&&n.substr(0,o).toLowerCase()===i&&e(t)}):t=>{var n;return!!(null===(n=s.getAttributeValue(t,r))||void 0===n?void 0:n.startsWith(i))&&e(t)}},end(e,t,n){const{adapter:s}=n,{name:r}=t;let{value:i}=t;const o=-i.length;return 0===o?rn.falseFunc:fn(t,n)?(i=i.toLowerCase(),t=>{var n;return(null===(n=s.getAttributeValue(t,r))||void 0===n?void 0:n.substr(o).toLowerCase())===i&&e(t)}):t=>{var n;return!!(null===(n=s.getAttributeValue(t,r))||void 0===n?void 0:n.endsWith(i))&&e(t)}},any(e,t,n){const{adapter:s}=n,{name:r,value:i}=t;if(""===i)return rn.falseFunc;if(fn(t,n)){const t=new RegExp(dn(i),"i");return function(n){const o=s.getAttributeValue(n,r);return null!=o&&o.length>=i.length&&t.test(o)&&e(n)}}return t=>{var n;return!!(null===(n=s.getAttributeValue(t,r))||void 0===n?void 0:n.includes(i))&&e(t)}},not(e,t,n){const{adapter:s}=n,{name:r}=t;let{value:i}=t;return""===i?t=>!!s.getAttributeValue(t,r)&&e(t):fn(t,n)?(i=i.toLowerCase(),t=>{const n=s.getAttributeValue(t,r);return(null==n||n.length!==i.length||n.toLowerCase()!==i)&&e(t)}):t=>s.getAttributeValue(t,r)!==i&&e(t)}},En=new Set([9,10,12,13,32]),Tn="0".charCodeAt(0),_n="9".charCodeAt(0);function An(e){return function(e){const t=e[0],n=e[1]-1;if(n<0&&t<=0)return rn.falseFunc;if(-1===t)return e=>e<=n;if(0===t)return e=>e===n;if(1===t)return n<0?rn.trueFunc:e=>e>=n;const s=Math.abs(t),r=(n%s+s)%s;return t>1?e=>e>=n&&e%s===r:e=>e<=n&&e%s===r}(function(e){if("even"===(e=e.trim().toLowerCase()))return[2,0];if("odd"===e)return[2,1];let t=0,n=0,s=i(),r=o();if(t<e.length&&"n"===e.charAt(t)&&(t++,n=s*(null!=r?r:1),a(),t<e.length?(s=i(),a(),r=o()):s=r=0),null===r||t<e.length)throw new Error(`n-th rule couldn't be parsed ('${e}')`);return[n,s*r];function i(){return"-"===e.charAt(t)?(t++,-1):("+"===e.charAt(t)&&t++,1)}function o(){const n=t;let s=0;for(;t<e.length&&e.charCodeAt(t)>=Tn&&e.charCodeAt(t)<=_n;)s=10*s+(e.charCodeAt(t)-Tn),t++;return t===n?null:s}function a(){for(;t<e.length&&En.has(e.charCodeAt(t));)t++}}(e))}function gn(e,t){return n=>{const s=t.getParent(n);return null!=s&&t.isTag(s)&&e(n)}}const Cn={contains:(e,t,{adapter:n})=>function(s){return e(s)&&n.getText(s).includes(t)},icontains(e,t,{adapter:n}){const s=t.toLowerCase();return function(t){return e(t)&&n.getText(t).toLowerCase().includes(s)}},"nth-child"(e,t,{adapter:n,equals:s}){const r=An(t);return r===rn.falseFunc?rn.falseFunc:r===rn.trueFunc?gn(e,n):function(t){const i=n.getSiblings(t);let o=0;for(let e=0;e<i.length&&!s(t,i[e]);e++)n.isTag(i[e])&&o++;return r(o)&&e(t)}},"nth-last-child"(e,t,{adapter:n,equals:s}){const r=An(t);return r===rn.falseFunc?rn.falseFunc:r===rn.trueFunc?gn(e,n):function(t){const i=n.getSiblings(t);let o=0;for(let e=i.length-1;e>=0&&!s(t,i[e]);e--)n.isTag(i[e])&&o++;return r(o)&&e(t)}},"nth-of-type"(e,t,{adapter:n,equals:s}){const r=An(t);return r===rn.falseFunc?rn.falseFunc:r===rn.trueFunc?gn(e,n):function(t){const i=n.getSiblings(t);let o=0;for(let e=0;e<i.length;e++){const r=i[e];if(s(t,r))break;n.isTag(r)&&n.getName(r)===n.getName(t)&&o++}return r(o)&&e(t)}},"nth-last-of-type"(e,t,{adapter:n,equals:s}){const r=An(t);return r===rn.falseFunc?rn.falseFunc:r===rn.trueFunc?gn(e,n):function(t){const i=n.getSiblings(t);let o=0;for(let e=i.length-1;e>=0;e--){const r=i[e];if(s(t,r))break;n.isTag(r)&&n.getName(r)===n.getName(t)&&o++}return r(o)&&e(t)}},root:(e,t,{adapter:n})=>t=>{const s=n.getParent(t);return(null==s||!n.isTag(s))&&e(t)},scope(e,t,n,s){const{equals:r}=n;return s&&0!==s.length?1===s.length?t=>r(s[0],t)&&e(t):t=>s.includes(t)&&e(t):Cn.root(e,t,n)},hover:bn("isHovered"),visited:bn("isVisited"),active:bn("isActive")};function bn(e){return function(t,n,{adapter:s}){const r=s[e];return"function"!=typeof r?rn.falseFunc:function(e){return r(e)&&t(e)}}}const kn={empty:(e,{adapter:t})=>!t.getChildren(e).some((e=>t.isTag(e)||""!==t.getText(e))),"first-child"(e,{adapter:t,equals:n}){if(t.prevElementSibling)return null==t.prevElementSibling(e);const s=t.getSiblings(e).find((e=>t.isTag(e)));return null!=s&&n(e,s)},"last-child"(e,{adapter:t,equals:n}){const s=t.getSiblings(e);for(let r=s.length-1;r>=0;r--){if(n(e,s[r]))return!0;if(t.isTag(s[r]))break}return!1},"first-of-type"(e,{adapter:t,equals:n}){const s=t.getSiblings(e),r=t.getName(e);for(let i=0;i<s.length;i++){const o=s[i];if(n(e,o))return!0;if(t.isTag(o)&&t.getName(o)===r)break}return!1},"last-of-type"(e,{adapter:t,equals:n}){const s=t.getSiblings(e),r=t.getName(e);for(let i=s.length-1;i>=0;i--){const o=s[i];if(n(e,o))return!0;if(t.isTag(o)&&t.getName(o)===r)break}return!1},"only-of-type"(e,{adapter:t,equals:n}){const s=t.getName(e);return t.getSiblings(e).every((r=>n(e,r)||!t.isTag(r)||t.getName(r)!==s))},"only-child":(e,{adapter:t,equals:n})=>t.getSiblings(e).every((s=>n(e,s)||!t.isTag(s)))};function Sn(e,t,n,s){if(null===n){if(e.length>s)throw new Error(`Pseudo-class :${t} requires an argument`)}else if(e.length===s)throw new Error(`Pseudo-class :${t} doesn't have any arguments`)}const Nn={"any-link":":is(a, area, link)[href]",link:":any-link:not(:visited)",disabled:":is(\n        :is(button, input, select, textarea, optgroup, option)[disabled],\n        optgroup[disabled] > option,\n        fieldset[disabled]:not(fieldset[disabled] legend:first-of-type *)\n    )",enabled:":not(:disabled)",checked:":is(:is(input[type=radio], input[type=checkbox])[checked], option:selected)",required:":is(input, select, textarea)[required]",optional:":is(input, select, textarea):not([required])",selected:"option:is([selected], select:not([multiple]):not(:has(> option[selected])) > :first-of-type)",checkbox:"[type=checkbox]",file:"[type=file]",password:"[type=password]",radio:"[type=radio]",reset:"[type=reset]",image:"[type=image]",submit:"[type=submit]",parent:":not(:empty)",header:":is(h1, h2, h3, h4, h5, h6)",button:":is(button, input[type=button])",input:":is(input, textarea, select, button)",text:"input:is(:not([type!='']), [type=text])"},In={};function Dn(e,t){const n=t.getSiblings(e);if(n.length<=1)return[];const s=n.indexOf(e);return s<0||s===n.length-1?[]:n.slice(s+1).filter(t.isTag)}function yn(e){return{xmlMode:!!e.xmlMode,lowerCaseAttributeNames:!!e.lowerCaseAttributeNames,lowerCaseTags:!!e.lowerCaseTags,quirksMode:!!e.quirksMode,cacheResults:!!e.cacheResults,pseudos:e.pseudos,adapter:e.adapter,equals:e.equals}}const On=(e,t,n,s,r)=>{const i=r(t,yn(n),s);return i===rn.trueFunc?e:i===rn.falseFunc?rn.falseFunc:t=>i(t)&&e(t)},Ln={is:On,matches:On,where:On,not(e,t,n,s,r){const i=r(t,yn(n),s);return i===rn.falseFunc?e:i===rn.trueFunc?rn.falseFunc:t=>!i(t)&&e(t)},has(e,t,n,s,r){const{adapter:i}=n,o=yn(n);o.relativeSelector=!0;const a=t.some((e=>e.some(an)))?[In]:void 0,c=r(t,o,a);if(c===rn.falseFunc)return rn.falseFunc;const l=function(e,t){return e===rn.falseFunc?rn.falseFunc:n=>t.isTag(n)&&e(n)}(c,i);if(a&&c!==rn.trueFunc){const{shouldTestNextSiblings:t=!1}=c;return n=>{if(!e(n))return!1;a[0]=n;const s=i.getChildren(n),r=t?[...s,...Dn(n,i)]:s;return i.existsOne(l,r)}}return t=>e(t)&&i.existsOne(l,i.getChildren(t))}};function Rn(e,t){const n=t.getParent(e);return n&&t.isTag(n)?n:null}function Mn(e,t,n,s,r){const{adapter:i,equals:o}=n;switch(t.type){case Bt.PseudoElement:throw new Error("Pseudo-elements are not supported by css-select");case Bt.ColumnCombinator:throw new Error("Column combinators are not yet supported by css-select");case Bt.Attribute:if(null!=t.namespace)throw new Error("Namespaced attributes are not yet supported by css-select");return n.xmlMode&&!n.lowerCaseAttributeNames||(t.name=t.name.toLowerCase()),mn[t.action](e,t,n);case Bt.Pseudo:return function(e,t,n,s,r){var i;const{name:o,data:a}=t;if(Array.isArray(a)){if(!(o in Ln))throw new Error(`Unknown pseudo-class :${o}(${a})`);return Ln[o](e,a,n,s,r)}const c=null===(i=n.pseudos)||void 0===i?void 0:i[o],l="string"==typeof c?c:Nn[o];if("string"==typeof l){if(null!=a)throw new Error(`Pseudo ${o} doesn't have any arguments`);const t=Zt(l);return Ln.is(e,t,n,s,r)}if("function"==typeof c)return Sn(c,o,a,1),t=>c(t,a)&&e(t);if(o in Cn)return Cn[o](e,a,n,s);if(o in kn){const t=kn[o];return Sn(t,o,a,2),s=>t(s,n,a)&&e(s)}throw new Error(`Unknown pseudo-class :${o}`)}(e,t,n,s,r);case Bt.Tag:{if(null!=t.namespace)throw new Error("Namespaced tag names are not yet supported by css-select");let{name:s}=t;return n.xmlMode&&!n.lowerCaseTags||(s=s.toLowerCase()),function(t){return i.getName(t)===s&&e(t)}}case Bt.Descendant:{if(!1===n.cacheResults||"undefined"==typeof WeakSet)return function(t){let n=t;for(;n=Rn(n,i);)if(e(n))return!0;return!1};const t=new WeakSet;return function(n){let s=n;for(;s=Rn(s,i);)if(!t.has(s)){if(i.isTag(s)&&e(s))return!0;t.add(s)}return!1}}case"_flexibleDescendant":return function(t){let n=t;do{if(e(n))return!0}while(n=Rn(n,i));return!1};case Bt.Parent:return function(t){return i.getChildren(t).some((t=>i.isTag(t)&&e(t)))};case Bt.Child:return function(t){const n=i.getParent(t);return null!=n&&i.isTag(n)&&e(n)};case Bt.Sibling:return function(t){const n=i.getSiblings(t);for(let s=0;s<n.length;s++){const r=n[s];if(o(t,r))break;if(i.isTag(r)&&e(r))return!0}return!1};case Bt.Adjacent:return i.prevElementSibling?function(t){const n=i.prevElementSibling(t);return null!=n&&e(n)}:function(t){const n=i.getSiblings(t);let s;for(let e=0;e<n.length;e++){const r=n[e];if(o(t,r))break;i.isTag(r)&&(s=r)}return!!s&&e(s)};case Bt.Universal:if(null!=t.namespace&&"*"!==t.namespace)throw new Error("Namespaced universal selectors are not yet supported by css-select");return e}}function vn(e){return e.type===Bt.Pseudo&&("scope"===e.name||Array.isArray(e.data)&&e.data.some((e=>e.some(vn))))}const xn={type:Bt.Descendant},wn={type:"_flexibleDescendant"},Fn={type:Bt.Pseudo,name:"scope",data:null};function Pn(e,t,n){var s;e.forEach(ln),n=null!==(s=t.context)&&void 0!==s?s:n;const r=Array.isArray(n),i=n&&(Array.isArray(n)?n:[n]);if(!1!==t.relativeSelector)!function(e,{adapter:t},n){const s=!!(null==n?void 0:n.every((e=>{const n=t.isTag(e)&&t.getParent(e);return e===In||n&&t.isTag(n)})));for(const t of e){if(t.length>0&&an(t[0])&&t[0].type!==Bt.Descendant);else{if(!s||t.some(vn))continue;t.unshift(xn)}t.unshift(Fn)}}(e,t,i);else if(e.some((e=>e.length>0&&an(e[0]))))throw new Error("Relative selectors are not allowed when the `relativeSelector` option is disabled");let o=!1;const a=e.map((e=>{if(e.length>=2){const[t,n]=e;t.type!==Bt.Pseudo||"scope"!==t.name||(r&&n.type===Bt.Descendant?e[1]=wn:n.type!==Bt.Adjacent&&n.type!==Bt.Sibling||(o=!0))}return function(e,t,n){var s;return e.reduce(((e,s)=>e===rn.falseFunc?rn.falseFunc:Mn(e,s,t,n,Pn)),null!==(s=t.rootFunc)&&void 0!==s?s:rn.trueFunc)}(e,t,i)})).reduce(Bn,rn.falseFunc);return a.shouldTestNextSiblings=o,a}function Bn(e,t){return t===rn.falseFunc||e===rn.trueFunc?e:e===rn.falseFunc||t===rn.trueFunc?t:function(n){return e(n)||t(n)}}const Un=(e,t)=>e===t,Hn={adapter:ht,equals:Un};const Gn=($n=Pn,function(e,t,n){const s=function(e){var t,n,s,r;const i=null!=e?e:Hn;return null!==(t=i.adapter)&&void 0!==t||(i.adapter=ht),null!==(n=i.equals)&&void 0!==n||(i.equals=null!==(r=null===(s=i.adapter)||void 0===s?void 0:s.equals)&&void 0!==r?r:Un),i}(t);return $n(e,s,n)});var $n;function qn(e,t,n=!1){return n&&(e=function(e,t){const n=Array.isArray(e)?e.slice(0):[e],s=n.length;for(let e=0;e<s;e++){const s=Dn(n[e],t);n.push(...s)}return n}(e,t)),Array.isArray(e)?t.removeSubsets(e):t.getChildren(e)}const Yn=new Set(["first","last","eq","gt","nth","lt","even","odd"]);function jn(e){return"pseudo"===e.type&&(!!Yn.has(e.name)||!("not"!==e.name||!Array.isArray(e.data))&&e.data.some((e=>e.some(jn))))}function Kn(e){const t=[],n=[];for(const s of e)s.some(jn)?t.push(s):n.push(s);return[n,t]}const Vn={type:Bt.Universal,namespace:null},zn={type:Bt.Pseudo,name:"scope",data:null};function Qn(e,t,n={}){return Wn([e],t,n)}function Wn(e,t,n={}){if("function"==typeof t)return e.some(t);const[s,r]=Kn(Zt(t));return s.length>0&&e.some(Gn(s,n))||r.some((t=>Jn(t,e,n).length>0))}function Xn(e,t,n={}){return Zn(Zt(e),t,n)}function Zn(e,t,n){if(0===t.length)return[];const[s,r]=Kn(e);let i;if(s.length){const e=ss(t,s,n);if(0===r.length)return e;e.length&&(i=new Set(e))}for(let e=0;e<r.length&&(null==i?void 0:i.size)!==t.length;e++){const s=r[e];if(0===(i?t.filter((e=>Y(e)&&!i.has(e))):t).length)break;const o=Jn(s,t,n);if(o.length)if(i)o.forEach((e=>i.add(e)));else{if(e===r.length-1)return o;i=new Set(o)}}return void 0!==i?i.size===t.length?t:t.filter((e=>i.has(e))):[]}function Jn(e,t,n){var s;if(e.some(Kt)){const r=null!==(s=n.root)&&void 0!==s?s:function(e){for(;e.parent;)e=e.parent;return e}(t[0]),i={...n,context:t,relativeSelector:!1};return e.push(zn),es(r,e,i,!0,t.length)}return es(t,e,n,!1,t.length)}function es(e,t,n,s,r){const i=t.findIndex(jn),o=t.slice(0,i),a=t[i],c=t.length-1===i?r:1/0,l=function(e,t,n){const s=null!=t?parseInt(t,10):NaN;switch(e){case"first":return 1;case"nth":case"eq":return isFinite(s)?s>=0?s+1:1/0:0;case"lt":return isFinite(s)?s>=0?Math.min(s,n):1/0:0;case"gt":return isFinite(s)?1/0:0;case"odd":return 2*n;case"even":return 2*n-1;case"last":case"not":return 1/0}}(a.name,a.data,c);if(0===l)return[];const u=(0!==o.length||Array.isArray(e)?0===o.length?(Array.isArray(e)?e:[e]).filter(Y):s||o.some(Kt)?ts(e,[o],n,l):ss(e,[o],n):He(e).filter(Y)).slice(0,l);let h=function(e,t,n,s){const r="string"==typeof n?parseInt(n,10):NaN;switch(e){case"first":case"lt":return t;case"last":return t.length>0?[t[t.length-1]]:t;case"nth":case"eq":return isFinite(r)&&Math.abs(r)<t.length?[r<0?t[t.length+r]:t[r]]:[];case"gt":return isFinite(r)?t.slice(r+1):[];case"even":return t.filter(((e,t)=>t%2==0));case"odd":return t.filter(((e,t)=>t%2==1));case"not":{const e=new Set(Zn(n,t,s));return t.filter((t=>!e.has(t)))}}}(a.name,u,a.data,n);if(0===h.length||t.length===i+1)return h;const d=t.slice(i+1),p=d.some(Kt);if(p){if(Kt(d[0])){const{type:e}=d[0];e!==Bt.Sibling&&e!==Bt.Adjacent||(h=qn(h,ht,!0)),d.unshift(Vn)}n={...n,relativeSelector:!1,rootFunc:e=>h.includes(e)}}else n.rootFunc&&n.rootFunc!==sn.trueFunc&&(n={...n,rootFunc:sn.trueFunc});return d.some(jn)?es(h,d,n,!1,r):p?ts(h,[d],n,r):ss(h,[d],n)}function ts(e,t,n,s){return ns(e,Gn(t,n,e),s)}function ns(e,t,n=1/0){return Ve((e=>Y(e)&&t(e)),qn(e,ht,t.shouldTestNextSiblings),!0,n)}function ss(e,t,n){const s=(Array.isArray(e)?e:[e]).filter(Y);if(0===s.length)return s;const r=Gn(t,n);return r===sn.trueFunc?s:s.filter(r)}const rs=/^\s*[+~]/;function is(e){return function(t,...n){return function(s){var r;let i=e(t,this);return s&&(i=bs(i,s,this.options.xmlMode,null===(r=this._root)||void 0===r?void 0:r[0])),this._make(this.length>1&&i.length>1?n.reduce(((e,t)=>t(e)),i):i)}}}const os=is(((e,t)=>{let n=[];for(let s=0;s<t.length;s++){const r=e(t[s]);r.length>0&&(n=n.concat(r))}return n})),as=is(((e,t)=>{const n=[];for(let s=0;s<t.length;s++){const r=e(t[s]);null!==r&&n.push(r)}return n}));function cs(e,...t){let n=null;const s=is(((e,t)=>{const s=[];return gt(t,(t=>{for(let r;(r=e(t))&&!(null==n?void 0:n(r,s.length));t=r)s.push(r)})),s}))(e,...t);return function(e,t){n="string"==typeof e?t=>Qn(t,e,this.options):e?Cs(e):null;const r=s.call(this,t);return n=null,r}}function ls(e){return e.length>1?Array.from(new Set(e)):e}const us=as((({parent:e})=>e&&!Q(e)?e:null),ls),hs=os((e=>{const t=[];for(;e.parent&&!Q(e.parent);)t.push(e.parent),e=e.parent;return t}),st,(e=>e.reverse())),ds=cs((({parent:e})=>e&&!Q(e)?e:null),st,(e=>e.reverse()));const ps=as((e=>qe(e))),fs=os((e=>{const t=[];for(;e.next;)Y(e=e.next)&&t.push(e);return t}),ls),ms=cs((e=>qe(e)),ls),Es=as((e=>Ye(e))),Ts=os((e=>{const t=[];for(;e.prev;)Y(e=e.prev)&&t.push(e);return t}),ls),_s=cs((e=>Ye(e)),ls),As=os((e=>$e(e).filter((t=>Y(t)&&t!==e))),st),gs=os((e=>He(e).filter(Y)),ls);function Cs(e){return"function"==typeof e?(t,n)=>e.call(t,n,t):At(e)?t=>Array.prototype.includes.call(e,t):function(t){return e===t}}function bs(e,t,n,s){return"string"==typeof t?Xn(t,e,{xmlMode:n,root:s}):e.filter(Cs(t))}const ks=Object.freeze(Object.defineProperty({__proto__:null,_findBySelector:function(e,t){var n;const s=this.toArray(),r=rs.test(e)?s:this.children().toArray(),i={context:s,root:null===(n=this._root)||void 0===n?void 0:n[0],xmlMode:this.options.xmlMode,lowerCaseTags:this.options.lowerCaseTags,lowerCaseAttributeNames:this.options.lowerCaseAttributeNames,pseudos:this.options.pseudos,quirksMode:this.options.quirksMode};return this._make(function(e,t,n={},s=1/0){if("function"==typeof e)return ns(t,e);const[r,i]=Kn(Zt(e)),o=i.map((e=>es(t,e,n,!0,s)));return r.length&&o.push(ts(t,r,n,s)),0===o.length?[]:1===o.length?o[0]:st(o.reduce(((e,t)=>[...e,...t])))}(e,r,i,t))},add:function(e,t){const n=this._make(e,t),s=st([...this.get(),...n.get()]);return this._make(s)},addBack:function(e){return this.prevObject?this.add(e?this.prevObject.filter(e):this.prevObject):this},children:gs,closest:function(e){var t;const n=[];if(!e)return this._make(n);const s={xmlMode:this.options.xmlMode,root:null===(t=this._root)||void 0===t?void 0:t[0]},r="string"==typeof e?t=>Qn(t,e,s):Cs(e);return gt(this,(e=>{for(!e||Q(e)||Y(e)||(e=e.parent);e&&Y(e);){if(r(e,0)){n.includes(e)||n.push(e);break}e=e.parent}})),this._make(n)},contents:function(){const e=this.toArray().reduce(((e,t)=>W(t)?e.concat(t.children):e),[]);return this._make(e)},each:function(e){let t=0;const n=this.length;for(;t<n&&!1!==e.call(this[t],t,this[t]);)++t;return this},end:function(){var e;return null!==(e=this.prevObject)&&void 0!==e?e:this._make([])},eq:function(e){var t;return 0===(e=+e)&&this.length<=1?this:(e<0&&(e=this.length+e),this._make(null!==(t=this[e])&&void 0!==t?t:[]))},filter:function(e){var t;return this._make(bs(this.toArray(),e,this.options.xmlMode,null===(t=this._root)||void 0===t?void 0:t[0]))},filterArray:bs,find:function(e){if(!e)return this._make([]);if("string"!=typeof e){const t=At(e)?e.toArray():[e],n=this.toArray();return this._make(t.filter((e=>n.some((t=>Et(t,e))))))}return this._findBySelector(e,Number.POSITIVE_INFINITY)},first:function(){return this.length>1?this._make(this[0]):this},get:function(e){return null==e?this.toArray():this[e<0?this.length+e:e]},has:function(e){return this.filter("string"==typeof e?`:has(${e})`:(t,n)=>this._make(n).find(e).length>0)},index:function(e){let t,n;return null==e?(t=this.parent().children(),n=this[0]):"string"==typeof e?(t=this._make(e),n=this[0]):(t=this,n=At(e)?e[0]:e),Array.prototype.indexOf.call(t,n)},is:function(e){const t=this.toArray();return"string"==typeof e?Wn(t.filter(Y),e,this.options):!!e&&t.some(Cs(e))},last:function(){return this.length>0?this._make(this[this.length-1]):this},map:function(e){let t=[];for(let n=0;n<this.length;n++){const s=this[n],r=e.call(s,n,s);null!=r&&(t=t.concat(r))}return this._make(t)},next:ps,nextAll:fs,nextUntil:ms,not:function(e){let t=this.toArray();if("string"==typeof e){const n=new Set(Xn(e,t,this.options));t=t.filter((e=>!n.has(e)))}else{const n=Cs(e);t=t.filter(((e,t)=>!n(e,t)))}return this._make(t)},parent:us,parents:hs,parentsUntil:ds,prev:Es,prevAll:Ts,prevUntil:_s,siblings:As,slice:function(e,t){return this._make(Array.prototype.slice.call(this,e,t))},toArray:function(){return Array.prototype.slice.call(this)}},Symbol.toStringTag,{value:"Module"}));function Ss(e,t){const n=Array.isArray(e)?e:[e];t?t.children=n:t=null;for(let e=0;e<n.length;e++){const s=n[e];s.parent&&s.parent.children!==n&&je(s),t?(s.prev=n[e-1]||null,s.next=n[e+1]||null):s.prev=s.next=null,s.parent=t}return t}function Ns(e){return function(...t){const n=this.length-1;return gt(this,((s,r)=>{if(!W(s))return;const i="function"==typeof t[0]?t[0].call(s,r,this._render(s.children)):t,o=this._makeDomArray(i,r<n);e(o,s.children,s)}))}}function Is(e,t,n,s,r){var i,o;const a=[t,n,...s],c=0===t?null:e[t-1],l=t+n>=e.length?null:e[t+n];for(let e=0;e<s.length;++e){const n=s[e],u=n.parent;if(u){const e=u.children.indexOf(n);e>-1&&(u.children.splice(e,1),r===u&&t>e&&a[0]--)}n.parent=r,n.prev&&(n.prev.next=null!==(i=n.next)&&void 0!==i?i:null),n.next&&(n.next.prev=null!==(o=n.prev)&&void 0!==o?o:null),n.prev=0===e?c:s[e-1],n.next=e===s.length-1?l:s[e+1]}return c&&(c.next=s[0]),l&&(l.prev=s[s.length-1]),e.splice(...a)}const Ds=Ns(((e,t,n)=>{Is(t,t.length,0,e,n)})),ys=Ns(((e,t,n)=>{Is(t,0,0,e,n)}));function Os(e){return function(t){const n=this.length-1,s=this.parents().last();for(let r=0;r<this.length;r++){const i=this[r],o="function"==typeof t?t.call(i,r,i):"string"!=typeof t||kt(t)?t:s.find(t).clone(),[a]=this._makeDomArray(o,r<n);if(!a||!W(a))continue;let c=a,l=0;for(;l<c.children.length;){const e=c.children[l];Y(e)?(c=e,l=0):l++}e(i,c,[a])}return this}}const Ls=Os(((e,t,n)=>{const{parent:s}=e;if(!s)return;const r=s.children,i=r.indexOf(e);Ss([e],t),Is(r,i,0,n,s)})),Rs=Os(((e,t,n)=>{W(e)&&(Ss(e.children,t),Ss(n,e))}));const Ms=Object.freeze(Object.defineProperty({__proto__:null,_makeDomArray:function(e,t){if(null==e)return[];if("string"==typeof e)return this._parse(e,this.options,!1,null).children.slice(0);if("length"in e){if(1===e.length)return this._makeDomArray(e[0],t);const n=[];for(let s=0;s<e.length;s++){const r=e[s];if("object"==typeof r){if(null==r)continue;if(!("length"in r)){n.push(t?X(r,!0):r);continue}}n.push(...this._makeDomArray(r,t))}return n}return[t?X(e,!0):e]},after:function(...e){const t=this.length-1;return gt(this,((n,s)=>{if(!W(n)||!n.parent)return;const r=n.parent.children,i=r.indexOf(n);if(i<0)return;const o="function"==typeof e[0]?e[0].call(n,s,this._render(n.children)):e;Is(r,i+1,0,this._makeDomArray(o,s<t),n.parent)}))},append:Ds,appendTo:function(e){return(At(e)?e:this._make(e)).append(this),this},before:function(...e){const t=this.length-1;return gt(this,((n,s)=>{if(!W(n)||!n.parent)return;const r=n.parent.children,i=r.indexOf(n);if(i<0)return;const o="function"==typeof e[0]?e[0].call(n,s,this._render(n.children)):e;Is(r,i,0,this._makeDomArray(o,s<t),n.parent)}))},clone:function(){const e=Array.prototype.map.call(this.get(),(e=>X(e,!0))),t=new $(e);for(const n of e)n.parent=t;return this._make(e)},empty:function(){return gt(this,(e=>{if(W(e)){for(const t of e.children)t.next=t.prev=t.parent=null;e.children.length=0}}))},html:function(e){if(void 0===e){const e=this[0];return e&&W(e)?this._render(e.children):null}return gt(this,(t=>{if(!W(t))return;for(const e of t.children)e.next=e.prev=e.parent=null;Ss(At(e)?e.toArray():this._parse(`${e}`,this.options,!1,t).children,t)}))},insertAfter:function(e){"string"==typeof e&&(e=this._make(e)),this.remove();const t=[];for(const n of this._makeDomArray(e)){const e=this.clone().toArray(),{parent:s}=n;if(!s)continue;const r=s.children,i=r.indexOf(n);i<0||(Is(r,i+1,0,e,s),t.push(...e))}return this._make(t)},insertBefore:function(e){const t=this._make(e);this.remove();const n=[];return gt(t,(e=>{const t=this.clone().toArray(),{parent:s}=e;if(!s)return;const r=s.children,i=r.indexOf(e);i<0||(Is(r,i,0,t,s),n.push(...t))})),this._make(n)},prepend:ys,prependTo:function(e){return(At(e)?e:this._make(e)).prepend(this),this},remove:function(e){return gt(e?this.filter(e):this,(e=>{je(e),e.prev=e.next=e.parent=null})),this},replaceWith:function(e){return gt(this,((t,n)=>{const{parent:s}=t;if(!s)return;const r=s.children,i="function"==typeof e?e.call(t,n,t):e,o=this._makeDomArray(i);Ss(o,null);const a=r.indexOf(t);Is(r,a,1,o,s),o.includes(t)||(t.parent=t.prev=t.next=null)}))},text:function(e){return void 0===e?mt(this):gt(this,"function"==typeof e?(t,n)=>this._make(t).text(e.call(t,n,mt([t]))):t=>{if(!W(t))return;for(const e of t.children)e.next=e.prev=e.parent=null;Ss(new P(`${e}`),t)})},toString:function(){return this._render(this)},unwrap:function(e){return this.parent(e).not("body").each(((e,t)=>{this._make(t).replaceWith(t.children)})),this},wrap:Ls,wrapAll:function(e){const t=this[0];if(t){const n=this._make("function"==typeof e?e.call(t,0,t):e).insertBefore(t);let s;for(let e=0;e<n.length;e++)"tag"===n[e].type&&(s=n[e]);let r=0;for(;s&&r<s.children.length;){const e=s.children[r];"tag"===e.type?(s=e,r=0):r++}s&&this._make(s).append(this)}return this},wrapInner:Rs},Symbol.toStringTag,{value:"Module"}));function vs(e,t,n,s){if("string"==typeof t){const i=xs(e),o="function"==typeof n?n.call(e,s,i[t]):n;""===o?delete i[t]:null!=o&&(i[t]=o),e.attribs.style=(r=i,Object.keys(r).reduce(((e,t)=>`${e}${e?" ":""}${t}: ${r[t]};`),""))}else if("object"==typeof t){const n=Object.keys(t);for(let s=0;s<n.length;s++){const r=n[s];vs(e,r,t[r],s)}}var r}function xs(e,t){if(!e||!Y(e))return;const n=function(e){if(e=(e||"").trim(),!e)return{};const t={};let n;for(const s of e.split(";")){const e=s.indexOf(":");if(e<1||e===s.length-1){const e=s.trimEnd();e.length>0&&void 0!==n&&(t[n]+=`;${e}`)}else n=s.slice(0,e).trim(),t[n]=s.slice(e+1).trim()}return t}(e.attribs.style);if("string"==typeof t)return n[t];if(Array.isArray(t)){const e={};for(const s of t)null!=n[s]&&(e[s]=n[s]);return e}return n}const ws=Object.freeze(Object.defineProperty({__proto__:null,css:function(e,t){return null!=e&&null!=t||"object"==typeof e&&!Array.isArray(e)?gt(this,((n,s)=>{Y(n)&&vs(n,e,t,s)})):0!==this.length?xs(this[0],e):void 0}},Symbol.toStringTag,{value:"Module"})),Fs="input,select,textarea,keygen",Ps=/%20/g,Bs=/\r?\n/g;const Us=Object.freeze(Object.defineProperty({__proto__:null,serialize:function(){return this.serializeArray().map((e=>`${encodeURIComponent(e.name)}=${encodeURIComponent(e.value)}`)).join("&").replace(Ps,"+")},serializeArray:function(){return this.map(((e,t)=>{const n=this._make(t);return Y(t)&&"form"===t.name?n.find(Fs).toArray():n.filter(Fs).toArray()})).filter('[name!=""]:enabled:not(:submit, :button, :image, :reset, :file):matches([checked], :not(:checkbox, :radio))').map(((e,t)=>{var n;const s=this._make(t),r=s.attr("name"),i=null!==(n=s.val())&&void 0!==n?n:"";return Array.isArray(i)?i.map((e=>({name:r,value:e.replace(Bs,"\r\n")}))):{name:r,value:i.replace(Bs,"\r\n")}})).toArray()}},Symbol.toStringTag,{value:"Module"}));function Hs(e){var t;return"string"==typeof e?{selector:e,value:"textContent"}:{selector:e.selector,value:null!==(t=e.value)&&void 0!==t?t:"textContent"}}const Gs=Object.freeze(Object.defineProperty({__proto__:null,extract:function(e){const t={};for(const n in e){const s=e[n],r=Array.isArray(s),{selector:i,value:o}=Hs(r?s[0]:s),a="function"==typeof o?o:"string"==typeof o?e=>this._make(e).prop(o):e=>this._make(e).extract(o);if(r)t[n]=this._findBySelector(i,Number.POSITIVE_INFINITY).map(((e,s)=>a(s,n,t))).get();else{const e=this._findBySelector(i,1);t[n]=e.length>0?a(e[0],n,t):void 0}}return t}},Symbol.toStringTag,{value:"Module"}));class $s{constructor(e,t,n){if(this.length=0,this.options=n,this._root=t,e){for(let t=0;t<e.length;t++)this[t]=e[t];this.length=e.length}}}$s.prototype.cheerio="[cheerio object]",$s.prototype.splice=Array.prototype.splice,$s.prototype[Symbol.iterator]=Array.prototype[Symbol.iterator],Object.assign($s.prototype,Pt,ks,Ms,ws,Us,Gs);const qs=new Set([65534,65535,131070,131071,196606,196607,262142,262143,327678,327679,393214,393215,458750,458751,524286,524287,589822,589823,655358,655359,720894,720895,786430,786431,851966,851967,917502,917503,983038,983039,1048574,1048575,1114110,1114111]),Ys="�";var js,Ks;(Ks=js=js||(js={}))[Ks.EOF=-1]="EOF",Ks[Ks.NULL=0]="NULL",Ks[Ks.TABULATION=9]="TABULATION",Ks[Ks.CARRIAGE_RETURN=13]="CARRIAGE_RETURN",Ks[Ks.LINE_FEED=10]="LINE_FEED",Ks[Ks.FORM_FEED=12]="FORM_FEED",Ks[Ks.SPACE=32]="SPACE",Ks[Ks.EXCLAMATION_MARK=33]="EXCLAMATION_MARK",Ks[Ks.QUOTATION_MARK=34]="QUOTATION_MARK",Ks[Ks.NUMBER_SIGN=35]="NUMBER_SIGN",Ks[Ks.AMPERSAND=38]="AMPERSAND",Ks[Ks.APOSTROPHE=39]="APOSTROPHE",Ks[Ks.HYPHEN_MINUS=45]="HYPHEN_MINUS",Ks[Ks.SOLIDUS=47]="SOLIDUS",Ks[Ks.DIGIT_0=48]="DIGIT_0",Ks[Ks.DIGIT_9=57]="DIGIT_9",Ks[Ks.SEMICOLON=59]="SEMICOLON",Ks[Ks.LESS_THAN_SIGN=60]="LESS_THAN_SIGN",Ks[Ks.EQUALS_SIGN=61]="EQUALS_SIGN",Ks[Ks.GREATER_THAN_SIGN=62]="GREATER_THAN_SIGN",Ks[Ks.QUESTION_MARK=63]="QUESTION_MARK",Ks[Ks.LATIN_CAPITAL_A=65]="LATIN_CAPITAL_A",Ks[Ks.LATIN_CAPITAL_F=70]="LATIN_CAPITAL_F",Ks[Ks.LATIN_CAPITAL_X=88]="LATIN_CAPITAL_X",Ks[Ks.LATIN_CAPITAL_Z=90]="LATIN_CAPITAL_Z",Ks[Ks.RIGHT_SQUARE_BRACKET=93]="RIGHT_SQUARE_BRACKET",Ks[Ks.GRAVE_ACCENT=96]="GRAVE_ACCENT",Ks[Ks.LATIN_SMALL_A=97]="LATIN_SMALL_A",Ks[Ks.LATIN_SMALL_F=102]="LATIN_SMALL_F",Ks[Ks.LATIN_SMALL_X=120]="LATIN_SMALL_X",Ks[Ks.LATIN_SMALL_Z=122]="LATIN_SMALL_Z",Ks[Ks.REPLACEMENT_CHARACTER=65533]="REPLACEMENT_CHARACTER";const Vs="--",zs="[CDATA[",Qs="doctype",Ws="script",Xs="public",Zs="system";function Js(e){return e>=55296&&e<=57343}function er(e){return 32!==e&&10!==e&&13!==e&&9!==e&&12!==e&&e>=1&&e<=31||e>=127&&e<=159}function tr(e){return e>=64976&&e<=65007||qs.has(e)}var nr,sr;(sr=nr=nr||(nr={})).controlCharacterInInputStream="control-character-in-input-stream",sr.noncharacterInInputStream="noncharacter-in-input-stream",sr.surrogateInInputStream="surrogate-in-input-stream",sr.nonVoidHtmlElementStartTagWithTrailingSolidus="non-void-html-element-start-tag-with-trailing-solidus",sr.endTagWithAttributes="end-tag-with-attributes",sr.endTagWithTrailingSolidus="end-tag-with-trailing-solidus",sr.unexpectedSolidusInTag="unexpected-solidus-in-tag",sr.unexpectedNullCharacter="unexpected-null-character",sr.unexpectedQuestionMarkInsteadOfTagName="unexpected-question-mark-instead-of-tag-name",sr.invalidFirstCharacterOfTagName="invalid-first-character-of-tag-name",sr.unexpectedEqualsSignBeforeAttributeName="unexpected-equals-sign-before-attribute-name",sr.missingEndTagName="missing-end-tag-name",sr.unexpectedCharacterInAttributeName="unexpected-character-in-attribute-name",sr.unknownNamedCharacterReference="unknown-named-character-reference",sr.missingSemicolonAfterCharacterReference="missing-semicolon-after-character-reference",sr.unexpectedCharacterAfterDoctypeSystemIdentifier="unexpected-character-after-doctype-system-identifier",sr.unexpectedCharacterInUnquotedAttributeValue="unexpected-character-in-unquoted-attribute-value",sr.eofBeforeTagName="eof-before-tag-name",sr.eofInTag="eof-in-tag",sr.missingAttributeValue="missing-attribute-value",sr.missingWhitespaceBetweenAttributes="missing-whitespace-between-attributes",sr.missingWhitespaceAfterDoctypePublicKeyword="missing-whitespace-after-doctype-public-keyword",sr.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers="missing-whitespace-between-doctype-public-and-system-identifiers",sr.missingWhitespaceAfterDoctypeSystemKeyword="missing-whitespace-after-doctype-system-keyword",sr.missingQuoteBeforeDoctypePublicIdentifier="missing-quote-before-doctype-public-identifier",sr.missingQuoteBeforeDoctypeSystemIdentifier="missing-quote-before-doctype-system-identifier",sr.missingDoctypePublicIdentifier="missing-doctype-public-identifier",sr.missingDoctypeSystemIdentifier="missing-doctype-system-identifier",sr.abruptDoctypePublicIdentifier="abrupt-doctype-public-identifier",sr.abruptDoctypeSystemIdentifier="abrupt-doctype-system-identifier",sr.cdataInHtmlContent="cdata-in-html-content",sr.incorrectlyOpenedComment="incorrectly-opened-comment",sr.eofInScriptHtmlCommentLikeText="eof-in-script-html-comment-like-text",sr.eofInDoctype="eof-in-doctype",sr.nestedComment="nested-comment",sr.abruptClosingOfEmptyComment="abrupt-closing-of-empty-comment",sr.eofInComment="eof-in-comment",sr.incorrectlyClosedComment="incorrectly-closed-comment",sr.eofInCdata="eof-in-cdata",sr.absenceOfDigitsInNumericCharacterReference="absence-of-digits-in-numeric-character-reference",sr.nullCharacterReference="null-character-reference",sr.surrogateCharacterReference="surrogate-character-reference",sr.characterReferenceOutsideUnicodeRange="character-reference-outside-unicode-range",sr.controlCharacterReference="control-character-reference",sr.noncharacterCharacterReference="noncharacter-character-reference",sr.missingWhitespaceBeforeDoctypeName="missing-whitespace-before-doctype-name",sr.missingDoctypeName="missing-doctype-name",sr.invalidCharacterSequenceAfterDoctypeName="invalid-character-sequence-after-doctype-name",sr.duplicateAttribute="duplicate-attribute",sr.nonConformingDoctype="non-conforming-doctype",sr.missingDoctype="missing-doctype",sr.misplacedDoctype="misplaced-doctype",sr.endTagWithoutMatchingOpenElement="end-tag-without-matching-open-element",sr.closingOfElementWithOpenChildElements="closing-of-element-with-open-child-elements",sr.disallowedContentInNoscriptInHead="disallowed-content-in-noscript-in-head",sr.openElementsLeftAfterEof="open-elements-left-after-eof",sr.abandonedHeadElementChild="abandoned-head-element-child",sr.misplacedStartTagForHeadElement="misplaced-start-tag-for-head-element",sr.nestedNoscriptInHead="nested-noscript-in-head",sr.eofInElementThatCanContainOnlyText="eof-in-element-that-can-contain-only-text";class rr{constructor(e){this.handler=e,this.html="",this.pos=-1,this.lastGapPos=-2,this.gapStack=[],this.skipNextNewLine=!1,this.lastChunkWritten=!1,this.endOfChunkHit=!1,this.bufferWaterline=65536,this.isEol=!1,this.lineStartPos=0,this.droppedBufferSize=0,this.line=1,this.lastErrOffset=-1}get col(){return this.pos-this.lineStartPos+Number(this.lastGapPos!==this.pos)}get offset(){return this.droppedBufferSize+this.pos}getError(e){const{line:t,col:n,offset:s}=this;return{code:e,startLine:t,endLine:t,startCol:n,endCol:n,startOffset:s,endOffset:s}}_err(e){this.handler.onParseError&&this.lastErrOffset!==this.offset&&(this.lastErrOffset=this.offset,this.handler.onParseError(this.getError(e)))}_addGap(){this.gapStack.push(this.lastGapPos),this.lastGapPos=this.pos}_processSurrogate(e){if(this.pos!==this.html.length-1){const t=this.html.charCodeAt(this.pos+1);if(function(e){return e>=56320&&e<=57343}(t))return this.pos++,this._addGap(),1024*(e-55296)+9216+t}else if(!this.lastChunkWritten)return this.endOfChunkHit=!0,js.EOF;return this._err(nr.surrogateInInputStream),e}willDropParsedChunk(){return this.pos>this.bufferWaterline}dropParsedChunk(){this.willDropParsedChunk()&&(this.html=this.html.substring(this.pos),this.lineStartPos-=this.pos,this.droppedBufferSize+=this.pos,this.pos=0,this.lastGapPos=-2,this.gapStack.length=0)}write(e,t){this.html.length>0?this.html+=e:this.html=e,this.endOfChunkHit=!1,this.lastChunkWritten=t}insertHtmlAtCurrentPos(e){this.html=this.html.substring(0,this.pos+1)+e+this.html.substring(this.pos+1),this.endOfChunkHit=!1}startsWith(e,t){if(this.pos+e.length>this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,!1;if(t)return this.html.startsWith(e,this.pos);for(let t=0;t<e.length;t++){if((32|this.html.charCodeAt(this.pos+t))!==e.charCodeAt(t))return!1}return!0}peek(e){const t=this.pos+e;if(t>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,js.EOF;const n=this.html.charCodeAt(t);return n===js.CARRIAGE_RETURN?js.LINE_FEED:n}advance(){if(this.pos++,this.isEol&&(this.isEol=!1,this.line++,this.lineStartPos=this.pos),this.pos>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,js.EOF;let e=this.html.charCodeAt(this.pos);if(e===js.CARRIAGE_RETURN)return this.isEol=!0,this.skipNextNewLine=!0,js.LINE_FEED;if(e===js.LINE_FEED&&(this.isEol=!0,this.skipNextNewLine))return this.line--,this.skipNextNewLine=!1,this._addGap(),this.advance();this.skipNextNewLine=!1,Js(e)&&(e=this._processSurrogate(e));return null===this.handler.onParseError||e>31&&e<127||e===js.LINE_FEED||e===js.CARRIAGE_RETURN||e>159&&e<64976||this._checkForProblematicCharacters(e),e}_checkForProblematicCharacters(e){er(e)?this._err(nr.controlCharacterInInputStream):tr(e)&&this._err(nr.noncharacterInInputStream)}retreat(e){for(this.pos-=e;this.pos<this.lastGapPos;)this.lastGapPos=this.gapStack.pop(),this.pos--;this.isEol=!1}}var ir,or,ar,cr,lr,ur,hr,dr,pr,fr,mr,Er;function Tr(e,t){for(let n=e.attrs.length-1;n>=0;n--)if(e.attrs[n].name===t)return e.attrs[n].value;return null}(or=ir=ir||(ir={}))[or.CHARACTER=0]="CHARACTER",or[or.NULL_CHARACTER=1]="NULL_CHARACTER",or[or.WHITESPACE_CHARACTER=2]="WHITESPACE_CHARACTER",or[or.START_TAG=3]="START_TAG",or[or.END_TAG=4]="END_TAG",or[or.COMMENT=5]="COMMENT",or[or.DOCTYPE=6]="DOCTYPE",or[or.EOF=7]="EOF",or[or.HIBERNATION=8]="HIBERNATION",(cr=ar=ar||(ar={})).HTML="http://www.w3.org/1999/xhtml",cr.MATHML="http://www.w3.org/1998/Math/MathML",cr.SVG="http://www.w3.org/2000/svg",cr.XLINK="http://www.w3.org/1999/xlink",cr.XML="http://www.w3.org/XML/1998/namespace",cr.XMLNS="http://www.w3.org/2000/xmlns/",(ur=lr=lr||(lr={})).TYPE="type",ur.ACTION="action",ur.ENCODING="encoding",ur.PROMPT="prompt",ur.NAME="name",ur.COLOR="color",ur.FACE="face",ur.SIZE="size",(dr=hr=hr||(hr={})).NO_QUIRKS="no-quirks",dr.QUIRKS="quirks",dr.LIMITED_QUIRKS="limited-quirks",(fr=pr=pr||(pr={})).A="a",fr.ADDRESS="address",fr.ANNOTATION_XML="annotation-xml",fr.APPLET="applet",fr.AREA="area",fr.ARTICLE="article",fr.ASIDE="aside",fr.B="b",fr.BASE="base",fr.BASEFONT="basefont",fr.BGSOUND="bgsound",fr.BIG="big",fr.BLOCKQUOTE="blockquote",fr.BODY="body",fr.BR="br",fr.BUTTON="button",fr.CAPTION="caption",fr.CENTER="center",fr.CODE="code",fr.COL="col",fr.COLGROUP="colgroup",fr.DD="dd",fr.DESC="desc",fr.DETAILS="details",fr.DIALOG="dialog",fr.DIR="dir",fr.DIV="div",fr.DL="dl",fr.DT="dt",fr.EM="em",fr.EMBED="embed",fr.FIELDSET="fieldset",fr.FIGCAPTION="figcaption",fr.FIGURE="figure",fr.FONT="font",fr.FOOTER="footer",fr.FOREIGN_OBJECT="foreignObject",fr.FORM="form",fr.FRAME="frame",fr.FRAMESET="frameset",fr.H1="h1",fr.H2="h2",fr.H3="h3",fr.H4="h4",fr.H5="h5",fr.H6="h6",fr.HEAD="head",fr.HEADER="header",fr.HGROUP="hgroup",fr.HR="hr",fr.HTML="html",fr.I="i",fr.IMG="img",fr.IMAGE="image",fr.INPUT="input",fr.IFRAME="iframe",fr.KEYGEN="keygen",fr.LABEL="label",fr.LI="li",fr.LINK="link",fr.LISTING="listing",fr.MAIN="main",fr.MALIGNMARK="malignmark",fr.MARQUEE="marquee",fr.MATH="math",fr.MENU="menu",fr.META="meta",fr.MGLYPH="mglyph",fr.MI="mi",fr.MO="mo",fr.MN="mn",fr.MS="ms",fr.MTEXT="mtext",fr.NAV="nav",fr.NOBR="nobr",fr.NOFRAMES="noframes",fr.NOEMBED="noembed",fr.NOSCRIPT="noscript",fr.OBJECT="object",fr.OL="ol",fr.OPTGROUP="optgroup",fr.OPTION="option",fr.P="p",fr.PARAM="param",fr.PLAINTEXT="plaintext",fr.PRE="pre",fr.RB="rb",fr.RP="rp",fr.RT="rt",fr.RTC="rtc",fr.RUBY="ruby",fr.S="s",fr.SCRIPT="script",fr.SECTION="section",fr.SELECT="select",fr.SOURCE="source",fr.SMALL="small",fr.SPAN="span",fr.STRIKE="strike",fr.STRONG="strong",fr.STYLE="style",fr.SUB="sub",fr.SUMMARY="summary",fr.SUP="sup",fr.TABLE="table",fr.TBODY="tbody",fr.TEMPLATE="template",fr.TEXTAREA="textarea",fr.TFOOT="tfoot",fr.TD="td",fr.TH="th",fr.THEAD="thead",fr.TITLE="title",fr.TR="tr",fr.TRACK="track",fr.TT="tt",fr.U="u",fr.UL="ul",fr.SVG="svg",fr.VAR="var",fr.WBR="wbr",fr.XMP="xmp",(Er=mr=mr||(mr={}))[Er.UNKNOWN=0]="UNKNOWN",Er[Er.A=1]="A",Er[Er.ADDRESS=2]="ADDRESS",Er[Er.ANNOTATION_XML=3]="ANNOTATION_XML",Er[Er.APPLET=4]="APPLET",Er[Er.AREA=5]="AREA",Er[Er.ARTICLE=6]="ARTICLE",Er[Er.ASIDE=7]="ASIDE",Er[Er.B=8]="B",Er[Er.BASE=9]="BASE",Er[Er.BASEFONT=10]="BASEFONT",Er[Er.BGSOUND=11]="BGSOUND",Er[Er.BIG=12]="BIG",Er[Er.BLOCKQUOTE=13]="BLOCKQUOTE",Er[Er.BODY=14]="BODY",Er[Er.BR=15]="BR",Er[Er.BUTTON=16]="BUTTON",Er[Er.CAPTION=17]="CAPTION",Er[Er.CENTER=18]="CENTER",Er[Er.CODE=19]="CODE",Er[Er.COL=20]="COL",Er[Er.COLGROUP=21]="COLGROUP",Er[Er.DD=22]="DD",Er[Er.DESC=23]="DESC",Er[Er.DETAILS=24]="DETAILS",Er[Er.DIALOG=25]="DIALOG",Er[Er.DIR=26]="DIR",Er[Er.DIV=27]="DIV",Er[Er.DL=28]="DL",Er[Er.DT=29]="DT",Er[Er.EM=30]="EM",Er[Er.EMBED=31]="EMBED",Er[Er.FIELDSET=32]="FIELDSET",Er[Er.FIGCAPTION=33]="FIGCAPTION",Er[Er.FIGURE=34]="FIGURE",Er[Er.FONT=35]="FONT",Er[Er.FOOTER=36]="FOOTER",Er[Er.FOREIGN_OBJECT=37]="FOREIGN_OBJECT",Er[Er.FORM=38]="FORM",Er[Er.FRAME=39]="FRAME",Er[Er.FRAMESET=40]="FRAMESET",Er[Er.H1=41]="H1",Er[Er.H2=42]="H2",Er[Er.H3=43]="H3",Er[Er.H4=44]="H4",Er[Er.H5=45]="H5",Er[Er.H6=46]="H6",Er[Er.HEAD=47]="HEAD",Er[Er.HEADER=48]="HEADER",Er[Er.HGROUP=49]="HGROUP",Er[Er.HR=50]="HR",Er[Er.HTML=51]="HTML",Er[Er.I=52]="I",Er[Er.IMG=53]="IMG",Er[Er.IMAGE=54]="IMAGE",Er[Er.INPUT=55]="INPUT",Er[Er.IFRAME=56]="IFRAME",Er[Er.KEYGEN=57]="KEYGEN",Er[Er.LABEL=58]="LABEL",Er[Er.LI=59]="LI",Er[Er.LINK=60]="LINK",Er[Er.LISTING=61]="LISTING",Er[Er.MAIN=62]="MAIN",Er[Er.MALIGNMARK=63]="MALIGNMARK",Er[Er.MARQUEE=64]="MARQUEE",Er[Er.MATH=65]="MATH",Er[Er.MENU=66]="MENU",Er[Er.META=67]="META",Er[Er.MGLYPH=68]="MGLYPH",Er[Er.MI=69]="MI",Er[Er.MO=70]="MO",Er[Er.MN=71]="MN",Er[Er.MS=72]="MS",Er[Er.MTEXT=73]="MTEXT",Er[Er.NAV=74]="NAV",Er[Er.NOBR=75]="NOBR",Er[Er.NOFRAMES=76]="NOFRAMES",Er[Er.NOEMBED=77]="NOEMBED",Er[Er.NOSCRIPT=78]="NOSCRIPT",Er[Er.OBJECT=79]="OBJECT",Er[Er.OL=80]="OL",Er[Er.OPTGROUP=81]="OPTGROUP",Er[Er.OPTION=82]="OPTION",Er[Er.P=83]="P",Er[Er.PARAM=84]="PARAM",Er[Er.PLAINTEXT=85]="PLAINTEXT",Er[Er.PRE=86]="PRE",Er[Er.RB=87]="RB",Er[Er.RP=88]="RP",Er[Er.RT=89]="RT",Er[Er.RTC=90]="RTC",Er[Er.RUBY=91]="RUBY",Er[Er.S=92]="S",Er[Er.SCRIPT=93]="SCRIPT",Er[Er.SECTION=94]="SECTION",Er[Er.SELECT=95]="SELECT",Er[Er.SOURCE=96]="SOURCE",Er[Er.SMALL=97]="SMALL",Er[Er.SPAN=98]="SPAN",Er[Er.STRIKE=99]="STRIKE",Er[Er.STRONG=100]="STRONG",Er[Er.STYLE=101]="STYLE",Er[Er.SUB=102]="SUB",Er[Er.SUMMARY=103]="SUMMARY",Er[Er.SUP=104]="SUP",Er[Er.TABLE=105]="TABLE",Er[Er.TBODY=106]="TBODY",Er[Er.TEMPLATE=107]="TEMPLATE",Er[Er.TEXTAREA=108]="TEXTAREA",Er[Er.TFOOT=109]="TFOOT",Er[Er.TD=110]="TD",Er[Er.TH=111]="TH",Er[Er.THEAD=112]="THEAD",Er[Er.TITLE=113]="TITLE",Er[Er.TR=114]="TR",Er[Er.TRACK=115]="TRACK",Er[Er.TT=116]="TT",Er[Er.U=117]="U",Er[Er.UL=118]="UL",Er[Er.SVG=119]="SVG",Er[Er.VAR=120]="VAR",Er[Er.WBR=121]="WBR",Er[Er.XMP=122]="XMP";const _r=new Map([[pr.A,mr.A],[pr.ADDRESS,mr.ADDRESS],[pr.ANNOTATION_XML,mr.ANNOTATION_XML],[pr.APPLET,mr.APPLET],[pr.AREA,mr.AREA],[pr.ARTICLE,mr.ARTICLE],[pr.ASIDE,mr.ASIDE],[pr.B,mr.B],[pr.BASE,mr.BASE],[pr.BASEFONT,mr.BASEFONT],[pr.BGSOUND,mr.BGSOUND],[pr.BIG,mr.BIG],[pr.BLOCKQUOTE,mr.BLOCKQUOTE],[pr.BODY,mr.BODY],[pr.BR,mr.BR],[pr.BUTTON,mr.BUTTON],[pr.CAPTION,mr.CAPTION],[pr.CENTER,mr.CENTER],[pr.CODE,mr.CODE],[pr.COL,mr.COL],[pr.COLGROUP,mr.COLGROUP],[pr.DD,mr.DD],[pr.DESC,mr.DESC],[pr.DETAILS,mr.DETAILS],[pr.DIALOG,mr.DIALOG],[pr.DIR,mr.DIR],[pr.DIV,mr.DIV],[pr.DL,mr.DL],[pr.DT,mr.DT],[pr.EM,mr.EM],[pr.EMBED,mr.EMBED],[pr.FIELDSET,mr.FIELDSET],[pr.FIGCAPTION,mr.FIGCAPTION],[pr.FIGURE,mr.FIGURE],[pr.FONT,mr.FONT],[pr.FOOTER,mr.FOOTER],[pr.FOREIGN_OBJECT,mr.FOREIGN_OBJECT],[pr.FORM,mr.FORM],[pr.FRAME,mr.FRAME],[pr.FRAMESET,mr.FRAMESET],[pr.H1,mr.H1],[pr.H2,mr.H2],[pr.H3,mr.H3],[pr.H4,mr.H4],[pr.H5,mr.H5],[pr.H6,mr.H6],[pr.HEAD,mr.HEAD],[pr.HEADER,mr.HEADER],[pr.HGROUP,mr.HGROUP],[pr.HR,mr.HR],[pr.HTML,mr.HTML],[pr.I,mr.I],[pr.IMG,mr.IMG],[pr.IMAGE,mr.IMAGE],[pr.INPUT,mr.INPUT],[pr.IFRAME,mr.IFRAME],[pr.KEYGEN,mr.KEYGEN],[pr.LABEL,mr.LABEL],[pr.LI,mr.LI],[pr.LINK,mr.LINK],[pr.LISTING,mr.LISTING],[pr.MAIN,mr.MAIN],[pr.MALIGNMARK,mr.MALIGNMARK],[pr.MARQUEE,mr.MARQUEE],[pr.MATH,mr.MATH],[pr.MENU,mr.MENU],[pr.META,mr.META],[pr.MGLYPH,mr.MGLYPH],[pr.MI,mr.MI],[pr.MO,mr.MO],[pr.MN,mr.MN],[pr.MS,mr.MS],[pr.MTEXT,mr.MTEXT],[pr.NAV,mr.NAV],[pr.NOBR,mr.NOBR],[pr.NOFRAMES,mr.NOFRAMES],[pr.NOEMBED,mr.NOEMBED],[pr.NOSCRIPT,mr.NOSCRIPT],[pr.OBJECT,mr.OBJECT],[pr.OL,mr.OL],[pr.OPTGROUP,mr.OPTGROUP],[pr.OPTION,mr.OPTION],[pr.P,mr.P],[pr.PARAM,mr.PARAM],[pr.PLAINTEXT,mr.PLAINTEXT],[pr.PRE,mr.PRE],[pr.RB,mr.RB],[pr.RP,mr.RP],[pr.RT,mr.RT],[pr.RTC,mr.RTC],[pr.RUBY,mr.RUBY],[pr.S,mr.S],[pr.SCRIPT,mr.SCRIPT],[pr.SECTION,mr.SECTION],[pr.SELECT,mr.SELECT],[pr.SOURCE,mr.SOURCE],[pr.SMALL,mr.SMALL],[pr.SPAN,mr.SPAN],[pr.STRIKE,mr.STRIKE],[pr.STRONG,mr.STRONG],[pr.STYLE,mr.STYLE],[pr.SUB,mr.SUB],[pr.SUMMARY,mr.SUMMARY],[pr.SUP,mr.SUP],[pr.TABLE,mr.TABLE],[pr.TBODY,mr.TBODY],[pr.TEMPLATE,mr.TEMPLATE],[pr.TEXTAREA,mr.TEXTAREA],[pr.TFOOT,mr.TFOOT],[pr.TD,mr.TD],[pr.TH,mr.TH],[pr.THEAD,mr.THEAD],[pr.TITLE,mr.TITLE],[pr.TR,mr.TR],[pr.TRACK,mr.TRACK],[pr.TT,mr.TT],[pr.U,mr.U],[pr.UL,mr.UL],[pr.SVG,mr.SVG],[pr.VAR,mr.VAR],[pr.WBR,mr.WBR],[pr.XMP,mr.XMP]]);function Ar(e){var t;return null!==(t=_r.get(e))&&void 0!==t?t:mr.UNKNOWN}const gr=mr,Cr={[ar.HTML]:new Set([gr.ADDRESS,gr.APPLET,gr.AREA,gr.ARTICLE,gr.ASIDE,gr.BASE,gr.BASEFONT,gr.BGSOUND,gr.BLOCKQUOTE,gr.BODY,gr.BR,gr.BUTTON,gr.CAPTION,gr.CENTER,gr.COL,gr.COLGROUP,gr.DD,gr.DETAILS,gr.DIR,gr.DIV,gr.DL,gr.DT,gr.EMBED,gr.FIELDSET,gr.FIGCAPTION,gr.FIGURE,gr.FOOTER,gr.FORM,gr.FRAME,gr.FRAMESET,gr.H1,gr.H2,gr.H3,gr.H4,gr.H5,gr.H6,gr.HEAD,gr.HEADER,gr.HGROUP,gr.HR,gr.HTML,gr.IFRAME,gr.IMG,gr.INPUT,gr.LI,gr.LINK,gr.LISTING,gr.MAIN,gr.MARQUEE,gr.MENU,gr.META,gr.NAV,gr.NOEMBED,gr.NOFRAMES,gr.NOSCRIPT,gr.OBJECT,gr.OL,gr.P,gr.PARAM,gr.PLAINTEXT,gr.PRE,gr.SCRIPT,gr.SECTION,gr.SELECT,gr.SOURCE,gr.STYLE,gr.SUMMARY,gr.TABLE,gr.TBODY,gr.TD,gr.TEMPLATE,gr.TEXTAREA,gr.TFOOT,gr.TH,gr.THEAD,gr.TITLE,gr.TR,gr.TRACK,gr.UL,gr.WBR,gr.XMP]),[ar.MATHML]:new Set([gr.MI,gr.MO,gr.MN,gr.MS,gr.MTEXT,gr.ANNOTATION_XML]),[ar.SVG]:new Set([gr.TITLE,gr.FOREIGN_OBJECT,gr.DESC]),[ar.XLINK]:new Set,[ar.XML]:new Set,[ar.XMLNS]:new Set};function br(e){return e===gr.H1||e===gr.H2||e===gr.H3||e===gr.H4||e===gr.H5||e===gr.H6}const kr=new Set([pr.STYLE,pr.SCRIPT,pr.XMP,pr.IFRAME,pr.NOEMBED,pr.NOFRAMES,pr.PLAINTEXT]);const Sr=new Map([[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]);var Nr,Ir;(Ir=Nr||(Nr={}))[Ir.DATA=0]="DATA",Ir[Ir.RCDATA=1]="RCDATA",Ir[Ir.RAWTEXT=2]="RAWTEXT",Ir[Ir.SCRIPT_DATA=3]="SCRIPT_DATA",Ir[Ir.PLAINTEXT=4]="PLAINTEXT",Ir[Ir.TAG_OPEN=5]="TAG_OPEN",Ir[Ir.END_TAG_OPEN=6]="END_TAG_OPEN",Ir[Ir.TAG_NAME=7]="TAG_NAME",Ir[Ir.RCDATA_LESS_THAN_SIGN=8]="RCDATA_LESS_THAN_SIGN",Ir[Ir.RCDATA_END_TAG_OPEN=9]="RCDATA_END_TAG_OPEN",Ir[Ir.RCDATA_END_TAG_NAME=10]="RCDATA_END_TAG_NAME",Ir[Ir.RAWTEXT_LESS_THAN_SIGN=11]="RAWTEXT_LESS_THAN_SIGN",Ir[Ir.RAWTEXT_END_TAG_OPEN=12]="RAWTEXT_END_TAG_OPEN",Ir[Ir.RAWTEXT_END_TAG_NAME=13]="RAWTEXT_END_TAG_NAME",Ir[Ir.SCRIPT_DATA_LESS_THAN_SIGN=14]="SCRIPT_DATA_LESS_THAN_SIGN",Ir[Ir.SCRIPT_DATA_END_TAG_OPEN=15]="SCRIPT_DATA_END_TAG_OPEN",Ir[Ir.SCRIPT_DATA_END_TAG_NAME=16]="SCRIPT_DATA_END_TAG_NAME",Ir[Ir.SCRIPT_DATA_ESCAPE_START=17]="SCRIPT_DATA_ESCAPE_START",Ir[Ir.SCRIPT_DATA_ESCAPE_START_DASH=18]="SCRIPT_DATA_ESCAPE_START_DASH",Ir[Ir.SCRIPT_DATA_ESCAPED=19]="SCRIPT_DATA_ESCAPED",Ir[Ir.SCRIPT_DATA_ESCAPED_DASH=20]="SCRIPT_DATA_ESCAPED_DASH",Ir[Ir.SCRIPT_DATA_ESCAPED_DASH_DASH=21]="SCRIPT_DATA_ESCAPED_DASH_DASH",Ir[Ir.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN=22]="SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN",Ir[Ir.SCRIPT_DATA_ESCAPED_END_TAG_OPEN=23]="SCRIPT_DATA_ESCAPED_END_TAG_OPEN",Ir[Ir.SCRIPT_DATA_ESCAPED_END_TAG_NAME=24]="SCRIPT_DATA_ESCAPED_END_TAG_NAME",Ir[Ir.SCRIPT_DATA_DOUBLE_ESCAPE_START=25]="SCRIPT_DATA_DOUBLE_ESCAPE_START",Ir[Ir.SCRIPT_DATA_DOUBLE_ESCAPED=26]="SCRIPT_DATA_DOUBLE_ESCAPED",Ir[Ir.SCRIPT_DATA_DOUBLE_ESCAPED_DASH=27]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH",Ir[Ir.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH=28]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH",Ir[Ir.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN=29]="SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN",Ir[Ir.SCRIPT_DATA_DOUBLE_ESCAPE_END=30]="SCRIPT_DATA_DOUBLE_ESCAPE_END",Ir[Ir.BEFORE_ATTRIBUTE_NAME=31]="BEFORE_ATTRIBUTE_NAME",Ir[Ir.ATTRIBUTE_NAME=32]="ATTRIBUTE_NAME",Ir[Ir.AFTER_ATTRIBUTE_NAME=33]="AFTER_ATTRIBUTE_NAME",Ir[Ir.BEFORE_ATTRIBUTE_VALUE=34]="BEFORE_ATTRIBUTE_VALUE",Ir[Ir.ATTRIBUTE_VALUE_DOUBLE_QUOTED=35]="ATTRIBUTE_VALUE_DOUBLE_QUOTED",Ir[Ir.ATTRIBUTE_VALUE_SINGLE_QUOTED=36]="ATTRIBUTE_VALUE_SINGLE_QUOTED",Ir[Ir.ATTRIBUTE_VALUE_UNQUOTED=37]="ATTRIBUTE_VALUE_UNQUOTED",Ir[Ir.AFTER_ATTRIBUTE_VALUE_QUOTED=38]="AFTER_ATTRIBUTE_VALUE_QUOTED",Ir[Ir.SELF_CLOSING_START_TAG=39]="SELF_CLOSING_START_TAG",Ir[Ir.BOGUS_COMMENT=40]="BOGUS_COMMENT",Ir[Ir.MARKUP_DECLARATION_OPEN=41]="MARKUP_DECLARATION_OPEN",Ir[Ir.COMMENT_START=42]="COMMENT_START",Ir[Ir.COMMENT_START_DASH=43]="COMMENT_START_DASH",Ir[Ir.COMMENT=44]="COMMENT",Ir[Ir.COMMENT_LESS_THAN_SIGN=45]="COMMENT_LESS_THAN_SIGN",Ir[Ir.COMMENT_LESS_THAN_SIGN_BANG=46]="COMMENT_LESS_THAN_SIGN_BANG",Ir[Ir.COMMENT_LESS_THAN_SIGN_BANG_DASH=47]="COMMENT_LESS_THAN_SIGN_BANG_DASH",Ir[Ir.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH=48]="COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH",Ir[Ir.COMMENT_END_DASH=49]="COMMENT_END_DASH",Ir[Ir.COMMENT_END=50]="COMMENT_END",Ir[Ir.COMMENT_END_BANG=51]="COMMENT_END_BANG",Ir[Ir.DOCTYPE=52]="DOCTYPE",Ir[Ir.BEFORE_DOCTYPE_NAME=53]="BEFORE_DOCTYPE_NAME",Ir[Ir.DOCTYPE_NAME=54]="DOCTYPE_NAME",Ir[Ir.AFTER_DOCTYPE_NAME=55]="AFTER_DOCTYPE_NAME",Ir[Ir.AFTER_DOCTYPE_PUBLIC_KEYWORD=56]="AFTER_DOCTYPE_PUBLIC_KEYWORD",Ir[Ir.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER=57]="BEFORE_DOCTYPE_PUBLIC_IDENTIFIER",Ir[Ir.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED=58]="DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED",Ir[Ir.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED=59]="DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED",Ir[Ir.AFTER_DOCTYPE_PUBLIC_IDENTIFIER=60]="AFTER_DOCTYPE_PUBLIC_IDENTIFIER",Ir[Ir.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS=61]="BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS",Ir[Ir.AFTER_DOCTYPE_SYSTEM_KEYWORD=62]="AFTER_DOCTYPE_SYSTEM_KEYWORD",Ir[Ir.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER=63]="BEFORE_DOCTYPE_SYSTEM_IDENTIFIER",Ir[Ir.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED=64]="DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED",Ir[Ir.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED=65]="DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED",Ir[Ir.AFTER_DOCTYPE_SYSTEM_IDENTIFIER=66]="AFTER_DOCTYPE_SYSTEM_IDENTIFIER",Ir[Ir.BOGUS_DOCTYPE=67]="BOGUS_DOCTYPE",Ir[Ir.CDATA_SECTION=68]="CDATA_SECTION",Ir[Ir.CDATA_SECTION_BRACKET=69]="CDATA_SECTION_BRACKET",Ir[Ir.CDATA_SECTION_END=70]="CDATA_SECTION_END",Ir[Ir.CHARACTER_REFERENCE=71]="CHARACTER_REFERENCE",Ir[Ir.NAMED_CHARACTER_REFERENCE=72]="NAMED_CHARACTER_REFERENCE",Ir[Ir.AMBIGUOUS_AMPERSAND=73]="AMBIGUOUS_AMPERSAND",Ir[Ir.NUMERIC_CHARACTER_REFERENCE=74]="NUMERIC_CHARACTER_REFERENCE",Ir[Ir.HEXADEMICAL_CHARACTER_REFERENCE_START=75]="HEXADEMICAL_CHARACTER_REFERENCE_START",Ir[Ir.HEXADEMICAL_CHARACTER_REFERENCE=76]="HEXADEMICAL_CHARACTER_REFERENCE",Ir[Ir.DECIMAL_CHARACTER_REFERENCE=77]="DECIMAL_CHARACTER_REFERENCE",Ir[Ir.NUMERIC_CHARACTER_REFERENCE_END=78]="NUMERIC_CHARACTER_REFERENCE_END";const Dr={DATA:Nr.DATA,RCDATA:Nr.RCDATA,RAWTEXT:Nr.RAWTEXT,SCRIPT_DATA:Nr.SCRIPT_DATA,PLAINTEXT:Nr.PLAINTEXT,CDATA_SECTION:Nr.CDATA_SECTION};function yr(e){return e>=js.DIGIT_0&&e<=js.DIGIT_9}function Or(e){return e>=js.LATIN_CAPITAL_A&&e<=js.LATIN_CAPITAL_Z}function Lr(e){return function(e){return e>=js.LATIN_SMALL_A&&e<=js.LATIN_SMALL_Z}(e)||Or(e)}function Rr(e){return Lr(e)||yr(e)}function Mr(e){return e>=js.LATIN_CAPITAL_A&&e<=js.LATIN_CAPITAL_F}function vr(e){return e>=js.LATIN_SMALL_A&&e<=js.LATIN_SMALL_F}function xr(e){return e+32}function wr(e){return e===js.SPACE||e===js.LINE_FEED||e===js.TABULATION||e===js.FORM_FEED}function Fr(e){return wr(e)||e===js.SOLIDUS||e===js.GREATER_THAN_SIGN}let Pr=class{constructor(e,t){this.options=e,this.handler=t,this.paused=!1,this.inLoop=!1,this.inForeignNode=!1,this.lastStartTagName="",this.active=!1,this.state=Nr.DATA,this.returnState=Nr.DATA,this.charRefCode=-1,this.consumedAfterSnapshot=-1,this.currentCharacterToken=null,this.currentToken=null,this.currentAttr={name:"",value:""},this.preprocessor=new rr(t),this.currentLocation=this.getCurrentLocation(-1)}_err(e){var t,n;null===(n=(t=this.handler).onParseError)||void 0===n||n.call(t,this.preprocessor.getError(e))}getCurrentLocation(e){return this.options.sourceCodeLocationInfo?{startLine:this.preprocessor.line,startCol:this.preprocessor.col-e,startOffset:this.preprocessor.offset-e,endLine:-1,endCol:-1,endOffset:-1}:null}_runParsingLoop(){if(!this.inLoop){for(this.inLoop=!0;this.active&&!this.paused;){this.consumedAfterSnapshot=0;const e=this._consume();this._ensureHibernation()||this._callState(e)}this.inLoop=!1}}pause(){this.paused=!0}resume(e){if(!this.paused)throw new Error("Parser was already resumed");this.paused=!1,this.inLoop||(this._runParsingLoop(),this.paused||null==e||e())}write(e,t,n){this.active=!0,this.preprocessor.write(e,t),this._runParsingLoop(),this.paused||null==n||n()}insertHtmlAtCurrentPos(e){this.active=!0,this.preprocessor.insertHtmlAtCurrentPos(e),this._runParsingLoop()}_ensureHibernation(){return!!this.preprocessor.endOfChunkHit&&(this._unconsume(this.consumedAfterSnapshot),this.active=!1,!0)}_consume(){return this.consumedAfterSnapshot++,this.preprocessor.advance()}_unconsume(e){this.consumedAfterSnapshot-=e,this.preprocessor.retreat(e)}_reconsumeInState(e,t){this.state=e,this._callState(t)}_advanceBy(e){this.consumedAfterSnapshot+=e;for(let t=0;t<e;t++)this.preprocessor.advance()}_consumeSequenceIfMatch(e,t){return!!this.preprocessor.startsWith(e,t)&&(this._advanceBy(e.length-1),!0)}_createStartTagToken(){this.currentToken={type:ir.START_TAG,tagName:"",tagID:mr.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(1)}}_createEndTagToken(){this.currentToken={type:ir.END_TAG,tagName:"",tagID:mr.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(2)}}_createCommentToken(e){this.currentToken={type:ir.COMMENT,data:"",location:this.getCurrentLocation(e)}}_createDoctypeToken(e){this.currentToken={type:ir.DOCTYPE,name:e,forceQuirks:!1,publicId:null,systemId:null,location:this.currentLocation}}_createCharacterToken(e,t){this.currentCharacterToken={type:e,chars:t,location:this.currentLocation}}_createAttr(e){this.currentAttr={name:e,value:""},this.currentLocation=this.getCurrentLocation(0)}_leaveAttrName(){var e,t;const n=this.currentToken;if(null===Tr(n,this.currentAttr.name)){if(n.attrs.push(this.currentAttr),n.location&&this.currentLocation){(null!==(e=(t=n.location).attrs)&&void 0!==e?e:t.attrs=Object.create(null))[this.currentAttr.name]=this.currentLocation,this._leaveAttrValue()}}else this._err(nr.duplicateAttribute)}_leaveAttrValue(){this.currentLocation&&(this.currentLocation.endLine=this.preprocessor.line,this.currentLocation.endCol=this.preprocessor.col,this.currentLocation.endOffset=this.preprocessor.offset)}prepareToken(e){this._emitCurrentCharacterToken(e.location),this.currentToken=null,e.location&&(e.location.endLine=this.preprocessor.line,e.location.endCol=this.preprocessor.col+1,e.location.endOffset=this.preprocessor.offset+1),this.currentLocation=this.getCurrentLocation(-1)}emitCurrentTagToken(){const e=this.currentToken;this.prepareToken(e),e.tagID=Ar(e.tagName),e.type===ir.START_TAG?(this.lastStartTagName=e.tagName,this.handler.onStartTag(e)):(e.attrs.length>0&&this._err(nr.endTagWithAttributes),e.selfClosing&&this._err(nr.endTagWithTrailingSolidus),this.handler.onEndTag(e)),this.preprocessor.dropParsedChunk()}emitCurrentComment(e){this.prepareToken(e),this.handler.onComment(e),this.preprocessor.dropParsedChunk()}emitCurrentDoctype(e){this.prepareToken(e),this.handler.onDoctype(e),this.preprocessor.dropParsedChunk()}_emitCurrentCharacterToken(e){if(this.currentCharacterToken){switch(e&&this.currentCharacterToken.location&&(this.currentCharacterToken.location.endLine=e.startLine,this.currentCharacterToken.location.endCol=e.startCol,this.currentCharacterToken.location.endOffset=e.startOffset),this.currentCharacterToken.type){case ir.CHARACTER:this.handler.onCharacter(this.currentCharacterToken);break;case ir.NULL_CHARACTER:this.handler.onNullCharacter(this.currentCharacterToken);break;case ir.WHITESPACE_CHARACTER:this.handler.onWhitespaceCharacter(this.currentCharacterToken)}this.currentCharacterToken=null}}_emitEOFToken(){const e=this.getCurrentLocation(0);e&&(e.endLine=e.startLine,e.endCol=e.startCol,e.endOffset=e.startOffset),this._emitCurrentCharacterToken(e),this.handler.onEof({type:ir.EOF,location:e}),this.active=!1}_appendCharToCurrentCharacterToken(e,t){if(this.currentCharacterToken){if(this.currentCharacterToken.type===e)return void(this.currentCharacterToken.chars+=t);this.currentLocation=this.getCurrentLocation(0),this._emitCurrentCharacterToken(this.currentLocation),this.preprocessor.dropParsedChunk()}this._createCharacterToken(e,t)}_emitCodePoint(e){const t=wr(e)?ir.WHITESPACE_CHARACTER:e===js.NULL?ir.NULL_CHARACTER:ir.CHARACTER;this._appendCharToCurrentCharacterToken(t,String.fromCodePoint(e))}_emitChars(e){this._appendCharToCurrentCharacterToken(ir.CHARACTER,e)}_matchNamedCharacterReference(e){let t=null,n=0,s=!1;for(let i=0,o=te[0];i>=0&&(i=_e(te,o,i+1,e),!(i<0));e=this._consume()){n+=1,o=te[i];const a=o&ce.VALUE_LENGTH;if(a){const o=(a>>14)-1;if(e!==js.SEMICOLON&&this._isCharacterReferenceInAttribute()&&((r=this.preprocessor.peek(1))===js.EQUALS_SIGN||Rr(r))?(t=[js.AMPERSAND],i+=o):(t=0===o?[te[i]&~ce.VALUE_LENGTH]:1===o?[te[++i]]:[te[++i],te[++i]],n=0,s=e!==js.SEMICOLON),0===o){this._consume();break}}}var r;return this._unconsume(n),s&&!this.preprocessor.endOfChunkHit&&this._err(nr.missingSemicolonAfterCharacterReference),this._unconsume(1),t}_isCharacterReferenceInAttribute(){return this.returnState===Nr.ATTRIBUTE_VALUE_DOUBLE_QUOTED||this.returnState===Nr.ATTRIBUTE_VALUE_SINGLE_QUOTED||this.returnState===Nr.ATTRIBUTE_VALUE_UNQUOTED}_flushCodePointConsumedAsCharacterReference(e){this._isCharacterReferenceInAttribute()?this.currentAttr.value+=String.fromCodePoint(e):this._emitCodePoint(e)}_callState(e){switch(this.state){case Nr.DATA:this._stateData(e);break;case Nr.RCDATA:this._stateRcdata(e);break;case Nr.RAWTEXT:this._stateRawtext(e);break;case Nr.SCRIPT_DATA:this._stateScriptData(e);break;case Nr.PLAINTEXT:this._statePlaintext(e);break;case Nr.TAG_OPEN:this._stateTagOpen(e);break;case Nr.END_TAG_OPEN:this._stateEndTagOpen(e);break;case Nr.TAG_NAME:this._stateTagName(e);break;case Nr.RCDATA_LESS_THAN_SIGN:this._stateRcdataLessThanSign(e);break;case Nr.RCDATA_END_TAG_OPEN:this._stateRcdataEndTagOpen(e);break;case Nr.RCDATA_END_TAG_NAME:this._stateRcdataEndTagName(e);break;case Nr.RAWTEXT_LESS_THAN_SIGN:this._stateRawtextLessThanSign(e);break;case Nr.RAWTEXT_END_TAG_OPEN:this._stateRawtextEndTagOpen(e);break;case Nr.RAWTEXT_END_TAG_NAME:this._stateRawtextEndTagName(e);break;case Nr.SCRIPT_DATA_LESS_THAN_SIGN:this._stateScriptDataLessThanSign(e);break;case Nr.SCRIPT_DATA_END_TAG_OPEN:this._stateScriptDataEndTagOpen(e);break;case Nr.SCRIPT_DATA_END_TAG_NAME:this._stateScriptDataEndTagName(e);break;case Nr.SCRIPT_DATA_ESCAPE_START:this._stateScriptDataEscapeStart(e);break;case Nr.SCRIPT_DATA_ESCAPE_START_DASH:this._stateScriptDataEscapeStartDash(e);break;case Nr.SCRIPT_DATA_ESCAPED:this._stateScriptDataEscaped(e);break;case Nr.SCRIPT_DATA_ESCAPED_DASH:this._stateScriptDataEscapedDash(e);break;case Nr.SCRIPT_DATA_ESCAPED_DASH_DASH:this._stateScriptDataEscapedDashDash(e);break;case Nr.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataEscapedLessThanSign(e);break;case Nr.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:this._stateScriptDataEscapedEndTagOpen(e);break;case Nr.SCRIPT_DATA_ESCAPED_END_TAG_NAME:this._stateScriptDataEscapedEndTagName(e);break;case Nr.SCRIPT_DATA_DOUBLE_ESCAPE_START:this._stateScriptDataDoubleEscapeStart(e);break;case Nr.SCRIPT_DATA_DOUBLE_ESCAPED:this._stateScriptDataDoubleEscaped(e);break;case Nr.SCRIPT_DATA_DOUBLE_ESCAPED_DASH:this._stateScriptDataDoubleEscapedDash(e);break;case Nr.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH:this._stateScriptDataDoubleEscapedDashDash(e);break;case Nr.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataDoubleEscapedLessThanSign(e);break;case Nr.SCRIPT_DATA_DOUBLE_ESCAPE_END:this._stateScriptDataDoubleEscapeEnd(e);break;case Nr.BEFORE_ATTRIBUTE_NAME:this._stateBeforeAttributeName(e);break;case Nr.ATTRIBUTE_NAME:this._stateAttributeName(e);break;case Nr.AFTER_ATTRIBUTE_NAME:this._stateAfterAttributeName(e);break;case Nr.BEFORE_ATTRIBUTE_VALUE:this._stateBeforeAttributeValue(e);break;case Nr.ATTRIBUTE_VALUE_DOUBLE_QUOTED:this._stateAttributeValueDoubleQuoted(e);break;case Nr.ATTRIBUTE_VALUE_SINGLE_QUOTED:this._stateAttributeValueSingleQuoted(e);break;case Nr.ATTRIBUTE_VALUE_UNQUOTED:this._stateAttributeValueUnquoted(e);break;case Nr.AFTER_ATTRIBUTE_VALUE_QUOTED:this._stateAfterAttributeValueQuoted(e);break;case Nr.SELF_CLOSING_START_TAG:this._stateSelfClosingStartTag(e);break;case Nr.BOGUS_COMMENT:this._stateBogusComment(e);break;case Nr.MARKUP_DECLARATION_OPEN:this._stateMarkupDeclarationOpen(e);break;case Nr.COMMENT_START:this._stateCommentStart(e);break;case Nr.COMMENT_START_DASH:this._stateCommentStartDash(e);break;case Nr.COMMENT:this._stateComment(e);break;case Nr.COMMENT_LESS_THAN_SIGN:this._stateCommentLessThanSign(e);break;case Nr.COMMENT_LESS_THAN_SIGN_BANG:this._stateCommentLessThanSignBang(e);break;case Nr.COMMENT_LESS_THAN_SIGN_BANG_DASH:this._stateCommentLessThanSignBangDash(e);break;case Nr.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:this._stateCommentLessThanSignBangDashDash(e);break;case Nr.COMMENT_END_DASH:this._stateCommentEndDash(e);break;case Nr.COMMENT_END:this._stateCommentEnd(e);break;case Nr.COMMENT_END_BANG:this._stateCommentEndBang(e);break;case Nr.DOCTYPE:this._stateDoctype(e);break;case Nr.BEFORE_DOCTYPE_NAME:this._stateBeforeDoctypeName(e);break;case Nr.DOCTYPE_NAME:this._stateDoctypeName(e);break;case Nr.AFTER_DOCTYPE_NAME:this._stateAfterDoctypeName(e);break;case Nr.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._stateAfterDoctypePublicKeyword(e);break;case Nr.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER:this._stateBeforeDoctypePublicIdentifier(e);break;case Nr.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypePublicIdentifierDoubleQuoted(e);break;case Nr.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypePublicIdentifierSingleQuoted(e);break;case Nr.AFTER_DOCTYPE_PUBLIC_IDENTIFIER:this._stateAfterDoctypePublicIdentifier(e);break;case Nr.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS:this._stateBetweenDoctypePublicAndSystemIdentifiers(e);break;case Nr.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._stateAfterDoctypeSystemKeyword(e);break;case Nr.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER:this._stateBeforeDoctypeSystemIdentifier(e);break;case Nr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypeSystemIdentifierDoubleQuoted(e);break;case Nr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypeSystemIdentifierSingleQuoted(e);break;case Nr.AFTER_DOCTYPE_SYSTEM_IDENTIFIER:this._stateAfterDoctypeSystemIdentifier(e);break;case Nr.BOGUS_DOCTYPE:this._stateBogusDoctype(e);break;case Nr.CDATA_SECTION:this._stateCdataSection(e);break;case Nr.CDATA_SECTION_BRACKET:this._stateCdataSectionBracket(e);break;case Nr.CDATA_SECTION_END:this._stateCdataSectionEnd(e);break;case Nr.CHARACTER_REFERENCE:this._stateCharacterReference(e);break;case Nr.NAMED_CHARACTER_REFERENCE:this._stateNamedCharacterReference(e);break;case Nr.AMBIGUOUS_AMPERSAND:this._stateAmbiguousAmpersand(e);break;case Nr.NUMERIC_CHARACTER_REFERENCE:this._stateNumericCharacterReference(e);break;case Nr.HEXADEMICAL_CHARACTER_REFERENCE_START:this._stateHexademicalCharacterReferenceStart(e);break;case Nr.HEXADEMICAL_CHARACTER_REFERENCE:this._stateHexademicalCharacterReference(e);break;case Nr.DECIMAL_CHARACTER_REFERENCE:this._stateDecimalCharacterReference(e);break;case Nr.NUMERIC_CHARACTER_REFERENCE_END:this._stateNumericCharacterReferenceEnd(e);break;default:throw new Error("Unknown state")}}_stateData(e){switch(e){case js.LESS_THAN_SIGN:this.state=Nr.TAG_OPEN;break;case js.AMPERSAND:this.returnState=Nr.DATA,this.state=Nr.CHARACTER_REFERENCE;break;case js.NULL:this._err(nr.unexpectedNullCharacter),this._emitCodePoint(e);break;case js.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRcdata(e){switch(e){case js.AMPERSAND:this.returnState=Nr.RCDATA,this.state=Nr.CHARACTER_REFERENCE;break;case js.LESS_THAN_SIGN:this.state=Nr.RCDATA_LESS_THAN_SIGN;break;case js.NULL:this._err(nr.unexpectedNullCharacter),this._emitChars(Ys);break;case js.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRawtext(e){switch(e){case js.LESS_THAN_SIGN:this.state=Nr.RAWTEXT_LESS_THAN_SIGN;break;case js.NULL:this._err(nr.unexpectedNullCharacter),this._emitChars(Ys);break;case js.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptData(e){switch(e){case js.LESS_THAN_SIGN:this.state=Nr.SCRIPT_DATA_LESS_THAN_SIGN;break;case js.NULL:this._err(nr.unexpectedNullCharacter),this._emitChars(Ys);break;case js.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_statePlaintext(e){switch(e){case js.NULL:this._err(nr.unexpectedNullCharacter),this._emitChars(Ys);break;case js.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateTagOpen(e){if(Lr(e))this._createStartTagToken(),this.state=Nr.TAG_NAME,this._stateTagName(e);else switch(e){case js.EXCLAMATION_MARK:this.state=Nr.MARKUP_DECLARATION_OPEN;break;case js.SOLIDUS:this.state=Nr.END_TAG_OPEN;break;case js.QUESTION_MARK:this._err(nr.unexpectedQuestionMarkInsteadOfTagName),this._createCommentToken(1),this.state=Nr.BOGUS_COMMENT,this._stateBogusComment(e);break;case js.EOF:this._err(nr.eofBeforeTagName),this._emitChars("<"),this._emitEOFToken();break;default:this._err(nr.invalidFirstCharacterOfTagName),this._emitChars("<"),this.state=Nr.DATA,this._stateData(e)}}_stateEndTagOpen(e){if(Lr(e))this._createEndTagToken(),this.state=Nr.TAG_NAME,this._stateTagName(e);else switch(e){case js.GREATER_THAN_SIGN:this._err(nr.missingEndTagName),this.state=Nr.DATA;break;case js.EOF:this._err(nr.eofBeforeTagName),this._emitChars("</"),this._emitEOFToken();break;default:this._err(nr.invalidFirstCharacterOfTagName),this._createCommentToken(2),this.state=Nr.BOGUS_COMMENT,this._stateBogusComment(e)}}_stateTagName(e){const t=this.currentToken;switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:this.state=Nr.BEFORE_ATTRIBUTE_NAME;break;case js.SOLIDUS:this.state=Nr.SELF_CLOSING_START_TAG;break;case js.GREATER_THAN_SIGN:this.state=Nr.DATA,this.emitCurrentTagToken();break;case js.NULL:this._err(nr.unexpectedNullCharacter),t.tagName+=Ys;break;case js.EOF:this._err(nr.eofInTag),this._emitEOFToken();break;default:t.tagName+=String.fromCodePoint(Or(e)?xr(e):e)}}_stateRcdataLessThanSign(e){e===js.SOLIDUS?this.state=Nr.RCDATA_END_TAG_OPEN:(this._emitChars("<"),this.state=Nr.RCDATA,this._stateRcdata(e))}_stateRcdataEndTagOpen(e){Lr(e)?(this.state=Nr.RCDATA_END_TAG_NAME,this._stateRcdataEndTagName(e)):(this._emitChars("</"),this.state=Nr.RCDATA,this._stateRcdata(e))}handleSpecialEndTag(e){if(!this.preprocessor.startsWith(this.lastStartTagName,!1))return!this._ensureHibernation();this._createEndTagToken();this.currentToken.tagName=this.lastStartTagName;switch(this.preprocessor.peek(this.lastStartTagName.length)){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:return this._advanceBy(this.lastStartTagName.length),this.state=Nr.BEFORE_ATTRIBUTE_NAME,!1;case js.SOLIDUS:return this._advanceBy(this.lastStartTagName.length),this.state=Nr.SELF_CLOSING_START_TAG,!1;case js.GREATER_THAN_SIGN:return this._advanceBy(this.lastStartTagName.length),this.emitCurrentTagToken(),this.state=Nr.DATA,!1;default:return!this._ensureHibernation()}}_stateRcdataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Nr.RCDATA,this._stateRcdata(e))}_stateRawtextLessThanSign(e){e===js.SOLIDUS?this.state=Nr.RAWTEXT_END_TAG_OPEN:(this._emitChars("<"),this.state=Nr.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagOpen(e){Lr(e)?(this.state=Nr.RAWTEXT_END_TAG_NAME,this._stateRawtextEndTagName(e)):(this._emitChars("</"),this.state=Nr.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Nr.RAWTEXT,this._stateRawtext(e))}_stateScriptDataLessThanSign(e){switch(e){case js.SOLIDUS:this.state=Nr.SCRIPT_DATA_END_TAG_OPEN;break;case js.EXCLAMATION_MARK:this.state=Nr.SCRIPT_DATA_ESCAPE_START,this._emitChars("<!");break;default:this._emitChars("<"),this.state=Nr.SCRIPT_DATA,this._stateScriptData(e)}}_stateScriptDataEndTagOpen(e){Lr(e)?(this.state=Nr.SCRIPT_DATA_END_TAG_NAME,this._stateScriptDataEndTagName(e)):(this._emitChars("</"),this.state=Nr.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Nr.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStart(e){e===js.HYPHEN_MINUS?(this.state=Nr.SCRIPT_DATA_ESCAPE_START_DASH,this._emitChars("-")):(this.state=Nr.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStartDash(e){e===js.HYPHEN_MINUS?(this.state=Nr.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-")):(this.state=Nr.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscaped(e){switch(e){case js.HYPHEN_MINUS:this.state=Nr.SCRIPT_DATA_ESCAPED_DASH,this._emitChars("-");break;case js.LESS_THAN_SIGN:this.state=Nr.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case js.NULL:this._err(nr.unexpectedNullCharacter),this._emitChars(Ys);break;case js.EOF:this._err(nr.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataEscapedDash(e){switch(e){case js.HYPHEN_MINUS:this.state=Nr.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-");break;case js.LESS_THAN_SIGN:this.state=Nr.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case js.NULL:this._err(nr.unexpectedNullCharacter),this.state=Nr.SCRIPT_DATA_ESCAPED,this._emitChars(Ys);break;case js.EOF:this._err(nr.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Nr.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedDashDash(e){switch(e){case js.HYPHEN_MINUS:this._emitChars("-");break;case js.LESS_THAN_SIGN:this.state=Nr.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case js.GREATER_THAN_SIGN:this.state=Nr.SCRIPT_DATA,this._emitChars(">");break;case js.NULL:this._err(nr.unexpectedNullCharacter),this.state=Nr.SCRIPT_DATA_ESCAPED,this._emitChars(Ys);break;case js.EOF:this._err(nr.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Nr.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedLessThanSign(e){e===js.SOLIDUS?this.state=Nr.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:Lr(e)?(this._emitChars("<"),this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPE_START,this._stateScriptDataDoubleEscapeStart(e)):(this._emitChars("<"),this.state=Nr.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagOpen(e){Lr(e)?(this.state=Nr.SCRIPT_DATA_ESCAPED_END_TAG_NAME,this._stateScriptDataEscapedEndTagName(e)):(this._emitChars("</"),this.state=Nr.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Nr.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscapeStart(e){if(this.preprocessor.startsWith(Ws,!1)&&Fr(this.preprocessor.peek(Ws.length))){this._emitCodePoint(e);for(let e=0;e<Ws.length;e++)this._emitCodePoint(this._consume());this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED}else this._ensureHibernation()||(this.state=Nr.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscaped(e){switch(e){case js.HYPHEN_MINUS:this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED_DASH,this._emitChars("-");break;case js.LESS_THAN_SIGN:this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case js.NULL:this._err(nr.unexpectedNullCharacter),this._emitChars(Ys);break;case js.EOF:this._err(nr.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDash(e){switch(e){case js.HYPHEN_MINUS:this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH,this._emitChars("-");break;case js.LESS_THAN_SIGN:this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case js.NULL:this._err(nr.unexpectedNullCharacter),this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars(Ys);break;case js.EOF:this._err(nr.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDashDash(e){switch(e){case js.HYPHEN_MINUS:this._emitChars("-");break;case js.LESS_THAN_SIGN:this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case js.GREATER_THAN_SIGN:this.state=Nr.SCRIPT_DATA,this._emitChars(">");break;case js.NULL:this._err(nr.unexpectedNullCharacter),this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars(Ys);break;case js.EOF:this._err(nr.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedLessThanSign(e){e===js.SOLIDUS?(this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPE_END,this._emitChars("/")):(this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateScriptDataDoubleEscapeEnd(e){if(this.preprocessor.startsWith(Ws,!1)&&Fr(this.preprocessor.peek(Ws.length))){this._emitCodePoint(e);for(let e=0;e<Ws.length;e++)this._emitCodePoint(this._consume());this.state=Nr.SCRIPT_DATA_ESCAPED}else this._ensureHibernation()||(this.state=Nr.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateBeforeAttributeName(e){switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:break;case js.SOLIDUS:case js.GREATER_THAN_SIGN:case js.EOF:this.state=Nr.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case js.EQUALS_SIGN:this._err(nr.unexpectedEqualsSignBeforeAttributeName),this._createAttr("="),this.state=Nr.ATTRIBUTE_NAME;break;default:this._createAttr(""),this.state=Nr.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateAttributeName(e){switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:case js.SOLIDUS:case js.GREATER_THAN_SIGN:case js.EOF:this._leaveAttrName(),this.state=Nr.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case js.EQUALS_SIGN:this._leaveAttrName(),this.state=Nr.BEFORE_ATTRIBUTE_VALUE;break;case js.QUOTATION_MARK:case js.APOSTROPHE:case js.LESS_THAN_SIGN:this._err(nr.unexpectedCharacterInAttributeName),this.currentAttr.name+=String.fromCodePoint(e);break;case js.NULL:this._err(nr.unexpectedNullCharacter),this.currentAttr.name+=Ys;break;default:this.currentAttr.name+=String.fromCodePoint(Or(e)?xr(e):e)}}_stateAfterAttributeName(e){switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:break;case js.SOLIDUS:this.state=Nr.SELF_CLOSING_START_TAG;break;case js.EQUALS_SIGN:this.state=Nr.BEFORE_ATTRIBUTE_VALUE;break;case js.GREATER_THAN_SIGN:this.state=Nr.DATA,this.emitCurrentTagToken();break;case js.EOF:this._err(nr.eofInTag),this._emitEOFToken();break;default:this._createAttr(""),this.state=Nr.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateBeforeAttributeValue(e){switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:break;case js.QUOTATION_MARK:this.state=Nr.ATTRIBUTE_VALUE_DOUBLE_QUOTED;break;case js.APOSTROPHE:this.state=Nr.ATTRIBUTE_VALUE_SINGLE_QUOTED;break;case js.GREATER_THAN_SIGN:this._err(nr.missingAttributeValue),this.state=Nr.DATA,this.emitCurrentTagToken();break;default:this.state=Nr.ATTRIBUTE_VALUE_UNQUOTED,this._stateAttributeValueUnquoted(e)}}_stateAttributeValueDoubleQuoted(e){switch(e){case js.QUOTATION_MARK:this.state=Nr.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case js.AMPERSAND:this.returnState=Nr.ATTRIBUTE_VALUE_DOUBLE_QUOTED,this.state=Nr.CHARACTER_REFERENCE;break;case js.NULL:this._err(nr.unexpectedNullCharacter),this.currentAttr.value+=Ys;break;case js.EOF:this._err(nr.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueSingleQuoted(e){switch(e){case js.APOSTROPHE:this.state=Nr.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case js.AMPERSAND:this.returnState=Nr.ATTRIBUTE_VALUE_SINGLE_QUOTED,this.state=Nr.CHARACTER_REFERENCE;break;case js.NULL:this._err(nr.unexpectedNullCharacter),this.currentAttr.value+=Ys;break;case js.EOF:this._err(nr.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueUnquoted(e){switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:this._leaveAttrValue(),this.state=Nr.BEFORE_ATTRIBUTE_NAME;break;case js.AMPERSAND:this.returnState=Nr.ATTRIBUTE_VALUE_UNQUOTED,this.state=Nr.CHARACTER_REFERENCE;break;case js.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=Nr.DATA,this.emitCurrentTagToken();break;case js.NULL:this._err(nr.unexpectedNullCharacter),this.currentAttr.value+=Ys;break;case js.QUOTATION_MARK:case js.APOSTROPHE:case js.LESS_THAN_SIGN:case js.EQUALS_SIGN:case js.GRAVE_ACCENT:this._err(nr.unexpectedCharacterInUnquotedAttributeValue),this.currentAttr.value+=String.fromCodePoint(e);break;case js.EOF:this._err(nr.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAfterAttributeValueQuoted(e){switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:this._leaveAttrValue(),this.state=Nr.BEFORE_ATTRIBUTE_NAME;break;case js.SOLIDUS:this._leaveAttrValue(),this.state=Nr.SELF_CLOSING_START_TAG;break;case js.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=Nr.DATA,this.emitCurrentTagToken();break;case js.EOF:this._err(nr.eofInTag),this._emitEOFToken();break;default:this._err(nr.missingWhitespaceBetweenAttributes),this.state=Nr.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateSelfClosingStartTag(e){switch(e){case js.GREATER_THAN_SIGN:this.currentToken.selfClosing=!0,this.state=Nr.DATA,this.emitCurrentTagToken();break;case js.EOF:this._err(nr.eofInTag),this._emitEOFToken();break;default:this._err(nr.unexpectedSolidusInTag),this.state=Nr.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateBogusComment(e){const t=this.currentToken;switch(e){case js.GREATER_THAN_SIGN:this.state=Nr.DATA,this.emitCurrentComment(t);break;case js.EOF:this.emitCurrentComment(t),this._emitEOFToken();break;case js.NULL:this._err(nr.unexpectedNullCharacter),t.data+=Ys;break;default:t.data+=String.fromCodePoint(e)}}_stateMarkupDeclarationOpen(e){this._consumeSequenceIfMatch(Vs,!0)?(this._createCommentToken(Vs.length+1),this.state=Nr.COMMENT_START):this._consumeSequenceIfMatch(Qs,!1)?(this.currentLocation=this.getCurrentLocation(Qs.length+1),this.state=Nr.DOCTYPE):this._consumeSequenceIfMatch(zs,!0)?this.inForeignNode?this.state=Nr.CDATA_SECTION:(this._err(nr.cdataInHtmlContent),this._createCommentToken(zs.length+1),this.currentToken.data="[CDATA[",this.state=Nr.BOGUS_COMMENT):this._ensureHibernation()||(this._err(nr.incorrectlyOpenedComment),this._createCommentToken(2),this.state=Nr.BOGUS_COMMENT,this._stateBogusComment(e))}_stateCommentStart(e){switch(e){case js.HYPHEN_MINUS:this.state=Nr.COMMENT_START_DASH;break;case js.GREATER_THAN_SIGN:{this._err(nr.abruptClosingOfEmptyComment),this.state=Nr.DATA;const e=this.currentToken;this.emitCurrentComment(e);break}default:this.state=Nr.COMMENT,this._stateComment(e)}}_stateCommentStartDash(e){const t=this.currentToken;switch(e){case js.HYPHEN_MINUS:this.state=Nr.COMMENT_END;break;case js.GREATER_THAN_SIGN:this._err(nr.abruptClosingOfEmptyComment),this.state=Nr.DATA,this.emitCurrentComment(t);break;case js.EOF:this._err(nr.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=Nr.COMMENT,this._stateComment(e)}}_stateComment(e){const t=this.currentToken;switch(e){case js.HYPHEN_MINUS:this.state=Nr.COMMENT_END_DASH;break;case js.LESS_THAN_SIGN:t.data+="<",this.state=Nr.COMMENT_LESS_THAN_SIGN;break;case js.NULL:this._err(nr.unexpectedNullCharacter),t.data+=Ys;break;case js.EOF:this._err(nr.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+=String.fromCodePoint(e)}}_stateCommentLessThanSign(e){const t=this.currentToken;switch(e){case js.EXCLAMATION_MARK:t.data+="!",this.state=Nr.COMMENT_LESS_THAN_SIGN_BANG;break;case js.LESS_THAN_SIGN:t.data+="<";break;default:this.state=Nr.COMMENT,this._stateComment(e)}}_stateCommentLessThanSignBang(e){e===js.HYPHEN_MINUS?this.state=Nr.COMMENT_LESS_THAN_SIGN_BANG_DASH:(this.state=Nr.COMMENT,this._stateComment(e))}_stateCommentLessThanSignBangDash(e){e===js.HYPHEN_MINUS?this.state=Nr.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:(this.state=Nr.COMMENT_END_DASH,this._stateCommentEndDash(e))}_stateCommentLessThanSignBangDashDash(e){e!==js.GREATER_THAN_SIGN&&e!==js.EOF&&this._err(nr.nestedComment),this.state=Nr.COMMENT_END,this._stateCommentEnd(e)}_stateCommentEndDash(e){const t=this.currentToken;switch(e){case js.HYPHEN_MINUS:this.state=Nr.COMMENT_END;break;case js.EOF:this._err(nr.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=Nr.COMMENT,this._stateComment(e)}}_stateCommentEnd(e){const t=this.currentToken;switch(e){case js.GREATER_THAN_SIGN:this.state=Nr.DATA,this.emitCurrentComment(t);break;case js.EXCLAMATION_MARK:this.state=Nr.COMMENT_END_BANG;break;case js.HYPHEN_MINUS:t.data+="-";break;case js.EOF:this._err(nr.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--",this.state=Nr.COMMENT,this._stateComment(e)}}_stateCommentEndBang(e){const t=this.currentToken;switch(e){case js.HYPHEN_MINUS:t.data+="--!",this.state=Nr.COMMENT_END_DASH;break;case js.GREATER_THAN_SIGN:this._err(nr.incorrectlyClosedComment),this.state=Nr.DATA,this.emitCurrentComment(t);break;case js.EOF:this._err(nr.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--!",this.state=Nr.COMMENT,this._stateComment(e)}}_stateDoctype(e){switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:this.state=Nr.BEFORE_DOCTYPE_NAME;break;case js.GREATER_THAN_SIGN:this.state=Nr.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e);break;case js.EOF:{this._err(nr.eofInDoctype),this._createDoctypeToken(null);const e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._err(nr.missingWhitespaceBeforeDoctypeName),this.state=Nr.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e)}}_stateBeforeDoctypeName(e){if(Or(e))this._createDoctypeToken(String.fromCharCode(xr(e))),this.state=Nr.DOCTYPE_NAME;else switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:break;case js.NULL:this._err(nr.unexpectedNullCharacter),this._createDoctypeToken(Ys),this.state=Nr.DOCTYPE_NAME;break;case js.GREATER_THAN_SIGN:{this._err(nr.missingDoctypeName),this._createDoctypeToken(null);const e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this.state=Nr.DATA;break}case js.EOF:{this._err(nr.eofInDoctype),this._createDoctypeToken(null);const e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._createDoctypeToken(String.fromCodePoint(e)),this.state=Nr.DOCTYPE_NAME}}_stateDoctypeName(e){const t=this.currentToken;switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:this.state=Nr.AFTER_DOCTYPE_NAME;break;case js.GREATER_THAN_SIGN:this.state=Nr.DATA,this.emitCurrentDoctype(t);break;case js.NULL:this._err(nr.unexpectedNullCharacter),t.name+=Ys;break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.name+=String.fromCodePoint(Or(e)?xr(e):e)}}_stateAfterDoctypeName(e){const t=this.currentToken;switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:break;case js.GREATER_THAN_SIGN:this.state=Nr.DATA,this.emitCurrentDoctype(t);break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._consumeSequenceIfMatch(Xs,!1)?this.state=Nr.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._consumeSequenceIfMatch(Zs,!1)?this.state=Nr.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._ensureHibernation()||(this._err(nr.invalidCharacterSequenceAfterDoctypeName),t.forceQuirks=!0,this.state=Nr.BOGUS_DOCTYPE,this._stateBogusDoctype(e))}}_stateAfterDoctypePublicKeyword(e){const t=this.currentToken;switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:this.state=Nr.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER;break;case js.QUOTATION_MARK:this._err(nr.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=Nr.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case js.APOSTROPHE:this._err(nr.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=Nr.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case js.GREATER_THAN_SIGN:this._err(nr.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Nr.DATA,this.emitCurrentDoctype(t);break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(nr.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Nr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypePublicIdentifier(e){const t=this.currentToken;switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:break;case js.QUOTATION_MARK:t.publicId="",this.state=Nr.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case js.APOSTROPHE:t.publicId="",this.state=Nr.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case js.GREATER_THAN_SIGN:this._err(nr.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Nr.DATA,this.emitCurrentDoctype(t);break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(nr.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Nr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypePublicIdentifierDoubleQuoted(e){const t=this.currentToken;switch(e){case js.QUOTATION_MARK:this.state=Nr.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case js.NULL:this._err(nr.unexpectedNullCharacter),t.publicId+=Ys;break;case js.GREATER_THAN_SIGN:this._err(nr.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Nr.DATA;break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateDoctypePublicIdentifierSingleQuoted(e){const t=this.currentToken;switch(e){case js.APOSTROPHE:this.state=Nr.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case js.NULL:this._err(nr.unexpectedNullCharacter),t.publicId+=Ys;break;case js.GREATER_THAN_SIGN:this._err(nr.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Nr.DATA;break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateAfterDoctypePublicIdentifier(e){const t=this.currentToken;switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:this.state=Nr.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS;break;case js.GREATER_THAN_SIGN:this.state=Nr.DATA,this.emitCurrentDoctype(t);break;case js.QUOTATION_MARK:this._err(nr.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=Nr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case js.APOSTROPHE:this._err(nr.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=Nr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(nr.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Nr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBetweenDoctypePublicAndSystemIdentifiers(e){const t=this.currentToken;switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:break;case js.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=Nr.DATA;break;case js.QUOTATION_MARK:t.systemId="",this.state=Nr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case js.APOSTROPHE:t.systemId="",this.state=Nr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(nr.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Nr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateAfterDoctypeSystemKeyword(e){const t=this.currentToken;switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:this.state=Nr.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER;break;case js.QUOTATION_MARK:this._err(nr.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=Nr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case js.APOSTROPHE:this._err(nr.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=Nr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case js.GREATER_THAN_SIGN:this._err(nr.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Nr.DATA,this.emitCurrentDoctype(t);break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(nr.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Nr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypeSystemIdentifier(e){const t=this.currentToken;switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:break;case js.QUOTATION_MARK:t.systemId="",this.state=Nr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case js.APOSTROPHE:t.systemId="",this.state=Nr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case js.GREATER_THAN_SIGN:this._err(nr.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Nr.DATA,this.emitCurrentDoctype(t);break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(nr.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Nr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypeSystemIdentifierDoubleQuoted(e){const t=this.currentToken;switch(e){case js.QUOTATION_MARK:this.state=Nr.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case js.NULL:this._err(nr.unexpectedNullCharacter),t.systemId+=Ys;break;case js.GREATER_THAN_SIGN:this._err(nr.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Nr.DATA;break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateDoctypeSystemIdentifierSingleQuoted(e){const t=this.currentToken;switch(e){case js.APOSTROPHE:this.state=Nr.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case js.NULL:this._err(nr.unexpectedNullCharacter),t.systemId+=Ys;break;case js.GREATER_THAN_SIGN:this._err(nr.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Nr.DATA;break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateAfterDoctypeSystemIdentifier(e){const t=this.currentToken;switch(e){case js.SPACE:case js.LINE_FEED:case js.TABULATION:case js.FORM_FEED:break;case js.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=Nr.DATA;break;case js.EOF:this._err(nr.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(nr.unexpectedCharacterAfterDoctypeSystemIdentifier),this.state=Nr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBogusDoctype(e){const t=this.currentToken;switch(e){case js.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=Nr.DATA;break;case js.NULL:this._err(nr.unexpectedNullCharacter);break;case js.EOF:this.emitCurrentDoctype(t),this._emitEOFToken()}}_stateCdataSection(e){switch(e){case js.RIGHT_SQUARE_BRACKET:this.state=Nr.CDATA_SECTION_BRACKET;break;case js.EOF:this._err(nr.eofInCdata),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateCdataSectionBracket(e){e===js.RIGHT_SQUARE_BRACKET?this.state=Nr.CDATA_SECTION_END:(this._emitChars("]"),this.state=Nr.CDATA_SECTION,this._stateCdataSection(e))}_stateCdataSectionEnd(e){switch(e){case js.GREATER_THAN_SIGN:this.state=Nr.DATA;break;case js.RIGHT_SQUARE_BRACKET:this._emitChars("]");break;default:this._emitChars("]]"),this.state=Nr.CDATA_SECTION,this._stateCdataSection(e)}}_stateCharacterReference(e){e===js.NUMBER_SIGN?this.state=Nr.NUMERIC_CHARACTER_REFERENCE:Rr(e)?(this.state=Nr.NAMED_CHARACTER_REFERENCE,this._stateNamedCharacterReference(e)):(this._flushCodePointConsumedAsCharacterReference(js.AMPERSAND),this._reconsumeInState(this.returnState,e))}_stateNamedCharacterReference(e){const t=this._matchNamedCharacterReference(e);if(this._ensureHibernation());else if(t){for(let e=0;e<t.length;e++)this._flushCodePointConsumedAsCharacterReference(t[e]);this.state=this.returnState}else this._flushCodePointConsumedAsCharacterReference(js.AMPERSAND),this.state=Nr.AMBIGUOUS_AMPERSAND}_stateAmbiguousAmpersand(e){Rr(e)?this._flushCodePointConsumedAsCharacterReference(e):(e===js.SEMICOLON&&this._err(nr.unknownNamedCharacterReference),this._reconsumeInState(this.returnState,e))}_stateNumericCharacterReference(e){this.charRefCode=0,e===js.LATIN_SMALL_X||e===js.LATIN_CAPITAL_X?this.state=Nr.HEXADEMICAL_CHARACTER_REFERENCE_START:yr(e)?(this.state=Nr.DECIMAL_CHARACTER_REFERENCE,this._stateDecimalCharacterReference(e)):(this._err(nr.absenceOfDigitsInNumericCharacterReference),this._flushCodePointConsumedAsCharacterReference(js.AMPERSAND),this._flushCodePointConsumedAsCharacterReference(js.NUMBER_SIGN),this._reconsumeInState(this.returnState,e))}_stateHexademicalCharacterReferenceStart(e){!function(e){return yr(e)||Mr(e)||vr(e)}(e)?(this._err(nr.absenceOfDigitsInNumericCharacterReference),this._flushCodePointConsumedAsCharacterReference(js.AMPERSAND),this._flushCodePointConsumedAsCharacterReference(js.NUMBER_SIGN),this._unconsume(2),this.state=this.returnState):(this.state=Nr.HEXADEMICAL_CHARACTER_REFERENCE,this._stateHexademicalCharacterReference(e))}_stateHexademicalCharacterReference(e){Mr(e)?this.charRefCode=16*this.charRefCode+e-55:vr(e)?this.charRefCode=16*this.charRefCode+e-87:yr(e)?this.charRefCode=16*this.charRefCode+e-48:e===js.SEMICOLON?this.state=Nr.NUMERIC_CHARACTER_REFERENCE_END:(this._err(nr.missingSemicolonAfterCharacterReference),this.state=Nr.NUMERIC_CHARACTER_REFERENCE_END,this._stateNumericCharacterReferenceEnd(e))}_stateDecimalCharacterReference(e){yr(e)?this.charRefCode=10*this.charRefCode+e-48:e===js.SEMICOLON?this.state=Nr.NUMERIC_CHARACTER_REFERENCE_END:(this._err(nr.missingSemicolonAfterCharacterReference),this.state=Nr.NUMERIC_CHARACTER_REFERENCE_END,this._stateNumericCharacterReferenceEnd(e))}_stateNumericCharacterReferenceEnd(e){if(this.charRefCode===js.NULL)this._err(nr.nullCharacterReference),this.charRefCode=js.REPLACEMENT_CHARACTER;else if(this.charRefCode>1114111)this._err(nr.characterReferenceOutsideUnicodeRange),this.charRefCode=js.REPLACEMENT_CHARACTER;else if(Js(this.charRefCode))this._err(nr.surrogateCharacterReference),this.charRefCode=js.REPLACEMENT_CHARACTER;else if(tr(this.charRefCode))this._err(nr.noncharacterCharacterReference);else if(er(this.charRefCode)||this.charRefCode===js.CARRIAGE_RETURN){this._err(nr.controlCharacterReference);const e=Sr.get(this.charRefCode);void 0!==e&&(this.charRefCode=e)}this._flushCodePointConsumedAsCharacterReference(this.charRefCode),this._reconsumeInState(this.returnState,e)}};const Br=new Set([mr.DD,mr.DT,mr.LI,mr.OPTGROUP,mr.OPTION,mr.P,mr.RB,mr.RP,mr.RT,mr.RTC]),Ur=new Set([...Br,mr.CAPTION,mr.COLGROUP,mr.TBODY,mr.TD,mr.TFOOT,mr.TH,mr.THEAD,mr.TR]),Hr=new Map([[mr.APPLET,ar.HTML],[mr.CAPTION,ar.HTML],[mr.HTML,ar.HTML],[mr.MARQUEE,ar.HTML],[mr.OBJECT,ar.HTML],[mr.TABLE,ar.HTML],[mr.TD,ar.HTML],[mr.TEMPLATE,ar.HTML],[mr.TH,ar.HTML],[mr.ANNOTATION_XML,ar.MATHML],[mr.MI,ar.MATHML],[mr.MN,ar.MATHML],[mr.MO,ar.MATHML],[mr.MS,ar.MATHML],[mr.MTEXT,ar.MATHML],[mr.DESC,ar.SVG],[mr.FOREIGN_OBJECT,ar.SVG],[mr.TITLE,ar.SVG]]),Gr=[mr.H1,mr.H2,mr.H3,mr.H4,mr.H5,mr.H6],$r=[mr.TR,mr.TEMPLATE,mr.HTML],qr=[mr.TBODY,mr.TFOOT,mr.THEAD,mr.TEMPLATE,mr.HTML],Yr=[mr.TABLE,mr.TEMPLATE,mr.HTML],jr=[mr.TD,mr.TH];class Kr{get currentTmplContentOrNode(){return this._isInTemplate()?this.treeAdapter.getTemplateContent(this.current):this.current}constructor(e,t,n){this.treeAdapter=t,this.handler=n,this.items=[],this.tagIDs=[],this.stackTop=-1,this.tmplCount=0,this.currentTagId=mr.UNKNOWN,this.current=e}_indexOf(e){return this.items.lastIndexOf(e,this.stackTop)}_isInTemplate(){return this.currentTagId===mr.TEMPLATE&&this.treeAdapter.getNamespaceURI(this.current)===ar.HTML}_updateCurrentElement(){this.current=this.items[this.stackTop],this.currentTagId=this.tagIDs[this.stackTop]}push(e,t){this.stackTop++,this.items[this.stackTop]=e,this.current=e,this.tagIDs[this.stackTop]=t,this.currentTagId=t,this._isInTemplate()&&this.tmplCount++,this.handler.onItemPush(e,t,!0)}pop(){const e=this.current;this.tmplCount>0&&this._isInTemplate()&&this.tmplCount--,this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!0)}replace(e,t){const n=this._indexOf(e);this.items[n]=t,n===this.stackTop&&(this.current=t)}insertAfter(e,t,n){const s=this._indexOf(e)+1;this.items.splice(s,0,t),this.tagIDs.splice(s,0,n),this.stackTop++,s===this.stackTop&&this._updateCurrentElement(),this.handler.onItemPush(this.current,this.currentTagId,s===this.stackTop)}popUntilTagNamePopped(e){let t=this.stackTop+1;do{t=this.tagIDs.lastIndexOf(e,t-1)}while(t>0&&this.treeAdapter.getNamespaceURI(this.items[t])!==ar.HTML);this.shortenToLength(t<0?0:t)}shortenToLength(e){for(;this.stackTop>=e;){const t=this.current;this.tmplCount>0&&this._isInTemplate()&&(this.tmplCount-=1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(t,this.stackTop<e)}}popUntilElementPopped(e){const t=this._indexOf(e);this.shortenToLength(t<0?0:t)}popUntilPopped(e,t){const n=this._indexOfTagNames(e,t);this.shortenToLength(n<0?0:n)}popUntilNumberedHeaderPopped(){this.popUntilPopped(Gr,ar.HTML)}popUntilTableCellPopped(){this.popUntilPopped(jr,ar.HTML)}popAllUpToHtmlElement(){this.tmplCount=0,this.shortenToLength(1)}_indexOfTagNames(e,t){for(let n=this.stackTop;n>=0;n--)if(e.includes(this.tagIDs[n])&&this.treeAdapter.getNamespaceURI(this.items[n])===t)return n;return-1}clearBackTo(e,t){const n=this._indexOfTagNames(e,t);this.shortenToLength(n+1)}clearBackToTableContext(){this.clearBackTo(Yr,ar.HTML)}clearBackToTableBodyContext(){this.clearBackTo(qr,ar.HTML)}clearBackToTableRowContext(){this.clearBackTo($r,ar.HTML)}remove(e){const t=this._indexOf(e);t>=0&&(t===this.stackTop?this.pop():(this.items.splice(t,1),this.tagIDs.splice(t,1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!1)))}tryPeekProperlyNestedBodyElement(){return this.stackTop>=1&&this.tagIDs[1]===mr.BODY?this.items[1]:null}contains(e){return this._indexOf(e)>-1}getCommonAncestor(e){const t=this._indexOf(e)-1;return t>=0?this.items[t]:null}isRootHtmlElementCurrent(){return 0===this.stackTop&&this.tagIDs[0]===mr.HTML}hasInScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t],s=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&s===ar.HTML)return!0;if(Hr.get(n)===s)return!1}return!0}hasNumberedHeaderInScope(){for(let e=this.stackTop;e>=0;e--){const t=this.tagIDs[e],n=this.treeAdapter.getNamespaceURI(this.items[e]);if(br(t)&&n===ar.HTML)return!0;if(Hr.get(t)===n)return!1}return!0}hasInListItemScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t],s=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&s===ar.HTML)return!0;if((n===mr.UL||n===mr.OL)&&s===ar.HTML||Hr.get(n)===s)return!1}return!0}hasInButtonScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t],s=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&s===ar.HTML)return!0;if(n===mr.BUTTON&&s===ar.HTML||Hr.get(n)===s)return!1}return!0}hasInTableScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t];if(this.treeAdapter.getNamespaceURI(this.items[t])===ar.HTML){if(n===e)return!0;if(n===mr.TABLE||n===mr.TEMPLATE||n===mr.HTML)return!1}}return!0}hasTableBodyContextInTableScope(){for(let e=this.stackTop;e>=0;e--){const t=this.tagIDs[e];if(this.treeAdapter.getNamespaceURI(this.items[e])===ar.HTML){if(t===mr.TBODY||t===mr.THEAD||t===mr.TFOOT)return!0;if(t===mr.TABLE||t===mr.HTML)return!1}}return!0}hasInSelectScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t];if(this.treeAdapter.getNamespaceURI(this.items[t])===ar.HTML){if(n===e)return!0;if(n!==mr.OPTION&&n!==mr.OPTGROUP)return!1}}return!0}generateImpliedEndTags(){for(;Br.has(this.currentTagId);)this.pop()}generateImpliedEndTagsThoroughly(){for(;Ur.has(this.currentTagId);)this.pop()}generateImpliedEndTagsWithExclusion(e){for(;this.currentTagId!==e&&Ur.has(this.currentTagId);)this.pop()}}var Vr,zr;(zr=Vr=Vr||(Vr={}))[zr.Marker=0]="Marker",zr[zr.Element=1]="Element";const Qr={type:Vr.Marker};class Wr{constructor(e){this.treeAdapter=e,this.entries=[],this.bookmark=null}_getNoahArkConditionCandidates(e,t){const n=[],s=t.length,r=this.treeAdapter.getTagName(e),i=this.treeAdapter.getNamespaceURI(e);for(let e=0;e<this.entries.length;e++){const t=this.entries[e];if(t.type===Vr.Marker)break;const{element:o}=t;if(this.treeAdapter.getTagName(o)===r&&this.treeAdapter.getNamespaceURI(o)===i){const t=this.treeAdapter.getAttrList(o);t.length===s&&n.push({idx:e,attrs:t})}}return n}_ensureNoahArkCondition(e){if(this.entries.length<3)return;const t=this.treeAdapter.getAttrList(e),n=this._getNoahArkConditionCandidates(e,t);if(n.length<3)return;const s=new Map(t.map((e=>[e.name,e.value])));let r=0;for(let e=0;e<n.length;e++){const t=n[e];t.attrs.every((e=>s.get(e.name)===e.value))&&(r+=1,r>=3&&this.entries.splice(t.idx,1))}}insertMarker(){this.entries.unshift(Qr)}pushElement(e,t){this._ensureNoahArkCondition(e),this.entries.unshift({type:Vr.Element,element:e,token:t})}insertElementAfterBookmark(e,t){const n=this.entries.indexOf(this.bookmark);this.entries.splice(n,0,{type:Vr.Element,element:e,token:t})}removeEntry(e){const t=this.entries.indexOf(e);t>=0&&this.entries.splice(t,1)}clearToLastMarker(){const e=this.entries.indexOf(Qr);e>=0?this.entries.splice(0,e+1):this.entries.length=0}getElementEntryInScopeWithTagName(e){const t=this.entries.find((t=>t.type===Vr.Marker||this.treeAdapter.getTagName(t.element)===e));return t&&t.type===Vr.Element?t:null}getElementEntry(e){return this.entries.find((t=>t.type===Vr.Element&&t.element===e))}}function Xr(e){return{nodeName:"#text",value:e,parentNode:null}}const Zr={createDocument:()=>({nodeName:"#document",mode:hr.NO_QUIRKS,childNodes:[]}),createDocumentFragment:()=>({nodeName:"#document-fragment",childNodes:[]}),createElement:(e,t,n)=>({nodeName:e,tagName:e,attrs:n,namespaceURI:t,childNodes:[],parentNode:null}),createCommentNode:e=>({nodeName:"#comment",data:e,parentNode:null}),appendChild(e,t){e.childNodes.push(t),t.parentNode=e},insertBefore(e,t,n){const s=e.childNodes.indexOf(n);e.childNodes.splice(s,0,t),t.parentNode=e},setTemplateContent(e,t){e.content=t},getTemplateContent:e=>e.content,setDocumentType(e,t,n,s){const r=e.childNodes.find((e=>"#documentType"===e.nodeName));if(r)r.name=t,r.publicId=n,r.systemId=s;else{const r={nodeName:"#documentType",name:t,publicId:n,systemId:s,parentNode:null};Zr.appendChild(e,r)}},setDocumentMode(e,t){e.mode=t},getDocumentMode:e=>e.mode,detachNode(e){if(e.parentNode){const t=e.parentNode.childNodes.indexOf(e);e.parentNode.childNodes.splice(t,1),e.parentNode=null}},insertText(e,t){if(e.childNodes.length>0){const n=e.childNodes[e.childNodes.length-1];if(Zr.isTextNode(n))return void(n.value+=t)}Zr.appendChild(e,Xr(t))},insertTextBefore(e,t,n){const s=e.childNodes[e.childNodes.indexOf(n)-1];s&&Zr.isTextNode(s)?s.value+=t:Zr.insertBefore(e,Xr(t),n)},adoptAttributes(e,t){const n=new Set(e.attrs.map((e=>e.name)));for(let s=0;s<t.length;s++)n.has(t[s].name)||e.attrs.push(t[s])},getFirstChild:e=>e.childNodes[0],getChildNodes:e=>e.childNodes,getParentNode:e=>e.parentNode,getAttrList:e=>e.attrs,getTagName:e=>e.tagName,getNamespaceURI:e=>e.namespaceURI,getTextNodeContent:e=>e.value,getCommentNodeContent:e=>e.data,getDocumentTypeNodeName:e=>e.name,getDocumentTypeNodePublicId:e=>e.publicId,getDocumentTypeNodeSystemId:e=>e.systemId,isTextNode:e=>"#text"===e.nodeName,isCommentNode:e=>"#comment"===e.nodeName,isDocumentTypeNode:e=>"#documentType"===e.nodeName,isElementNode:e=>Object.prototype.hasOwnProperty.call(e,"tagName"),setNodeSourceCodeLocation(e,t){e.sourceCodeLocation=t},getNodeSourceCodeLocation:e=>e.sourceCodeLocation,updateNodeSourceCodeLocation(e,t){e.sourceCodeLocation={...e.sourceCodeLocation,...t}}},Jr="html",ei=["+//silmaril//dtd html pro v0r11 19970101//","-//as//dtd html 3.0 aswedit + extensions//","-//advasoft ltd//dtd html 3.0 aswedit + extensions//","-//ietf//dtd html 2.0 level 1//","-//ietf//dtd html 2.0 level 2//","-//ietf//dtd html 2.0 strict level 1//","-//ietf//dtd html 2.0 strict level 2//","-//ietf//dtd html 2.0 strict//","-//ietf//dtd html 2.0//","-//ietf//dtd html 2.1e//","-//ietf//dtd html 3.0//","-//ietf//dtd html 3.2 final//","-//ietf//dtd html 3.2//","-//ietf//dtd html 3//","-//ietf//dtd html level 0//","-//ietf//dtd html level 1//","-//ietf//dtd html level 2//","-//ietf//dtd html level 3//","-//ietf//dtd html strict level 0//","-//ietf//dtd html strict level 1//","-//ietf//dtd html strict level 2//","-//ietf//dtd html strict level 3//","-//ietf//dtd html strict//","-//ietf//dtd html//","-//metrius//dtd metrius presentational//","-//microsoft//dtd internet explorer 2.0 html strict//","-//microsoft//dtd internet explorer 2.0 html//","-//microsoft//dtd internet explorer 2.0 tables//","-//microsoft//dtd internet explorer 3.0 html strict//","-//microsoft//dtd internet explorer 3.0 html//","-//microsoft//dtd internet explorer 3.0 tables//","-//netscape comm. corp.//dtd html//","-//netscape comm. corp.//dtd strict html//","-//o'reilly and associates//dtd html 2.0//","-//o'reilly and associates//dtd html extended 1.0//","-//o'reilly and associates//dtd html extended relaxed 1.0//","-//sq//dtd html 2.0 hotmetal + extensions//","-//softquad software//dtd hotmetal pro 6.0::19990601::extensions to html 4.0//","-//softquad//dtd hotmetal pro 4.0::19971010::extensions to html 4.0//","-//spyglass//dtd html 2.0 extended//","-//sun microsystems corp.//dtd hotjava html//","-//sun microsystems corp.//dtd hotjava strict html//","-//w3c//dtd html 3 1995-03-24//","-//w3c//dtd html 3.2 draft//","-//w3c//dtd html 3.2 final//","-//w3c//dtd html 3.2//","-//w3c//dtd html 3.2s draft//","-//w3c//dtd html 4.0 frameset//","-//w3c//dtd html 4.0 transitional//","-//w3c//dtd html experimental 19960712//","-//w3c//dtd html experimental 970421//","-//w3c//dtd w3 html//","-//w3o//dtd w3 html 3.0//","-//webtechs//dtd mozilla html 2.0//","-//webtechs//dtd mozilla html//"],ti=[...ei,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"],ni=new Set(["-//w3o//dtd w3 html strict 3.0//en//","-/w3c/dtd html 4.0 transitional/en","html"]),si=["-//w3c//dtd xhtml 1.0 frameset//","-//w3c//dtd xhtml 1.0 transitional//"],ri=[...si,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"];function ii(e,t){return t.some((t=>e.startsWith(t)))}const oi="text/html",ai="application/xhtml+xml",ci=new Map(["attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map((e=>[e.toLowerCase(),e]))),li=new Map([["xlink:actuate",{prefix:"xlink",name:"actuate",namespace:ar.XLINK}],["xlink:arcrole",{prefix:"xlink",name:"arcrole",namespace:ar.XLINK}],["xlink:href",{prefix:"xlink",name:"href",namespace:ar.XLINK}],["xlink:role",{prefix:"xlink",name:"role",namespace:ar.XLINK}],["xlink:show",{prefix:"xlink",name:"show",namespace:ar.XLINK}],["xlink:title",{prefix:"xlink",name:"title",namespace:ar.XLINK}],["xlink:type",{prefix:"xlink",name:"type",namespace:ar.XLINK}],["xml:base",{prefix:"xml",name:"base",namespace:ar.XML}],["xml:lang",{prefix:"xml",name:"lang",namespace:ar.XML}],["xml:space",{prefix:"xml",name:"space",namespace:ar.XML}],["xmlns",{prefix:"",name:"xmlns",namespace:ar.XMLNS}],["xmlns:xlink",{prefix:"xmlns",name:"xlink",namespace:ar.XMLNS}]]),ui=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map((e=>[e.toLowerCase(),e]))),hi=new Set([mr.B,mr.BIG,mr.BLOCKQUOTE,mr.BODY,mr.BR,mr.CENTER,mr.CODE,mr.DD,mr.DIV,mr.DL,mr.DT,mr.EM,mr.EMBED,mr.H1,mr.H2,mr.H3,mr.H4,mr.H5,mr.H6,mr.HEAD,mr.HR,mr.I,mr.IMG,mr.LI,mr.LISTING,mr.MENU,mr.META,mr.NOBR,mr.OL,mr.P,mr.PRE,mr.RUBY,mr.S,mr.SMALL,mr.SPAN,mr.STRONG,mr.STRIKE,mr.SUB,mr.SUP,mr.TABLE,mr.TT,mr.U,mr.UL,mr.VAR]);function di(e){for(let t=0;t<e.attrs.length;t++)if("definitionurl"===e.attrs[t].name){e.attrs[t].name="definitionURL";break}}function pi(e){for(let t=0;t<e.attrs.length;t++){const n=ci.get(e.attrs[t].name);null!=n&&(e.attrs[t].name=n)}}function fi(e){for(let t=0;t<e.attrs.length;t++){const n=li.get(e.attrs[t].name);n&&(e.attrs[t].prefix=n.prefix,e.attrs[t].name=n.name,e.attrs[t].namespace=n.namespace)}}function mi(e,t,n,s){return(!s||s===ar.HTML)&&function(e,t,n){if(t===ar.MATHML&&e===mr.ANNOTATION_XML)for(let e=0;e<n.length;e++)if(n[e].name===lr.ENCODING){const t=n[e].value.toLowerCase();return t===oi||t===ai}return t===ar.SVG&&(e===mr.FOREIGN_OBJECT||e===mr.DESC||e===mr.TITLE)}(e,t,n)||(!s||s===ar.MATHML)&&function(e,t){return t===ar.MATHML&&(e===mr.MI||e===mr.MO||e===mr.MN||e===mr.MS||e===mr.MTEXT)}(e,t)}var Ei,Ti;(Ti=Ei||(Ei={}))[Ti.INITIAL=0]="INITIAL",Ti[Ti.BEFORE_HTML=1]="BEFORE_HTML",Ti[Ti.BEFORE_HEAD=2]="BEFORE_HEAD",Ti[Ti.IN_HEAD=3]="IN_HEAD",Ti[Ti.IN_HEAD_NO_SCRIPT=4]="IN_HEAD_NO_SCRIPT",Ti[Ti.AFTER_HEAD=5]="AFTER_HEAD",Ti[Ti.IN_BODY=6]="IN_BODY",Ti[Ti.TEXT=7]="TEXT",Ti[Ti.IN_TABLE=8]="IN_TABLE",Ti[Ti.IN_TABLE_TEXT=9]="IN_TABLE_TEXT",Ti[Ti.IN_CAPTION=10]="IN_CAPTION",Ti[Ti.IN_COLUMN_GROUP=11]="IN_COLUMN_GROUP",Ti[Ti.IN_TABLE_BODY=12]="IN_TABLE_BODY",Ti[Ti.IN_ROW=13]="IN_ROW",Ti[Ti.IN_CELL=14]="IN_CELL",Ti[Ti.IN_SELECT=15]="IN_SELECT",Ti[Ti.IN_SELECT_IN_TABLE=16]="IN_SELECT_IN_TABLE",Ti[Ti.IN_TEMPLATE=17]="IN_TEMPLATE",Ti[Ti.AFTER_BODY=18]="AFTER_BODY",Ti[Ti.IN_FRAMESET=19]="IN_FRAMESET",Ti[Ti.AFTER_FRAMESET=20]="AFTER_FRAMESET",Ti[Ti.AFTER_AFTER_BODY=21]="AFTER_AFTER_BODY",Ti[Ti.AFTER_AFTER_FRAMESET=22]="AFTER_AFTER_FRAMESET";const _i={startLine:-1,startCol:-1,startOffset:-1,endLine:-1,endCol:-1,endOffset:-1},Ai=new Set([mr.TABLE,mr.TBODY,mr.TFOOT,mr.THEAD,mr.TR]),gi={scriptingEnabled:!0,sourceCodeLocationInfo:!1,treeAdapter:Zr,onParseError:null};let Ci=class{constructor(e,t,n=null,s=null){this.fragmentContext=n,this.scriptHandler=s,this.currentToken=null,this.stopped=!1,this.insertionMode=Ei.INITIAL,this.originalInsertionMode=Ei.INITIAL,this.headElement=null,this.formElement=null,this.currentNotInHTML=!1,this.tmplInsertionModeStack=[],this.pendingCharacterTokens=[],this.hasNonWhitespacePendingCharacterToken=!1,this.framesetOk=!0,this.skipNextNewLine=!1,this.fosterParentingEnabled=!1,this.options={...gi,...e},this.treeAdapter=this.options.treeAdapter,this.onParseError=this.options.onParseError,this.onParseError&&(this.options.sourceCodeLocationInfo=!0),this.document=null!=t?t:this.treeAdapter.createDocument(),this.tokenizer=new Pr(this.options,this),this.activeFormattingElements=new Wr(this.treeAdapter),this.fragmentContextID=n?Ar(this.treeAdapter.getTagName(n)):mr.UNKNOWN,this._setContextModes(null!=n?n:this.document,this.fragmentContextID),this.openElements=new Kr(this.document,this.treeAdapter,this)}static parse(e,t){const n=new this(t);return n.tokenizer.write(e,!0),n.document}static getFragmentParser(e,t){const n={...gi,...t};null!=e||(e=n.treeAdapter.createElement(pr.TEMPLATE,ar.HTML,[]));const s=n.treeAdapter.createElement("documentmock",ar.HTML,[]),r=new this(n,s,e);return r.fragmentContextID===mr.TEMPLATE&&r.tmplInsertionModeStack.unshift(Ei.IN_TEMPLATE),r._initTokenizerForFragmentParsing(),r._insertFakeRootElement(),r._resetInsertionMode(),r._findFormInFragmentContext(),r}getFragment(){const e=this.treeAdapter.getFirstChild(this.document),t=this.treeAdapter.createDocumentFragment();return this._adoptNodes(e,t),t}_err(e,t,n){var s;if(!this.onParseError)return;const r=null!==(s=e.location)&&void 0!==s?s:_i,i={code:t,startLine:r.startLine,startCol:r.startCol,startOffset:r.startOffset,endLine:n?r.startLine:r.endLine,endCol:n?r.startCol:r.endCol,endOffset:n?r.startOffset:r.endOffset};this.onParseError(i)}onItemPush(e,t,n){var s,r;null===(r=(s=this.treeAdapter).onItemPush)||void 0===r||r.call(s,e),n&&this.openElements.stackTop>0&&this._setContextModes(e,t)}onItemPop(e,t){var n,s;if(this.options.sourceCodeLocationInfo&&this._setEndLocation(e,this.currentToken),null===(s=(n=this.treeAdapter).onItemPop)||void 0===s||s.call(n,e,this.openElements.current),t){let e,t;0===this.openElements.stackTop&&this.fragmentContext?(e=this.fragmentContext,t=this.fragmentContextID):({current:e,currentTagId:t}=this.openElements),this._setContextModes(e,t)}}_setContextModes(e,t){const n=e===this.document||this.treeAdapter.getNamespaceURI(e)===ar.HTML;this.currentNotInHTML=!n,this.tokenizer.inForeignNode=!n&&!this._isIntegrationPoint(t,e)}_switchToTextParsing(e,t){this._insertElement(e,ar.HTML),this.tokenizer.state=t,this.originalInsertionMode=this.insertionMode,this.insertionMode=Ei.TEXT}switchToPlaintextParsing(){this.insertionMode=Ei.TEXT,this.originalInsertionMode=Ei.IN_BODY,this.tokenizer.state=Dr.PLAINTEXT}_getAdjustedCurrentElement(){return 0===this.openElements.stackTop&&this.fragmentContext?this.fragmentContext:this.openElements.current}_findFormInFragmentContext(){let e=this.fragmentContext;for(;e;){if(this.treeAdapter.getTagName(e)===pr.FORM){this.formElement=e;break}e=this.treeAdapter.getParentNode(e)}}_initTokenizerForFragmentParsing(){if(this.fragmentContext&&this.treeAdapter.getNamespaceURI(this.fragmentContext)===ar.HTML)switch(this.fragmentContextID){case mr.TITLE:case mr.TEXTAREA:this.tokenizer.state=Dr.RCDATA;break;case mr.STYLE:case mr.XMP:case mr.IFRAME:case mr.NOEMBED:case mr.NOFRAMES:case mr.NOSCRIPT:this.tokenizer.state=Dr.RAWTEXT;break;case mr.SCRIPT:this.tokenizer.state=Dr.SCRIPT_DATA;break;case mr.PLAINTEXT:this.tokenizer.state=Dr.PLAINTEXT}}_setDocumentType(e){const t=e.name||"",n=e.publicId||"",s=e.systemId||"";if(this.treeAdapter.setDocumentType(this.document,t,n,s),e.location){const t=this.treeAdapter.getChildNodes(this.document).find((e=>this.treeAdapter.isDocumentTypeNode(e)));t&&this.treeAdapter.setNodeSourceCodeLocation(t,e.location)}}_attachElementToTree(e,t){if(this.options.sourceCodeLocationInfo){const n=t&&{...t,startTag:t};this.treeAdapter.setNodeSourceCodeLocation(e,n)}if(this._shouldFosterParentOnInsertion())this._fosterParentElement(e);else{const t=this.openElements.currentTmplContentOrNode;this.treeAdapter.appendChild(t,e)}}_appendElement(e,t){const n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location)}_insertElement(e,t){const n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location),this.openElements.push(n,e.tagID)}_insertFakeElement(e,t){const n=this.treeAdapter.createElement(e,ar.HTML,[]);this._attachElementToTree(n,null),this.openElements.push(n,t)}_insertTemplate(e){const t=this.treeAdapter.createElement(e.tagName,ar.HTML,e.attrs),n=this.treeAdapter.createDocumentFragment();this.treeAdapter.setTemplateContent(t,n),this._attachElementToTree(t,e.location),this.openElements.push(t,e.tagID),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,null)}_insertFakeRootElement(){const e=this.treeAdapter.createElement(pr.HTML,ar.HTML,[]);this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(e,null),this.treeAdapter.appendChild(this.openElements.current,e),this.openElements.push(e,mr.HTML)}_appendCommentNode(e,t){const n=this.treeAdapter.createCommentNode(e.data);this.treeAdapter.appendChild(t,n),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,e.location)}_insertCharacters(e){let t,n;if(this._shouldFosterParentOnInsertion()?(({parent:t,beforeElement:n}=this._findFosterParentingLocation()),n?this.treeAdapter.insertTextBefore(t,e.chars,n):this.treeAdapter.insertText(t,e.chars)):(t=this.openElements.currentTmplContentOrNode,this.treeAdapter.insertText(t,e.chars)),!e.location)return;const s=this.treeAdapter.getChildNodes(t),r=n?s.lastIndexOf(n):s.length,i=s[r-1];if(this.treeAdapter.getNodeSourceCodeLocation(i)){const{endLine:t,endCol:n,endOffset:s}=e.location;this.treeAdapter.updateNodeSourceCodeLocation(i,{endLine:t,endCol:n,endOffset:s})}else this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(i,e.location)}_adoptNodes(e,t){for(let n=this.treeAdapter.getFirstChild(e);n;n=this.treeAdapter.getFirstChild(e))this.treeAdapter.detachNode(n),this.treeAdapter.appendChild(t,n)}_setEndLocation(e,t){if(this.treeAdapter.getNodeSourceCodeLocation(e)&&t.location){const n=t.location,s=this.treeAdapter.getTagName(e),r=t.type===ir.END_TAG&&s===t.tagName?{endTag:{...n},endLine:n.endLine,endCol:n.endCol,endOffset:n.endOffset}:{endLine:n.startLine,endCol:n.startCol,endOffset:n.startOffset};this.treeAdapter.updateNodeSourceCodeLocation(e,r)}}shouldProcessStartTagTokenInForeignContent(e){if(!this.currentNotInHTML)return!1;let t,n;return 0===this.openElements.stackTop&&this.fragmentContext?(t=this.fragmentContext,n=this.fragmentContextID):({current:t,currentTagId:n}=this.openElements),(e.tagID!==mr.SVG||this.treeAdapter.getTagName(t)!==pr.ANNOTATION_XML||this.treeAdapter.getNamespaceURI(t)!==ar.MATHML)&&(this.tokenizer.inForeignNode||(e.tagID===mr.MGLYPH||e.tagID===mr.MALIGNMARK)&&!this._isIntegrationPoint(n,t,ar.HTML))}_processToken(e){switch(e.type){case ir.CHARACTER:this.onCharacter(e);break;case ir.NULL_CHARACTER:this.onNullCharacter(e);break;case ir.COMMENT:this.onComment(e);break;case ir.DOCTYPE:this.onDoctype(e);break;case ir.START_TAG:this._processStartTag(e);break;case ir.END_TAG:this.onEndTag(e);break;case ir.EOF:this.onEof(e);break;case ir.WHITESPACE_CHARACTER:this.onWhitespaceCharacter(e)}}_isIntegrationPoint(e,t,n){return mi(e,this.treeAdapter.getNamespaceURI(t),this.treeAdapter.getAttrList(t),n)}_reconstructActiveFormattingElements(){const e=this.activeFormattingElements.entries.length;if(e){const t=this.activeFormattingElements.entries.findIndex((e=>e.type===Vr.Marker||this.openElements.contains(e.element)));for(let n=t<0?e-1:t-1;n>=0;n--){const e=this.activeFormattingElements.entries[n];this._insertElement(e.token,this.treeAdapter.getNamespaceURI(e.element)),e.element=this.openElements.current}}}_closeTableCell(){this.openElements.generateImpliedEndTags(),this.openElements.popUntilTableCellPopped(),this.activeFormattingElements.clearToLastMarker(),this.insertionMode=Ei.IN_ROW}_closePElement(){this.openElements.generateImpliedEndTagsWithExclusion(mr.P),this.openElements.popUntilTagNamePopped(mr.P)}_resetInsertionMode(){for(let e=this.openElements.stackTop;e>=0;e--)switch(0===e&&this.fragmentContext?this.fragmentContextID:this.openElements.tagIDs[e]){case mr.TR:return void(this.insertionMode=Ei.IN_ROW);case mr.TBODY:case mr.THEAD:case mr.TFOOT:return void(this.insertionMode=Ei.IN_TABLE_BODY);case mr.CAPTION:return void(this.insertionMode=Ei.IN_CAPTION);case mr.COLGROUP:return void(this.insertionMode=Ei.IN_COLUMN_GROUP);case mr.TABLE:return void(this.insertionMode=Ei.IN_TABLE);case mr.BODY:return void(this.insertionMode=Ei.IN_BODY);case mr.FRAMESET:return void(this.insertionMode=Ei.IN_FRAMESET);case mr.SELECT:return void this._resetInsertionModeForSelect(e);case mr.TEMPLATE:return void(this.insertionMode=this.tmplInsertionModeStack[0]);case mr.HTML:return void(this.insertionMode=this.headElement?Ei.AFTER_HEAD:Ei.BEFORE_HEAD);case mr.TD:case mr.TH:if(e>0)return void(this.insertionMode=Ei.IN_CELL);break;case mr.HEAD:if(e>0)return void(this.insertionMode=Ei.IN_HEAD)}this.insertionMode=Ei.IN_BODY}_resetInsertionModeForSelect(e){if(e>0)for(let t=e-1;t>0;t--){const e=this.openElements.tagIDs[t];if(e===mr.TEMPLATE)break;if(e===mr.TABLE)return void(this.insertionMode=Ei.IN_SELECT_IN_TABLE)}this.insertionMode=Ei.IN_SELECT}_isElementCausesFosterParenting(e){return Ai.has(e)}_shouldFosterParentOnInsertion(){return this.fosterParentingEnabled&&this._isElementCausesFosterParenting(this.openElements.currentTagId)}_findFosterParentingLocation(){for(let e=this.openElements.stackTop;e>=0;e--){const t=this.openElements.items[e];switch(this.openElements.tagIDs[e]){case mr.TEMPLATE:if(this.treeAdapter.getNamespaceURI(t)===ar.HTML)return{parent:this.treeAdapter.getTemplateContent(t),beforeElement:null};break;case mr.TABLE:{const n=this.treeAdapter.getParentNode(t);return n?{parent:n,beforeElement:t}:{parent:this.openElements.items[e-1],beforeElement:null}}}}return{parent:this.openElements.items[0],beforeElement:null}}_fosterParentElement(e){const t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertBefore(t.parent,e,t.beforeElement):this.treeAdapter.appendChild(t.parent,e)}_isSpecialElement(e,t){const n=this.treeAdapter.getNamespaceURI(e);return Cr[n].has(t)}onCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode)!function(e,t){e._insertCharacters(t),e.framesetOk=!1}(this,e);else switch(this.insertionMode){case Ei.INITIAL:Ri(this,e);break;case Ei.BEFORE_HTML:Mi(this,e);break;case Ei.BEFORE_HEAD:vi(this,e);break;case Ei.IN_HEAD:Fi(this,e);break;case Ei.IN_HEAD_NO_SCRIPT:Pi(this,e);break;case Ei.AFTER_HEAD:Bi(this,e);break;case Ei.IN_BODY:case Ei.IN_CAPTION:case Ei.IN_CELL:case Ei.IN_TEMPLATE:Gi(this,e);break;case Ei.TEXT:case Ei.IN_SELECT:case Ei.IN_SELECT_IN_TABLE:this._insertCharacters(e);break;case Ei.IN_TABLE:case Ei.IN_TABLE_BODY:case Ei.IN_ROW:Wi(this,e);break;case Ei.IN_TABLE_TEXT:to(this,e);break;case Ei.IN_COLUMN_GROUP:io(this,e);break;case Ei.AFTER_BODY:mo(this,e);break;case Ei.AFTER_AFTER_BODY:Eo(this,e)}}onNullCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode)!function(e,t){t.chars=Ys,e._insertCharacters(t)}(this,e);else switch(this.insertionMode){case Ei.INITIAL:Ri(this,e);break;case Ei.BEFORE_HTML:Mi(this,e);break;case Ei.BEFORE_HEAD:vi(this,e);break;case Ei.IN_HEAD:Fi(this,e);break;case Ei.IN_HEAD_NO_SCRIPT:Pi(this,e);break;case Ei.AFTER_HEAD:Bi(this,e);break;case Ei.TEXT:this._insertCharacters(e);break;case Ei.IN_TABLE:case Ei.IN_TABLE_BODY:case Ei.IN_ROW:Wi(this,e);break;case Ei.IN_COLUMN_GROUP:io(this,e);break;case Ei.AFTER_BODY:mo(this,e);break;case Ei.AFTER_AFTER_BODY:Eo(this,e)}}onComment(e){if(this.skipNextNewLine=!1,this.currentNotInHTML)Oi(this,e);else switch(this.insertionMode){case Ei.INITIAL:case Ei.BEFORE_HTML:case Ei.BEFORE_HEAD:case Ei.IN_HEAD:case Ei.IN_HEAD_NO_SCRIPT:case Ei.AFTER_HEAD:case Ei.IN_BODY:case Ei.IN_TABLE:case Ei.IN_CAPTION:case Ei.IN_COLUMN_GROUP:case Ei.IN_TABLE_BODY:case Ei.IN_ROW:case Ei.IN_CELL:case Ei.IN_SELECT:case Ei.IN_SELECT_IN_TABLE:case Ei.IN_TEMPLATE:case Ei.IN_FRAMESET:case Ei.AFTER_FRAMESET:Oi(this,e);break;case Ei.IN_TABLE_TEXT:no(this,e);break;case Ei.AFTER_BODY:!function(e,t){e._appendCommentNode(t,e.openElements.items[0])}(this,e);break;case Ei.AFTER_AFTER_BODY:case Ei.AFTER_AFTER_FRAMESET:!function(e,t){e._appendCommentNode(t,e.document)}(this,e)}}onDoctype(e){switch(this.skipNextNewLine=!1,this.insertionMode){case Ei.INITIAL:!function(e,t){e._setDocumentType(t);const n=t.forceQuirks?hr.QUIRKS:function(e){if(e.name!==Jr)return hr.QUIRKS;const{systemId:t}=e;if(t&&"http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd"===t.toLowerCase())return hr.QUIRKS;let{publicId:n}=e;if(null!==n){if(n=n.toLowerCase(),ni.has(n))return hr.QUIRKS;let e=null===t?ti:ei;if(ii(n,e))return hr.QUIRKS;if(e=null===t?si:ri,ii(n,e))return hr.LIMITED_QUIRKS}return hr.NO_QUIRKS}(t);(function(e){return e.name===Jr&&null===e.publicId&&(null===e.systemId||"about:legacy-compat"===e.systemId)})(t)||e._err(t,nr.nonConformingDoctype);e.treeAdapter.setDocumentMode(e.document,n),e.insertionMode=Ei.BEFORE_HTML}(this,e);break;case Ei.BEFORE_HEAD:case Ei.IN_HEAD:case Ei.IN_HEAD_NO_SCRIPT:case Ei.AFTER_HEAD:this._err(e,nr.misplacedDoctype);break;case Ei.IN_TABLE_TEXT:no(this,e)}}onStartTag(e){this.skipNextNewLine=!1,this.currentToken=e,this._processStartTag(e),e.selfClosing&&!e.ackSelfClosing&&this._err(e,nr.nonVoidHtmlElementStartTagWithTrailingSolidus)}_processStartTag(e){this.shouldProcessStartTagTokenInForeignContent(e)?function(e,t){if(function(e){const t=e.tagID;return t===mr.FONT&&e.attrs.some((({name:e})=>e===lr.COLOR||e===lr.SIZE||e===lr.FACE))||hi.has(t)}(t))To(e),e._startTagOutsideForeignContent(t);else{const n=e._getAdjustedCurrentElement(),s=e.treeAdapter.getNamespaceURI(n);s===ar.MATHML?di(t):s===ar.SVG&&(!function(e){const t=ui.get(e.tagName);null!=t&&(e.tagName=t,e.tagID=Ar(e.tagName))}(t),pi(t)),fi(t),t.selfClosing?e._appendElement(t,s):e._insertElement(t,s),t.ackSelfClosing=!0}}(this,e):this._startTagOutsideForeignContent(e)}_startTagOutsideForeignContent(e){switch(this.insertionMode){case Ei.INITIAL:Ri(this,e);break;case Ei.BEFORE_HTML:!function(e,t){t.tagID===mr.HTML?(e._insertElement(t,ar.HTML),e.insertionMode=Ei.BEFORE_HEAD):Mi(e,t)}(this,e);break;case Ei.BEFORE_HEAD:!function(e,t){switch(t.tagID){case mr.HTML:Ki(e,t);break;case mr.HEAD:e._insertElement(t,ar.HTML),e.headElement=e.openElements.current,e.insertionMode=Ei.IN_HEAD;break;default:vi(e,t)}}(this,e);break;case Ei.IN_HEAD:xi(this,e);break;case Ei.IN_HEAD_NO_SCRIPT:!function(e,t){switch(t.tagID){case mr.HTML:Ki(e,t);break;case mr.BASEFONT:case mr.BGSOUND:case mr.HEAD:case mr.LINK:case mr.META:case mr.NOFRAMES:case mr.STYLE:xi(e,t);break;case mr.NOSCRIPT:e._err(t,nr.nestedNoscriptInHead);break;default:Pi(e,t)}}(this,e);break;case Ei.AFTER_HEAD:!function(e,t){switch(t.tagID){case mr.HTML:Ki(e,t);break;case mr.BODY:e._insertElement(t,ar.HTML),e.framesetOk=!1,e.insertionMode=Ei.IN_BODY;break;case mr.FRAMESET:e._insertElement(t,ar.HTML),e.insertionMode=Ei.IN_FRAMESET;break;case mr.BASE:case mr.BASEFONT:case mr.BGSOUND:case mr.LINK:case mr.META:case mr.NOFRAMES:case mr.SCRIPT:case mr.STYLE:case mr.TEMPLATE:case mr.TITLE:e._err(t,nr.abandonedHeadElementChild),e.openElements.push(e.headElement,mr.HEAD),xi(e,t),e.openElements.remove(e.headElement);break;case mr.HEAD:e._err(t,nr.misplacedStartTagForHeadElement);break;default:Bi(e,t)}}(this,e);break;case Ei.IN_BODY:Ki(this,e);break;case Ei.IN_TABLE:Xi(this,e);break;case Ei.IN_TABLE_TEXT:no(this,e);break;case Ei.IN_CAPTION:!function(e,t){const n=t.tagID;so.has(n)?e.openElements.hasInTableScope(mr.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(mr.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=Ei.IN_TABLE,Xi(e,t)):Ki(e,t)}(this,e);break;case Ei.IN_COLUMN_GROUP:ro(this,e);break;case Ei.IN_TABLE_BODY:oo(this,e);break;case Ei.IN_ROW:co(this,e);break;case Ei.IN_CELL:!function(e,t){const n=t.tagID;so.has(n)?(e.openElements.hasInTableScope(mr.TD)||e.openElements.hasInTableScope(mr.TH))&&(e._closeTableCell(),co(e,t)):Ki(e,t)}(this,e);break;case Ei.IN_SELECT:uo(this,e);break;case Ei.IN_SELECT_IN_TABLE:!function(e,t){const n=t.tagID;n===mr.CAPTION||n===mr.TABLE||n===mr.TBODY||n===mr.TFOOT||n===mr.THEAD||n===mr.TR||n===mr.TD||n===mr.TH?(e.openElements.popUntilTagNamePopped(mr.SELECT),e._resetInsertionMode(),e._processStartTag(t)):uo(e,t)}(this,e);break;case Ei.IN_TEMPLATE:!function(e,t){switch(t.tagID){case mr.BASE:case mr.BASEFONT:case mr.BGSOUND:case mr.LINK:case mr.META:case mr.NOFRAMES:case mr.SCRIPT:case mr.STYLE:case mr.TEMPLATE:case mr.TITLE:xi(e,t);break;case mr.CAPTION:case mr.COLGROUP:case mr.TBODY:case mr.TFOOT:case mr.THEAD:e.tmplInsertionModeStack[0]=Ei.IN_TABLE,e.insertionMode=Ei.IN_TABLE,Xi(e,t);break;case mr.COL:e.tmplInsertionModeStack[0]=Ei.IN_COLUMN_GROUP,e.insertionMode=Ei.IN_COLUMN_GROUP,ro(e,t);break;case mr.TR:e.tmplInsertionModeStack[0]=Ei.IN_TABLE_BODY,e.insertionMode=Ei.IN_TABLE_BODY,oo(e,t);break;case mr.TD:case mr.TH:e.tmplInsertionModeStack[0]=Ei.IN_ROW,e.insertionMode=Ei.IN_ROW,co(e,t);break;default:e.tmplInsertionModeStack[0]=Ei.IN_BODY,e.insertionMode=Ei.IN_BODY,Ki(e,t)}}(this,e);break;case Ei.AFTER_BODY:!function(e,t){t.tagID===mr.HTML?Ki(e,t):mo(e,t)}(this,e);break;case Ei.IN_FRAMESET:!function(e,t){switch(t.tagID){case mr.HTML:Ki(e,t);break;case mr.FRAMESET:e._insertElement(t,ar.HTML);break;case mr.FRAME:e._appendElement(t,ar.HTML),t.ackSelfClosing=!0;break;case mr.NOFRAMES:xi(e,t)}}(this,e);break;case Ei.AFTER_FRAMESET:!function(e,t){switch(t.tagID){case mr.HTML:Ki(e,t);break;case mr.NOFRAMES:xi(e,t)}}(this,e);break;case Ei.AFTER_AFTER_BODY:!function(e,t){t.tagID===mr.HTML?Ki(e,t):Eo(e,t)}(this,e);break;case Ei.AFTER_AFTER_FRAMESET:!function(e,t){switch(t.tagID){case mr.HTML:Ki(e,t);break;case mr.NOFRAMES:xi(e,t)}}(this,e)}}onEndTag(e){this.skipNextNewLine=!1,this.currentToken=e,this.currentNotInHTML?function(e,t){if(t.tagID===mr.P||t.tagID===mr.BR)return To(e),void e._endTagOutsideForeignContent(t);for(let n=e.openElements.stackTop;n>0;n--){const s=e.openElements.items[n];if(e.treeAdapter.getNamespaceURI(s)===ar.HTML){e._endTagOutsideForeignContent(t);break}const r=e.treeAdapter.getTagName(s);if(r.toLowerCase()===t.tagName){t.tagName=r,e.openElements.shortenToLength(n);break}}}(this,e):this._endTagOutsideForeignContent(e)}_endTagOutsideForeignContent(e){switch(this.insertionMode){case Ei.INITIAL:Ri(this,e);break;case Ei.BEFORE_HTML:!function(e,t){const n=t.tagID;n!==mr.HTML&&n!==mr.HEAD&&n!==mr.BODY&&n!==mr.BR||Mi(e,t)}(this,e);break;case Ei.BEFORE_HEAD:!function(e,t){const n=t.tagID;n===mr.HEAD||n===mr.BODY||n===mr.HTML||n===mr.BR?vi(e,t):e._err(t,nr.endTagWithoutMatchingOpenElement)}(this,e);break;case Ei.IN_HEAD:!function(e,t){switch(t.tagID){case mr.HEAD:e.openElements.pop(),e.insertionMode=Ei.AFTER_HEAD;break;case mr.BODY:case mr.BR:case mr.HTML:Fi(e,t);break;case mr.TEMPLATE:wi(e,t);break;default:e._err(t,nr.endTagWithoutMatchingOpenElement)}}(this,e);break;case Ei.IN_HEAD_NO_SCRIPT:!function(e,t){switch(t.tagID){case mr.NOSCRIPT:e.openElements.pop(),e.insertionMode=Ei.IN_HEAD;break;case mr.BR:Pi(e,t);break;default:e._err(t,nr.endTagWithoutMatchingOpenElement)}}(this,e);break;case Ei.AFTER_HEAD:!function(e,t){switch(t.tagID){case mr.BODY:case mr.HTML:case mr.BR:Bi(e,t);break;case mr.TEMPLATE:wi(e,t);break;default:e._err(t,nr.endTagWithoutMatchingOpenElement)}}(this,e);break;case Ei.IN_BODY:zi(this,e);break;case Ei.TEXT:!function(e,t){var n;t.tagID===mr.SCRIPT&&(null===(n=e.scriptHandler)||void 0===n||n.call(e,e.openElements.current));e.openElements.pop(),e.insertionMode=e.originalInsertionMode}(this,e);break;case Ei.IN_TABLE:Zi(this,e);break;case Ei.IN_TABLE_TEXT:no(this,e);break;case Ei.IN_CAPTION:!function(e,t){const n=t.tagID;switch(n){case mr.CAPTION:case mr.TABLE:e.openElements.hasInTableScope(mr.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(mr.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=Ei.IN_TABLE,n===mr.TABLE&&Zi(e,t));break;case mr.BODY:case mr.COL:case mr.COLGROUP:case mr.HTML:case mr.TBODY:case mr.TD:case mr.TFOOT:case mr.TH:case mr.THEAD:case mr.TR:break;default:zi(e,t)}}(this,e);break;case Ei.IN_COLUMN_GROUP:!function(e,t){switch(t.tagID){case mr.COLGROUP:e.openElements.currentTagId===mr.COLGROUP&&(e.openElements.pop(),e.insertionMode=Ei.IN_TABLE);break;case mr.TEMPLATE:wi(e,t);break;case mr.COL:break;default:io(e,t)}}(this,e);break;case Ei.IN_TABLE_BODY:ao(this,e);break;case Ei.IN_ROW:lo(this,e);break;case Ei.IN_CELL:!function(e,t){const n=t.tagID;switch(n){case mr.TD:case mr.TH:e.openElements.hasInTableScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=Ei.IN_ROW);break;case mr.TABLE:case mr.TBODY:case mr.TFOOT:case mr.THEAD:case mr.TR:e.openElements.hasInTableScope(n)&&(e._closeTableCell(),lo(e,t));break;case mr.BODY:case mr.CAPTION:case mr.COL:case mr.COLGROUP:case mr.HTML:break;default:zi(e,t)}}(this,e);break;case Ei.IN_SELECT:ho(this,e);break;case Ei.IN_SELECT_IN_TABLE:!function(e,t){const n=t.tagID;n===mr.CAPTION||n===mr.TABLE||n===mr.TBODY||n===mr.TFOOT||n===mr.THEAD||n===mr.TR||n===mr.TD||n===mr.TH?e.openElements.hasInTableScope(n)&&(e.openElements.popUntilTagNamePopped(mr.SELECT),e._resetInsertionMode(),e.onEndTag(t)):ho(e,t)}(this,e);break;case Ei.IN_TEMPLATE:!function(e,t){t.tagID===mr.TEMPLATE&&wi(e,t)}(this,e);break;case Ei.AFTER_BODY:fo(this,e);break;case Ei.IN_FRAMESET:!function(e,t){t.tagID!==mr.FRAMESET||e.openElements.isRootHtmlElementCurrent()||(e.openElements.pop(),e.fragmentContext||e.openElements.currentTagId===mr.FRAMESET||(e.insertionMode=Ei.AFTER_FRAMESET))}(this,e);break;case Ei.AFTER_FRAMESET:!function(e,t){t.tagID===mr.HTML&&(e.insertionMode=Ei.AFTER_AFTER_FRAMESET)}(this,e);break;case Ei.AFTER_AFTER_BODY:Eo(this,e)}}onEof(e){switch(this.insertionMode){case Ei.INITIAL:Ri(this,e);break;case Ei.BEFORE_HTML:Mi(this,e);break;case Ei.BEFORE_HEAD:vi(this,e);break;case Ei.IN_HEAD:Fi(this,e);break;case Ei.IN_HEAD_NO_SCRIPT:Pi(this,e);break;case Ei.AFTER_HEAD:Bi(this,e);break;case Ei.IN_BODY:case Ei.IN_TABLE:case Ei.IN_CAPTION:case Ei.IN_COLUMN_GROUP:case Ei.IN_TABLE_BODY:case Ei.IN_ROW:case Ei.IN_CELL:case Ei.IN_SELECT:case Ei.IN_SELECT_IN_TABLE:Qi(this,e);break;case Ei.TEXT:!function(e,t){e._err(t,nr.eofInElementThatCanContainOnlyText),e.openElements.pop(),e.insertionMode=e.originalInsertionMode,e.onEof(t)}(this,e);break;case Ei.IN_TABLE_TEXT:no(this,e);break;case Ei.IN_TEMPLATE:po(this,e);break;case Ei.AFTER_BODY:case Ei.IN_FRAMESET:case Ei.AFTER_FRAMESET:case Ei.AFTER_AFTER_BODY:case Ei.AFTER_AFTER_FRAMESET:Li(this,e)}}onWhitespaceCharacter(e){if(this.skipNextNewLine&&(this.skipNextNewLine=!1,e.chars.charCodeAt(0)===js.LINE_FEED)){if(1===e.chars.length)return;e.chars=e.chars.substr(1)}if(this.tokenizer.inForeignNode)this._insertCharacters(e);else switch(this.insertionMode){case Ei.IN_HEAD:case Ei.IN_HEAD_NO_SCRIPT:case Ei.AFTER_HEAD:case Ei.TEXT:case Ei.IN_COLUMN_GROUP:case Ei.IN_SELECT:case Ei.IN_SELECT_IN_TABLE:case Ei.IN_FRAMESET:case Ei.AFTER_FRAMESET:this._insertCharacters(e);break;case Ei.IN_BODY:case Ei.IN_CAPTION:case Ei.IN_CELL:case Ei.IN_TEMPLATE:case Ei.AFTER_BODY:case Ei.AFTER_AFTER_BODY:case Ei.AFTER_AFTER_FRAMESET:Hi(this,e);break;case Ei.IN_TABLE:case Ei.IN_TABLE_BODY:case Ei.IN_ROW:Wi(this,e);break;case Ei.IN_TABLE_TEXT:eo(this,e)}}};function bi(e,t){let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(t.tagName);return n?e.openElements.contains(n.element)?e.openElements.hasInScope(t.tagID)||(n=null):(e.activeFormattingElements.removeEntry(n),n=null):Vi(e,t),n}function ki(e,t){let n=null,s=e.openElements.stackTop;for(;s>=0;s--){const r=e.openElements.items[s];if(r===t.element)break;e._isSpecialElement(r,e.openElements.tagIDs[s])&&(n=r)}return n||(e.openElements.shortenToLength(s<0?0:s),e.activeFormattingElements.removeEntry(t)),n}function Si(e,t,n){let s=t,r=e.openElements.getCommonAncestor(t);for(let i=0,o=r;o!==n;i++,o=r){r=e.openElements.getCommonAncestor(o);const n=e.activeFormattingElements.getElementEntry(o),a=n&&i>=3;!n||a?(a&&e.activeFormattingElements.removeEntry(n),e.openElements.remove(o)):(o=Ni(e,n),s===t&&(e.activeFormattingElements.bookmark=n),e.treeAdapter.detachNode(s),e.treeAdapter.appendChild(o,s),s=o)}return s}function Ni(e,t){const n=e.treeAdapter.getNamespaceURI(t.element),s=e.treeAdapter.createElement(t.token.tagName,n,t.token.attrs);return e.openElements.replace(t.element,s),t.element=s,s}function Ii(e,t,n){const s=Ar(e.treeAdapter.getTagName(t));if(e._isElementCausesFosterParenting(s))e._fosterParentElement(n);else{const r=e.treeAdapter.getNamespaceURI(t);s===mr.TEMPLATE&&r===ar.HTML&&(t=e.treeAdapter.getTemplateContent(t)),e.treeAdapter.appendChild(t,n)}}function Di(e,t,n){const s=e.treeAdapter.getNamespaceURI(n.element),{token:r}=n,i=e.treeAdapter.createElement(r.tagName,s,r.attrs);e._adoptNodes(t,i),e.treeAdapter.appendChild(t,i),e.activeFormattingElements.insertElementAfterBookmark(i,r),e.activeFormattingElements.removeEntry(n),e.openElements.remove(n.element),e.openElements.insertAfter(t,i,r.tagID)}function yi(e,t){for(let n=0;n<8;n++){const n=bi(e,t);if(!n)break;const s=ki(e,n);if(!s)break;e.activeFormattingElements.bookmark=n;const r=Si(e,s,n.element),i=e.openElements.getCommonAncestor(n.element);e.treeAdapter.detachNode(r),i&&Ii(e,i,r),Di(e,s,n)}}function Oi(e,t){e._appendCommentNode(t,e.openElements.currentTmplContentOrNode)}function Li(e,t){if(e.stopped=!0,t.location){const n=e.fragmentContext?0:2;for(let s=e.openElements.stackTop;s>=n;s--)e._setEndLocation(e.openElements.items[s],t);if(!e.fragmentContext&&e.openElements.stackTop>=0){const n=e.openElements.items[0],s=e.treeAdapter.getNodeSourceCodeLocation(n);if(s&&!s.endTag&&(e._setEndLocation(n,t),e.openElements.stackTop>=1)){const n=e.openElements.items[1],s=e.treeAdapter.getNodeSourceCodeLocation(n);s&&!s.endTag&&e._setEndLocation(n,t)}}}}function Ri(e,t){e._err(t,nr.missingDoctype,!0),e.treeAdapter.setDocumentMode(e.document,hr.QUIRKS),e.insertionMode=Ei.BEFORE_HTML,e._processToken(t)}function Mi(e,t){e._insertFakeRootElement(),e.insertionMode=Ei.BEFORE_HEAD,e._processToken(t)}function vi(e,t){e._insertFakeElement(pr.HEAD,mr.HEAD),e.headElement=e.openElements.current,e.insertionMode=Ei.IN_HEAD,e._processToken(t)}function xi(e,t){switch(t.tagID){case mr.HTML:Ki(e,t);break;case mr.BASE:case mr.BASEFONT:case mr.BGSOUND:case mr.LINK:case mr.META:e._appendElement(t,ar.HTML),t.ackSelfClosing=!0;break;case mr.TITLE:e._switchToTextParsing(t,Dr.RCDATA);break;case mr.NOSCRIPT:e.options.scriptingEnabled?e._switchToTextParsing(t,Dr.RAWTEXT):(e._insertElement(t,ar.HTML),e.insertionMode=Ei.IN_HEAD_NO_SCRIPT);break;case mr.NOFRAMES:case mr.STYLE:e._switchToTextParsing(t,Dr.RAWTEXT);break;case mr.SCRIPT:e._switchToTextParsing(t,Dr.SCRIPT_DATA);break;case mr.TEMPLATE:e._insertTemplate(t),e.activeFormattingElements.insertMarker(),e.framesetOk=!1,e.insertionMode=Ei.IN_TEMPLATE,e.tmplInsertionModeStack.unshift(Ei.IN_TEMPLATE);break;case mr.HEAD:e._err(t,nr.misplacedStartTagForHeadElement);break;default:Fi(e,t)}}function wi(e,t){e.openElements.tmplCount>0?(e.openElements.generateImpliedEndTagsThoroughly(),e.openElements.currentTagId!==mr.TEMPLATE&&e._err(t,nr.closingOfElementWithOpenChildElements),e.openElements.popUntilTagNamePopped(mr.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode()):e._err(t,nr.endTagWithoutMatchingOpenElement)}function Fi(e,t){e.openElements.pop(),e.insertionMode=Ei.AFTER_HEAD,e._processToken(t)}function Pi(e,t){const n=t.type===ir.EOF?nr.openElementsLeftAfterEof:nr.disallowedContentInNoscriptInHead;e._err(t,n),e.openElements.pop(),e.insertionMode=Ei.IN_HEAD,e._processToken(t)}function Bi(e,t){e._insertFakeElement(pr.BODY,mr.BODY),e.insertionMode=Ei.IN_BODY,Ui(e,t)}function Ui(e,t){switch(t.type){case ir.CHARACTER:Gi(e,t);break;case ir.WHITESPACE_CHARACTER:Hi(e,t);break;case ir.COMMENT:Oi(e,t);break;case ir.START_TAG:Ki(e,t);break;case ir.END_TAG:zi(e,t);break;case ir.EOF:Qi(e,t)}}function Hi(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t)}function Gi(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t),e.framesetOk=!1}function $i(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,ar.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}function qi(e){const t=Tr(e,lr.TYPE);return null!=t&&"hidden"===t.toLowerCase()}function Yi(e,t){e._switchToTextParsing(t,Dr.RAWTEXT)}function ji(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,ar.HTML)}function Ki(e,t){switch(t.tagID){case mr.I:case mr.S:case mr.B:case mr.U:case mr.EM:case mr.TT:case mr.BIG:case mr.CODE:case mr.FONT:case mr.SMALL:case mr.STRIKE:case mr.STRONG:!function(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,ar.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case mr.A:!function(e,t){const n=e.activeFormattingElements.getElementEntryInScopeWithTagName(pr.A);n&&(yi(e,t),e.openElements.remove(n.element),e.activeFormattingElements.removeEntry(n)),e._reconstructActiveFormattingElements(),e._insertElement(t,ar.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case mr.H1:case mr.H2:case mr.H3:case mr.H4:case mr.H5:case mr.H6:!function(e,t){e.openElements.hasInButtonScope(mr.P)&&e._closePElement(),br(e.openElements.currentTagId)&&e.openElements.pop(),e._insertElement(t,ar.HTML)}(e,t);break;case mr.P:case mr.DL:case mr.OL:case mr.UL:case mr.DIV:case mr.DIR:case mr.NAV:case mr.MAIN:case mr.MENU:case mr.ASIDE:case mr.CENTER:case mr.FIGURE:case mr.FOOTER:case mr.HEADER:case mr.HGROUP:case mr.DIALOG:case mr.DETAILS:case mr.ADDRESS:case mr.ARTICLE:case mr.SECTION:case mr.SUMMARY:case mr.FIELDSET:case mr.BLOCKQUOTE:case mr.FIGCAPTION:!function(e,t){e.openElements.hasInButtonScope(mr.P)&&e._closePElement(),e._insertElement(t,ar.HTML)}(e,t);break;case mr.LI:case mr.DD:case mr.DT:!function(e,t){e.framesetOk=!1;const n=t.tagID;for(let t=e.openElements.stackTop;t>=0;t--){const s=e.openElements.tagIDs[t];if(n===mr.LI&&s===mr.LI||(n===mr.DD||n===mr.DT)&&(s===mr.DD||s===mr.DT)){e.openElements.generateImpliedEndTagsWithExclusion(s),e.openElements.popUntilTagNamePopped(s);break}if(s!==mr.ADDRESS&&s!==mr.DIV&&s!==mr.P&&e._isSpecialElement(e.openElements.items[t],s))break}e.openElements.hasInButtonScope(mr.P)&&e._closePElement(),e._insertElement(t,ar.HTML)}(e,t);break;case mr.BR:case mr.IMG:case mr.WBR:case mr.AREA:case mr.EMBED:case mr.KEYGEN:$i(e,t);break;case mr.HR:!function(e,t){e.openElements.hasInButtonScope(mr.P)&&e._closePElement(),e._appendElement(t,ar.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}(e,t);break;case mr.RB:case mr.RTC:!function(e,t){e.openElements.hasInScope(mr.RUBY)&&e.openElements.generateImpliedEndTags(),e._insertElement(t,ar.HTML)}(e,t);break;case mr.RT:case mr.RP:!function(e,t){e.openElements.hasInScope(mr.RUBY)&&e.openElements.generateImpliedEndTagsWithExclusion(mr.RTC),e._insertElement(t,ar.HTML)}(e,t);break;case mr.PRE:case mr.LISTING:!function(e,t){e.openElements.hasInButtonScope(mr.P)&&e._closePElement(),e._insertElement(t,ar.HTML),e.skipNextNewLine=!0,e.framesetOk=!1}(e,t);break;case mr.XMP:!function(e,t){e.openElements.hasInButtonScope(mr.P)&&e._closePElement(),e._reconstructActiveFormattingElements(),e.framesetOk=!1,e._switchToTextParsing(t,Dr.RAWTEXT)}(e,t);break;case mr.SVG:!function(e,t){e._reconstructActiveFormattingElements(),pi(t),fi(t),t.selfClosing?e._appendElement(t,ar.SVG):e._insertElement(t,ar.SVG),t.ackSelfClosing=!0}(e,t);break;case mr.HTML:!function(e,t){0===e.openElements.tmplCount&&e.treeAdapter.adoptAttributes(e.openElements.items[0],t.attrs)}(e,t);break;case mr.BASE:case mr.LINK:case mr.META:case mr.STYLE:case mr.TITLE:case mr.SCRIPT:case mr.BGSOUND:case mr.BASEFONT:case mr.TEMPLATE:xi(e,t);break;case mr.BODY:!function(e,t){const n=e.openElements.tryPeekProperlyNestedBodyElement();n&&0===e.openElements.tmplCount&&(e.framesetOk=!1,e.treeAdapter.adoptAttributes(n,t.attrs))}(e,t);break;case mr.FORM:!function(e,t){const n=e.openElements.tmplCount>0;e.formElement&&!n||(e.openElements.hasInButtonScope(mr.P)&&e._closePElement(),e._insertElement(t,ar.HTML),n||(e.formElement=e.openElements.current))}(e,t);break;case mr.NOBR:!function(e,t){e._reconstructActiveFormattingElements(),e.openElements.hasInScope(mr.NOBR)&&(yi(e,t),e._reconstructActiveFormattingElements()),e._insertElement(t,ar.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case mr.MATH:!function(e,t){e._reconstructActiveFormattingElements(),di(t),fi(t),t.selfClosing?e._appendElement(t,ar.MATHML):e._insertElement(t,ar.MATHML),t.ackSelfClosing=!0}(e,t);break;case mr.TABLE:!function(e,t){e.treeAdapter.getDocumentMode(e.document)!==hr.QUIRKS&&e.openElements.hasInButtonScope(mr.P)&&e._closePElement(),e._insertElement(t,ar.HTML),e.framesetOk=!1,e.insertionMode=Ei.IN_TABLE}(e,t);break;case mr.INPUT:!function(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,ar.HTML),qi(t)||(e.framesetOk=!1),t.ackSelfClosing=!0}(e,t);break;case mr.PARAM:case mr.TRACK:case mr.SOURCE:!function(e,t){e._appendElement(t,ar.HTML),t.ackSelfClosing=!0}(e,t);break;case mr.IMAGE:!function(e,t){t.tagName=pr.IMG,t.tagID=mr.IMG,$i(e,t)}(e,t);break;case mr.BUTTON:!function(e,t){e.openElements.hasInScope(mr.BUTTON)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(mr.BUTTON)),e._reconstructActiveFormattingElements(),e._insertElement(t,ar.HTML),e.framesetOk=!1}(e,t);break;case mr.APPLET:case mr.OBJECT:case mr.MARQUEE:!function(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,ar.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1}(e,t);break;case mr.IFRAME:!function(e,t){e.framesetOk=!1,e._switchToTextParsing(t,Dr.RAWTEXT)}(e,t);break;case mr.SELECT:!function(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,ar.HTML),e.framesetOk=!1,e.insertionMode=e.insertionMode===Ei.IN_TABLE||e.insertionMode===Ei.IN_CAPTION||e.insertionMode===Ei.IN_TABLE_BODY||e.insertionMode===Ei.IN_ROW||e.insertionMode===Ei.IN_CELL?Ei.IN_SELECT_IN_TABLE:Ei.IN_SELECT}(e,t);break;case mr.OPTION:case mr.OPTGROUP:!function(e,t){e.openElements.currentTagId===mr.OPTION&&e.openElements.pop(),e._reconstructActiveFormattingElements(),e._insertElement(t,ar.HTML)}(e,t);break;case mr.NOEMBED:Yi(e,t);break;case mr.FRAMESET:!function(e,t){const n=e.openElements.tryPeekProperlyNestedBodyElement();e.framesetOk&&n&&(e.treeAdapter.detachNode(n),e.openElements.popAllUpToHtmlElement(),e._insertElement(t,ar.HTML),e.insertionMode=Ei.IN_FRAMESET)}(e,t);break;case mr.TEXTAREA:!function(e,t){e._insertElement(t,ar.HTML),e.skipNextNewLine=!0,e.tokenizer.state=Dr.RCDATA,e.originalInsertionMode=e.insertionMode,e.framesetOk=!1,e.insertionMode=Ei.TEXT}(e,t);break;case mr.NOSCRIPT:e.options.scriptingEnabled?Yi(e,t):ji(e,t);break;case mr.PLAINTEXT:!function(e,t){e.openElements.hasInButtonScope(mr.P)&&e._closePElement(),e._insertElement(t,ar.HTML),e.tokenizer.state=Dr.PLAINTEXT}(e,t);break;case mr.COL:case mr.TH:case mr.TD:case mr.TR:case mr.HEAD:case mr.FRAME:case mr.TBODY:case mr.TFOOT:case mr.THEAD:case mr.CAPTION:case mr.COLGROUP:break;default:ji(e,t)}}function Vi(e,t){const n=t.tagName,s=t.tagID;for(let t=e.openElements.stackTop;t>0;t--){const r=e.openElements.items[t],i=e.openElements.tagIDs[t];if(s===i&&(s!==mr.UNKNOWN||e.treeAdapter.getTagName(r)===n)){e.openElements.generateImpliedEndTagsWithExclusion(s),e.openElements.stackTop>=t&&e.openElements.shortenToLength(t);break}if(e._isSpecialElement(r,i))break}}function zi(e,t){switch(t.tagID){case mr.A:case mr.B:case mr.I:case mr.S:case mr.U:case mr.EM:case mr.TT:case mr.BIG:case mr.CODE:case mr.FONT:case mr.NOBR:case mr.SMALL:case mr.STRIKE:case mr.STRONG:yi(e,t);break;case mr.P:!function(e){e.openElements.hasInButtonScope(mr.P)||e._insertFakeElement(pr.P,mr.P),e._closePElement()}(e);break;case mr.DL:case mr.UL:case mr.OL:case mr.DIR:case mr.DIV:case mr.NAV:case mr.PRE:case mr.MAIN:case mr.MENU:case mr.ASIDE:case mr.BUTTON:case mr.CENTER:case mr.FIGURE:case mr.FOOTER:case mr.HEADER:case mr.HGROUP:case mr.DIALOG:case mr.ADDRESS:case mr.ARTICLE:case mr.DETAILS:case mr.SECTION:case mr.SUMMARY:case mr.LISTING:case mr.FIELDSET:case mr.BLOCKQUOTE:case mr.FIGCAPTION:!function(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n))}(e,t);break;case mr.LI:!function(e){e.openElements.hasInListItemScope(mr.LI)&&(e.openElements.generateImpliedEndTagsWithExclusion(mr.LI),e.openElements.popUntilTagNamePopped(mr.LI))}(e);break;case mr.DD:case mr.DT:!function(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilTagNamePopped(n))}(e,t);break;case mr.H1:case mr.H2:case mr.H3:case mr.H4:case mr.H5:case mr.H6:!function(e){e.openElements.hasNumberedHeaderInScope()&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilNumberedHeaderPopped())}(e);break;case mr.BR:!function(e){e._reconstructActiveFormattingElements(),e._insertFakeElement(pr.BR,mr.BR),e.openElements.pop(),e.framesetOk=!1}(e);break;case mr.BODY:!function(e,t){if(e.openElements.hasInScope(mr.BODY)&&(e.insertionMode=Ei.AFTER_BODY,e.options.sourceCodeLocationInfo)){const n=e.openElements.tryPeekProperlyNestedBodyElement();n&&e._setEndLocation(n,t)}}(e,t);break;case mr.HTML:!function(e,t){e.openElements.hasInScope(mr.BODY)&&(e.insertionMode=Ei.AFTER_BODY,fo(e,t))}(e,t);break;case mr.FORM:!function(e){const t=e.openElements.tmplCount>0,{formElement:n}=e;t||(e.formElement=null),(n||t)&&e.openElements.hasInScope(mr.FORM)&&(e.openElements.generateImpliedEndTags(),t?e.openElements.popUntilTagNamePopped(mr.FORM):n&&e.openElements.remove(n))}(e);break;case mr.APPLET:case mr.OBJECT:case mr.MARQUEE:!function(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker())}(e,t);break;case mr.TEMPLATE:wi(e,t);break;default:Vi(e,t)}}function Qi(e,t){e.tmplInsertionModeStack.length>0?po(e,t):Li(e,t)}function Wi(e,t){if(Ai.has(e.openElements.currentTagId))switch(e.pendingCharacterTokens.length=0,e.hasNonWhitespacePendingCharacterToken=!1,e.originalInsertionMode=e.insertionMode,e.insertionMode=Ei.IN_TABLE_TEXT,t.type){case ir.CHARACTER:to(e,t);break;case ir.WHITESPACE_CHARACTER:eo(e,t)}else Ji(e,t)}function Xi(e,t){switch(t.tagID){case mr.TD:case mr.TH:case mr.TR:!function(e,t){e.openElements.clearBackToTableContext(),e._insertFakeElement(pr.TBODY,mr.TBODY),e.insertionMode=Ei.IN_TABLE_BODY,oo(e,t)}(e,t);break;case mr.STYLE:case mr.SCRIPT:case mr.TEMPLATE:xi(e,t);break;case mr.COL:!function(e,t){e.openElements.clearBackToTableContext(),e._insertFakeElement(pr.COLGROUP,mr.COLGROUP),e.insertionMode=Ei.IN_COLUMN_GROUP,ro(e,t)}(e,t);break;case mr.FORM:!function(e,t){e.formElement||0!==e.openElements.tmplCount||(e._insertElement(t,ar.HTML),e.formElement=e.openElements.current,e.openElements.pop())}(e,t);break;case mr.TABLE:!function(e,t){e.openElements.hasInTableScope(mr.TABLE)&&(e.openElements.popUntilTagNamePopped(mr.TABLE),e._resetInsertionMode(),e._processStartTag(t))}(e,t);break;case mr.TBODY:case mr.TFOOT:case mr.THEAD:!function(e,t){e.openElements.clearBackToTableContext(),e._insertElement(t,ar.HTML),e.insertionMode=Ei.IN_TABLE_BODY}(e,t);break;case mr.INPUT:!function(e,t){qi(t)?e._appendElement(t,ar.HTML):Ji(e,t),t.ackSelfClosing=!0}(e,t);break;case mr.CAPTION:!function(e,t){e.openElements.clearBackToTableContext(),e.activeFormattingElements.insertMarker(),e._insertElement(t,ar.HTML),e.insertionMode=Ei.IN_CAPTION}(e,t);break;case mr.COLGROUP:!function(e,t){e.openElements.clearBackToTableContext(),e._insertElement(t,ar.HTML),e.insertionMode=Ei.IN_COLUMN_GROUP}(e,t);break;default:Ji(e,t)}}function Zi(e,t){switch(t.tagID){case mr.TABLE:e.openElements.hasInTableScope(mr.TABLE)&&(e.openElements.popUntilTagNamePopped(mr.TABLE),e._resetInsertionMode());break;case mr.TEMPLATE:wi(e,t);break;case mr.BODY:case mr.CAPTION:case mr.COL:case mr.COLGROUP:case mr.HTML:case mr.TBODY:case mr.TD:case mr.TFOOT:case mr.TH:case mr.THEAD:case mr.TR:break;default:Ji(e,t)}}function Ji(e,t){const n=e.fosterParentingEnabled;e.fosterParentingEnabled=!0,Ui(e,t),e.fosterParentingEnabled=n}function eo(e,t){e.pendingCharacterTokens.push(t)}function to(e,t){e.pendingCharacterTokens.push(t),e.hasNonWhitespacePendingCharacterToken=!0}function no(e,t){let n=0;if(e.hasNonWhitespacePendingCharacterToken)for(;n<e.pendingCharacterTokens.length;n++)Ji(e,e.pendingCharacterTokens[n]);else for(;n<e.pendingCharacterTokens.length;n++)e._insertCharacters(e.pendingCharacterTokens[n]);e.insertionMode=e.originalInsertionMode,e._processToken(t)}const so=new Set([mr.CAPTION,mr.COL,mr.COLGROUP,mr.TBODY,mr.TD,mr.TFOOT,mr.TH,mr.THEAD,mr.TR]);function ro(e,t){switch(t.tagID){case mr.HTML:Ki(e,t);break;case mr.COL:e._appendElement(t,ar.HTML),t.ackSelfClosing=!0;break;case mr.TEMPLATE:xi(e,t);break;default:io(e,t)}}function io(e,t){e.openElements.currentTagId===mr.COLGROUP&&(e.openElements.pop(),e.insertionMode=Ei.IN_TABLE,e._processToken(t))}function oo(e,t){switch(t.tagID){case mr.TR:e.openElements.clearBackToTableBodyContext(),e._insertElement(t,ar.HTML),e.insertionMode=Ei.IN_ROW;break;case mr.TH:case mr.TD:e.openElements.clearBackToTableBodyContext(),e._insertFakeElement(pr.TR,mr.TR),e.insertionMode=Ei.IN_ROW,co(e,t);break;case mr.CAPTION:case mr.COL:case mr.COLGROUP:case mr.TBODY:case mr.TFOOT:case mr.THEAD:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=Ei.IN_TABLE,Xi(e,t));break;default:Xi(e,t)}}function ao(e,t){const n=t.tagID;switch(t.tagID){case mr.TBODY:case mr.TFOOT:case mr.THEAD:e.openElements.hasInTableScope(n)&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=Ei.IN_TABLE);break;case mr.TABLE:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=Ei.IN_TABLE,Zi(e,t));break;case mr.BODY:case mr.CAPTION:case mr.COL:case mr.COLGROUP:case mr.HTML:case mr.TD:case mr.TH:case mr.TR:break;default:Zi(e,t)}}function co(e,t){switch(t.tagID){case mr.TH:case mr.TD:e.openElements.clearBackToTableRowContext(),e._insertElement(t,ar.HTML),e.insertionMode=Ei.IN_CELL,e.activeFormattingElements.insertMarker();break;case mr.CAPTION:case mr.COL:case mr.COLGROUP:case mr.TBODY:case mr.TFOOT:case mr.THEAD:case mr.TR:e.openElements.hasInTableScope(mr.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=Ei.IN_TABLE_BODY,oo(e,t));break;default:Xi(e,t)}}function lo(e,t){switch(t.tagID){case mr.TR:e.openElements.hasInTableScope(mr.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=Ei.IN_TABLE_BODY);break;case mr.TABLE:e.openElements.hasInTableScope(mr.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=Ei.IN_TABLE_BODY,ao(e,t));break;case mr.TBODY:case mr.TFOOT:case mr.THEAD:(e.openElements.hasInTableScope(t.tagID)||e.openElements.hasInTableScope(mr.TR))&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=Ei.IN_TABLE_BODY,ao(e,t));break;case mr.BODY:case mr.CAPTION:case mr.COL:case mr.COLGROUP:case mr.HTML:case mr.TD:case mr.TH:break;default:Zi(e,t)}}function uo(e,t){switch(t.tagID){case mr.HTML:Ki(e,t);break;case mr.OPTION:e.openElements.currentTagId===mr.OPTION&&e.openElements.pop(),e._insertElement(t,ar.HTML);break;case mr.OPTGROUP:e.openElements.currentTagId===mr.OPTION&&e.openElements.pop(),e.openElements.currentTagId===mr.OPTGROUP&&e.openElements.pop(),e._insertElement(t,ar.HTML);break;case mr.INPUT:case mr.KEYGEN:case mr.TEXTAREA:case mr.SELECT:e.openElements.hasInSelectScope(mr.SELECT)&&(e.openElements.popUntilTagNamePopped(mr.SELECT),e._resetInsertionMode(),t.tagID!==mr.SELECT&&e._processStartTag(t));break;case mr.SCRIPT:case mr.TEMPLATE:xi(e,t)}}function ho(e,t){switch(t.tagID){case mr.OPTGROUP:e.openElements.stackTop>0&&e.openElements.currentTagId===mr.OPTION&&e.openElements.tagIDs[e.openElements.stackTop-1]===mr.OPTGROUP&&e.openElements.pop(),e.openElements.currentTagId===mr.OPTGROUP&&e.openElements.pop();break;case mr.OPTION:e.openElements.currentTagId===mr.OPTION&&e.openElements.pop();break;case mr.SELECT:e.openElements.hasInSelectScope(mr.SELECT)&&(e.openElements.popUntilTagNamePopped(mr.SELECT),e._resetInsertionMode());break;case mr.TEMPLATE:wi(e,t)}}function po(e,t){e.openElements.tmplCount>0?(e.openElements.popUntilTagNamePopped(mr.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode(),e.onEof(t)):Li(e,t)}function fo(e,t){var n;if(t.tagID===mr.HTML){if(e.fragmentContext||(e.insertionMode=Ei.AFTER_AFTER_BODY),e.options.sourceCodeLocationInfo&&e.openElements.tagIDs[0]===mr.HTML){e._setEndLocation(e.openElements.items[0],t);const s=e.openElements.items[1];s&&!(null===(n=e.treeAdapter.getNodeSourceCodeLocation(s))||void 0===n?void 0:n.endTag)&&e._setEndLocation(s,t)}}else mo(e,t)}function mo(e,t){e.insertionMode=Ei.IN_BODY,Ui(e,t)}function Eo(e,t){e.insertionMode=Ei.IN_BODY,Ui(e,t)}function To(e){for(;e.treeAdapter.getNamespaceURI(e.openElements.current)!==ar.HTML&&!e._isIntegrationPoint(e.openElements.currentTagId,e.openElements.current);)e.openElements.pop()}const _o=new Set([pr.AREA,pr.BASE,pr.BASEFONT,pr.BGSOUND,pr.BR,pr.COL,pr.EMBED,pr.FRAME,pr.HR,pr.IMG,pr.INPUT,pr.KEYGEN,pr.LINK,pr.META,pr.PARAM,pr.SOURCE,pr.TRACK,pr.WBR]);const Ao={treeAdapter:Zr,scriptingEnabled:!0};function go(e,t){return Co(e,{...Ao,...t})}function Co(e,t){return t.treeAdapter.isElementNode(e)?function(e,t){const n=t.treeAdapter.getTagName(e);return`<${n}${function(e,{treeAdapter:t}){let n="";for(const s of t.getAttrList(e)){if(n+=" ",s.namespace)switch(s.namespace){case ar.XML:n+=`xml:${s.name}`;break;case ar.XMLNS:"xmlns"!==s.name&&(n+="xmlns:"),n+=s.name;break;case ar.XLINK:n+=`xlink:${s.name}`;break;default:n+=`${s.prefix}:${s.name}`}else n+=s.name;n+=`="${Ie(s.value)}"`}return n}(e,t)}>${function(e,t){return t.treeAdapter.isElementNode(e)&&t.treeAdapter.getNamespaceURI(e)===ar.HTML&&_o.has(t.treeAdapter.getTagName(e))}(e,t)?"":`${function(e,t){let n="";const s=t.treeAdapter.isElementNode(e)&&t.treeAdapter.getTagName(e)===pr.TEMPLATE&&t.treeAdapter.getNamespaceURI(e)===ar.HTML?t.treeAdapter.getTemplateContent(e):e,r=t.treeAdapter.getChildNodes(s);if(r)for(const e of r)n+=Co(e,t);return n}(e,t)}</${n}>`}`}(e,t):t.treeAdapter.isTextNode(e)?function(e,t){const{treeAdapter:n}=t,s=n.getTextNodeContent(e),r=n.getParentNode(e),i=r&&n.isElementNode(r)&&n.getTagName(r);return i&&n.getNamespaceURI(r)===ar.HTML&&(o=i,a=t.scriptingEnabled,kr.has(o)||a&&o===pr.NOSCRIPT)?s:De(s);var o,a}(e,t):t.treeAdapter.isCommentNode(e)?function(e,{treeAdapter:t}){return`\x3c!--${t.getCommentNodeContent(e)}--\x3e`}(e,t):t.treeAdapter.isDocumentTypeNode(e)?function(e,{treeAdapter:t}){return`<!DOCTYPE ${t.getDocumentTypeNodeName(e)}>`}(e,t):""}function bo(e){return new P(e)}function ko(e){const t=e.includes('"')?"'":'"';return t+e+t}const So={isCommentNode:V,isElementNode:Y,isTextNode:K,createDocument(){const e=new $([]);return e["x-mode"]=hr.NO_QUIRKS,e},createDocumentFragment:()=>new $([]),createElement(e,t,n){const s=Object.create(null),r=Object.create(null),i=Object.create(null);for(let e=0;e<n.length;e++){const t=n[e].name;s[t]=n[e].value,r[t]=n[e].namespace,i[t]=n[e].prefix}const o=new q(e,s,[]);return o.namespace=t,o["x-attribsNamespace"]=r,o["x-attribsPrefix"]=i,o},createCommentNode:e=>new B(e),appendChild(e,t){const n=e.children[e.children.length-1];n&&(n.next=t,t.prev=n),e.children.push(t),t.parent=e},insertBefore(e,t,n){const s=e.children.indexOf(n),{prev:r}=n;r&&(r.next=t,t.prev=r),n.prev=t,t.next=n,e.children.splice(s,0,t),t.parent=e},setTemplateContent(e,t){So.appendChild(e,t)},getTemplateContent:e=>e.children[0],setDocumentType(e,t,n,s){const r=function(e,t,n){let s="!DOCTYPE ";return e&&(s+=e),t?s+=` PUBLIC ${ko(t)}`:n&&(s+=" SYSTEM"),n&&(s+=` ${ko(n)}`),s}(t,n,s);let i=e.children.find((e=>z(e)&&"!doctype"===e.name));i?i.data=null!=r?r:null:(i=new U("!doctype",r),So.appendChild(e,i)),i["x-name"]=null!=t?t:void 0,i["x-publicId"]=null!=n?n:void 0,i["x-systemId"]=null!=s?s:void 0},setDocumentMode(e,t){e["x-mode"]=t},getDocumentMode:e=>e["x-mode"],detachNode(e){if(e.parent){const t=e.parent.children.indexOf(e),{prev:n,next:s}=e;e.prev=null,e.next=null,n&&(n.next=s),s&&(s.prev=n),e.parent.children.splice(t,1),e.parent=null}},insertText(e,t){const n=e.children[e.children.length-1];n&&K(n)?n.data+=t:So.appendChild(e,bo(t))},insertTextBefore(e,t,n){const s=e.children[e.children.indexOf(n)-1];s&&K(s)?s.data+=t:So.insertBefore(e,bo(t),n)},adoptAttributes(e,t){for(let n=0;n<t.length;n++){const s=t[n].name;void 0===e.attribs[s]&&(e.attribs[s]=t[n].value,e["x-attribsNamespace"][s]=t[n].namespace,e["x-attribsPrefix"][s]=t[n].prefix)}},getFirstChild:e=>e.children[0],getChildNodes:e=>e.children,getParentNode:e=>e.parent,getAttrList:e=>e.attributes,getTagName:e=>e.name,getNamespaceURI:e=>e.namespace,getTextNodeContent:e=>e.data,getCommentNodeContent:e=>e.data,getDocumentTypeNodeName(e){var t;return null!==(t=e["x-name"])&&void 0!==t?t:""},getDocumentTypeNodePublicId(e){var t;return null!==(t=e["x-publicId"])&&void 0!==t?t:""},getDocumentTypeNodeSystemId(e){var t;return null!==(t=e["x-systemId"])&&void 0!==t?t:""},isDocumentTypeNode:e=>z(e)&&"!doctype"===e.name,setNodeSourceCodeLocation(e,t){t&&(e.startIndex=t.startOffset,e.endIndex=t.endOffset),e.sourceCodeLocation=t},getNodeSourceCodeLocation:e=>e.sourceCodeLocation,updateNodeSourceCodeLocation(e,t){null!=t.endOffset&&(e.endIndex=t.endOffset),e.sourceCodeLocation={...e.sourceCodeLocation,...t}}};function No(e,t,n,s){var r;return null!==(r=t.treeAdapter)&&void 0!==r||(t.treeAdapter=So),!1!==t.scriptingEnabled&&(t.scriptingEnabled=!0),n?function(e,t){return Ci.parse(e,t)}(e,t):function(e,t,n){"string"==typeof e&&(n=t,t=e,e=null);const s=Ci.getFragmentParser(e,n);return s.tokenizer.write(t,!0),s.getFragment()}(s,e,t)}const Io={treeAdapter:So};var Do,yo,Oo,Lo;function Ro(e){return e===Do.Space||e===Do.NewLine||e===Do.Tab||e===Do.FormFeed||e===Do.CarriageReturn}function Mo(e){return e===Do.Slash||e===Do.Gt||Ro(e)}!function(e){e[e.Tab=9]="Tab",e[e.NewLine=10]="NewLine",e[e.FormFeed=12]="FormFeed",e[e.CarriageReturn=13]="CarriageReturn",e[e.Space=32]="Space",e[e.ExclamationMark=33]="ExclamationMark",e[e.Number=35]="Number",e[e.Amp=38]="Amp",e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote",e[e.Dash=45]="Dash",e[e.Slash=47]="Slash",e[e.Zero=48]="Zero",e[e.Nine=57]="Nine",e[e.Semi=59]="Semi",e[e.Lt=60]="Lt",e[e.Eq=61]="Eq",e[e.Gt=62]="Gt",e[e.Questionmark=63]="Questionmark",e[e.UpperA=65]="UpperA",e[e.LowerA=97]="LowerA",e[e.UpperF=70]="UpperF",e[e.LowerF=102]="LowerF",e[e.UpperZ=90]="UpperZ",e[e.LowerZ=122]="LowerZ",e[e.LowerX=120]="LowerX",e[e.OpeningSquareBracket=91]="OpeningSquareBracket"}(Do||(Do={})),function(e){e[e.Text=1]="Text",e[e.BeforeTagName=2]="BeforeTagName",e[e.InTagName=3]="InTagName",e[e.InSelfClosingTag=4]="InSelfClosingTag",e[e.BeforeClosingTagName=5]="BeforeClosingTagName",e[e.InClosingTagName=6]="InClosingTagName",e[e.AfterClosingTagName=7]="AfterClosingTagName",e[e.BeforeAttributeName=8]="BeforeAttributeName",e[e.InAttributeName=9]="InAttributeName",e[e.AfterAttributeName=10]="AfterAttributeName",e[e.BeforeAttributeValue=11]="BeforeAttributeValue",e[e.InAttributeValueDq=12]="InAttributeValueDq",e[e.InAttributeValueSq=13]="InAttributeValueSq",e[e.InAttributeValueNq=14]="InAttributeValueNq",e[e.BeforeDeclaration=15]="BeforeDeclaration",e[e.InDeclaration=16]="InDeclaration",e[e.InProcessingInstruction=17]="InProcessingInstruction",e[e.BeforeComment=18]="BeforeComment",e[e.CDATASequence=19]="CDATASequence",e[e.InSpecialComment=20]="InSpecialComment",e[e.InCommentLike=21]="InCommentLike",e[e.BeforeSpecialS=22]="BeforeSpecialS",e[e.BeforeSpecialT=23]="BeforeSpecialT",e[e.SpecialStartSequence=24]="SpecialStartSequence",e[e.InSpecialTag=25]="InSpecialTag",e[e.InEntity=26]="InEntity"}(yo||(yo={})),(Lo=Oo||(Oo={}))[Lo.NoValue=0]="NoValue",Lo[Lo.Unquoted=1]="Unquoted",Lo[Lo.Single=2]="Single",Lo[Lo.Double=3]="Double";const vo={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101]),TextareaEnd:new Uint8Array([60,47,116,101,120,116,97,114,101,97])};class xo{constructor({xmlMode:e=!1,decodeEntities:t=!0},n){this.cbs=n,this.state=yo.Text,this.buffer="",this.sectionStart=0,this.index=0,this.entityStart=0,this.baseState=yo.Text,this.isSpecial=!1,this.running=!0,this.offset=0,this.currentSequence=void 0,this.sequenceIndex=0,this.xmlMode=e,this.decodeEntities=t,this.entityDecoder=new Ee(e?ne:te,((e,t)=>this.emitCodePoint(e,t)))}reset(){this.state=yo.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=yo.Text,this.currentSequence=void 0,this.running=!0,this.offset=0}write(e){this.offset+=this.buffer.length,this.buffer=e,this.parse()}end(){this.running&&this.finish()}pause(){this.running=!1}resume(){this.running=!0,this.index<this.buffer.length+this.offset&&this.parse()}stateText(e){e===Do.Lt||!this.decodeEntities&&this.fastForwardTo(Do.Lt)?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=yo.BeforeTagName,this.sectionStart=this.index):this.decodeEntities&&e===Do.Amp&&this.startEntity()}stateSpecialStartSequence(e){const t=this.sequenceIndex===this.currentSequence.length;if(t?Mo(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.isSpecial=!1;this.sequenceIndex=0,this.state=yo.InTagName,this.stateInTagName(e)}stateInSpecialTag(e){if(this.sequenceIndex===this.currentSequence.length){if(e===Do.Gt||Ro(e)){const t=this.index-this.currentSequence.length;if(this.sectionStart<t){const e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}return this.isSpecial=!1,this.sectionStart=t+2,void this.stateInClosingTagName(e)}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===vo.TitleEnd?this.decodeEntities&&e===Do.Amp&&this.startEntity():this.fastForwardTo(Do.Lt)&&(this.sequenceIndex=1):this.sequenceIndex=Number(e===Do.Lt)}stateCDATASequence(e){e===vo.Cdata[this.sequenceIndex]?++this.sequenceIndex===vo.Cdata.length&&(this.state=yo.InCommentLike,this.currentSequence=vo.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=yo.InDeclaration,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length+this.offset;)if(this.buffer.charCodeAt(this.index-this.offset)===e)return!0;return this.index=this.buffer.length+this.offset-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===vo.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index,2):this.cbs.oncomment(this.sectionStart,this.index,2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=yo.Text):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}isTagStartChar(e){return this.xmlMode?!Mo(e):function(e){return e>=Do.LowerA&&e<=Do.LowerZ||e>=Do.UpperA&&e<=Do.UpperZ}(e)}startSpecial(e,t){this.isSpecial=!0,this.currentSequence=e,this.sequenceIndex=t,this.state=yo.SpecialStartSequence}stateBeforeTagName(e){if(e===Do.ExclamationMark)this.state=yo.BeforeDeclaration,this.sectionStart=this.index+1;else if(e===Do.Questionmark)this.state=yo.InProcessingInstruction,this.sectionStart=this.index+1;else if(this.isTagStartChar(e)){const t=32|e;this.sectionStart=this.index,this.xmlMode?this.state=yo.InTagName:t===vo.ScriptEnd[2]?this.state=yo.BeforeSpecialS:t===vo.TitleEnd[2]?this.state=yo.BeforeSpecialT:this.state=yo.InTagName}else e===Do.Slash?this.state=yo.BeforeClosingTagName:(this.state=yo.Text,this.stateText(e))}stateInTagName(e){Mo(e)&&(this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=yo.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateBeforeClosingTagName(e){Ro(e)||(e===Do.Gt?this.state=yo.Text:(this.state=this.isTagStartChar(e)?yo.InClosingTagName:yo.InSpecialComment,this.sectionStart=this.index))}stateInClosingTagName(e){(e===Do.Gt||Ro(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=yo.AfterClosingTagName,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){(e===Do.Gt||this.fastForwardTo(Do.Gt))&&(this.state=yo.Text,this.sectionStart=this.index+1)}stateBeforeAttributeName(e){e===Do.Gt?(this.cbs.onopentagend(this.index),this.isSpecial?(this.state=yo.InSpecialTag,this.sequenceIndex=0):this.state=yo.Text,this.sectionStart=this.index+1):e===Do.Slash?this.state=yo.InSelfClosingTag:Ro(e)||(this.state=yo.InAttributeName,this.sectionStart=this.index)}stateInSelfClosingTag(e){e===Do.Gt?(this.cbs.onselfclosingtag(this.index),this.state=yo.Text,this.sectionStart=this.index+1,this.isSpecial=!1):Ro(e)||(this.state=yo.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateInAttributeName(e){(e===Do.Eq||Mo(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.sectionStart=this.index,this.state=yo.AfterAttributeName,this.stateAfterAttributeName(e))}stateAfterAttributeName(e){e===Do.Eq?this.state=yo.BeforeAttributeValue:e===Do.Slash||e===Do.Gt?(this.cbs.onattribend(Oo.NoValue,this.sectionStart),this.sectionStart=-1,this.state=yo.BeforeAttributeName,this.stateBeforeAttributeName(e)):Ro(e)||(this.cbs.onattribend(Oo.NoValue,this.sectionStart),this.state=yo.InAttributeName,this.sectionStart=this.index)}stateBeforeAttributeValue(e){e===Do.DoubleQuote?(this.state=yo.InAttributeValueDq,this.sectionStart=this.index+1):e===Do.SingleQuote?(this.state=yo.InAttributeValueSq,this.sectionStart=this.index+1):Ro(e)||(this.sectionStart=this.index,this.state=yo.InAttributeValueNq,this.stateInAttributeValueNoQuotes(e))}handleInAttributeValue(e,t){e===t||!this.decodeEntities&&this.fastForwardTo(t)?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(t===Do.DoubleQuote?Oo.Double:Oo.Single,this.index+1),this.state=yo.BeforeAttributeName):this.decodeEntities&&e===Do.Amp&&this.startEntity()}stateInAttributeValueDoubleQuotes(e){this.handleInAttributeValue(e,Do.DoubleQuote)}stateInAttributeValueSingleQuotes(e){this.handleInAttributeValue(e,Do.SingleQuote)}stateInAttributeValueNoQuotes(e){Ro(e)||e===Do.Gt?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(Oo.Unquoted,this.index),this.state=yo.BeforeAttributeName,this.stateBeforeAttributeName(e)):this.decodeEntities&&e===Do.Amp&&this.startEntity()}stateBeforeDeclaration(e){e===Do.OpeningSquareBracket?(this.state=yo.CDATASequence,this.sequenceIndex=0):this.state=e===Do.Dash?yo.BeforeComment:yo.InDeclaration}stateInDeclaration(e){(e===Do.Gt||this.fastForwardTo(Do.Gt))&&(this.cbs.ondeclaration(this.sectionStart,this.index),this.state=yo.Text,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(e===Do.Gt||this.fastForwardTo(Do.Gt))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=yo.Text,this.sectionStart=this.index+1)}stateBeforeComment(e){e===Do.Dash?(this.state=yo.InCommentLike,this.currentSequence=vo.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=yo.InDeclaration}stateInSpecialComment(e){(e===Do.Gt||this.fastForwardTo(Do.Gt))&&(this.cbs.oncomment(this.sectionStart,this.index,0),this.state=yo.Text,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){const t=32|e;t===vo.ScriptEnd[3]?this.startSpecial(vo.ScriptEnd,4):t===vo.StyleEnd[3]?this.startSpecial(vo.StyleEnd,4):(this.state=yo.InTagName,this.stateInTagName(e))}stateBeforeSpecialT(e){const t=32|e;t===vo.TitleEnd[3]?this.startSpecial(vo.TitleEnd,4):t===vo.TextareaEnd[3]?this.startSpecial(vo.TextareaEnd,4):(this.state=yo.InTagName,this.stateInTagName(e))}startEntity(){this.baseState=this.state,this.state=yo.InEntity,this.entityStart=this.index,this.entityDecoder.startEntity(this.xmlMode?de.Strict:this.baseState===yo.Text||this.baseState===yo.InSpecialTag?de.Legacy:de.Attribute)}stateInEntity(){const e=this.entityDecoder.write(this.buffer,this.index-this.offset);e>=0?(this.state=this.baseState,0===e&&(this.index=this.entityStart)):this.index=this.offset+this.buffer.length-1}cleanup(){this.running&&this.sectionStart!==this.index&&(this.state===yo.Text||this.state===yo.InSpecialTag&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):this.state!==yo.InAttributeValueDq&&this.state!==yo.InAttributeValueSq&&this.state!==yo.InAttributeValueNq||(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}shouldContinue(){return this.index<this.buffer.length+this.offset&&this.running}parse(){for(;this.shouldContinue();){const e=this.buffer.charCodeAt(this.index-this.offset);switch(this.state){case yo.Text:this.stateText(e);break;case yo.SpecialStartSequence:this.stateSpecialStartSequence(e);break;case yo.InSpecialTag:this.stateInSpecialTag(e);break;case yo.CDATASequence:this.stateCDATASequence(e);break;case yo.InAttributeValueDq:this.stateInAttributeValueDoubleQuotes(e);break;case yo.InAttributeName:this.stateInAttributeName(e);break;case yo.InCommentLike:this.stateInCommentLike(e);break;case yo.InSpecialComment:this.stateInSpecialComment(e);break;case yo.BeforeAttributeName:this.stateBeforeAttributeName(e);break;case yo.InTagName:this.stateInTagName(e);break;case yo.InClosingTagName:this.stateInClosingTagName(e);break;case yo.BeforeTagName:this.stateBeforeTagName(e);break;case yo.AfterAttributeName:this.stateAfterAttributeName(e);break;case yo.InAttributeValueSq:this.stateInAttributeValueSingleQuotes(e);break;case yo.BeforeAttributeValue:this.stateBeforeAttributeValue(e);break;case yo.BeforeClosingTagName:this.stateBeforeClosingTagName(e);break;case yo.AfterClosingTagName:this.stateAfterClosingTagName(e);break;case yo.BeforeSpecialS:this.stateBeforeSpecialS(e);break;case yo.BeforeSpecialT:this.stateBeforeSpecialT(e);break;case yo.InAttributeValueNq:this.stateInAttributeValueNoQuotes(e);break;case yo.InSelfClosingTag:this.stateInSelfClosingTag(e);break;case yo.InDeclaration:this.stateInDeclaration(e);break;case yo.BeforeDeclaration:this.stateBeforeDeclaration(e);break;case yo.BeforeComment:this.stateBeforeComment(e);break;case yo.InProcessingInstruction:this.stateInProcessingInstruction(e);break;case yo.InEntity:this.stateInEntity()}this.index++}this.cleanup()}finish(){this.state===yo.InEntity&&(this.entityDecoder.end(),this.state=this.baseState),this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){const e=this.buffer.length+this.offset;this.sectionStart>=e||(this.state===yo.InCommentLike?this.currentSequence===vo.CdataEnd?this.cbs.oncdata(this.sectionStart,e,0):this.cbs.oncomment(this.sectionStart,e,0):this.state===yo.InTagName||this.state===yo.BeforeAttributeName||this.state===yo.BeforeAttributeValue||this.state===yo.AfterAttributeName||this.state===yo.InAttributeName||this.state===yo.InAttributeValueSq||this.state===yo.InAttributeValueDq||this.state===yo.InAttributeValueNq||this.state===yo.InClosingTagName||this.cbs.ontext(this.sectionStart,e))}emitCodePoint(e,t){this.baseState!==yo.Text&&this.baseState!==yo.InSpecialTag?(this.sectionStart<this.entityStart&&this.cbs.onattribdata(this.sectionStart,this.entityStart),this.sectionStart=this.entityStart+t,this.index=this.sectionStart-1,this.cbs.onattribentity(e)):(this.sectionStart<this.entityStart&&this.cbs.ontext(this.sectionStart,this.entityStart),this.sectionStart=this.entityStart+t,this.index=this.sectionStart-1,this.cbs.ontextentity(e,this.sectionStart))}}const wo=new Set(["input","option","optgroup","select","button","datalist","textarea"]),Fo=new Set(["p"]),Po=new Set(["thead","tbody"]),Bo=new Set(["dd","dt"]),Uo=new Set(["rt","rp"]),Ho=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",Fo],["h1",Fo],["h2",Fo],["h3",Fo],["h4",Fo],["h5",Fo],["h6",Fo],["select",wo],["input",wo],["output",wo],["button",wo],["datalist",wo],["textarea",wo],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",Bo],["dt",Bo],["address",Fo],["article",Fo],["aside",Fo],["blockquote",Fo],["details",Fo],["div",Fo],["dl",Fo],["fieldset",Fo],["figcaption",Fo],["figure",Fo],["footer",Fo],["form",Fo],["header",Fo],["hr",Fo],["main",Fo],["nav",Fo],["ol",Fo],["pre",Fo],["section",Fo],["table",Fo],["ul",Fo],["rt",Uo],["rp",Uo],["tbody",Po],["tfoot",Po]]),Go=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),$o=new Set(["math","svg"]),qo=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),Yo=/\s|\//;let jo=class{constructor(e,t={}){var n,s,r,i,o,a;this.options=t,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.buffers=[],this.bufferOffset=0,this.writeIndex=0,this.ended=!1,this.cbs=null!=e?e:{},this.htmlMode=!this.options.xmlMode,this.lowerCaseTagNames=null!==(n=t.lowerCaseTags)&&void 0!==n?n:this.htmlMode,this.lowerCaseAttributeNames=null!==(s=t.lowerCaseAttributeNames)&&void 0!==s?s:this.htmlMode,this.recognizeSelfClosing=null!==(r=t.recognizeSelfClosing)&&void 0!==r?r:!this.htmlMode,this.tokenizer=new(null!==(i=t.Tokenizer)&&void 0!==i?i:xo)(this.options,this),this.foreignContext=[!this.htmlMode],null===(a=(o=this.cbs).onparserinit)||void 0===a||a.call(o,this)}ontext(e,t){var n,s;const r=this.getSlice(e,t);this.endIndex=t-1,null===(s=(n=this.cbs).ontext)||void 0===s||s.call(n,r),this.startIndex=t}ontextentity(e,t){var n,s;this.endIndex=t-1,null===(s=(n=this.cbs).ontext)||void 0===s||s.call(n,ie(e)),this.startIndex=t}isVoidElement(e){return this.htmlMode&&Go.has(e)}onopentagname(e,t){this.endIndex=t;let n=this.getSlice(e,t);this.lowerCaseTagNames&&(n=n.toLowerCase()),this.emitOpenTag(n)}emitOpenTag(e){var t,n,s,r;this.openTagStart=this.startIndex,this.tagname=e;const i=this.htmlMode&&Ho.get(e);if(i)for(;this.stack.length>0&&i.has(this.stack[0]);){const e=this.stack.shift();null===(n=(t=this.cbs).onclosetag)||void 0===n||n.call(t,e,!0)}this.isVoidElement(e)||(this.stack.unshift(e),this.htmlMode&&($o.has(e)?this.foreignContext.unshift(!0):qo.has(e)&&this.foreignContext.unshift(!1))),null===(r=(s=this.cbs).onopentagname)||void 0===r||r.call(s,e),this.cbs.onopentag&&(this.attribs={})}endOpenTag(e){var t,n;this.startIndex=this.openTagStart,this.attribs&&(null===(n=(t=this.cbs).onopentag)||void 0===n||n.call(t,this.tagname,this.attribs,e),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""}onopentagend(e){this.endIndex=e,this.endOpenTag(!1),this.startIndex=e+1}onclosetag(e,t){var n,s,r,i,o,a,c,l;this.endIndex=t;let u=this.getSlice(e,t);if(this.lowerCaseTagNames&&(u=u.toLowerCase()),this.htmlMode&&($o.has(u)||qo.has(u))&&this.foreignContext.shift(),this.isVoidElement(u))this.htmlMode&&"br"===u&&(null===(i=(r=this.cbs).onopentagname)||void 0===i||i.call(r,"br"),null===(a=(o=this.cbs).onopentag)||void 0===a||a.call(o,"br",{},!0),null===(l=(c=this.cbs).onclosetag)||void 0===l||l.call(c,"br",!1));else{const e=this.stack.indexOf(u);if(-1!==e)for(let t=0;t<=e;t++){const r=this.stack.shift();null===(s=(n=this.cbs).onclosetag)||void 0===s||s.call(n,r,t!==e)}else this.htmlMode&&"p"===u&&(this.emitOpenTag("p"),this.closeCurrentTag(!0))}this.startIndex=t+1}onselfclosingtag(e){this.endIndex=e,this.recognizeSelfClosing||this.foreignContext[0]?(this.closeCurrentTag(!1),this.startIndex=e+1):this.onopentagend(e)}closeCurrentTag(e){var t,n;const s=this.tagname;this.endOpenTag(e),this.stack[0]===s&&(null===(n=(t=this.cbs).onclosetag)||void 0===n||n.call(t,s,!e),this.stack.shift())}onattribname(e,t){this.startIndex=e;const n=this.getSlice(e,t);this.attribname=this.lowerCaseAttributeNames?n.toLowerCase():n}onattribdata(e,t){this.attribvalue+=this.getSlice(e,t)}onattribentity(e){this.attribvalue+=ie(e)}onattribend(e,t){var n,s;this.endIndex=t,null===(s=(n=this.cbs).onattribute)||void 0===s||s.call(n,this.attribname,this.attribvalue,e===Oo.Double?'"':e===Oo.Single?"'":e===Oo.NoValue?void 0:null),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribvalue=""}getInstructionName(e){const t=e.search(Yo);let n=t<0?e:e.substr(0,t);return this.lowerCaseTagNames&&(n=n.toLowerCase()),n}ondeclaration(e,t){this.endIndex=t;const n=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){const e=this.getInstructionName(n);this.cbs.onprocessinginstruction(`!${e}`,`!${n}`)}this.startIndex=t+1}onprocessinginstruction(e,t){this.endIndex=t;const n=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){const e=this.getInstructionName(n);this.cbs.onprocessinginstruction(`?${e}`,`?${n}`)}this.startIndex=t+1}oncomment(e,t,n){var s,r,i,o;this.endIndex=t,null===(r=(s=this.cbs).oncomment)||void 0===r||r.call(s,this.getSlice(e,t-n)),null===(o=(i=this.cbs).oncommentend)||void 0===o||o.call(i),this.startIndex=t+1}oncdata(e,t,n){var s,r,i,o,a,c,l,u,h,d;this.endIndex=t;const p=this.getSlice(e,t-n);!this.htmlMode||this.options.recognizeCDATA?(null===(r=(s=this.cbs).oncdatastart)||void 0===r||r.call(s),null===(o=(i=this.cbs).ontext)||void 0===o||o.call(i,p),null===(c=(a=this.cbs).oncdataend)||void 0===c||c.call(a)):(null===(u=(l=this.cbs).oncomment)||void 0===u||u.call(l,`[CDATA[${p}]]`),null===(d=(h=this.cbs).oncommentend)||void 0===d||d.call(h)),this.startIndex=t+1}onend(){var e,t;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(let e=0;e<this.stack.length;e++)this.cbs.onclosetag(this.stack[e],!0)}null===(t=(e=this.cbs).onend)||void 0===t||t.call(e)}reset(){var e,t,n,s;null===(t=(e=this.cbs).onreset)||void 0===t||t.call(e),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack.length=0,this.startIndex=0,this.endIndex=0,null===(s=(n=this.cbs).onparserinit)||void 0===s||s.call(n,this),this.buffers.length=0,this.foreignContext.length=0,this.foreignContext.unshift(!this.htmlMode),this.bufferOffset=0,this.writeIndex=0,this.ended=!1}parseComplete(e){this.reset(),this.end(e)}getSlice(e,t){for(;e-this.bufferOffset>=this.buffers[0].length;)this.shiftBuffer();let n=this.buffers[0].slice(e-this.bufferOffset,t-this.bufferOffset);for(;t-this.bufferOffset>this.buffers[0].length;)this.shiftBuffer(),n+=this.buffers[0].slice(0,t-this.bufferOffset);return n}shiftBuffer(){this.bufferOffset+=this.buffers[0].length,this.writeIndex--,this.buffers.shift()}write(e){var t,n;this.ended?null===(n=(t=this.cbs).onerror)||void 0===n||n.call(t,new Error(".write() after done!")):(this.buffers.push(e),this.tokenizer.running&&(this.tokenizer.write(e),this.writeIndex++))}end(e){var t,n;this.ended?null===(n=(t=this.cbs).onerror)||void 0===n||n.call(t,new Error(".end() after done!")):(e&&this.write(e),this.ended=!0,this.tokenizer.end())}pause(){this.tokenizer.pause()}resume(){for(this.tokenizer.resume();this.tokenizer.running&&this.writeIndex<this.buffers.length;)this.tokenizer.write(this.buffers[this.writeIndex++]);this.ended&&this.tokenizer.end()}parseChunk(e){this.write(e)}done(e){this.end(e)}};const Ko=(Vo=(e,t,n,s)=>t._useHtmlParser2?function(e,t){const n=new ee(void 0,t);return new jo(n,t).end(e),n.root}(e,t):No(e,t,n,s),function(e,t,n,s){if("undefined"!=typeof Buffer&&Buffer.isBuffer(e)&&(e=e.toString()),"string"==typeof e)return Vo(e,t,n,s);const r=e;if(!Array.isArray(r)&&Q(r))return r;const i=new $([]);return Ss(r,i),i});var Vo;const zo=(Qo=Ko,Wo=(e,t)=>t._useHtmlParser2?ve(e,t):function(e){const t="length"in e?e:[e];for(let e=0;e<t.length;e+=1){const n=t[e];Q(n)&&Array.prototype.splice.call(t,e,1,...n.children)}let n="";for(let e=0;e<t.length;e+=1)n+=go(t[e],Io);return n}(e),function e(t,n,s=!0){if(null==t)throw new Error("cheerio.load() expects a string");const r=pt(n),i=Qo(t,r,s,null);class o extends $s{_make(e,t){const n=a(e,t);return n.prevObject=this,n}_parse(e,t,n,s){return Qo(e,t,n,s)}_render(e){return Wo(e,this.options)}}function a(e,t,n=i,s){if(e&&At(e))return e;const a=pt(s,r),c="string"==typeof n?[Qo(n,a,!1,null)]:"length"in n?n:[n],l=At(c)?c:new o(c,null,a);if(l._root=l,!e)return new o(void 0,l,a);const u="string"==typeof e&&kt(e)?Qo(e,a,!1,null).children:(h=e).name||"root"===h.type||"text"===h.type||"comment"===h.type?[e]:Array.isArray(e)?e:void 0;var h;const d=new o(u,l,a);if(u)return d;if("string"!=typeof e)throw new TypeError("Unexpected type of selector");let p=e;const f=t?"string"==typeof t?kt(t)?new o([Qo(t,a,!1,null)],l,a):(p=`${t} ${p}`,l):At(t)?t:new o(Array.isArray(t)?t:[t],l,a):l;return f?f.find(p):d}return Object.assign(a,_t,{load:e,_root:i,_options:r,fn:o.prototype,prototype:o.prototype}),a});var Qo,Wo;const Xo={selector:"h1,h2,h3,h4,h5,h6,ul,ol,li,table,pre,p>img:only-child",selectorRules:{"div,p":({$node:e})=>({queue:e.children()}),"h1,h2,h3,h4,h5,h6":({$node:e,getContent:t})=>({...t(e.contents())}),"ul,ol":({$node:e})=>({queue:e.children(),nesting:!0}),li:({$node:e,getContent:t})=>{const n=e.children().filter("ul,ol");let s;if(e.contents().first().is("div,p"))s=t(e.children().first());else{let r=e.contents();const i=r.index(n);i>=0&&(r=r.slice(0,i)),s=t(r)}return{queue:n,nesting:!0,...s}},"table,pre,p>img:only-child":({$node:e,getContent:t})=>({...t(e)})}},Zo="markmap: ",Jo=/^h[1-6]$/,ea=/^[uo]l$/,ta=/^li$/;function na(e,t){const n={...Xo,...t},s=zo(e),r=s("body");let i=0;const o={id:i,tag:"",html:"",level:0,parent:0,childrenLevel:0,children:[]},a=[];let c=0;return function e(t,r){t.each(((t,o)=>{var h;const d=s(o),p=null==(h=Object.entries(n.selectorRules).find((([e])=>d.is(e))))?void 0:h[1],f=null==p?void 0:p({$node:d,$:s,getContent:u});if((null==f?void 0:f.queue)&&!f.nesting)return void e(f.queue,r);const m=(E=o.tagName,Jo.test(E)?+E[1]:ea.test(E)?8:ta.test(E)?9:7);var E;if(!f)return void(m<=6&&(c=m));if(c>0&&m>c)return;if(!d.is(n.selector))return;c=0;const T=m<=6;let _={...d.closest("p").data(),...d.data()},A=f.html||"";if(d.is("ol>li")&&(null==r?void 0:r.children)){const e=+(d.parent().attr("start")||1)+r.children.length;A=`${e}. ${A}`,_={..._,listIndex:e}}const g=function(e){var t;const{parent:n}=e,s={id:++i,tag:e.tagName,level:e.level,html:e.html,childrenLevel:0,children:e.nesting?[]:void 0,parent:n.id};(null==(t=e.comments)?void 0:t.length)&&(s.comments=e.comments);Object.keys(e.data||{}).length&&(s.data=e.data);n.children&&((0===n.childrenLevel||n.childrenLevel>s.level)&&(n.children=[],n.childrenLevel=s.level),n.childrenLevel===s.level&&n.children.push(s));return s}({parent:r||l(m),nesting:!!f.queue||T,tagName:o.tagName,level:m,html:A,comments:f.comments,data:_});T&&a.push(g),f.queue&&e(f.queue,g)}))}(r.children()),o;function l(e){let t;for(;(t=a[a.length-1])&&t.level>=e;)a.pop();return t||o}function u(e){var t;const n=function(e){const t=[];return e=e.filter(((e,n)=>{if("comment"===n.type){const e=n.data.trim();if(e.startsWith(Zo))return t.push(e.slice(9).trim()),!1}return!0})),{$node:e,comments:t}}(e),r=null==(t=s.html(n.$node))?void 0:t.trimEnd();return{comments:n.comments,html:r}}}const sa={};function ra(e,t){"string"!=typeof t&&(t=ra.defaultChars);const n=function(e){let t=sa[e];if(t)return t;t=sa[e]=[];for(let e=0;e<128;e++){const n=String.fromCharCode(e);t.push(n)}for(let n=0;n<e.length;n++){const s=e.charCodeAt(n);t[s]="%"+("0"+s.toString(16).toUpperCase()).slice(-2)}return t}(t);return e.replace(/(%[a-f0-9]{2})+/gi,(function(e){let t="";for(let s=0,r=e.length;s<r;s+=3){const i=parseInt(e.slice(s+1,s+3),16);if(i<128)t+=n[i];else{if(192==(224&i)&&s+3<r){const n=parseInt(e.slice(s+4,s+6),16);if(128==(192&n)){const e=i<<6&1984|63&n;t+=e<128?"��":String.fromCharCode(e),s+=3;continue}}if(224==(240&i)&&s+6<r){const n=parseInt(e.slice(s+4,s+6),16),r=parseInt(e.slice(s+7,s+9),16);if(128==(192&n)&&128==(192&r)){const e=i<<12&61440|n<<6&4032|63&r;t+=e<2048||e>=55296&&e<=57343?"���":String.fromCharCode(e),s+=6;continue}}if(240==(248&i)&&s+9<r){const n=parseInt(e.slice(s+4,s+6),16),r=parseInt(e.slice(s+7,s+9),16),o=parseInt(e.slice(s+10,s+12),16);if(128==(192&n)&&128==(192&r)&&128==(192&o)){let e=i<<18&1835008|n<<12&258048|r<<6&4032|63&o;e<65536||e>1114111?t+="����":(e-=65536,t+=String.fromCharCode(55296+(e>>10),56320+(1023&e))),s+=9;continue}}t+="�"}}return t}))}ra.defaultChars=";/?:@&=+$,#",ra.componentChars="";const ia={};function oa(e,t,n){"string"!=typeof t&&(n=t,t=oa.defaultChars),void 0===n&&(n=!0);const s=function(e){let t=ia[e];if(t)return t;t=ia[e]=[];for(let e=0;e<128;e++){const n=String.fromCharCode(e);/^[0-9a-z]$/i.test(n)?t.push(n):t.push("%"+("0"+e.toString(16).toUpperCase()).slice(-2))}for(let n=0;n<e.length;n++)t[e.charCodeAt(n)]=e[n];return t}(t);let r="";for(let t=0,i=e.length;t<i;t++){const o=e.charCodeAt(t);if(n&&37===o&&t+2<i&&/^[0-9a-f]{2}$/i.test(e.slice(t+1,t+3)))r+=e.slice(t,t+3),t+=2;else if(o<128)r+=s[o];else if(o>=55296&&o<=57343){if(o>=55296&&o<=56319&&t+1<i){const n=e.charCodeAt(t+1);if(n>=56320&&n<=57343){r+=encodeURIComponent(e[t]+e[t+1]),t++;continue}}r+="%EF%BF%BD"}else r+=encodeURIComponent(e[t])}return r}function aa(e){let t="";return t+=e.protocol||"",t+=e.slashes?"//":"",t+=e.auth?e.auth+"@":"",e.hostname&&-1!==e.hostname.indexOf(":")?t+="["+e.hostname+"]":t+=e.hostname||"",t+=e.port?":"+e.port:"",t+=e.pathname||"",t+=e.search||"",t+=e.hash||"",t}function ca(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}oa.defaultChars=";/?:@&=+$,-_.!~*'()#",oa.componentChars="-_.!~*'()";const la=/^([a-z0-9.+-]+:)/i,ua=/:[0-9]*$/,ha=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,da=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),pa=["'"].concat(da),fa=["%","/","?",";","#"].concat(pa),ma=["/","?","#"],Ea=/^[+a-z0-9A-Z_-]{0,63}$/,Ta=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,_a={javascript:!0,"javascript:":!0},Aa={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function ga(e,t){if(e&&e instanceof ca)return e;const n=new ca;return n.parse(e,t),n}ca.prototype.parse=function(e,t){let n,s,r,i=e;if(i=i.trim(),!t&&1===e.split("#").length){const e=ha.exec(i);if(e)return this.pathname=e[1],e[2]&&(this.search=e[2]),this}let o=la.exec(i);if(o&&(o=o[0],n=o.toLowerCase(),this.protocol=o,i=i.substr(o.length)),(t||o||i.match(/^\/\/[^@\/]+@[^@\/]+/))&&(r="//"===i.substr(0,2),!r||o&&_a[o]||(i=i.substr(2),this.slashes=!0)),!_a[o]&&(r||o&&!Aa[o])){let e,t,n=-1;for(let e=0;e<ma.length;e++)s=i.indexOf(ma[e]),-1!==s&&(-1===n||s<n)&&(n=s);t=-1===n?i.lastIndexOf("@"):i.lastIndexOf("@",n),-1!==t&&(e=i.slice(0,t),i=i.slice(t+1),this.auth=e),n=-1;for(let e=0;e<fa.length;e++)s=i.indexOf(fa[e]),-1!==s&&(-1===n||s<n)&&(n=s);-1===n&&(n=i.length),":"===i[n-1]&&n--;const r=i.slice(0,n);i=i.slice(n),this.parseHost(r),this.hostname=this.hostname||"";const o="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!o){const e=this.hostname.split(/\./);for(let t=0,n=e.length;t<n;t++){const n=e[t];if(n&&!n.match(Ea)){let s="";for(let e=0,t=n.length;e<t;e++)n.charCodeAt(e)>127?s+="x":s+=n[e];if(!s.match(Ea)){const s=e.slice(0,t),r=e.slice(t+1),o=n.match(Ta);o&&(s.push(o[1]),r.unshift(o[2])),r.length&&(i=r.join(".")+i),this.hostname=s.join(".");break}}}}this.hostname.length>255&&(this.hostname=""),o&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}const a=i.indexOf("#");-1!==a&&(this.hash=i.substr(a),i=i.slice(0,a));const c=i.indexOf("?");return-1!==c&&(this.search=i.substr(c),i=i.slice(0,c)),i&&(this.pathname=i),Aa[n]&&this.hostname&&!this.pathname&&(this.pathname=""),this},ca.prototype.parseHost=function(e){let t=ua.exec(e);t&&(t=t[0],":"!==t&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)};const Ca=Object.freeze(Object.defineProperty({__proto__:null,decode:ra,encode:oa,format:aa,parse:ga},Symbol.toStringTag,{value:"Module"})),ba=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,ka=/[\0-\x1F\x7F-\x9F]/,Sa=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,Na=/[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/,Ia=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,Da=Object.freeze(Object.defineProperty({__proto__:null,Any:ba,Cc:ka,Cf:/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/,P:Sa,S:Na,Z:Ia},Symbol.toStringTag,{value:"Module"}));function ya(e){return"[object String]"===function(e){return Object.prototype.toString.call(e)}(e)}const Oa=Object.prototype.hasOwnProperty;function La(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach((function(n){e[n]=t[n]}))}})),e}function Ra(e,t,n){return[].concat(e.slice(0,t),n,e.slice(t+1))}function Ma(e){return!(e>=55296&&e<=57343)&&(!(e>=64976&&e<=65007)&&(!!(65535&~e&&65534!=(65535&e))&&(!(e>=0&&e<=8)&&(11!==e&&(!(e>=14&&e<=31)&&(!(e>=127&&e<=159)&&!(e>1114111)))))))}function va(e){if(e>65535){const t=55296+((e-=65536)>>10),n=56320+(1023&e);return String.fromCharCode(t,n)}return String.fromCharCode(e)}const xa=/\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,wa=new RegExp(xa.source+"|"+/&([a-z#][a-z0-9]{1,31});/gi.source,"gi"),Fa=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;function Pa(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(wa,(function(e,t,n){return t||function(e,t){if(35===t.charCodeAt(0)&&Fa.test(t)){const n="x"===t[1].toLowerCase()?parseInt(t.slice(2),16):parseInt(t.slice(1),10);return Ma(n)?va(n):e}const n=ge(e);return n!==e?n:e}(e,n)}))}const Ba=/[&<>"]/,Ua=/[&<>"]/g,Ha={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function Ga(e){return Ha[e]}function $a(e){return Ba.test(e)?e.replace(Ua,Ga):e}const qa=/[.?*+^$[\]\\(){}|-]/g;function Ya(e){switch(e){case 9:case 32:return!0}return!1}function ja(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}function Ka(e){return Sa.test(e)||Na.test(e)}function Va(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function za(e){return e=e.trim().replace(/\s+/g," "),"Ṿ"==="ẞ".toLowerCase()&&(e=e.replace(/ẞ/g,"ß")),e.toLowerCase().toUpperCase()}const Qa={mdurl:Ca,ucmicro:Da},Wa=Object.freeze(Object.defineProperty({__proto__:null,arrayReplaceAt:Ra,assign:La,escapeHtml:$a,escapeRE:function(e){return e.replace(qa,"\\$&")},fromCodePoint:va,has:function(e,t){return Oa.call(e,t)},isMdAsciiPunct:Va,isPunctChar:Ka,isSpace:Ya,isString:ya,isValidEntityCode:Ma,isWhiteSpace:ja,lib:Qa,normalizeReference:za,unescapeAll:Pa,unescapeMd:function(e){return e.indexOf("\\")<0?e:e.replace(xa,"$1")}},Symbol.toStringTag,{value:"Module"}));const Xa=Object.freeze(Object.defineProperty({__proto__:null,parseLinkDestination:function(e,t,n){let s,r=t;const i={ok:!1,pos:0,str:""};if(60===e.charCodeAt(r)){for(r++;r<n;){if(s=e.charCodeAt(r),10===s)return i;if(60===s)return i;if(62===s)return i.pos=r+1,i.str=Pa(e.slice(t+1,r)),i.ok=!0,i;92===s&&r+1<n?r+=2:r++}return i}let o=0;for(;r<n&&(s=e.charCodeAt(r),32!==s)&&!(s<32||127===s);)if(92===s&&r+1<n){if(32===e.charCodeAt(r+1))break;r+=2}else{if(40===s&&(o++,o>32))return i;if(41===s){if(0===o)break;o--}r++}return t===r||0!==o||(i.str=Pa(e.slice(t,r)),i.pos=r,i.ok=!0),i},parseLinkLabel:function(e,t,n){let s,r,i,o;const a=e.posMax,c=e.pos;for(e.pos=t+1,s=1;e.pos<a;){if(i=e.src.charCodeAt(e.pos),93===i&&(s--,0===s)){r=!0;break}if(o=e.pos,e.md.inline.skipToken(e),91===i)if(o===e.pos-1)s++;else if(n)return e.pos=c,-1}let l=-1;return r&&(l=e.pos),e.pos=c,l},parseLinkTitle:function(e,t,n,s){let r,i=t;const o={ok:!1,can_continue:!1,pos:0,str:"",marker:0};if(s)o.str=s.str,o.marker=s.marker;else{if(i>=n)return o;let s=e.charCodeAt(i);if(34!==s&&39!==s&&40!==s)return o;t++,i++,40===s&&(s=41),o.marker=s}for(;i<n;){if(r=e.charCodeAt(i),r===o.marker)return o.pos=i+1,o.str+=Pa(e.slice(t,i)),o.ok=!0,o;if(40===r&&41===o.marker)return o;92===r&&i+1<n&&i++,i++}return o.can_continue=!0,o.str+=Pa(e.slice(t,i)),o}},Symbol.toStringTag,{value:"Module"})),Za={};function Ja(){this.rules=La({},Za)}function ec(){this.__rules__=[],this.__cache__=null}function tc(e,t,n){this.type=e,this.tag=t,this.attrs=null,this.map=null,this.nesting=n,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}function nc(e,t,n){this.src=e,this.env=n,this.tokens=[],this.inlineMode=!1,this.md=t}Za.code_inline=function(e,t,n,s,r){const i=e[t];return"<code"+r.renderAttrs(i)+">"+$a(i.content)+"</code>"},Za.code_block=function(e,t,n,s,r){const i=e[t];return"<pre"+r.renderAttrs(i)+"><code>"+$a(e[t].content)+"</code></pre>\n"},Za.fence=function(e,t,n,s,r){const i=e[t],o=i.info?Pa(i.info).trim():"";let a,c="",l="";if(o){const e=o.split(/(\s+)/g);c=e[0],l=e.slice(2).join("")}if(a=n.highlight&&n.highlight(i.content,c,l)||$a(i.content),0===a.indexOf("<pre"))return a+"\n";if(o){const e=i.attrIndex("class"),t=i.attrs?i.attrs.slice():[];e<0?t.push(["class",n.langPrefix+c]):(t[e]=t[e].slice(),t[e][1]+=" "+n.langPrefix+c);const s={attrs:t};return`<pre><code${r.renderAttrs(s)}>${a}</code></pre>\n`}return`<pre><code${r.renderAttrs(i)}>${a}</code></pre>\n`},Za.image=function(e,t,n,s,r){const i=e[t];return i.attrs[i.attrIndex("alt")][1]=r.renderInlineAsText(i.children,n,s),r.renderToken(e,t,n)},Za.hardbreak=function(e,t,n){return n.xhtmlOut?"<br />\n":"<br>\n"},Za.softbreak=function(e,t,n){return n.breaks?n.xhtmlOut?"<br />\n":"<br>\n":"\n"},Za.text=function(e,t){return $a(e[t].content)},Za.html_block=function(e,t){return e[t].content},Za.html_inline=function(e,t){return e[t].content},Ja.prototype.renderAttrs=function(e){let t,n,s;if(!e.attrs)return"";for(s="",t=0,n=e.attrs.length;t<n;t++)s+=" "+$a(e.attrs[t][0])+'="'+$a(e.attrs[t][1])+'"';return s},Ja.prototype.renderToken=function(e,t,n){const s=e[t];let r="";if(s.hidden)return"";s.block&&-1!==s.nesting&&t&&e[t-1].hidden&&(r+="\n"),r+=(-1===s.nesting?"</":"<")+s.tag,r+=this.renderAttrs(s),0===s.nesting&&n.xhtmlOut&&(r+=" /");let i=!1;if(s.block&&(i=!0,1===s.nesting&&t+1<e.length)){const n=e[t+1];("inline"===n.type||n.hidden||-1===n.nesting&&n.tag===s.tag)&&(i=!1)}return r+=i?">\n":">",r},Ja.prototype.renderInline=function(e,t,n){let s="";const r=this.rules;for(let i=0,o=e.length;i<o;i++){const o=e[i].type;void 0!==r[o]?s+=r[o](e,i,t,n,this):s+=this.renderToken(e,i,t)}return s},Ja.prototype.renderInlineAsText=function(e,t,n){let s="";for(let r=0,i=e.length;r<i;r++)switch(e[r].type){case"text":case"html_inline":case"html_block":s+=e[r].content;break;case"image":s+=this.renderInlineAsText(e[r].children,t,n);break;case"softbreak":case"hardbreak":s+="\n"}return s},Ja.prototype.render=function(e,t,n){let s="";const r=this.rules;for(let i=0,o=e.length;i<o;i++){const o=e[i].type;"inline"===o?s+=this.renderInline(e[i].children,t,n):void 0!==r[o]?s+=r[o](e,i,t,n,this):s+=this.renderToken(e,i,t,n)}return s},ec.prototype.__find__=function(e){for(let t=0;t<this.__rules__.length;t++)if(this.__rules__[t].name===e)return t;return-1},ec.prototype.__compile__=function(){const e=this,t=[""];e.__rules__.forEach((function(e){e.enabled&&e.alt.forEach((function(e){t.indexOf(e)<0&&t.push(e)}))})),e.__cache__={},t.forEach((function(t){e.__cache__[t]=[],e.__rules__.forEach((function(n){n.enabled&&(t&&n.alt.indexOf(t)<0||e.__cache__[t].push(n.fn))}))}))},ec.prototype.at=function(e,t,n){const s=this.__find__(e),r=n||{};if(-1===s)throw new Error("Parser rule not found: "+e);this.__rules__[s].fn=t,this.__rules__[s].alt=r.alt||[],this.__cache__=null},ec.prototype.before=function(e,t,n,s){const r=this.__find__(e),i=s||{};if(-1===r)throw new Error("Parser rule not found: "+e);this.__rules__.splice(r,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},ec.prototype.after=function(e,t,n,s){const r=this.__find__(e),i=s||{};if(-1===r)throw new Error("Parser rule not found: "+e);this.__rules__.splice(r+1,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},ec.prototype.push=function(e,t,n){const s=n||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:s.alt||[]}),this.__cache__=null},ec.prototype.enable=function(e,t){Array.isArray(e)||(e=[e]);const n=[];return e.forEach((function(e){const s=this.__find__(e);if(s<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[s].enabled=!0,n.push(e)}),this),this.__cache__=null,n},ec.prototype.enableOnly=function(e,t){Array.isArray(e)||(e=[e]),this.__rules__.forEach((function(e){e.enabled=!1})),this.enable(e,t)},ec.prototype.disable=function(e,t){Array.isArray(e)||(e=[e]);const n=[];return e.forEach((function(e){const s=this.__find__(e);if(s<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[s].enabled=!1,n.push(e)}),this),this.__cache__=null,n},ec.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},tc.prototype.attrIndex=function(e){if(!this.attrs)return-1;const t=this.attrs;for(let n=0,s=t.length;n<s;n++)if(t[n][0]===e)return n;return-1},tc.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},tc.prototype.attrSet=function(e,t){const n=this.attrIndex(e),s=[e,t];n<0?this.attrPush(s):this.attrs[n]=s},tc.prototype.attrGet=function(e){const t=this.attrIndex(e);let n=null;return t>=0&&(n=this.attrs[t][1]),n},tc.prototype.attrJoin=function(e,t){const n=this.attrIndex(e);n<0?this.attrPush([e,t]):this.attrs[n][1]=this.attrs[n][1]+" "+t},nc.prototype.Token=tc;const sc=/\r\n?|\n/g,rc=/\0/g;function ic(e){return/^<\/a\s*>/i.test(e)}const oc=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,ac=/\((c|tm|r)\)/i,cc=/\((c|tm|r)\)/gi,lc={c:"©",r:"®",tm:"™"};function uc(e,t){return lc[t.toLowerCase()]}function hc(e){let t=0;for(let n=e.length-1;n>=0;n--){const s=e[n];"text"!==s.type||t||(s.content=s.content.replace(cc,uc)),"link_open"===s.type&&"auto"===s.info&&t--,"link_close"===s.type&&"auto"===s.info&&t++}}function dc(e){let t=0;for(let n=e.length-1;n>=0;n--){const s=e[n];"text"!==s.type||t||oc.test(s.content)&&(s.content=s.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/gm,"$1—").replace(/(^|\s)--(?=\s|$)/gm,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm,"$1–")),"link_open"===s.type&&"auto"===s.info&&t--,"link_close"===s.type&&"auto"===s.info&&t++}}const pc=/['"]/,fc=/['"]/g;function mc(e,t,n){return e.slice(0,t)+n+e.slice(t+1)}function Ec(e,t){let n;const s=[];for(let r=0;r<e.length;r++){const i=e[r],o=e[r].level;for(n=s.length-1;n>=0&&!(s[n].level<=o);n--);if(s.length=n+1,"text"!==i.type)continue;let a=i.content,c=0,l=a.length;e:for(;c<l;){fc.lastIndex=c;const u=fc.exec(a);if(!u)break;let h=!0,d=!0;c=u.index+1;const p="'"===u[0];let f=32;if(u.index-1>=0)f=a.charCodeAt(u.index-1);else for(n=r-1;n>=0&&("softbreak"!==e[n].type&&"hardbreak"!==e[n].type);n--)if(e[n].content){f=e[n].content.charCodeAt(e[n].content.length-1);break}let m=32;if(c<l)m=a.charCodeAt(c);else for(n=r+1;n<e.length&&("softbreak"!==e[n].type&&"hardbreak"!==e[n].type);n++)if(e[n].content){m=e[n].content.charCodeAt(0);break}const E=Va(f)||Ka(String.fromCharCode(f)),T=Va(m)||Ka(String.fromCharCode(m)),_=ja(f),A=ja(m);if(A?h=!1:T&&(_||E||(h=!1)),_?d=!1:E&&(A||T||(d=!1)),34===m&&'"'===u[0]&&f>=48&&f<=57&&(d=h=!1),h&&d&&(h=E,d=T),h||d){if(d)for(n=s.length-1;n>=0;n--){let h=s[n];if(s[n].level<o)break;if(h.single===p&&s[n].level===o){let o,d;h=s[n],p?(o=t.md.options.quotes[2],d=t.md.options.quotes[3]):(o=t.md.options.quotes[0],d=t.md.options.quotes[1]),i.content=mc(i.content,u.index,d),e[h.token].content=mc(e[h.token].content,h.pos,o),c+=d.length-1,h.token===r&&(c+=o.length-1),a=i.content,l=a.length,s.length=n;continue e}}h?s.push({token:r,pos:u.index,single:p,level:o}):d&&p&&(i.content=mc(i.content,u.index,"’"))}else p&&(i.content=mc(i.content,u.index,"’"))}}}const Tc=[["normalize",function(e){let t;t=e.src.replace(sc,"\n"),t=t.replace(rc,"�"),e.src=t}],["block",function(e){let t;e.inlineMode?(t=new e.Token("inline","",0),t.content=e.src,t.map=[0,1],t.children=[],e.tokens.push(t)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}],["inline",function(e){const t=e.tokens;for(let n=0,s=t.length;n<s;n++){const s=t[n];"inline"===s.type&&e.md.inline.parse(s.content,e.md,e.env,s.children)}}],["linkify",function(e){const t=e.tokens;var n;if(e.md.options.linkify)for(let s=0,r=t.length;s<r;s++){if("inline"!==t[s].type||!e.md.linkify.pretest(t[s].content))continue;let r=t[s].children,i=0;for(let o=r.length-1;o>=0;o--){const a=r[o];if("link_close"!==a.type){if("html_inline"===a.type&&(n=a.content,/^<a[>\s]/i.test(n)&&i>0&&i--,ic(a.content)&&i++),!(i>0)&&"text"===a.type&&e.md.linkify.test(a.content)){const n=a.content;let i=e.md.linkify.match(n);const c=[];let l=a.level,u=0;i.length>0&&0===i[0].index&&o>0&&"text_special"===r[o-1].type&&(i=i.slice(1));for(let t=0;t<i.length;t++){const s=i[t].url,r=e.md.normalizeLink(s);if(!e.md.validateLink(r))continue;let o=i[t].text;o=i[t].schema?"mailto:"!==i[t].schema||/^mailto:/i.test(o)?e.md.normalizeLinkText(o):e.md.normalizeLinkText("mailto:"+o).replace(/^mailto:/,""):e.md.normalizeLinkText("http://"+o).replace(/^http:\/\//,"");const a=i[t].index;if(a>u){const t=new e.Token("text","",0);t.content=n.slice(u,a),t.level=l,c.push(t)}const h=new e.Token("link_open","a",1);h.attrs=[["href",r]],h.level=l++,h.markup="linkify",h.info="auto",c.push(h);const d=new e.Token("text","",0);d.content=o,d.level=l,c.push(d);const p=new e.Token("link_close","a",-1);p.level=--l,p.markup="linkify",p.info="auto",c.push(p),u=i[t].lastIndex}if(u<n.length){const t=new e.Token("text","",0);t.content=n.slice(u),t.level=l,c.push(t)}t[s].children=r=Ra(r,o,c)}}else for(o--;r[o].level!==a.level&&"link_open"!==r[o].type;)o--}}}],["replacements",function(e){let t;if(e.md.options.typographer)for(t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&(ac.test(e.tokens[t].content)&&hc(e.tokens[t].children),oc.test(e.tokens[t].content)&&dc(e.tokens[t].children))}],["smartquotes",function(e){if(e.md.options.typographer)for(let t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&pc.test(e.tokens[t].content)&&Ec(e.tokens[t].children,e)}],["text_join",function(e){let t,n;const s=e.tokens,r=s.length;for(let e=0;e<r;e++){if("inline"!==s[e].type)continue;const r=s[e].children,i=r.length;for(t=0;t<i;t++)"text_special"===r[t].type&&(r[t].type="text");for(t=n=0;t<i;t++)"text"===r[t].type&&t+1<i&&"text"===r[t+1].type?r[t+1].content=r[t].content+r[t+1].content:(t!==n&&(r[n]=r[t]),n++);t!==n&&(r.length=n)}}]];function _c(){this.ruler=new ec;for(let e=0;e<Tc.length;e++)this.ruler.push(Tc[e][0],Tc[e][1])}function Ac(e,t,n,s){this.src=e,this.md=t,this.env=n,this.tokens=s,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0;const r=this.src;for(let e=0,t=0,n=0,s=0,i=r.length,o=!1;t<i;t++){const a=r.charCodeAt(t);if(!o){if(Ya(a)){n++,9===a?s+=4-s%4:s++;continue}o=!0}10!==a&&t!==i-1||(10!==a&&t++,this.bMarks.push(e),this.eMarks.push(t),this.tShift.push(n),this.sCount.push(s),this.bsCount.push(0),o=!1,n=0,s=0,e=t+1)}this.bMarks.push(r.length),this.eMarks.push(r.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}_c.prototype.process=function(e){const t=this.ruler.getRules("");for(let n=0,s=t.length;n<s;n++)t[n](e)},_c.prototype.State=nc,Ac.prototype.push=function(e,t,n){const s=new tc(e,t,n);return s.block=!0,n<0&&this.level--,s.level=this.level,n>0&&this.level++,this.tokens.push(s),s},Ac.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},Ac.prototype.skipEmptyLines=function(e){for(let t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},Ac.prototype.skipSpaces=function(e){for(let t=this.src.length;e<t;e++){if(!Ya(this.src.charCodeAt(e)))break}return e},Ac.prototype.skipSpacesBack=function(e,t){if(e<=t)return e;for(;e>t;)if(!Ya(this.src.charCodeAt(--e)))return e+1;return e},Ac.prototype.skipChars=function(e,t){for(let n=this.src.length;e<n&&this.src.charCodeAt(e)===t;e++);return e},Ac.prototype.skipCharsBack=function(e,t,n){if(e<=n)return e;for(;e>n;)if(t!==this.src.charCodeAt(--e))return e+1;return e},Ac.prototype.getLines=function(e,t,n,s){if(e>=t)return"";const r=new Array(t-e);for(let i=0,o=e;o<t;o++,i++){let e=0;const a=this.bMarks[o];let c,l=a;for(c=o+1<t||s?this.eMarks[o]+1:this.eMarks[o];l<c&&e<n;){const t=this.src.charCodeAt(l);if(Ya(t))9===t?e+=4-(e+this.bsCount[o])%4:e++;else{if(!(l-a<this.tShift[o]))break;e++}l++}r[i]=e>n?new Array(e-n+1).join(" ")+this.src.slice(l,c):this.src.slice(l,c)}return r.join("")},Ac.prototype.Token=tc;function gc(e,t){const n=e.bMarks[t]+e.tShift[t],s=e.eMarks[t];return e.src.slice(n,s)}function Cc(e){const t=[],n=e.length;let s=0,r=e.charCodeAt(s),i=!1,o=0,a="";for(;s<n;)124===r&&(i?(a+=e.substring(o,s-1),o=s):(t.push(a+e.substring(o,s)),a="",o=s+1)),i=92===r,s++,r=e.charCodeAt(s);return t.push(a+e.substring(o)),t}function bc(e,t){const n=e.eMarks[t];let s=e.bMarks[t]+e.tShift[t];const r=e.src.charCodeAt(s++);if(42!==r&&45!==r&&43!==r)return-1;if(s<n){if(!Ya(e.src.charCodeAt(s)))return-1}return s}function kc(e,t){const n=e.bMarks[t]+e.tShift[t],s=e.eMarks[t];let r=n;if(r+1>=s)return-1;let i=e.src.charCodeAt(r++);if(i<48||i>57)return-1;for(;;){if(r>=s)return-1;if(i=e.src.charCodeAt(r++),!(i>=48&&i<=57)){if(41===i||46===i)break;return-1}if(r-n>=10)return-1}return r<s&&(i=e.src.charCodeAt(r),!Ya(i))?-1:r}const Sc="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",Nc="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",Ic=new RegExp("^(?:"+Sc+"|"+Nc+"|\x3c!---?>|\x3c!--(?:[^-]|-[^-]|--[^>])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Za-z][^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"),Dc=new RegExp("^(?:"+Sc+"|"+Nc+")"),yc=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"].join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(Dc.source+"\\s*$"),/^$/,!1]];const Oc=[["table",function(e,t,n,s){if(t+2>n)return!1;let r=t+1;if(e.sCount[r]<e.blkIndent)return!1;if(e.sCount[r]-e.blkIndent>=4)return!1;let i=e.bMarks[r]+e.tShift[r];if(i>=e.eMarks[r])return!1;const o=e.src.charCodeAt(i++);if(124!==o&&45!==o&&58!==o)return!1;if(i>=e.eMarks[r])return!1;const a=e.src.charCodeAt(i++);if(124!==a&&45!==a&&58!==a&&!Ya(a))return!1;if(45===o&&Ya(a))return!1;for(;i<e.eMarks[r];){const t=e.src.charCodeAt(i);if(124!==t&&45!==t&&58!==t&&!Ya(t))return!1;i++}let c=gc(e,t+1),l=c.split("|");const u=[];for(let e=0;e<l.length;e++){const t=l[e].trim();if(!t){if(0===e||e===l.length-1)continue;return!1}if(!/^:?-+:?$/.test(t))return!1;58===t.charCodeAt(t.length-1)?u.push(58===t.charCodeAt(0)?"center":"right"):58===t.charCodeAt(0)?u.push("left"):u.push("")}if(c=gc(e,t).trim(),-1===c.indexOf("|"))return!1;if(e.sCount[t]-e.blkIndent>=4)return!1;l=Cc(c),l.length&&""===l[0]&&l.shift(),l.length&&""===l[l.length-1]&&l.pop();const h=l.length;if(0===h||h!==u.length)return!1;if(s)return!0;const d=e.parentType;e.parentType="table";const p=e.md.block.ruler.getRules("blockquote"),f=[t,0];e.push("table_open","table",1).map=f,e.push("thead_open","thead",1).map=[t,t+1],e.push("tr_open","tr",1).map=[t,t+1];for(let t=0;t<l.length;t++){const n=e.push("th_open","th",1);u[t]&&(n.attrs=[["style","text-align:"+u[t]]]);const s=e.push("inline","",0);s.content=l[t].trim(),s.children=[],e.push("th_close","th",-1)}let m;e.push("tr_close","tr",-1),e.push("thead_close","thead",-1);let E=0;for(r=t+2;r<n&&!(e.sCount[r]<e.blkIndent);r++){let s=!1;for(let t=0,i=p.length;t<i;t++)if(p[t](e,r,n,!0)){s=!0;break}if(s)break;if(c=gc(e,r).trim(),!c)break;if(e.sCount[r]-e.blkIndent>=4)break;if(l=Cc(c),l.length&&""===l[0]&&l.shift(),l.length&&""===l[l.length-1]&&l.pop(),E+=h-l.length,E>65536)break;if(r===t+2){e.push("tbody_open","tbody",1).map=m=[t+2,0]}e.push("tr_open","tr",1).map=[r,r+1];for(let t=0;t<h;t++){const n=e.push("td_open","td",1);u[t]&&(n.attrs=[["style","text-align:"+u[t]]]);const s=e.push("inline","",0);s.content=l[t]?l[t].trim():"",s.children=[],e.push("td_close","td",-1)}e.push("tr_close","tr",-1)}return m&&(e.push("tbody_close","tbody",-1),m[1]=r),e.push("table_close","table",-1),f[1]=r,e.parentType=d,e.line=r,!0},["paragraph","reference"]],["code",function(e,t,n){if(e.sCount[t]-e.blkIndent<4)return!1;let s=t+1,r=s;for(;s<n;)if(e.isEmpty(s))s++;else{if(!(e.sCount[s]-e.blkIndent>=4))break;s++,r=s}e.line=r;const i=e.push("code_block","code",0);return i.content=e.getLines(t,r,4+e.blkIndent,!1)+"\n",i.map=[t,e.line],!0}],["fence",function(e,t,n,s){let r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(r+3>i)return!1;const o=e.src.charCodeAt(r);if(126!==o&&96!==o)return!1;let a=r;r=e.skipChars(r,o);let c=r-a;if(c<3)return!1;const l=e.src.slice(a,r),u=e.src.slice(r,i);if(96===o&&u.indexOf(String.fromCharCode(o))>=0)return!1;if(s)return!0;let h=t,d=!1;for(;(h++,!(h>=n))&&(r=a=e.bMarks[h]+e.tShift[h],i=e.eMarks[h],!(r<i&&e.sCount[h]<e.blkIndent));)if(e.src.charCodeAt(r)===o&&!(e.sCount[h]-e.blkIndent>=4||(r=e.skipChars(r,o),r-a<c||(r=e.skipSpaces(r),r<i)))){d=!0;break}c=e.sCount[t],e.line=h+(d?1:0);const p=e.push("fence","code",0);return p.info=u,p.content=e.getLines(t+1,h,c,!0),p.markup=l,p.map=[t,e.line],!0},["paragraph","reference","blockquote","list"]],["blockquote",function(e,t,n,s){let r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];const o=e.lineMax;if(e.sCount[t]-e.blkIndent>=4)return!1;if(62!==e.src.charCodeAt(r))return!1;if(s)return!0;const a=[],c=[],l=[],u=[],h=e.md.block.ruler.getRules("blockquote"),d=e.parentType;e.parentType="blockquote";let p,f=!1;for(p=t;p<n;p++){const t=e.sCount[p]<e.blkIndent;if(r=e.bMarks[p]+e.tShift[p],i=e.eMarks[p],r>=i)break;if(62===e.src.charCodeAt(r++)&&!t){let t,n,s=e.sCount[p]+1;32===e.src.charCodeAt(r)?(r++,s++,n=!1,t=!0):9===e.src.charCodeAt(r)?(t=!0,(e.bsCount[p]+s)%4==3?(r++,s++,n=!1):n=!0):t=!1;let o=s;for(a.push(e.bMarks[p]),e.bMarks[p]=r;r<i;){const t=e.src.charCodeAt(r);if(!Ya(t))break;9===t?o+=4-(o+e.bsCount[p]+(n?1:0))%4:o++,r++}f=r>=i,c.push(e.bsCount[p]),e.bsCount[p]=e.sCount[p]+1+(t?1:0),l.push(e.sCount[p]),e.sCount[p]=o-s,u.push(e.tShift[p]),e.tShift[p]=r-e.bMarks[p];continue}if(f)break;let s=!1;for(let t=0,r=h.length;t<r;t++)if(h[t](e,p,n,!0)){s=!0;break}if(s){e.lineMax=p,0!==e.blkIndent&&(a.push(e.bMarks[p]),c.push(e.bsCount[p]),u.push(e.tShift[p]),l.push(e.sCount[p]),e.sCount[p]-=e.blkIndent);break}a.push(e.bMarks[p]),c.push(e.bsCount[p]),u.push(e.tShift[p]),l.push(e.sCount[p]),e.sCount[p]=-1}const m=e.blkIndent;e.blkIndent=0;const E=e.push("blockquote_open","blockquote",1);E.markup=">";const T=[t,0];E.map=T,e.md.block.tokenize(e,t,p),e.push("blockquote_close","blockquote",-1).markup=">",e.lineMax=o,e.parentType=d,T[1]=e.line;for(let n=0;n<u.length;n++)e.bMarks[n+t]=a[n],e.tShift[n+t]=u[n],e.sCount[n+t]=l[n],e.bsCount[n+t]=c[n];return e.blkIndent=m,!0},["paragraph","reference","blockquote","list"]],["hr",function(e,t,n,s){const r=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;let i=e.bMarks[t]+e.tShift[t];const o=e.src.charCodeAt(i++);if(42!==o&&45!==o&&95!==o)return!1;let a=1;for(;i<r;){const t=e.src.charCodeAt(i++);if(t!==o&&!Ya(t))return!1;t===o&&a++}if(a<3)return!1;if(s)return!0;e.line=t+1;const c=e.push("hr","hr",0);return c.map=[t,e.line],c.markup=Array(a+1).join(String.fromCharCode(o)),!0},["paragraph","reference","blockquote","list"]],["list",function(e,t,n,s){let r,i,o,a,c=t,l=!0;if(e.sCount[c]-e.blkIndent>=4)return!1;if(e.listIndent>=0&&e.sCount[c]-e.listIndent>=4&&e.sCount[c]<e.blkIndent)return!1;let u,h,d,p=!1;if(s&&"paragraph"===e.parentType&&e.sCount[c]>=e.blkIndent&&(p=!0),(d=kc(e,c))>=0){if(u=!0,o=e.bMarks[c]+e.tShift[c],h=Number(e.src.slice(o,d-1)),p&&1!==h)return!1}else{if(!((d=bc(e,c))>=0))return!1;u=!1}if(p&&e.skipSpaces(d)>=e.eMarks[c])return!1;if(s)return!0;const f=e.src.charCodeAt(d-1),m=e.tokens.length;u?(a=e.push("ordered_list_open","ol",1),1!==h&&(a.attrs=[["start",h]])):a=e.push("bullet_list_open","ul",1);const E=[c,0];a.map=E,a.markup=String.fromCharCode(f);let T=!1;const _=e.md.block.ruler.getRules("list"),A=e.parentType;for(e.parentType="list";c<n;){i=d,r=e.eMarks[c];const t=e.sCount[c]+d-(e.bMarks[c]+e.tShift[c]);let s=t;for(;i<r;){const t=e.src.charCodeAt(i);if(9===t)s+=4-(s+e.bsCount[c])%4;else{if(32!==t)break;s++}i++}const h=i;let p;p=h>=r?1:s-t,p>4&&(p=1);const m=t+p;a=e.push("list_item_open","li",1),a.markup=String.fromCharCode(f);const E=[c,0];a.map=E,u&&(a.info=e.src.slice(o,d-1));const A=e.tight,g=e.tShift[c],C=e.sCount[c],b=e.listIndent;if(e.listIndent=e.blkIndent,e.blkIndent=m,e.tight=!0,e.tShift[c]=h-e.bMarks[c],e.sCount[c]=s,h>=r&&e.isEmpty(c+1)?e.line=Math.min(e.line+2,n):e.md.block.tokenize(e,c,n,!0),e.tight&&!T||(l=!1),T=e.line-c>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=b,e.tShift[c]=g,e.sCount[c]=C,e.tight=A,a=e.push("list_item_close","li",-1),a.markup=String.fromCharCode(f),c=e.line,E[1]=c,c>=n)break;if(e.sCount[c]<e.blkIndent)break;if(e.sCount[c]-e.blkIndent>=4)break;let k=!1;for(let t=0,s=_.length;t<s;t++)if(_[t](e,c,n,!0)){k=!0;break}if(k)break;if(u){if(d=kc(e,c),d<0)break;o=e.bMarks[c]+e.tShift[c]}else if(d=bc(e,c),d<0)break;if(f!==e.src.charCodeAt(d-1))break}return a=u?e.push("ordered_list_close","ol",-1):e.push("bullet_list_close","ul",-1),a.markup=String.fromCharCode(f),E[1]=c,e.line=c,e.parentType=A,l&&function(e,t){const n=e.level+2;for(let s=t+2,r=e.tokens.length-2;s<r;s++)e.tokens[s].level===n&&"paragraph_open"===e.tokens[s].type&&(e.tokens[s+2].hidden=!0,e.tokens[s].hidden=!0,s+=2)}(e,m),!0},["paragraph","reference","blockquote"]],["reference",function(e,t,n,s){let r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t],o=t+1;if(e.sCount[t]-e.blkIndent>=4)return!1;if(91!==e.src.charCodeAt(r))return!1;function a(t){const n=e.lineMax;if(t>=n||e.isEmpty(t))return null;let s=!1;if(e.sCount[t]-e.blkIndent>3&&(s=!0),e.sCount[t]<0&&(s=!0),!s){const s=e.md.block.ruler.getRules("reference"),r=e.parentType;e.parentType="reference";let i=!1;for(let r=0,o=s.length;r<o;r++)if(s[r](e,t,n,!0)){i=!0;break}if(e.parentType=r,i)return null}const r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];return e.src.slice(r,i+1)}let c=e.src.slice(r,i+1);i=c.length;let l=-1;for(r=1;r<i;r++){const e=c.charCodeAt(r);if(91===e)return!1;if(93===e){l=r;break}if(10===e){const e=a(o);null!==e&&(c+=e,i=c.length,o++)}else if(92===e&&(r++,r<i&&10===c.charCodeAt(r))){const e=a(o);null!==e&&(c+=e,i=c.length,o++)}}if(l<0||58!==c.charCodeAt(l+1))return!1;for(r=l+2;r<i;r++){const e=c.charCodeAt(r);if(10===e){const e=a(o);null!==e&&(c+=e,i=c.length,o++)}else if(!Ya(e))break}const u=e.md.helpers.parseLinkDestination(c,r,i);if(!u.ok)return!1;const h=e.md.normalizeLink(u.str);if(!e.md.validateLink(h))return!1;r=u.pos;const d=r,p=o,f=r;for(;r<i;r++){const e=c.charCodeAt(r);if(10===e){const e=a(o);null!==e&&(c+=e,i=c.length,o++)}else if(!Ya(e))break}let m,E=e.md.helpers.parseLinkTitle(c,r,i);for(;E.can_continue;){const t=a(o);if(null===t)break;c+=t,r=i,i=c.length,o++,E=e.md.helpers.parseLinkTitle(c,r,i,E)}for(r<i&&f!==r&&E.ok?(m=E.str,r=E.pos):(m="",r=d,o=p);r<i;){if(!Ya(c.charCodeAt(r)))break;r++}if(r<i&&10!==c.charCodeAt(r)&&m)for(m="",r=d,o=p;r<i;){if(!Ya(c.charCodeAt(r)))break;r++}if(r<i&&10!==c.charCodeAt(r))return!1;const T=za(c.slice(1,l));return!!T&&(s||(void 0===e.env.references&&(e.env.references={}),void 0===e.env.references[T]&&(e.env.references[T]={title:m,href:h}),e.line=o),!0)}],["html_block",function(e,t,n,s){let r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(!e.md.options.html)return!1;if(60!==e.src.charCodeAt(r))return!1;let o=e.src.slice(r,i),a=0;for(;a<yc.length&&!yc[a][0].test(o);a++);if(a===yc.length)return!1;if(s)return yc[a][2];let c=t+1;if(!yc[a][1].test(o))for(;c<n&&!(e.sCount[c]<e.blkIndent);c++)if(r=e.bMarks[c]+e.tShift[c],i=e.eMarks[c],o=e.src.slice(r,i),yc[a][1].test(o)){0!==o.length&&c++;break}e.line=c;const l=e.push("html_block","",0);return l.map=[t,c],l.content=e.getLines(t,c,e.blkIndent,!0),!0},["paragraph","reference","blockquote"]],["heading",function(e,t,n,s){let r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;let o=e.src.charCodeAt(r);if(35!==o||r>=i)return!1;let a=1;for(o=e.src.charCodeAt(++r);35===o&&r<i&&a<=6;)a++,o=e.src.charCodeAt(++r);if(a>6||r<i&&!Ya(o))return!1;if(s)return!0;i=e.skipSpacesBack(i,r);const c=e.skipCharsBack(i,35,r);c>r&&Ya(e.src.charCodeAt(c-1))&&(i=c),e.line=t+1;const l=e.push("heading_open","h"+String(a),1);l.markup="########".slice(0,a),l.map=[t,e.line];const u=e.push("inline","",0);return u.content=e.src.slice(r,i).trim(),u.map=[t,e.line],u.children=[],e.push("heading_close","h"+String(a),-1).markup="########".slice(0,a),!0},["paragraph","reference","blockquote"]],["lheading",function(e,t,n){const s=e.md.block.ruler.getRules("paragraph");if(e.sCount[t]-e.blkIndent>=4)return!1;const r=e.parentType;e.parentType="paragraph";let i,o=0,a=t+1;for(;a<n&&!e.isEmpty(a);a++){if(e.sCount[a]-e.blkIndent>3)continue;if(e.sCount[a]>=e.blkIndent){let t=e.bMarks[a]+e.tShift[a];const n=e.eMarks[a];if(t<n&&(i=e.src.charCodeAt(t),(45===i||61===i)&&(t=e.skipChars(t,i),t=e.skipSpaces(t),t>=n))){o=61===i?1:2;break}}if(e.sCount[a]<0)continue;let t=!1;for(let r=0,i=s.length;r<i;r++)if(s[r](e,a,n,!0)){t=!0;break}if(t)break}if(!o)return!1;const c=e.getLines(t,a,e.blkIndent,!1).trim();e.line=a+1;const l=e.push("heading_open","h"+String(o),1);l.markup=String.fromCharCode(i),l.map=[t,e.line];const u=e.push("inline","",0);return u.content=c,u.map=[t,e.line-1],u.children=[],e.push("heading_close","h"+String(o),-1).markup=String.fromCharCode(i),e.parentType=r,!0}],["paragraph",function(e,t,n){const s=e.md.block.ruler.getRules("paragraph"),r=e.parentType;let i=t+1;for(e.parentType="paragraph";i<n&&!e.isEmpty(i);i++){if(e.sCount[i]-e.blkIndent>3)continue;if(e.sCount[i]<0)continue;let t=!1;for(let r=0,o=s.length;r<o;r++)if(s[r](e,i,n,!0)){t=!0;break}if(t)break}const o=e.getLines(t,i,e.blkIndent,!1).trim();e.line=i,e.push("paragraph_open","p",1).map=[t,e.line];const a=e.push("inline","",0);return a.content=o,a.map=[t,e.line],a.children=[],e.push("paragraph_close","p",-1),e.parentType=r,!0}]];function Lc(){this.ruler=new ec;for(let e=0;e<Oc.length;e++)this.ruler.push(Oc[e][0],Oc[e][1],{alt:(Oc[e][2]||[]).slice()})}function Rc(e,t,n,s){this.src=e,this.env=n,this.md=t,this.tokens=s,this.tokens_meta=Array(s.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}function Mc(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}Lc.prototype.tokenize=function(e,t,n){const s=this.ruler.getRules(""),r=s.length,i=e.md.options.maxNesting;let o=t,a=!1;for(;o<n&&(e.line=o=e.skipEmptyLines(o),!(o>=n))&&!(e.sCount[o]<e.blkIndent);){if(e.level>=i){e.line=n;break}const t=e.line;let c=!1;for(let i=0;i<r;i++)if(c=s[i](e,o,n,!1),c){if(t>=e.line)throw new Error("block rule didn't increment state.line");break}if(!c)throw new Error("none of the block rules matched");e.tight=!a,e.isEmpty(e.line-1)&&(a=!0),o=e.line,o<n&&e.isEmpty(o)&&(a=!0,o++,e.line=o)}},Lc.prototype.parse=function(e,t,n,s){if(!e)return;const r=new this.State(e,t,n,s);this.tokenize(r,r.line,r.lineMax)},Lc.prototype.State=Ac,Rc.prototype.pushPending=function(){const e=new tc("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e},Rc.prototype.push=function(e,t,n){this.pending&&this.pushPending();const s=new tc(e,t,n);let r=null;return n<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),s.level=this.level,n>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],r={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(s),this.tokens_meta.push(r),s},Rc.prototype.scanDelims=function(e,t){const n=this.posMax,s=this.src.charCodeAt(e),r=e>0?this.src.charCodeAt(e-1):32;let i=e;for(;i<n&&this.src.charCodeAt(i)===s;)i++;const o=i-e,a=i<n?this.src.charCodeAt(i):32,c=Va(r)||Ka(String.fromCharCode(r)),l=Va(a)||Ka(String.fromCharCode(a)),u=ja(r),h=ja(a),d=!h&&(!l||u||c),p=!u&&(!c||h||l);return{can_open:d&&(t||!p||c),can_close:p&&(t||!d||l),length:o}},Rc.prototype.Token=tc;const vc=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;const xc=[];for(let e=0;e<256;e++)xc.push(0);function wc(e,t){let n;const s=[],r=t.length;for(let i=0;i<r;i++){const r=t[i];if(126!==r.marker)continue;if(-1===r.end)continue;const o=t[r.end];n=e.tokens[r.token],n.type="s_open",n.tag="s",n.nesting=1,n.markup="~~",n.content="",n=e.tokens[o.token],n.type="s_close",n.tag="s",n.nesting=-1,n.markup="~~",n.content="","text"===e.tokens[o.token-1].type&&"~"===e.tokens[o.token-1].content&&s.push(o.token-1)}for(;s.length;){const t=s.pop();let r=t+1;for(;r<e.tokens.length&&"s_close"===e.tokens[r].type;)r++;r--,t!==r&&(n=e.tokens[r],e.tokens[r]=e.tokens[t],e.tokens[t]=n)}}"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(e){xc[e.charCodeAt(0)]=1}));const Fc={tokenize:function(e,t){const n=e.pos,s=e.src.charCodeAt(n);if(t)return!1;if(126!==s)return!1;const r=e.scanDelims(e.pos,!0);let i=r.length;const o=String.fromCharCode(s);if(i<2)return!1;let a;i%2&&(a=e.push("text","",0),a.content=o,i--);for(let t=0;t<i;t+=2)a=e.push("text","",0),a.content=o+o,e.delimiters.push({marker:s,length:0,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close});return e.pos+=r.length,!0},postProcess:function(e){const t=e.tokens_meta,n=e.tokens_meta.length;wc(e,e.delimiters);for(let s=0;s<n;s++)t[s]&&t[s].delimiters&&wc(e,t[s].delimiters)}};function Pc(e,t){for(let n=t.length-1;n>=0;n--){const s=t[n];if(95!==s.marker&&42!==s.marker)continue;if(-1===s.end)continue;const r=t[s.end],i=n>0&&t[n-1].end===s.end+1&&t[n-1].marker===s.marker&&t[n-1].token===s.token-1&&t[s.end+1].token===r.token+1,o=String.fromCharCode(s.marker),a=e.tokens[s.token];a.type=i?"strong_open":"em_open",a.tag=i?"strong":"em",a.nesting=1,a.markup=i?o+o:o,a.content="";const c=e.tokens[r.token];c.type=i?"strong_close":"em_close",c.tag=i?"strong":"em",c.nesting=-1,c.markup=i?o+o:o,c.content="",i&&(e.tokens[t[n-1].token].content="",e.tokens[t[s.end+1].token].content="",n--)}}const Bc={tokenize:function(e,t){const n=e.pos,s=e.src.charCodeAt(n);if(t)return!1;if(95!==s&&42!==s)return!1;const r=e.scanDelims(e.pos,42===s);for(let t=0;t<r.length;t++){e.push("text","",0).content=String.fromCharCode(s),e.delimiters.push({marker:s,length:r.length,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close})}return e.pos+=r.length,!0},postProcess:function(e){const t=e.tokens_meta,n=e.tokens_meta.length;Pc(e,e.delimiters);for(let s=0;s<n;s++)t[s]&&t[s].delimiters&&Pc(e,t[s].delimiters)}};const Uc=/^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,Hc=/^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/;const Gc=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,$c=/^&([a-z][a-z0-9]{1,31});/i;function qc(e){const t={},n=e.length;if(!n)return;let s=0,r=-2;const i=[];for(let o=0;o<n;o++){const n=e[o];if(i.push(0),e[s].marker===n.marker&&r===n.token-1||(s=o),r=n.token,n.length=n.length||0,!n.close)continue;t.hasOwnProperty(n.marker)||(t[n.marker]=[-1,-1,-1,-1,-1,-1]);const a=t[n.marker][(n.open?3:0)+n.length%3];let c=s-i[s]-1,l=c;for(;c>a;c-=i[c]+1){const t=e[c];if(t.marker===n.marker&&(t.open&&t.end<0)){let s=!1;if((t.close||n.open)&&(t.length+n.length)%3==0&&(t.length%3==0&&n.length%3==0||(s=!0)),!s){const s=c>0&&!e[c-1].open?i[c-1]+1:0;i[o]=o-c+s,i[c]=s,n.open=!1,t.end=o,t.close=!1,l=-1,r=-2;break}}}-1!==l&&(t[n.marker][(n.open?3:0)+(n.length||0)%3]=l)}}const Yc=[["text",function(e,t){let n=e.pos;for(;n<e.posMax&&!Mc(e.src.charCodeAt(n));)n++;return n!==e.pos&&(t||(e.pending+=e.src.slice(e.pos,n)),e.pos=n,!0)}],["linkify",function(e,t){if(!e.md.options.linkify)return!1;if(e.linkLevel>0)return!1;const n=e.pos;if(n+3>e.posMax)return!1;if(58!==e.src.charCodeAt(n))return!1;if(47!==e.src.charCodeAt(n+1))return!1;if(47!==e.src.charCodeAt(n+2))return!1;const s=e.pending.match(vc);if(!s)return!1;const r=s[1],i=e.md.linkify.matchAtStart(e.src.slice(n-r.length));if(!i)return!1;let o=i.url;if(o.length<=r.length)return!1;o=o.replace(/\*+$/,"");const a=e.md.normalizeLink(o);if(!e.md.validateLink(a))return!1;if(!t){e.pending=e.pending.slice(0,-r.length);const t=e.push("link_open","a",1);t.attrs=[["href",a]],t.markup="linkify",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(o);const n=e.push("link_close","a",-1);n.markup="linkify",n.info="auto"}return e.pos+=o.length-r.length,!0}],["newline",function(e,t){let n=e.pos;if(10!==e.src.charCodeAt(n))return!1;const s=e.pending.length-1,r=e.posMax;if(!t)if(s>=0&&32===e.pending.charCodeAt(s))if(s>=1&&32===e.pending.charCodeAt(s-1)){let t=s-1;for(;t>=1&&32===e.pending.charCodeAt(t-1);)t--;e.pending=e.pending.slice(0,t),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(n++;n<r&&Ya(e.src.charCodeAt(n));)n++;return e.pos=n,!0}],["escape",function(e,t){let n=e.pos;const s=e.posMax;if(92!==e.src.charCodeAt(n))return!1;if(n++,n>=s)return!1;let r=e.src.charCodeAt(n);if(10===r){for(t||e.push("hardbreak","br",0),n++;n<s&&(r=e.src.charCodeAt(n),Ya(r));)n++;return e.pos=n,!0}let i=e.src[n];if(r>=55296&&r<=56319&&n+1<s){const t=e.src.charCodeAt(n+1);t>=56320&&t<=57343&&(i+=e.src[n+1],n++)}const o="\\"+i;if(!t){const t=e.push("text_special","",0);r<256&&0!==xc[r]?t.content=i:t.content=o,t.markup=o,t.info="escape"}return e.pos=n+1,!0}],["backticks",function(e,t){let n=e.pos;if(96!==e.src.charCodeAt(n))return!1;const s=n;n++;const r=e.posMax;for(;n<r&&96===e.src.charCodeAt(n);)n++;const i=e.src.slice(s,n),o=i.length;if(e.backticksScanned&&(e.backticks[o]||0)<=s)return t||(e.pending+=i),e.pos+=o,!0;let a,c=n;for(;-1!==(a=e.src.indexOf("`",c));){for(c=a+1;c<r&&96===e.src.charCodeAt(c);)c++;const s=c-a;if(s===o){if(!t){const t=e.push("code_inline","code",0);t.markup=i,t.content=e.src.slice(n,a).replace(/\n/g," ").replace(/^ (.+) $/,"$1")}return e.pos=c,!0}e.backticks[s]=a}return e.backticksScanned=!0,t||(e.pending+=i),e.pos+=o,!0}],["strikethrough",Fc.tokenize],["emphasis",Bc.tokenize],["link",function(e,t){let n,s,r,i,o="",a="",c=e.pos,l=!0;if(91!==e.src.charCodeAt(e.pos))return!1;const u=e.pos,h=e.posMax,d=e.pos+1,p=e.md.helpers.parseLinkLabel(e,e.pos,!0);if(p<0)return!1;let f=p+1;if(f<h&&40===e.src.charCodeAt(f)){for(l=!1,f++;f<h&&(n=e.src.charCodeAt(f),Ya(n)||10===n);f++);if(f>=h)return!1;if(c=f,r=e.md.helpers.parseLinkDestination(e.src,f,e.posMax),r.ok){for(o=e.md.normalizeLink(r.str),e.md.validateLink(o)?f=r.pos:o="",c=f;f<h&&(n=e.src.charCodeAt(f),Ya(n)||10===n);f++);if(r=e.md.helpers.parseLinkTitle(e.src,f,e.posMax),f<h&&c!==f&&r.ok)for(a=r.str,f=r.pos;f<h&&(n=e.src.charCodeAt(f),Ya(n)||10===n);f++);}(f>=h||41!==e.src.charCodeAt(f))&&(l=!0),f++}if(l){if(void 0===e.env.references)return!1;if(f<h&&91===e.src.charCodeAt(f)?(c=f+1,f=e.md.helpers.parseLinkLabel(e,f),f>=0?s=e.src.slice(c,f++):f=p+1):f=p+1,s||(s=e.src.slice(d,p)),i=e.env.references[za(s)],!i)return e.pos=u,!1;o=i.href,a=i.title}if(!t){e.pos=d,e.posMax=p;const t=[["href",o]];e.push("link_open","a",1).attrs=t,a&&t.push(["title",a]),e.linkLevel++,e.md.inline.tokenize(e),e.linkLevel--,e.push("link_close","a",-1)}return e.pos=f,e.posMax=h,!0}],["image",function(e,t){let n,s,r,i,o,a,c,l,u="";const h=e.pos,d=e.posMax;if(33!==e.src.charCodeAt(e.pos))return!1;if(91!==e.src.charCodeAt(e.pos+1))return!1;const p=e.pos+2,f=e.md.helpers.parseLinkLabel(e,e.pos+1,!1);if(f<0)return!1;if(i=f+1,i<d&&40===e.src.charCodeAt(i)){for(i++;i<d&&(n=e.src.charCodeAt(i),Ya(n)||10===n);i++);if(i>=d)return!1;for(l=i,a=e.md.helpers.parseLinkDestination(e.src,i,e.posMax),a.ok&&(u=e.md.normalizeLink(a.str),e.md.validateLink(u)?i=a.pos:u=""),l=i;i<d&&(n=e.src.charCodeAt(i),Ya(n)||10===n);i++);if(a=e.md.helpers.parseLinkTitle(e.src,i,e.posMax),i<d&&l!==i&&a.ok)for(c=a.str,i=a.pos;i<d&&(n=e.src.charCodeAt(i),Ya(n)||10===n);i++);else c="";if(i>=d||41!==e.src.charCodeAt(i))return e.pos=h,!1;i++}else{if(void 0===e.env.references)return!1;if(i<d&&91===e.src.charCodeAt(i)?(l=i+1,i=e.md.helpers.parseLinkLabel(e,i),i>=0?r=e.src.slice(l,i++):i=f+1):i=f+1,r||(r=e.src.slice(p,f)),o=e.env.references[za(r)],!o)return e.pos=h,!1;u=o.href,c=o.title}if(!t){s=e.src.slice(p,f);const t=[];e.md.inline.parse(s,e.md,e.env,t);const n=e.push("image","img",0),r=[["src",u],["alt",""]];n.attrs=r,n.children=t,n.content=s,c&&r.push(["title",c])}return e.pos=i,e.posMax=d,!0}],["autolink",function(e,t){let n=e.pos;if(60!==e.src.charCodeAt(n))return!1;const s=e.pos,r=e.posMax;for(;;){if(++n>=r)return!1;const t=e.src.charCodeAt(n);if(60===t)return!1;if(62===t)break}const i=e.src.slice(s+1,n);if(Hc.test(i)){const n=e.md.normalizeLink(i);if(!e.md.validateLink(n))return!1;if(!t){const t=e.push("link_open","a",1);t.attrs=[["href",n]],t.markup="autolink",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(i);const s=e.push("link_close","a",-1);s.markup="autolink",s.info="auto"}return e.pos+=i.length+2,!0}if(Uc.test(i)){const n=e.md.normalizeLink("mailto:"+i);if(!e.md.validateLink(n))return!1;if(!t){const t=e.push("link_open","a",1);t.attrs=[["href",n]],t.markup="autolink",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(i);const s=e.push("link_close","a",-1);s.markup="autolink",s.info="auto"}return e.pos+=i.length+2,!0}return!1}],["html_inline",function(e,t){if(!e.md.options.html)return!1;const n=e.posMax,s=e.pos;if(60!==e.src.charCodeAt(s)||s+2>=n)return!1;const r=e.src.charCodeAt(s+1);if(33!==r&&63!==r&&47!==r&&!function(e){const t=32|e;return t>=97&&t<=122}(r))return!1;const i=e.src.slice(s).match(Ic);if(!i)return!1;if(!t){const t=e.push("html_inline","",0);t.content=i[0],o=t.content,/^<a[>\s]/i.test(o)&&e.linkLevel++,function(e){return/^<\/a\s*>/i.test(e)}(t.content)&&e.linkLevel--}var o;return e.pos+=i[0].length,!0}],["entity",function(e,t){const n=e.pos,s=e.posMax;if(38!==e.src.charCodeAt(n))return!1;if(n+1>=s)return!1;if(35===e.src.charCodeAt(n+1)){const s=e.src.slice(n).match(Gc);if(s){if(!t){const t="x"===s[1][0].toLowerCase()?parseInt(s[1].slice(1),16):parseInt(s[1],10),n=e.push("text_special","",0);n.content=Ma(t)?va(t):va(65533),n.markup=s[0],n.info="entity"}return e.pos+=s[0].length,!0}}else{const s=e.src.slice(n).match($c);if(s){const n=ge(s[0]);if(n!==s[0]){if(!t){const t=e.push("text_special","",0);t.content=n,t.markup=s[0],t.info="entity"}return e.pos+=s[0].length,!0}}}return!1}]],jc=[["balance_pairs",function(e){const t=e.tokens_meta,n=e.tokens_meta.length;qc(e.delimiters);for(let e=0;e<n;e++)t[e]&&t[e].delimiters&&qc(t[e].delimiters)}],["strikethrough",Fc.postProcess],["emphasis",Bc.postProcess],["fragments_join",function(e){let t,n,s=0;const r=e.tokens,i=e.tokens.length;for(t=n=0;t<i;t++)r[t].nesting<0&&s--,r[t].level=s,r[t].nesting>0&&s++,"text"===r[t].type&&t+1<i&&"text"===r[t+1].type?r[t+1].content=r[t].content+r[t+1].content:(t!==n&&(r[n]=r[t]),n++);t!==n&&(r.length=n)}]];function Kc(){this.ruler=new ec;for(let e=0;e<Yc.length;e++)this.ruler.push(Yc[e][0],Yc[e][1]);this.ruler2=new ec;for(let e=0;e<jc.length;e++)this.ruler2.push(jc[e][0],jc[e][1])}function Vc(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){t&&Object.keys(t).forEach((function(n){e[n]=t[n]}))})),e}function zc(e){return Object.prototype.toString.call(e)}function Qc(e){return"[object Function]"===zc(e)}function Wc(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}Kc.prototype.skipToken=function(e){const t=e.pos,n=this.ruler.getRules(""),s=n.length,r=e.md.options.maxNesting,i=e.cache;if(void 0!==i[t])return void(e.pos=i[t]);let o=!1;if(e.level<r){for(let r=0;r<s;r++)if(e.level++,o=n[r](e,!0),e.level--,o){if(t>=e.pos)throw new Error("inline rule didn't increment state.pos");break}}else e.pos=e.posMax;o||e.pos++,i[t]=e.pos},Kc.prototype.tokenize=function(e){const t=this.ruler.getRules(""),n=t.length,s=e.posMax,r=e.md.options.maxNesting;for(;e.pos<s;){const i=e.pos;let o=!1;if(e.level<r)for(let s=0;s<n;s++)if(o=t[s](e,!1),o){if(i>=e.pos)throw new Error("inline rule didn't increment state.pos");break}if(o){if(e.pos>=s)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},Kc.prototype.parse=function(e,t,n,s){const r=new this.State(e,t,n,s);this.tokenize(r);const i=this.ruler2.getRules(""),o=i.length;for(let e=0;e<o;e++)i[e](r)},Kc.prototype.State=Rc;const Xc={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};const Zc={"http:":{validate:function(e,t,n){const s=e.slice(t);return n.re.http||(n.re.http=new RegExp("^\\/\\/"+n.re.src_auth+n.re.src_host_port_strict+n.re.src_path,"i")),n.re.http.test(s)?s.match(n.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,t,n){const s=e.slice(t);return n.re.no_http||(n.re.no_http=new RegExp("^"+n.re.src_auth+"(?:localhost|(?:(?:"+n.re.src_domain+")\\.)+"+n.re.src_domain_root+")"+n.re.src_port+n.re.src_host_terminator+n.re.src_path,"i")),n.re.no_http.test(s)?t>=3&&":"===e[t-3]||t>=3&&"/"===e[t-3]?0:s.match(n.re.no_http)[0].length:0}},"mailto:":{validate:function(e,t,n){const s=e.slice(t);return n.re.mailto||(n.re.mailto=new RegExp("^"+n.re.src_email_name+"@"+n.re.src_host_strict,"i")),n.re.mailto.test(s)?s.match(n.re.mailto)[0].length:0}}},Jc="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function el(e){const t=e.re=function(e){const t={};e=e||{},t.src_Any=ba.source,t.src_Cc=ka.source,t.src_Z=Ia.source,t.src_P=Sa.source,t.src_ZPCc=[t.src_Z,t.src_P,t.src_Cc].join("|"),t.src_ZCc=[t.src_Z,t.src_Cc].join("|");const n="[><｜]";return t.src_pseudo_letter="(?:(?![><｜]|"+t.src_ZPCc+")"+t.src_Any+")",t.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",t.src_auth="(?:(?:(?!"+t.src_ZCc+"|[@/\\[\\]()]).)+@)?",t.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",t.src_host_terminator="(?=$|[><｜]|"+t.src_ZPCc+")(?!"+(e["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+t.src_ZPCc+"))",t.src_path="(?:[/?#](?:(?!"+t.src_ZCc+"|"+n+"|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!"+t.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+t.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+t.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+t.src_ZCc+'|["]).)+\\"|\\\'(?:(?!'+t.src_ZCc+"|[']).)+\\'|\\'(?="+t.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+t.src_ZCc+"|[.]|$)|"+(e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+t.src_ZCc+"|$)|;(?!"+t.src_ZCc+"|$)|\\!+(?!"+t.src_ZCc+"|[!]|$)|\\?(?!"+t.src_ZCc+"|[?]|$))+|\\/)?",t.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',t.src_xn="xn--[a-z0-9\\-]{1,59}",t.src_domain_root="(?:"+t.src_xn+"|"+t.src_pseudo_letter+"{1,63})",t.src_domain="(?:"+t.src_xn+"|(?:"+t.src_pseudo_letter+")|(?:"+t.src_pseudo_letter+"(?:-|"+t.src_pseudo_letter+"){0,61}"+t.src_pseudo_letter+"))",t.src_host="(?:(?:(?:(?:"+t.src_domain+")\\.)*"+t.src_domain+"))",t.tpl_host_fuzzy="(?:"+t.src_ip4+"|(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%)))",t.tpl_host_no_ip_fuzzy="(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%))",t.src_host_strict=t.src_host+t.src_host_terminator,t.tpl_host_fuzzy_strict=t.tpl_host_fuzzy+t.src_host_terminator,t.src_host_port_strict=t.src_host+t.src_port+t.src_host_terminator,t.tpl_host_port_fuzzy_strict=t.tpl_host_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_port_no_ip_fuzzy_strict=t.tpl_host_no_ip_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+t.src_ZPCc+"|>|$))",t.tpl_email_fuzzy='(^|[><｜]|"|\\(|'+t.src_ZCc+")("+t.src_email_name+"@"+t.tpl_host_fuzzy_strict+")",t.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_fuzzy_strict+t.src_path+")",t.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_no_ip_fuzzy_strict+t.src_path+")",t}(e.__opts__),n=e.__tlds__.slice();function s(e){return e.replace("%TLDS%",t.src_tlds)}e.onCompile(),e.__tlds_replaced__||n.push("a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]"),n.push(t.src_xn),t.src_tlds=n.join("|"),t.email_fuzzy=RegExp(s(t.tpl_email_fuzzy),"i"),t.link_fuzzy=RegExp(s(t.tpl_link_fuzzy),"i"),t.link_no_ip_fuzzy=RegExp(s(t.tpl_link_no_ip_fuzzy),"i"),t.host_fuzzy_test=RegExp(s(t.tpl_host_fuzzy_test),"i");const r=[];function i(e,t){throw new Error('(LinkifyIt) Invalid schema "'+e+'": '+t)}e.__compiled__={},Object.keys(e.__schemas__).forEach((function(t){const n=e.__schemas__[t];if(null===n)return;const s={validate:null,link:null};if(e.__compiled__[t]=s,"[object Object]"===zc(n))return!function(e){return"[object RegExp]"===zc(e)}(n.validate)?Qc(n.validate)?s.validate=n.validate:i(t,n):s.validate=function(e){return function(t,n){const s=t.slice(n);return e.test(s)?s.match(e)[0].length:0}}(n.validate),void(Qc(n.normalize)?s.normalize=n.normalize:n.normalize?i(t,n):s.normalize=function(e,t){t.normalize(e)});!function(e){return"[object String]"===zc(e)}(n)?i(t,n):r.push(t)})),r.forEach((function(t){e.__compiled__[e.__schemas__[t]]&&(e.__compiled__[t].validate=e.__compiled__[e.__schemas__[t]].validate,e.__compiled__[t].normalize=e.__compiled__[e.__schemas__[t]].normalize)})),e.__compiled__[""]={validate:null,normalize:function(e,t){t.normalize(e)}};const o=Object.keys(e.__compiled__).filter((function(t){return t.length>0&&e.__compiled__[t]})).map(Wc).join("|");e.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+o+")","i"),e.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+o+")","ig"),e.re.schema_at_start=RegExp("^"+e.re.schema_search.source,"i"),e.re.pretest=RegExp("("+e.re.schema_test.source+")|("+e.re.host_fuzzy_test.source+")|@","i"),function(e){e.__index__=-1,e.__text_cache__=""}(e)}function tl(e,t){const n=e.__index__,s=e.__last_index__,r=e.__text_cache__.slice(n,s);this.schema=e.__schema__.toLowerCase(),this.index=n+t,this.lastIndex=s+t,this.raw=r,this.text=r,this.url=r}function nl(e,t){const n=new tl(e,t);return e.__compiled__[n.schema].normalize(n,e),n}function sl(e,t){if(!(this instanceof sl))return new sl(e,t);var n;t||(n=e,Object.keys(n||{}).reduce((function(e,t){return e||Xc.hasOwnProperty(t)}),!1)&&(t=e,e={})),this.__opts__=Vc({},Xc,t),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=Vc({},Zc,e),this.__compiled__={},this.__tlds__=Jc,this.__tlds_replaced__=!1,this.re={},el(this)}sl.prototype.add=function(e,t){return this.__schemas__[e]=t,el(this),this},sl.prototype.set=function(e){return this.__opts__=Vc(this.__opts__,e),this},sl.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;let t,n,s,r,i,o,a,c,l;if(this.re.schema_test.test(e))for(a=this.re.schema_search,a.lastIndex=0;null!==(t=a.exec(e));)if(r=this.testSchemaAt(e,t[2],a.lastIndex),r){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+r;break}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(c=e.search(this.re.host_fuzzy_test),c>=0&&(this.__index__<0||c<this.__index__)&&null!==(n=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(i=n.index+n[1].length,(this.__index__<0||i<this.__index__)&&(this.__schema__="",this.__index__=i,this.__last_index__=n.index+n[0].length))),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&(l=e.indexOf("@"),l>=0&&null!==(s=e.match(this.re.email_fuzzy))&&(i=s.index+s[1].length,o=s.index+s[0].length,(this.__index__<0||i<this.__index__||i===this.__index__&&o>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=i,this.__last_index__=o))),this.__index__>=0},sl.prototype.pretest=function(e){return this.re.pretest.test(e)},sl.prototype.testSchemaAt=function(e,t,n){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,n,this):0},sl.prototype.match=function(e){const t=[];let n=0;this.__index__>=0&&this.__text_cache__===e&&(t.push(nl(this,n)),n=this.__last_index__);let s=n?e.slice(n):e;for(;this.test(s);)t.push(nl(this,n)),s=s.slice(this.__last_index__),n+=this.__last_index__;return t.length?t:null},sl.prototype.matchAtStart=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return null;const t=this.re.schema_at_start.exec(e);if(!t)return null;const n=this.testSchemaAt(e,t[2],t[0].length);return n?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+n,nl(this,0)):null},sl.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?(this.__tlds__=this.__tlds__.concat(e).sort().filter((function(e,t,n){return e!==n[t-1]})).reverse(),el(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,el(this),this)},sl.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),"mailto:"!==e.schema||/^mailto:/i.test(e.url)||(e.url="mailto:"+e.url)},sl.prototype.onCompile=function(){};const rl=2147483647,il=36,ol=/^xn--/,al=/[^\0-\x7F]/,cl=/[\x2E\u3002\uFF0E\uFF61]/g,ll={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},ul=Math.floor,hl=String.fromCharCode;function dl(e){throw new RangeError(ll[e])}function pl(e,t){const n=e.split("@");let s="";n.length>1&&(s=n[0]+"@",e=n[1]);const r=function(e,t){const n=[];let s=e.length;for(;s--;)n[s]=t(e[s]);return n}((e=e.replace(cl,".")).split("."),t).join(".");return s+r}function fl(e){const t=[];let n=0;const s=e.length;for(;n<s;){const r=e.charCodeAt(n++);if(r>=55296&&r<=56319&&n<s){const s=e.charCodeAt(n++);56320==(64512&s)?t.push(((1023&r)<<10)+(1023&s)+65536):(t.push(r),n--)}else t.push(r)}return t}const ml=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},El=function(e,t,n){let s=0;for(e=n?ul(e/700):e>>1,e+=ul(e/t);e>455;s+=il)e=ul(e/35);return ul(s+36*e/(e+38))},Tl=function(e){const t=[],n=e.length;let s=0,r=128,i=72,o=e.lastIndexOf("-");o<0&&(o=0);for(let n=0;n<o;++n)e.charCodeAt(n)>=128&&dl("not-basic"),t.push(e.charCodeAt(n));for(let c=o>0?o+1:0;c<n;){const o=s;for(let t=1,r=il;;r+=il){c>=n&&dl("invalid-input");const o=(a=e.charCodeAt(c++))>=48&&a<58?a-48+26:a>=65&&a<91?a-65:a>=97&&a<123?a-97:il;o>=il&&dl("invalid-input"),o>ul((rl-s)/t)&&dl("overflow"),s+=o*t;const l=r<=i?1:r>=i+26?26:r-i;if(o<l)break;const u=il-l;t>ul(rl/u)&&dl("overflow"),t*=u}const l=t.length+1;i=El(s-o,l,0==o),ul(s/l)>rl-r&&dl("overflow"),r+=ul(s/l),s%=l,t.splice(s++,0,r)}var a;return String.fromCodePoint(...t)},_l=function(e){const t=[],n=(e=fl(e)).length;let s=128,r=0,i=72;for(const n of e)n<128&&t.push(hl(n));const o=t.length;let a=o;for(o&&t.push("-");a<n;){let n=rl;for(const t of e)t>=s&&t<n&&(n=t);const c=a+1;n-s>ul((rl-r)/c)&&dl("overflow"),r+=(n-s)*c,s=n;for(const n of e)if(n<s&&++r>rl&&dl("overflow"),n===s){let e=r;for(let n=il;;n+=il){const s=n<=i?1:n>=i+26?26:n-i;if(e<s)break;const r=e-s,o=il-s;t.push(hl(ml(s+r%o,0))),e=ul(r/o)}t.push(hl(ml(e,0))),i=El(r,c,a===o),r=0,++a}++r,++s}return t.join("")},Al=function(e){return pl(e,(function(e){return al.test(e)?"xn--"+_l(e):e}))},gl=function(e){return pl(e,(function(e){return ol.test(e)?Tl(e.slice(4).toLowerCase()):e}))},Cl={default:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}},zero:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}},commonmark:{options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}}},bl=/^(vbscript|javascript|file|data):/,kl=/^data:image\/(gif|png|jpeg|webp);/;function Sl(e){const t=e.trim().toLowerCase();return!bl.test(t)||kl.test(t)}const Nl=["http:","https:","mailto:"];function Il(e){const t=ga(e,!0);if(t.hostname&&(!t.protocol||Nl.indexOf(t.protocol)>=0))try{t.hostname=Al(t.hostname)}catch(e){}return oa(aa(t))}function Dl(e){const t=ga(e,!0);if(t.hostname&&(!t.protocol||Nl.indexOf(t.protocol)>=0))try{t.hostname=gl(t.hostname)}catch(e){}return ra(aa(t),ra.defaultChars+"%")}function yl(e,t){if(!(this instanceof yl))return new yl(e,t);t||ya(e)||(t=e||{},e="default"),this.inline=new Kc,this.block=new Lc,this.core=new _c,this.renderer=new Ja,this.linkify=new sl,this.validateLink=Sl,this.normalizeLink=Il,this.normalizeLinkText=Dl,this.utils=Wa,this.helpers=La({},Xa),this.options={},this.configure(e),t&&this.set(t)}function Ol(e){function t(e,t){let n;const s=[],r=t.length;for(let i=0;i<r;i++){const r=t[i];if(43!==r.marker)continue;if(-1===r.end)continue;const o=t[r.end];n=e.tokens[r.token],n.type="ins_open",n.tag="ins",n.nesting=1,n.markup="++",n.content="",n=e.tokens[o.token],n.type="ins_close",n.tag="ins",n.nesting=-1,n.markup="++",n.content="","text"===e.tokens[o.token-1].type&&"+"===e.tokens[o.token-1].content&&s.push(o.token-1)}for(;s.length;){const t=s.pop();let r=t+1;for(;r<e.tokens.length&&"ins_close"===e.tokens[r].type;)r++;r--,t!==r&&(n=e.tokens[r],e.tokens[r]=e.tokens[t],e.tokens[t]=n)}}e.inline.ruler.before("emphasis","ins",(function(e,t){const n=e.pos,s=e.src.charCodeAt(n);if(t)return!1;if(43!==s)return!1;const r=e.scanDelims(e.pos,!0);let i=r.length;const o=String.fromCharCode(s);if(i<2)return!1;if(i%2){e.push("text","",0).content=o,i--}for(let t=0;t<i;t+=2){e.push("text","",0).content=o+o,(r.can_open||r.can_close)&&e.delimiters.push({marker:s,length:0,jump:t/2,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close})}return e.pos+=r.length,!0})),e.inline.ruler2.before("emphasis","ins",(function(e){const n=e.tokens_meta,s=(e.tokens_meta||[]).length;t(e,e.delimiters);for(let r=0;r<s;r++)n[r]&&n[r].delimiters&&t(e,n[r].delimiters)}))}function Ll(e){function t(e,t){const n=[],s=t.length;for(let r=0;r<s;r++){const s=t[r];if(61!==s.marker)continue;if(-1===s.end)continue;const i=t[s.end],o=e.tokens[s.token];o.type="mark_open",o.tag="mark",o.nesting=1,o.markup="==",o.content="";const a=e.tokens[i.token];a.type="mark_close",a.tag="mark",a.nesting=-1,a.markup="==",a.content="","text"===e.tokens[i.token-1].type&&"="===e.tokens[i.token-1].content&&n.push(i.token-1)}for(;n.length;){const t=n.pop();let s=t+1;for(;s<e.tokens.length&&"mark_close"===e.tokens[s].type;)s++;if(s--,t!==s){const n=e.tokens[s];e.tokens[s]=e.tokens[t],e.tokens[t]=n}}}e.inline.ruler.before("emphasis","mark",(function(e,t){const n=e.pos,s=e.src.charCodeAt(n);if(t)return!1;if(61!==s)return!1;const r=e.scanDelims(e.pos,!0);let i=r.length;const o=String.fromCharCode(s);if(i<2)return!1;if(i%2){e.push("text","",0).content=o,i--}for(let t=0;t<i;t+=2){e.push("text","",0).content=o+o,(r.can_open||r.can_close)&&e.delimiters.push({marker:s,length:0,jump:t/2,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close})}return e.pos+=r.length,!0})),e.inline.ruler2.before("emphasis","mark",(function(e){let n;const s=e.tokens_meta,r=(e.tokens_meta||[]).length;for(t(e,e.delimiters),n=0;n<r;n++)s[n]&&s[n].delimiters&&t(e,s[n].delimiters)}))}yl.prototype.set=function(e){return La(this.options,e),this},yl.prototype.configure=function(e){const t=this;if(ya(e)){const t=e;if(!(e=Cl[t]))throw new Error('Wrong `markdown-it` preset "'+t+'", check name')}if(!e)throw new Error("Wrong `markdown-it` preset, can't be empty");return e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach((function(n){e.components[n].rules&&t[n].ruler.enableOnly(e.components[n].rules),e.components[n].rules2&&t[n].ruler2.enableOnly(e.components[n].rules2)})),this},yl.prototype.enable=function(e,t){let n=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){n=n.concat(this[t].ruler.enable(e,!0))}),this),n=n.concat(this.inline.ruler2.enable(e,!0));const s=e.filter((function(e){return n.indexOf(e)<0}));if(s.length&&!t)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+s);return this},yl.prototype.disable=function(e,t){let n=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){n=n.concat(this[t].ruler.disable(e,!0))}),this),n=n.concat(this.inline.ruler2.disable(e,!0));const s=e.filter((function(e){return n.indexOf(e)<0}));if(s.length&&!t)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+s);return this},yl.prototype.use=function(e){const t=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,t),this},yl.prototype.parse=function(e,t){if("string"!=typeof e)throw new Error("Input data should be a String");const n=new this.core.State(e,this,t);return this.core.process(n),n.tokens},yl.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},yl.prototype.parseInline=function(e,t){const n=new this.core.State(e,this,t);return n.inlineMode=!0,this.core.process(n),n.tokens},yl.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)};const Rl=/\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;function Ml(e,t){const n=e.posMax,s=e.pos;if(126!==e.src.charCodeAt(s))return!1;if(t)return!1;if(s+2>=n)return!1;e.pos=s+1;let r=!1;for(;e.pos<n;){if(126===e.src.charCodeAt(e.pos)){r=!0;break}e.md.inline.skipToken(e)}if(!r||s+1===e.pos)return e.pos=s,!1;const i=e.src.slice(s+1,e.pos);if(i.match(/(^|[^\\])(\\\\)*\s/))return e.pos=s,!1;e.posMax=e.pos,e.pos=s+1;e.push("sub_open","sub",1).markup="~";e.push("text","",0).content=i.replace(Rl,"$1");return e.push("sub_close","sub",-1).markup="~",e.pos=e.posMax+1,e.posMax=n,!0}function vl(e){e.inline.ruler.after("emphasis","sub",Ml)}const xl=/\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;function wl(e,t){const n=e.posMax,s=e.pos;if(94!==e.src.charCodeAt(s))return!1;if(t)return!1;if(s+2>=n)return!1;e.pos=s+1;let r=!1;for(;e.pos<n;){if(94===e.src.charCodeAt(e.pos)){r=!0;break}e.md.inline.skipToken(e)}if(!r||s+1===e.pos)return e.pos=s,!1;const i=e.src.slice(s+1,e.pos);if(i.match(/(^|[^\\])(\\\\)*\s/))return e.pos=s,!1;e.posMax=e.pos,e.pos=s+1;e.push("sup_open","sup",1).markup="^";e.push("text","",0).content=i.replace(xl,"$1");return e.push("sup_close","sup",-1).markup="^",e.pos=e.posMax+1,e.posMax=n,!0}function Fl(e){e.inline.ruler.after("emphasis","sup",wl)}const Pl={" ":'<svg width="16" height="16" viewBox="0 -3 24 24"><path fill-rule="evenodd" d="M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z" clip-rule="evenodd"/></svg>\n'.trim(),x:'<svg width="16" height="16" viewBox="0 -3 24 24"><path d="M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"/></svg>\n'.trim()},Bl={name:"checkbox",transform:e=>(e.parser.tap((e=>{e.core.ruler.before("inline","checkbox",(e=>{for(let t=2;t<e.tokens.length;t+=1){const n=e.tokens[t];if("inline"===n.type&&n.content){const s=e.tokens[t-1].type,r=e.tokens[t-2].type;("heading_open"===s||"paragraph_open"===s&&"list_item_open"===r)&&(n.content=n.content.replace(/^\[(.)\] /,((e,t)=>Pl[t]?`${Pl[t]} `:e)))}}return!1}))})),{})},Ul=Symbol.for("yaml.alias"),Hl=Symbol.for("yaml.document"),Gl=Symbol.for("yaml.map"),$l=Symbol.for("yaml.pair"),ql=Symbol.for("yaml.scalar"),Yl=Symbol.for("yaml.seq"),jl=Symbol.for("yaml.node.type"),Kl=e=>!!e&&"object"==typeof e&&e[jl]===Ul,Vl=e=>!!e&&"object"==typeof e&&e[jl]===Hl,zl=e=>!!e&&"object"==typeof e&&e[jl]===Gl,Ql=e=>!!e&&"object"==typeof e&&e[jl]===$l,Wl=e=>!!e&&"object"==typeof e&&e[jl]===ql,Xl=e=>!!e&&"object"==typeof e&&e[jl]===Yl;function Zl(e){if(e&&"object"==typeof e)switch(e[jl]){case Gl:case Yl:return!0}return!1}function Jl(e){if(e&&"object"==typeof e)switch(e[jl]){case Ul:case Gl:case ql:case Yl:return!0}return!1}const eu=Symbol("break visit"),tu=Symbol("skip children"),nu=Symbol("remove node");function su(e,t){const n=function(e){if("object"==typeof e&&(e.Collection||e.Node||e.Value))return Object.assign({Alias:e.Node,Map:e.Node,Scalar:e.Node,Seq:e.Node},e.Value&&{Map:e.Value,Scalar:e.Value,Seq:e.Value},e.Collection&&{Map:e.Collection,Seq:e.Collection},e);return e}(t);if(Vl(e)){ru(null,e.contents,n,Object.freeze([e]))===nu&&(e.contents=null)}else ru(null,e,n,Object.freeze([]))}function ru(e,t,n,s){const r=function(e,t,n,s){var r,i,o,a,c;return"function"==typeof n?n(e,t,s):zl(t)?null==(r=n.Map)?void 0:r.call(n,e,t,s):Xl(t)?null==(i=n.Seq)?void 0:i.call(n,e,t,s):Ql(t)?null==(o=n.Pair)?void 0:o.call(n,e,t,s):Wl(t)?null==(a=n.Scalar)?void 0:a.call(n,e,t,s):Kl(t)?null==(c=n.Alias)?void 0:c.call(n,e,t,s):void 0}(e,t,n,s);if(Jl(r)||Ql(r))return function(e,t,n){const s=t[t.length-1];if(Zl(s))s.items[e]=n;else if(Ql(s))"key"===e?s.key=n:s.value=n;else{if(!Vl(s)){const e=Kl(s)?"alias":"scalar";throw new Error(`Cannot replace node with ${e} parent`)}s.contents=n}}(e,s,r),ru(e,r,n,s);if("symbol"!=typeof r)if(Zl(t)){s=Object.freeze(s.concat(t));for(let e=0;e<t.items.length;++e){const r=ru(e,t.items[e],n,s);if("number"==typeof r)e=r-1;else{if(r===eu)return eu;r===nu&&(t.items.splice(e,1),e-=1)}}}else if(Ql(t)){s=Object.freeze(s.concat(t));const e=ru("key",t.key,n,s);if(e===eu)return eu;e===nu&&(t.key=null);const r=ru("value",t.value,n,s);if(r===eu)return eu;r===nu&&(t.value=null)}return r}su.BREAK=eu,su.SKIP=tu,su.REMOVE=nu;const iu={"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"};class ou{constructor(e,t){this.docStart=null,this.docEnd=!1,this.yaml=Object.assign({},ou.defaultYaml,e),this.tags=Object.assign({},ou.defaultTags,t)}clone(){const e=new ou(this.yaml,this.tags);return e.docStart=this.docStart,e}atDocument(){const e=new ou(this.yaml,this.tags);switch(this.yaml.version){case"1.1":this.atNextDocument=!0;break;case"1.2":this.atNextDocument=!1,this.yaml={explicit:ou.defaultYaml.explicit,version:"1.2"},this.tags=Object.assign({},ou.defaultTags)}return e}add(e,t){this.atNextDocument&&(this.yaml={explicit:ou.defaultYaml.explicit,version:"1.1"},this.tags=Object.assign({},ou.defaultTags),this.atNextDocument=!1);const n=e.trim().split(/[ \t]+/),s=n.shift();switch(s){case"%TAG":{if(2!==n.length&&(t(0,"%TAG directive should contain exactly two parts"),n.length<2))return!1;const[e,s]=n;return this.tags[e]=s,!0}case"%YAML":{if(this.yaml.explicit=!0,1!==n.length)return t(0,"%YAML directive should contain exactly one part"),!1;const[e]=n;if("1.1"===e||"1.2"===e)return this.yaml.version=e,!0;return t(6,`Unsupported YAML version ${e}`,/^\d+\.\d+$/.test(e)),!1}default:return t(0,`Unknown directive ${s}`,!0),!1}}tagName(e,t){if("!"===e)return"!";if("!"!==e[0])return t(`Not a valid tag: ${e}`),null;if("<"===e[1]){const n=e.slice(2,-1);return"!"===n||"!!"===n?(t(`Verbatim tags aren't resolved, so ${e} is invalid.`),null):(">"!==e[e.length-1]&&t("Verbatim tags must end with a >"),n)}const[,n,s]=e.match(/^(.*!)([^!]*)$/s);s||t(`The ${e} tag has no suffix`);const r=this.tags[n];if(r)try{return r+decodeURIComponent(s)}catch(e){return t(String(e)),null}return"!"===n?e:(t(`Could not resolve tag: ${e}`),null)}tagString(e){for(const[t,n]of Object.entries(this.tags))if(e.startsWith(n))return t+e.substring(n.length).replace(/[!,[\]{}]/g,(e=>iu[e]));return"!"===e[0]?e:`!<${e}>`}toString(e){const t=this.yaml.explicit?[`%YAML ${this.yaml.version||"1.2"}`]:[],n=Object.entries(this.tags);let s;if(e&&n.length>0&&Jl(e.contents)){const t={};su(e.contents,((e,n)=>{Jl(n)&&n.tag&&(t[n.tag]=!0)})),s=Object.keys(t)}else s=[];for(const[r,i]of n)"!!"===r&&"tag:yaml.org,2002:"===i||e&&!s.some((e=>e.startsWith(i)))||t.push(`%TAG ${r} ${i}`);return t.join("\n")}}function au(e){if(/[\x00-\x19\s,[\]{}]/.test(e)){const t=JSON.stringify(e);throw new Error(`Anchor must not contain whitespace or control characters: ${t}`)}return!0}function cu(e){const t=new Set;return su(e,{Value(e,n){n.anchor&&t.add(n.anchor)}}),t}function lu(e,t){for(let n=1;;++n){const s=`${e}${n}`;if(!t.has(s))return s}}function uu(e,t,n,s){if(s&&"object"==typeof s)if(Array.isArray(s))for(let t=0,n=s.length;t<n;++t){const n=s[t],r=uu(e,s,String(t),n);void 0===r?delete s[t]:r!==n&&(s[t]=r)}else if(s instanceof Map)for(const t of Array.from(s.keys())){const n=s.get(t),r=uu(e,s,t,n);void 0===r?s.delete(t):r!==n&&s.set(t,r)}else if(s instanceof Set)for(const t of Array.from(s)){const n=uu(e,s,t,t);void 0===n?s.delete(t):n!==t&&(s.delete(t),s.add(n))}else for(const[t,n]of Object.entries(s)){const r=uu(e,s,t,n);void 0===r?delete s[t]:r!==n&&(s[t]=r)}return e.call(t,n,s)}function hu(e,t,n){if(Array.isArray(e))return e.map(((e,t)=>hu(e,String(t),n)));if(e&&"function"==typeof e.toJSON){if(!n||(!Wl(s=e)&&!Zl(s)||!s.anchor))return e.toJSON(t,n);const r={aliasCount:0,count:1,res:void 0};n.anchors.set(e,r),n.onCreate=e=>{r.res=e,delete n.onCreate};const i=e.toJSON(t,n);return n.onCreate&&n.onCreate(i),i}var s;return"bigint"!=typeof e||(null==n?void 0:n.keep)?e:Number(e)}ou.defaultYaml={explicit:!1,version:"1.2"},ou.defaultTags={"!!":"tag:yaml.org,2002:"};class du{constructor(e){Object.defineProperty(this,jl,{value:e})}clone(){const e=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return this.range&&(e.range=this.range.slice()),e}toJS(e,{mapAsMap:t,maxAliasCount:n,onAnchor:s,reviver:r}={}){if(!Vl(e))throw new TypeError("A document argument is required");const i={anchors:new Map,doc:e,keep:!0,mapAsMap:!0===t,mapKeyWarned:!1,maxAliasCount:"number"==typeof n?n:100},o=hu(this,"",i);if("function"==typeof s)for(const{count:e,res:t}of i.anchors.values())s(t,e);return"function"==typeof r?uu(r,{"":o},"",o):o}}class pu extends du{constructor(e){super(Ul),this.source=e,Object.defineProperty(this,"tag",{set(){throw new Error("Alias nodes cannot have tags")}})}resolve(e){let t;return su(e,{Node:(e,n)=>{if(n===this)return su.BREAK;n.anchor===this.source&&(t=n)}}),t}toJSON(e,t){if(!t)return{source:this.source};const{anchors:n,doc:s,maxAliasCount:r}=t,i=this.resolve(s);if(!i){const e=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new ReferenceError(e)}let o=n.get(i);if(o||(hu(i,null,t),o=n.get(i)),!o||void 0===o.res){throw new ReferenceError("This should not happen: Alias anchor was not resolved?")}if(r>=0&&(o.count+=1,0===o.aliasCount&&(o.aliasCount=fu(s,i,n)),o.count*o.aliasCount>r)){throw new ReferenceError("Excessive alias count indicates a resource exhaustion attack")}return o.res}toString(e,t,n){const s=`*${this.source}`;if(e){if(au(this.source),e.options.verifyAliasOrder&&!e.anchors.has(this.source)){const e=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new Error(e)}if(e.implicitKey)return`${s} `}return s}}function fu(e,t,n){if(Kl(t)){const s=t.resolve(e),r=n&&s&&n.get(s);return r?r.count*r.aliasCount:0}if(Zl(t)){let s=0;for(const r of t.items){const t=fu(e,r,n);t>s&&(s=t)}return s}if(Ql(t)){const s=fu(e,t.key,n),r=fu(e,t.value,n);return Math.max(s,r)}return 1}const mu=e=>!e||"function"!=typeof e&&"object"!=typeof e;class Eu extends du{constructor(e){super(ql),this.value=e}toJSON(e,t){return(null==t?void 0:t.keep)?this.value:hu(this.value,e,t)}toString(){return String(this.value)}}Eu.BLOCK_FOLDED="BLOCK_FOLDED",Eu.BLOCK_LITERAL="BLOCK_LITERAL",Eu.PLAIN="PLAIN",Eu.QUOTE_DOUBLE="QUOTE_DOUBLE",Eu.QUOTE_SINGLE="QUOTE_SINGLE";function Tu(e,t,n){var s,r,i;if(Vl(e)&&(e=e.contents),Jl(e))return e;if(Ql(e)){const t=null==(r=(s=n.schema[Gl]).createNode)?void 0:r.call(s,n.schema,null,n);return t.items.push(e),t}(e instanceof String||e instanceof Number||e instanceof Boolean||"undefined"!=typeof BigInt&&e instanceof BigInt)&&(e=e.valueOf());const{aliasDuplicateObjects:o,onAnchor:a,onTagObj:c,schema:l,sourceObjects:u}=n;let h;if(o&&e&&"object"==typeof e){if(h=u.get(e),h)return h.anchor||(h.anchor=a(e)),new pu(h.anchor);h={anchor:null,node:null},u.set(e,h)}(null==t?void 0:t.startsWith("!!"))&&(t="tag:yaml.org,2002:"+t.slice(2));let d=function(e,t,n){if(t){const e=n.filter((e=>e.tag===t)),s=e.find((e=>!e.format))??e[0];if(!s)throw new Error(`Tag ${t} not found`);return s}return n.find((t=>{var n;return(null==(n=t.identify)?void 0:n.call(t,e))&&!t.format}))}(e,t,l.tags);if(!d){if(e&&"function"==typeof e.toJSON&&(e=e.toJSON()),!e||"object"!=typeof e){const t=new Eu(e);return h&&(h.node=t),t}d=e instanceof Map?l[Gl]:Symbol.iterator in Object(e)?l[Yl]:l[Gl]}c&&(c(d),delete n.onTagObj);const p=(null==d?void 0:d.createNode)?d.createNode(n.schema,e,n):"function"==typeof(null==(i=null==d?void 0:d.nodeClass)?void 0:i.from)?d.nodeClass.from(n.schema,e,n):new Eu(e);return t?p.tag=t:d.default||(p.tag=d.tag),h&&(h.node=p),p}function _u(e,t,n){let s=n;for(let e=t.length-1;e>=0;--e){const n=t[e];if("number"==typeof n&&Number.isInteger(n)&&n>=0){const e=[];e[n]=s,s=e}else s=new Map([[n,s]])}return Tu(s,void 0,{aliasDuplicateObjects:!1,keepUndefined:!1,onAnchor:()=>{throw new Error("This should not happen, please report a bug.")},schema:e,sourceObjects:new Map})}const Au=e=>null==e||"object"==typeof e&&!!e[Symbol.iterator]().next().done;class gu extends du{constructor(e,t){super(e),Object.defineProperty(this,"schema",{value:t,configurable:!0,enumerable:!1,writable:!0})}clone(e){const t=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return e&&(t.schema=e),t.items=t.items.map((t=>Jl(t)||Ql(t)?t.clone(e):t)),this.range&&(t.range=this.range.slice()),t}addIn(e,t){if(Au(e))this.add(t);else{const[n,...s]=e,r=this.get(n,!0);if(Zl(r))r.addIn(s,t);else{if(void 0!==r||!this.schema)throw new Error(`Expected YAML collection at ${n}. Remaining path: ${s}`);this.set(n,_u(this.schema,s,t))}}}deleteIn(e){const[t,...n]=e;if(0===n.length)return this.delete(t);const s=this.get(t,!0);if(Zl(s))return s.deleteIn(n);throw new Error(`Expected YAML collection at ${t}. Remaining path: ${n}`)}getIn(e,t){const[n,...s]=e,r=this.get(n,!0);return 0===s.length?!t&&Wl(r)?r.value:r:Zl(r)?r.getIn(s,t):void 0}hasAllNullValues(e){return this.items.every((t=>{if(!Ql(t))return!1;const n=t.value;return null==n||e&&Wl(n)&&null==n.value&&!n.commentBefore&&!n.comment&&!n.tag}))}hasIn(e){const[t,...n]=e;if(0===n.length)return this.has(t);const s=this.get(t,!0);return!!Zl(s)&&s.hasIn(n)}setIn(e,t){const[n,...s]=e;if(0===s.length)this.set(n,t);else{const e=this.get(n,!0);if(Zl(e))e.setIn(s,t);else{if(void 0!==e||!this.schema)throw new Error(`Expected YAML collection at ${n}. Remaining path: ${s}`);this.set(n,_u(this.schema,s,t))}}}}const Cu=e=>e.replace(/^(?!$)(?: $)?/gm,"#");function bu(e,t){return/^\n+$/.test(e)?e.substring(1):t?e.replace(/^(?! *$)/gm,t):e}const ku=(e,t,n)=>e.endsWith("\n")?bu(n,t):n.includes("\n")?"\n"+bu(n,t):(e.endsWith(" ")?"":" ")+n,Su="flow",Nu="block",Iu="quoted";function Du(e,t,n="flow",{indentAtStart:s,lineWidth:r=80,minContentWidth:i=20,onFold:o,onOverflow:a}={}){if(!r||r<0)return e;r<i&&(i=0);const c=Math.max(1+i,1+r-t.length);if(e.length<=c)return e;const l=[],u={};let h,d,p=r-t.length;"number"==typeof s&&(s>r-Math.max(2,i)?l.push(0):p=r-s);let f=!1,m=-1,E=-1,T=-1;n===Nu&&(m=yu(e,m,t.length),-1!==m&&(p=m+c));for(let s;s=e[m+=1];){if(n===Iu&&"\\"===s){switch(E=m,e[m+1]){case"x":m+=3;break;case"u":m+=5;break;case"U":m+=9;break;default:m+=1}T=m}if("\n"===s)n===Nu&&(m=yu(e,m,t.length)),p=m+t.length+c,h=void 0;else{if(" "===s&&d&&" "!==d&&"\n"!==d&&"\t"!==d){const t=e[m+1];t&&" "!==t&&"\n"!==t&&"\t"!==t&&(h=m)}if(m>=p)if(h)l.push(h),p=h+c,h=void 0;else if(n===Iu){for(;" "===d||"\t"===d;)d=s,s=e[m+=1],f=!0;const t=m>T+1?m-2:E-1;if(u[t])return e;l.push(t),u[t]=!0,p=t+c,h=void 0}else f=!0}d=s}if(f&&a&&a(),0===l.length)return e;o&&o();let _=e.slice(0,l[0]);for(let s=0;s<l.length;++s){const r=l[s],i=l[s+1]||e.length;0===r?_=`\n${t}${e.slice(0,i)}`:(n===Iu&&u[r]&&(_+=`${e[r]}\\`),_+=`\n${t}${e.slice(r+1,i)}`)}return _}function yu(e,t,n){let s=t,r=t+1,i=e[r];for(;" "===i||"\t"===i;)if(t<r+n)i=e[++t];else{do{i=e[++t]}while(i&&"\n"!==i);s=t,r=t+1,i=e[r]}return s}const Ou=(e,t)=>({indentAtStart:t?e.indent.length:e.indentAtStart,lineWidth:e.options.lineWidth,minContentWidth:e.options.minContentWidth}),Lu=e=>/^(%|---|\.\.\.)/m.test(e);function Ru(e,t){const n=JSON.stringify(e);if(t.options.doubleQuotedAsJSON)return n;const{implicitKey:s}=t,r=t.options.doubleQuotedMinMultiLineLength,i=t.indent||(Lu(e)?"  ":"");let o="",a=0;for(let e=0,t=n[e];t;t=n[++e])if(" "===t&&"\\"===n[e+1]&&"n"===n[e+2]&&(o+=n.slice(a,e)+"\\ ",e+=1,a=e,t="\\"),"\\"===t)switch(n[e+1]){case"u":{o+=n.slice(a,e);const t=n.substr(e+2,4);switch(t){case"0000":o+="\\0";break;case"0007":o+="\\a";break;case"000b":o+="\\v";break;case"001b":o+="\\e";break;case"0085":o+="\\N";break;case"00a0":o+="\\_";break;case"2028":o+="\\L";break;case"2029":o+="\\P";break;default:"00"===t.substr(0,2)?o+="\\x"+t.substr(2):o+=n.substr(e,6)}e+=5,a=e+1}break;case"n":if(s||'"'===n[e+2]||n.length<r)e+=1;else{for(o+=n.slice(a,e)+"\n\n";"\\"===n[e+2]&&"n"===n[e+3]&&'"'!==n[e+4];)o+="\n",e+=2;o+=i," "===n[e+2]&&(o+="\\"),e+=1,a=e+1}break;default:e+=1}return o=a?o+n.slice(a):n,s?o:Du(o,i,Iu,Ou(t,!1))}function Mu(e,t){if(!1===t.options.singleQuote||t.implicitKey&&e.includes("\n")||/[ \t]\n|\n[ \t]/.test(e))return Ru(e,t);const n=t.indent||(Lu(e)?"  ":""),s="'"+e.replace(/'/g,"''").replace(/\n+/g,`$&\n${n}`)+"'";return t.implicitKey?s:Du(s,n,Su,Ou(t,!1))}function vu(e,t){const{singleQuote:n}=t.options;let s;if(!1===n)s=Ru;else{const t=e.includes('"'),r=e.includes("'");s=t&&!r?Mu:r&&!t?Ru:n?Mu:Ru}return s(e,t)}let xu;try{xu=new RegExp("(^|(?<!\n))\n+(?!\n|$)","g")}catch{xu=/\n+(?!\n|$)/g}function wu({comment:e,type:t,value:n},s,r,i){const{blockQuote:o,commentString:a,lineWidth:c}=s.options;if(!o||/\n[\t ]+$/.test(n)||/^\s*$/.test(n))return vu(n,s);const l=s.indent||(s.forceBlockIndent||Lu(n)?"  ":""),u="literal"===o||"folded"!==o&&t!==Eu.BLOCK_FOLDED&&(t===Eu.BLOCK_LITERAL||!function(e,t,n){if(!t||t<0)return!1;const s=t-n,r=e.length;if(r<=s)return!1;for(let t=0,n=0;t<r;++t)if("\n"===e[t]){if(t-n>s)return!0;if(n=t+1,r-n<=s)return!1}return!0}(n,c,l.length));if(!n)return u?"|\n":">\n";let h,d;for(d=n.length;d>0;--d){const e=n[d-1];if("\n"!==e&&"\t"!==e&&" "!==e)break}let p=n.substring(d);const f=p.indexOf("\n");-1===f?h="-":n===p||f!==p.length-1?(h="+",i&&i()):h="",p&&(n=n.slice(0,-p.length),"\n"===p[p.length-1]&&(p=p.slice(0,-1)),p=p.replace(xu,`$&${l}`));let m,E=!1,T=-1;for(m=0;m<n.length;++m){const e=n[m];if(" "===e)E=!0;else{if("\n"!==e)break;T=m}}let _=n.substring(0,T<m?T+1:m);_&&(n=n.substring(_.length),_=_.replace(/\n+/g,`$&${l}`));let A=(E?l?"2":"1":"")+h;if(e&&(A+=" "+a(e.replace(/ ?[\r\n]+/g," ")),r&&r()),!u){const e=n.replace(/\n+/g,"\n$&").replace(/(?:^|\n)([\t ].*)(?:([\n\t ]*)\n(?![\n\t ]))?/g,"$1$2").replace(/\n+/g,`$&${l}`);let r=!1;const i=Ou(s,!0);"folded"!==o&&t!==Eu.BLOCK_FOLDED&&(i.onOverflow=()=>{r=!0});const a=Du(`${_}${e}${p}`,l,Nu,i);if(!r)return`>${A}\n${l}${a}`}return`|${A}\n${l}${_}${n=n.replace(/\n+/g,`$&${l}`)}${p}`}function Fu(e,t,n,s){const{implicitKey:r,inFlow:i}=t,o="string"==typeof e.value?e:Object.assign({},e,{value:String(e.value)});let{type:a}=e;a!==Eu.QUOTE_DOUBLE&&/[\x00-\x08\x0b-\x1f\x7f-\x9f\u{D800}-\u{DFFF}]/u.test(o.value)&&(a=Eu.QUOTE_DOUBLE);const c=e=>{switch(e){case Eu.BLOCK_FOLDED:case Eu.BLOCK_LITERAL:return r||i?vu(o.value,t):wu(o,t,n,s);case Eu.QUOTE_DOUBLE:return Ru(o.value,t);case Eu.QUOTE_SINGLE:return Mu(o.value,t);case Eu.PLAIN:return function(e,t,n,s){const{type:r,value:i}=e,{actualString:o,implicitKey:a,indent:c,indentStep:l,inFlow:u}=t;if(a&&i.includes("\n")||u&&/[[\]{},]/.test(i))return vu(i,t);if(!i||/^[\n\t ,[\]{}#&*!|>'"%@`]|^[?-]$|^[?-][ \t]|[\n:][ \t]|[ \t]\n|[\n\t ]#|[\n\t :]$/.test(i))return a||u||!i.includes("\n")?vu(i,t):wu(e,t,n,s);if(!a&&!u&&r!==Eu.PLAIN&&i.includes("\n"))return wu(e,t,n,s);if(Lu(i)){if(""===c)return t.forceBlockIndent=!0,wu(e,t,n,s);if(a&&c===l)return vu(i,t)}const h=i.replace(/\n+/g,`$&\n${c}`);if(o){const e=e=>{var t;return e.default&&"tag:yaml.org,2002:str"!==e.tag&&(null==(t=e.test)?void 0:t.test(h))},{compat:n,tags:s}=t.doc.schema;if(s.some(e)||(null==n?void 0:n.some(e)))return vu(i,t)}return a?h:Du(h,c,Su,Ou(t,!1))}(o,t,n,s);default:return null}};let l=c(a);if(null===l){const{defaultKeyType:e,defaultStringType:n}=t.options,s=r&&e||n;if(l=c(s),null===l)throw new Error(`Unsupported default string type ${s}`)}return l}function Pu(e,t){const n=Object.assign({blockQuote:!0,commentString:Cu,defaultKeyType:null,defaultStringType:"PLAIN",directives:null,doubleQuotedAsJSON:!1,doubleQuotedMinMultiLineLength:40,falseStr:"false",flowCollectionPadding:!0,indentSeq:!0,lineWidth:80,minContentWidth:20,nullStr:"null",simpleKeys:!1,singleQuote:null,trueStr:"true",verifyAliasOrder:!0},e.schema.toStringOptions,t);let s;switch(n.collectionStyle){case"block":s=!1;break;case"flow":s=!0;break;default:s=null}return{anchors:new Set,doc:e,flowCollectionPadding:n.flowCollectionPadding?" ":"",indent:"",indentStep:"number"==typeof n.indent?" ".repeat(n.indent):"  ",inFlow:s,options:n}}function Bu(e,t,n,s){var r;if(Ql(e))return e.toString(t,n,s);if(Kl(e)){if(t.doc.directives)return e.toString(t);if(null==(r=t.resolvedAliases)?void 0:r.has(e))throw new TypeError("Cannot stringify circular structure without alias nodes");t.resolvedAliases?t.resolvedAliases.add(e):t.resolvedAliases=new Set([e]),e=e.resolve(t.doc)}let i;const o=Jl(e)?e:t.doc.createNode(e,{onTagObj:e=>i=e});i||(i=function(e,t){var n;if(t.tag){const n=e.filter((e=>e.tag===t.tag));if(n.length>0)return n.find((e=>e.format===t.format))??n[0]}let s,r;if(Wl(t)){r=t.value;let n=e.filter((e=>{var t;return null==(t=e.identify)?void 0:t.call(e,r)}));if(n.length>1){const e=n.filter((e=>e.test));e.length>0&&(n=e)}s=n.find((e=>e.format===t.format))??n.find((e=>!e.format))}else r=t,s=e.find((e=>e.nodeClass&&r instanceof e.nodeClass));if(!s){const e=(null==(n=null==r?void 0:r.constructor)?void 0:n.name)??typeof r;throw new Error(`Tag not resolved for ${e} value`)}return s}(t.doc.schema.tags,o));const a=function(e,t,{anchors:n,doc:s}){if(!s.directives)return"";const r=[],i=(Wl(e)||Zl(e))&&e.anchor;i&&au(i)&&(n.add(i),r.push(`&${i}`));const o=e.tag?e.tag:t.default?null:t.tag;return o&&r.push(s.directives.tagString(o)),r.join(" ")}(o,i,t);a.length>0&&(t.indentAtStart=(t.indentAtStart??0)+a.length+1);const c="function"==typeof i.stringify?i.stringify(o,t,n,s):Wl(o)?Fu(o,t,n,s):o.toString(t,n,s);return a?Wl(o)||"{"===c[0]||"["===c[0]?`${a} ${c}`:`${a}\n${t.indent}${c}`:c}function Uu(e,t){"debug"!==e&&"warn"!==e||("undefined"!=typeof process&&process.emitWarning?process.emitWarning(t):console.warn(t))}const Hu="<<",Gu={identify:e=>e===Hu||"symbol"==typeof e&&e.description===Hu,default:"key",tag:"tag:yaml.org,2002:merge",test:/^<<$/,resolve:()=>Object.assign(new Eu(Symbol(Hu)),{addToJSMap:$u}),stringify:()=>Hu};function $u(e,t,n){if(n=e&&Kl(n)?n.resolve(e.doc):n,Xl(n))for(const s of n.items)qu(e,t,s);else if(Array.isArray(n))for(const s of n)qu(e,t,s);else qu(e,t,n)}function qu(e,t,n){const s=e&&Kl(n)?n.resolve(e.doc):n;if(!zl(s))throw new Error("Merge sources must be maps or map aliases");const r=s.toJSON(null,e,Map);for(const[e,n]of r)t instanceof Map?t.has(e)||t.set(e,n):t instanceof Set?t.add(e):Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{value:n,writable:!0,enumerable:!0,configurable:!0});return t}function Yu(e,t,{key:n,value:s}){if(Jl(n)&&n.addToJSMap)n.addToJSMap(e,t,s);else if(((e,t)=>(Gu.identify(t)||Wl(t)&&(!t.type||t.type===Eu.PLAIN)&&Gu.identify(t.value))&&(null==e?void 0:e.doc.schema.tags.some((e=>e.tag===Gu.tag&&e.default))))(e,n))$u(e,t,s);else{const r=hu(n,"",e);if(t instanceof Map)t.set(r,hu(s,r,e));else if(t instanceof Set)t.add(r);else{const i=function(e,t,n){if(null===t)return"";if("object"!=typeof t)return String(t);if(Jl(e)&&(null==n?void 0:n.doc)){const t=Pu(n.doc,{});t.anchors=new Set;for(const e of n.anchors.keys())t.anchors.add(e.anchor);t.inFlow=!0,t.inStringifyKey=!0;const s=e.toString(t);if(!n.mapKeyWarned){let e=JSON.stringify(s);e.length>40&&(e=e.substring(0,36)+'..."'),Uu(n.doc.options.logLevel,`Keys with collection values will be stringified due to JS Object restrictions: ${e}. Set mapAsMap: true to use object keys.`),n.mapKeyWarned=!0}return s}return JSON.stringify(t)}(n,r,e),o=hu(s,i,e);i in t?Object.defineProperty(t,i,{value:o,writable:!0,enumerable:!0,configurable:!0}):t[i]=o}}return t}function ju(e,t,n){const s=Tu(e,void 0,n),r=Tu(t,void 0,n);return new Ku(s,r)}class Ku{constructor(e,t=null){Object.defineProperty(this,jl,{value:$l}),this.key=e,this.value=t}clone(e){let{key:t,value:n}=this;return Jl(t)&&(t=t.clone(e)),Jl(n)&&(n=n.clone(e)),new Ku(t,n)}toJSON(e,t){return Yu(t,(null==t?void 0:t.mapAsMap)?new Map:{},this)}toString(e,t,n){return(null==e?void 0:e.doc)?function({key:e,value:t},n,s,r){const{allNullValues:i,doc:o,indent:a,indentStep:c,options:{commentString:l,indentSeq:u,simpleKeys:h}}=n;let d=Jl(e)&&e.comment||null;if(h){if(d)throw new Error("With simple keys, key nodes cannot have comments");if(Zl(e)||!Jl(e)&&"object"==typeof e)throw new Error("With simple keys, collection cannot be used as a key value")}let p=!h&&(!e||d&&null==t&&!n.inFlow||Zl(e)||(Wl(e)?e.type===Eu.BLOCK_FOLDED||e.type===Eu.BLOCK_LITERAL:"object"==typeof e));n=Object.assign({},n,{allNullValues:!1,implicitKey:!p&&(h||!i),indent:a+c});let f,m,E,T=!1,_=!1,A=Bu(e,n,(()=>T=!0),(()=>_=!0));if(!p&&!n.inFlow&&A.length>1024){if(h)throw new Error("With simple keys, single line scalar must not span more than 1024 characters");p=!0}if(n.inFlow){if(i||null==t)return T&&s&&s(),""===A?"?":p?`? ${A}`:A}else if(i&&!h||null==t&&p)return A=`? ${A}`,d&&!T?A+=ku(A,n.indent,l(d)):_&&r&&r(),A;T&&(d=null),p?(d&&(A+=ku(A,n.indent,l(d))),A=`? ${A}\n${a}:`):(A=`${A}:`,d&&(A+=ku(A,n.indent,l(d)))),Jl(t)?(f=!!t.spaceBefore,m=t.commentBefore,E=t.comment):(f=!1,m=null,E=null,t&&"object"==typeof t&&(t=o.createNode(t))),n.implicitKey=!1,p||d||!Wl(t)||(n.indentAtStart=A.length+1),_=!1,u||!(c.length>=2)||n.inFlow||p||!Xl(t)||t.flow||t.tag||t.anchor||(n.indent=n.indent.substring(2));let g=!1;const C=Bu(t,n,(()=>g=!0),(()=>_=!0));let b=" ";if(d||f||m)b=f?"\n":"",m&&(b+=`\n${bu(l(m),n.indent)}`),""!==C||n.inFlow?b+=`\n${n.indent}`:"\n"===b&&(b="\n\n");else if(!p&&Zl(t)){const e=C[0],s=C.indexOf("\n"),r=-1!==s,i=n.inFlow??t.flow??0===t.items.length;if(r||!i){let t=!1;if(r&&("&"===e||"!"===e)){let n=C.indexOf(" ");"&"===e&&-1!==n&&n<s&&"!"===C[n+1]&&(n=C.indexOf(" ",n+1)),(-1===n||s<n)&&(t=!0)}t||(b=`\n${n.indent}`)}}else""!==C&&"\n"!==C[0]||(b="");return A+=b+C,n.inFlow?g&&s&&s():E&&!g?A+=ku(A,n.indent,l(E)):_&&r&&r(),A}(this,e,t,n):JSON.stringify(this)}}function Vu(e,t,n){return(t.inFlow??e.flow?Qu:zu)(e,t,n)}function zu({comment:e,items:t},n,{blockItemPrefix:s,flowChars:r,itemIndent:i,onChompKeep:o,onComment:a}){const{indent:c,options:{commentString:l}}=n,u=Object.assign({},n,{indent:i,type:null});let h=!1;const d=[];for(let e=0;e<t.length;++e){const r=t[e];let o=null;if(Jl(r))!h&&r.spaceBefore&&d.push(""),Wu(n,d,r.commentBefore,h),r.comment&&(o=r.comment);else if(Ql(r)){const e=Jl(r.key)?r.key:null;e&&(!h&&e.spaceBefore&&d.push(""),Wu(n,d,e.commentBefore,h))}h=!1;let a=Bu(r,u,(()=>o=null),(()=>h=!0));o&&(a+=ku(a,i,l(o))),h&&o&&(h=!1),d.push(s+a)}let p;if(0===d.length)p=r.start+r.end;else{p=d[0];for(let e=1;e<d.length;++e){const t=d[e];p+=t?`\n${c}${t}`:"\n"}}return e?(p+="\n"+bu(l(e),c),a&&a()):h&&o&&o(),p}function Qu({items:e},t,{flowChars:n,itemIndent:s}){const{indent:r,indentStep:i,flowCollectionPadding:o,options:{commentString:a}}=t;s+=i;const c=Object.assign({},t,{indent:s,inFlow:!0,type:null});let l=!1,u=0;const h=[];for(let n=0;n<e.length;++n){const r=e[n];let i=null;if(Jl(r))r.spaceBefore&&h.push(""),Wu(t,h,r.commentBefore,!1),r.comment&&(i=r.comment);else if(Ql(r)){const e=Jl(r.key)?r.key:null;e&&(e.spaceBefore&&h.push(""),Wu(t,h,e.commentBefore,!1),e.comment&&(l=!0));const n=Jl(r.value)?r.value:null;n?(n.comment&&(i=n.comment),n.commentBefore&&(l=!0)):null==r.value&&(null==e?void 0:e.comment)&&(i=e.comment)}i&&(l=!0);let o=Bu(r,c,(()=>i=null));n<e.length-1&&(o+=","),i&&(o+=ku(o,s,a(i))),!l&&(h.length>u||o.includes("\n"))&&(l=!0),h.push(o),u=h.length}const{start:d,end:p}=n;if(0===h.length)return d+p;if(!l){const e=h.reduce(((e,t)=>e+t.length+2),2);l=t.options.lineWidth>0&&e>t.options.lineWidth}if(l){let e=d;for(const t of h)e+=t?`\n${i}${r}${t}`:"\n";return`${e}\n${r}${p}`}return`${d}${o}${h.join(" ")}${o}${p}`}function Wu({indent:e,options:{commentString:t}},n,s,r){if(s&&r&&(s=s.replace(/^\n+/,"")),s){const r=bu(t(s),e);n.push(r.trimStart())}}function Xu(e,t){const n=Wl(t)?t.value:t;for(const s of e)if(Ql(s)){if(s.key===t||s.key===n)return s;if(Wl(s.key)&&s.key.value===n)return s}}class Zu extends gu{static get tagName(){return"tag:yaml.org,2002:map"}constructor(e){super(Gl,e),this.items=[]}static from(e,t,n){const{keepUndefined:s,replacer:r}=n,i=new this(e),o=(e,o)=>{if("function"==typeof r)o=r.call(t,e,o);else if(Array.isArray(r)&&!r.includes(e))return;(void 0!==o||s)&&i.items.push(ju(e,o,n))};if(t instanceof Map)for(const[e,n]of t)o(e,n);else if(t&&"object"==typeof t)for(const e of Object.keys(t))o(e,t[e]);return"function"==typeof e.sortMapEntries&&i.items.sort(e.sortMapEntries),i}add(e,t){var n;let s;s=Ql(e)?e:e&&"object"==typeof e&&"key"in e?new Ku(e.key,e.value):new Ku(e,null==e?void 0:e.value);const r=Xu(this.items,s.key),i=null==(n=this.schema)?void 0:n.sortMapEntries;if(r){if(!t)throw new Error(`Key ${s.key} already set`);Wl(r.value)&&mu(s.value)?r.value.value=s.value:r.value=s.value}else if(i){const e=this.items.findIndex((e=>i(s,e)<0));-1===e?this.items.push(s):this.items.splice(e,0,s)}else this.items.push(s)}delete(e){const t=Xu(this.items,e);if(!t)return!1;return this.items.splice(this.items.indexOf(t),1).length>0}get(e,t){const n=Xu(this.items,e),s=null==n?void 0:n.value;return(!t&&Wl(s)?s.value:s)??void 0}has(e){return!!Xu(this.items,e)}set(e,t){this.add(new Ku(e,t),!0)}toJSON(e,t,n){const s=n?new n:(null==t?void 0:t.mapAsMap)?new Map:{};(null==t?void 0:t.onCreate)&&t.onCreate(s);for(const e of this.items)Yu(t,s,e);return s}toString(e,t,n){if(!e)return JSON.stringify(this);for(const e of this.items)if(!Ql(e))throw new Error(`Map items must all be pairs; found ${JSON.stringify(e)} instead`);return!e.allNullValues&&this.hasAllNullValues(!1)&&(e=Object.assign({},e,{allNullValues:!0})),Vu(this,e,{blockItemPrefix:"",flowChars:{start:"{",end:"}"},itemIndent:e.indent||"",onChompKeep:n,onComment:t})}}const Ju={collection:"map",default:!0,nodeClass:Zu,tag:"tag:yaml.org,2002:map",resolve:(e,t)=>(zl(e)||t("Expected a mapping for this tag"),e),createNode:(e,t,n)=>Zu.from(e,t,n)};class eh extends gu{static get tagName(){return"tag:yaml.org,2002:seq"}constructor(e){super(Yl,e),this.items=[]}add(e){this.items.push(e)}delete(e){const t=th(e);if("number"!=typeof t)return!1;return this.items.splice(t,1).length>0}get(e,t){const n=th(e);if("number"!=typeof n)return;const s=this.items[n];return!t&&Wl(s)?s.value:s}has(e){const t=th(e);return"number"==typeof t&&t<this.items.length}set(e,t){const n=th(e);if("number"!=typeof n)throw new Error(`Expected a valid index, not ${e}.`);const s=this.items[n];Wl(s)&&mu(t)?s.value=t:this.items[n]=t}toJSON(e,t){const n=[];(null==t?void 0:t.onCreate)&&t.onCreate(n);let s=0;for(const e of this.items)n.push(hu(e,String(s++),t));return n}toString(e,t,n){return e?Vu(this,e,{blockItemPrefix:"- ",flowChars:{start:"[",end:"]"},itemIndent:(e.indent||"")+"  ",onChompKeep:n,onComment:t}):JSON.stringify(this)}static from(e,t,n){const{replacer:s}=n,r=new this(e);if(t&&Symbol.iterator in Object(t)){let e=0;for(let i of t){if("function"==typeof s){const n=t instanceof Set?i:String(e++);i=s.call(t,n,i)}r.items.push(Tu(i,void 0,n))}}return r}}function th(e){let t=Wl(e)?e.value:e;return t&&"string"==typeof t&&(t=Number(t)),"number"==typeof t&&Number.isInteger(t)&&t>=0?t:null}const nh={collection:"seq",default:!0,nodeClass:eh,tag:"tag:yaml.org,2002:seq",resolve:(e,t)=>(Xl(e)||t("Expected a sequence for this tag"),e),createNode:(e,t,n)=>eh.from(e,t,n)},sh={identify:e=>"string"==typeof e,default:!0,tag:"tag:yaml.org,2002:str",resolve:e=>e,stringify:(e,t,n,s)=>Fu(e,t=Object.assign({actualString:!0},t),n,s)},rh={identify:e=>null==e,createNode:()=>new Eu(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new Eu(null),stringify:({source:e},t)=>"string"==typeof e&&rh.test.test(e)?e:t.options.nullStr},ih={identify:e=>"boolean"==typeof e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:e=>new Eu("t"===e[0]||"T"===e[0]),stringify({source:e,value:t},n){if(e&&ih.test.test(e)){if(t===("t"===e[0]||"T"===e[0]))return e}return t?n.options.trueStr:n.options.falseStr}};function oh({format:e,minFractionDigits:t,tag:n,value:s}){if("bigint"==typeof s)return String(s);const r="number"==typeof s?s:Number(s);if(!isFinite(r))return isNaN(r)?".nan":r<0?"-.inf":".inf";let i=JSON.stringify(s);if(!e&&t&&(!n||"tag:yaml.org,2002:float"===n)&&/^\d/.test(i)){let e=i.indexOf(".");e<0&&(e=i.length,i+=".");let n=t-(i.length-e-1);for(;n-- >0;)i+="0"}return i}const ah={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:e=>"nan"===e.slice(-3).toLowerCase()?NaN:"-"===e[0]?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:oh},ch={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:e=>parseFloat(e),stringify(e){const t=Number(e.value);return isFinite(t)?t.toExponential():oh(e)}},lh={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(e){const t=new Eu(parseFloat(e)),n=e.indexOf(".");return-1!==n&&"0"===e[e.length-1]&&(t.minFractionDigits=e.length-n-1),t},stringify:oh},uh=e=>"bigint"==typeof e||Number.isInteger(e),hh=(e,t,n,{intAsBigInt:s})=>s?BigInt(e):parseInt(e.substring(t),n);function dh(e,t,n){const{value:s}=e;return uh(s)&&s>=0?n+s.toString(t):oh(e)}const ph={identify:e=>uh(e)&&e>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(e,t,n)=>hh(e,2,8,n),stringify:e=>dh(e,8,"0o")},fh={identify:uh,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(e,t,n)=>hh(e,0,10,n),stringify:oh},mh={identify:e=>uh(e)&&e>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(e,t,n)=>hh(e,2,16,n),stringify:e=>dh(e,16,"0x")},Eh=[Ju,nh,sh,rh,ih,ph,fh,mh,ah,ch,lh];function Th(e){return"bigint"==typeof e||Number.isInteger(e)}const _h=({value:e})=>JSON.stringify(e),Ah=[Ju,nh].concat([{identify:e=>"string"==typeof e,default:!0,tag:"tag:yaml.org,2002:str",resolve:e=>e,stringify:_h},{identify:e=>null==e,createNode:()=>new Eu(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:_h},{identify:e=>"boolean"==typeof e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^true$|^false$/,resolve:e=>"true"===e,stringify:_h},{identify:Th,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(e,t,{intAsBigInt:n})=>n?BigInt(e):parseInt(e,10),stringify:({value:e})=>Th(e)?e.toString():JSON.stringify(e)},{identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:e=>parseFloat(e),stringify:_h}],{default:!0,tag:"",test:/^/,resolve:(e,t)=>(t(`Unresolved plain scalar ${JSON.stringify(e)}`),e)}),gh={identify:e=>e instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(e,t){if("function"==typeof Buffer)return Buffer.from(e,"base64");if("function"==typeof atob){const t=atob(e.replace(/[\n\r]/g,"")),n=new Uint8Array(t.length);for(let e=0;e<t.length;++e)n[e]=t.charCodeAt(e);return n}return t("This environment does not support reading binary tags; either Buffer or atob is required"),e},stringify({comment:e,type:t,value:n},s,r,i){const o=n;let a;if("function"==typeof Buffer)a=o instanceof Buffer?o.toString("base64"):Buffer.from(o.buffer).toString("base64");else{if("function"!=typeof btoa)throw new Error("This environment does not support writing binary tags; either Buffer or btoa is required");{let e="";for(let t=0;t<o.length;++t)e+=String.fromCharCode(o[t]);a=btoa(e)}}if(t||(t=Eu.BLOCK_LITERAL),t!==Eu.QUOTE_DOUBLE){const e=Math.max(s.options.lineWidth-s.indent.length,s.options.minContentWidth),n=Math.ceil(a.length/e),r=new Array(n);for(let t=0,s=0;t<n;++t,s+=e)r[t]=a.substr(s,e);a=r.join(t===Eu.BLOCK_LITERAL?"\n":" ")}return Fu({comment:e,type:t,value:a},s,r,i)}};function Ch(e,t){if(Xl(e))for(let n=0;n<e.items.length;++n){let s=e.items[n];if(!Ql(s)){if(zl(s)){s.items.length>1&&t("Each pair must have its own sequence indicator");const e=s.items[0]||new Ku(new Eu(null));if(s.commentBefore&&(e.key.commentBefore=e.key.commentBefore?`${s.commentBefore}\n${e.key.commentBefore}`:s.commentBefore),s.comment){const t=e.value??e.key;t.comment=t.comment?`${s.comment}\n${t.comment}`:s.comment}s=e}e.items[n]=Ql(s)?s:new Ku(s)}}else t("Expected a sequence for this tag");return e}function bh(e,t,n){const{replacer:s}=n,r=new eh(e);r.tag="tag:yaml.org,2002:pairs";let i=0;if(t&&Symbol.iterator in Object(t))for(let e of t){let o,a;if("function"==typeof s&&(e=s.call(t,String(i++),e)),Array.isArray(e)){if(2!==e.length)throw new TypeError(`Expected [key, value] tuple: ${e}`);o=e[0],a=e[1]}else if(e&&e instanceof Object){const t=Object.keys(e);if(1!==t.length)throw new TypeError(`Expected tuple with one key, not ${t.length} keys`);o=t[0],a=e[o]}else o=e;r.items.push(ju(o,a,n))}return r}const kh={collection:"seq",default:!1,tag:"tag:yaml.org,2002:pairs",resolve:Ch,createNode:bh};class Sh extends eh{constructor(){super(),this.add=Zu.prototype.add.bind(this),this.delete=Zu.prototype.delete.bind(this),this.get=Zu.prototype.get.bind(this),this.has=Zu.prototype.has.bind(this),this.set=Zu.prototype.set.bind(this),this.tag=Sh.tag}toJSON(e,t){if(!t)return super.toJSON(e);const n=new Map;(null==t?void 0:t.onCreate)&&t.onCreate(n);for(const e of this.items){let s,r;if(Ql(e)?(s=hu(e.key,"",t),r=hu(e.value,s,t)):s=hu(e,"",t),n.has(s))throw new Error("Ordered maps must not include duplicate keys");n.set(s,r)}return n}static from(e,t,n){const s=bh(e,t,n),r=new this;return r.items=s.items,r}}Sh.tag="tag:yaml.org,2002:omap";const Nh={collection:"seq",identify:e=>e instanceof Map,nodeClass:Sh,default:!1,tag:"tag:yaml.org,2002:omap",resolve(e,t){const n=Ch(e,t),s=[];for(const{key:e}of n.items)Wl(e)&&(s.includes(e.value)?t(`Ordered maps must not include duplicate keys: ${e.value}`):s.push(e.value));return Object.assign(new Sh,n)},createNode:(e,t,n)=>Sh.from(e,t,n)};function Ih({value:e,source:t},n){return t&&(e?Dh:yh).test.test(t)?t:e?n.options.trueStr:n.options.falseStr}const Dh={identify:e=>!0===e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new Eu(!0),stringify:Ih},yh={identify:e=>!1===e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new Eu(!1),stringify:Ih},Oh={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:e=>"nan"===e.slice(-3).toLowerCase()?NaN:"-"===e[0]?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:oh},Lh={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:e=>parseFloat(e.replace(/_/g,"")),stringify(e){const t=Number(e.value);return isFinite(t)?t.toExponential():oh(e)}},Rh={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(e){const t=new Eu(parseFloat(e.replace(/_/g,""))),n=e.indexOf(".");if(-1!==n){const s=e.substring(n+1).replace(/_/g,"");"0"===s[s.length-1]&&(t.minFractionDigits=s.length)}return t},stringify:oh},Mh=e=>"bigint"==typeof e||Number.isInteger(e);function vh(e,t,n,{intAsBigInt:s}){const r=e[0];if("-"!==r&&"+"!==r||(t+=1),e=e.substring(t).replace(/_/g,""),s){switch(n){case 2:e=`0b${e}`;break;case 8:e=`0o${e}`;break;case 16:e=`0x${e}`}const t=BigInt(e);return"-"===r?BigInt(-1)*t:t}const i=parseInt(e,n);return"-"===r?-1*i:i}function xh(e,t,n){const{value:s}=e;if(Mh(s)){const e=s.toString(t);return s<0?"-"+n+e.substr(1):n+e}return oh(e)}const wh={identify:Mh,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(e,t,n)=>vh(e,2,2,n),stringify:e=>xh(e,2,"0b")},Fh={identify:Mh,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(e,t,n)=>vh(e,1,8,n),stringify:e=>xh(e,8,"0")},Ph={identify:Mh,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(e,t,n)=>vh(e,0,10,n),stringify:oh},Bh={identify:Mh,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(e,t,n)=>vh(e,2,16,n),stringify:e=>xh(e,16,"0x")};class Uh extends Zu{constructor(e){super(e),this.tag=Uh.tag}add(e){let t;t=Ql(e)?e:e&&"object"==typeof e&&"key"in e&&"value"in e&&null===e.value?new Ku(e.key,null):new Ku(e,null);Xu(this.items,t.key)||this.items.push(t)}get(e,t){const n=Xu(this.items,e);return!t&&Ql(n)?Wl(n.key)?n.key.value:n.key:n}set(e,t){if("boolean"!=typeof t)throw new Error("Expected boolean value for set(key, value) in a YAML set, not "+typeof t);const n=Xu(this.items,e);n&&!t?this.items.splice(this.items.indexOf(n),1):!n&&t&&this.items.push(new Ku(e))}toJSON(e,t){return super.toJSON(e,t,Set)}toString(e,t,n){if(!e)return JSON.stringify(this);if(this.hasAllNullValues(!0))return super.toString(Object.assign({},e,{allNullValues:!0}),t,n);throw new Error("Set items must all have null values")}static from(e,t,n){const{replacer:s}=n,r=new this(e);if(t&&Symbol.iterator in Object(t))for(let e of t)"function"==typeof s&&(e=s.call(t,e,e)),r.items.push(ju(e,null,n));return r}}Uh.tag="tag:yaml.org,2002:set";const Hh={collection:"map",identify:e=>e instanceof Set,nodeClass:Uh,default:!1,tag:"tag:yaml.org,2002:set",createNode:(e,t,n)=>Uh.from(e,t,n),resolve(e,t){if(zl(e)){if(e.hasAllNullValues(!0))return Object.assign(new Uh,e);t("Set items must all have null values")}else t("Expected a mapping for this tag");return e}};function Gh(e,t){const n=e[0],s="-"===n||"+"===n?e.substring(1):e,r=e=>t?BigInt(e):Number(e),i=s.replace(/_/g,"").split(":").reduce(((e,t)=>e*r(60)+r(t)),r(0));return"-"===n?r(-1)*i:i}function $h(e){let{value:t}=e,n=e=>e;if("bigint"==typeof t)n=e=>BigInt(e);else if(isNaN(t)||!isFinite(t))return oh(e);let s="";t<0&&(s="-",t*=n(-1));const r=n(60),i=[t%r];return t<60?i.unshift(0):(t=(t-i[0])/r,i.unshift(t%r),t>=60&&(t=(t-i[0])/r,i.unshift(t))),s+i.map((e=>String(e).padStart(2,"0"))).join(":").replace(/000000\d*$/,"")}const qh={identify:e=>"bigint"==typeof e||Number.isInteger(e),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(e,t,{intAsBigInt:n})=>Gh(e,n),stringify:$h},Yh={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:e=>Gh(e,!1),stringify:$h},jh={identify:e=>e instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(e){const t=e.match(jh.test);if(!t)throw new Error("!!timestamp expects a date, starting with yyyy-mm-dd");const[,n,s,r,i,o,a]=t.map(Number),c=t[7]?Number((t[7]+"00").substr(1,3)):0;let l=Date.UTC(n,s-1,r,i||0,o||0,a||0,c);const u=t[8];if(u&&"Z"!==u){let e=Gh(u,!1);Math.abs(e)<30&&(e*=60),l-=6e4*e}return new Date(l)},stringify:({value:e})=>e.toISOString().replace(/(T00:00:00)?\.000Z$/,"")},Kh=[Ju,nh,sh,rh,Dh,yh,wh,Fh,Ph,Bh,Oh,Lh,Rh,gh,Gu,Nh,kh,Hh,qh,Yh,jh],Vh=new Map([["core",Eh],["failsafe",[Ju,nh,sh]],["json",Ah],["yaml11",Kh],["yaml-1.1",Kh]]),zh={binary:gh,bool:ih,float:lh,floatExp:ch,floatNaN:ah,floatTime:Yh,int:fh,intHex:mh,intOct:ph,intTime:qh,map:Ju,merge:Gu,null:rh,omap:Nh,pairs:kh,seq:nh,set:Hh,timestamp:jh},Qh={"tag:yaml.org,2002:binary":gh,"tag:yaml.org,2002:merge":Gu,"tag:yaml.org,2002:omap":Nh,"tag:yaml.org,2002:pairs":kh,"tag:yaml.org,2002:set":Hh,"tag:yaml.org,2002:timestamp":jh};function Wh(e,t,n){const s=Vh.get(t);if(s&&!e)return n&&!s.includes(Gu)?s.concat(Gu):s.slice();let r=s;if(!r){if(!Array.isArray(e)){const e=Array.from(Vh.keys()).filter((e=>"yaml11"!==e)).map((e=>JSON.stringify(e))).join(", ");throw new Error(`Unknown schema "${t}"; use one of ${e} or define customTags array`)}r=[]}if(Array.isArray(e))for(const t of e)r=r.concat(t);else"function"==typeof e&&(r=e(r.slice()));return n&&(r=r.concat(Gu)),r.reduce(((e,t)=>{const n="string"==typeof t?zh[t]:t;if(!n){const e=JSON.stringify(t),n=Object.keys(zh).map((e=>JSON.stringify(e))).join(", ");throw new Error(`Unknown custom tag ${e}; use one of ${n}`)}return e.includes(n)||e.push(n),e}),[])}const Xh=(e,t)=>e.key<t.key?-1:e.key>t.key?1:0;class Zh{constructor({compat:e,customTags:t,merge:n,resolveKnownTags:s,schema:r,sortMapEntries:i,toStringDefaults:o}){this.compat=Array.isArray(e)?Wh(e,"compat"):e?Wh(null,e):null,this.name="string"==typeof r&&r||"core",this.knownTags=s?Qh:{},this.tags=Wh(t,this.name,n),this.toStringOptions=o??null,Object.defineProperty(this,Gl,{value:Ju}),Object.defineProperty(this,ql,{value:sh}),Object.defineProperty(this,Yl,{value:nh}),this.sortMapEntries="function"==typeof i?i:!0===i?Xh:null}clone(){const e=Object.create(Zh.prototype,Object.getOwnPropertyDescriptors(this));return e.tags=this.tags.slice(),e}}class Jh{constructor(e,t,n){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,jl,{value:Hl});let s=null;"function"==typeof t||Array.isArray(t)?s=t:void 0===n&&t&&(n=t,t=void 0);const r=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},n);this.options=r;let{version:i}=r;(null==n?void 0:n._directives)?(this.directives=n._directives.atDocument(),this.directives.yaml.explicit&&(i=this.directives.yaml.version)):this.directives=new ou({version:i}),this.setSchema(i,n),this.contents=void 0===e?null:this.createNode(e,s,n)}clone(){const e=Object.create(Jh.prototype,{[jl]:{value:Hl}});return e.commentBefore=this.commentBefore,e.comment=this.comment,e.errors=this.errors.slice(),e.warnings=this.warnings.slice(),e.options=Object.assign({},this.options),this.directives&&(e.directives=this.directives.clone()),e.schema=this.schema.clone(),e.contents=Jl(this.contents)?this.contents.clone(e.schema):this.contents,this.range&&(e.range=this.range.slice()),e}add(e){ed(this.contents)&&this.contents.add(e)}addIn(e,t){ed(this.contents)&&this.contents.addIn(e,t)}createAlias(e,t){if(!e.anchor){const n=cu(this);e.anchor=!t||n.has(t)?lu(t||"a",n):t}return new pu(e.anchor)}createNode(e,t,n){let s;if("function"==typeof t)e=t.call({"":e},"",e),s=t;else if(Array.isArray(t)){const e=e=>"number"==typeof e||e instanceof String||e instanceof Number,n=t.filter(e).map(String);n.length>0&&(t=t.concat(n)),s=t}else void 0===n&&t&&(n=t,t=void 0);const{aliasDuplicateObjects:r,anchorPrefix:i,flow:o,keepUndefined:a,onTagObj:c,tag:l}=n??{},{onAnchor:u,setAnchors:h,sourceObjects:d}=function(e,t){const n=[],s=new Map;let r=null;return{onAnchor:s=>{n.push(s),r||(r=cu(e));const i=lu(t,r);return r.add(i),i},setAnchors:()=>{for(const e of n){const t=s.get(e);if("object"!=typeof t||!t.anchor||!Wl(t.node)&&!Zl(t.node)){const t=new Error("Failed to resolve repeated object (this should not happen)");throw t.source=e,t}t.node.anchor=t.anchor}},sourceObjects:s}}(this,i||"a"),p=Tu(e,l,{aliasDuplicateObjects:r??!0,keepUndefined:a??!1,onAnchor:u,onTagObj:c,replacer:s,schema:this.schema,sourceObjects:d});return o&&Zl(p)&&(p.flow=!0),h(),p}createPair(e,t,n={}){const s=this.createNode(e,null,n),r=this.createNode(t,null,n);return new Ku(s,r)}delete(e){return!!ed(this.contents)&&this.contents.delete(e)}deleteIn(e){return Au(e)?null!=this.contents&&(this.contents=null,!0):!!ed(this.contents)&&this.contents.deleteIn(e)}get(e,t){return Zl(this.contents)?this.contents.get(e,t):void 0}getIn(e,t){return Au(e)?!t&&Wl(this.contents)?this.contents.value:this.contents:Zl(this.contents)?this.contents.getIn(e,t):void 0}has(e){return!!Zl(this.contents)&&this.contents.has(e)}hasIn(e){return Au(e)?void 0!==this.contents:!!Zl(this.contents)&&this.contents.hasIn(e)}set(e,t){null==this.contents?this.contents=_u(this.schema,[e],t):ed(this.contents)&&this.contents.set(e,t)}setIn(e,t){Au(e)?this.contents=t:null==this.contents?this.contents=_u(this.schema,Array.from(e),t):ed(this.contents)&&this.contents.setIn(e,t)}setSchema(e,t={}){let n;switch("number"==typeof e&&(e=String(e)),e){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new ou({version:"1.1"}),n={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=e:this.directives=new ou({version:e}),n={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,n=null;break;default:{const t=JSON.stringify(e);throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${t}`)}}if(t.schema instanceof Object)this.schema=t.schema;else{if(!n)throw new Error("With a null YAML version, the { schema: Schema } option is required");this.schema=new Zh(Object.assign(n,t))}}toJS({json:e,jsonArg:t,mapAsMap:n,maxAliasCount:s,onAnchor:r,reviver:i}={}){const o={anchors:new Map,doc:this,keep:!e,mapAsMap:!0===n,mapKeyWarned:!1,maxAliasCount:"number"==typeof s?s:100},a=hu(this.contents,t??"",o);if("function"==typeof r)for(const{count:e,res:t}of o.anchors.values())r(t,e);return"function"==typeof i?uu(i,{"":a},"",a):a}toJSON(e,t){return this.toJS({json:!0,jsonArg:e,mapAsMap:!1,onAnchor:t})}toString(e={}){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");if("indent"in e&&(!Number.isInteger(e.indent)||Number(e.indent)<=0)){const t=JSON.stringify(e.indent);throw new Error(`"indent" option must be a positive integer, not ${t}`)}return function(e,t){var n;const s=[];let r=!0===t.directives;if(!1!==t.directives&&e.directives){const t=e.directives.toString(e);t?(s.push(t),r=!0):e.directives.docStart&&(r=!0)}r&&s.push("---");const i=Pu(e,t),{commentString:o}=i.options;if(e.commentBefore){1!==s.length&&s.unshift("");const t=o(e.commentBefore);s.unshift(bu(t,""))}let a=!1,c=null;if(e.contents){if(Jl(e.contents)){if(e.contents.spaceBefore&&r&&s.push(""),e.contents.commentBefore){const t=o(e.contents.commentBefore);s.push(bu(t,""))}i.forceBlockIndent=!!e.comment,c=e.contents.comment}const t=c?void 0:()=>a=!0;let n=Bu(e.contents,i,(()=>c=null),t);c&&(n+=ku(n,"",o(c))),"|"!==n[0]&&">"!==n[0]||"---"!==s[s.length-1]?s.push(n):s[s.length-1]=`--- ${n}`}else s.push(Bu(e.contents,i));if(null==(n=e.directives)?void 0:n.docEnd)if(e.comment){const t=o(e.comment);t.includes("\n")?(s.push("..."),s.push(bu(t,""))):s.push(`... ${t}`)}else s.push("...");else{let t=e.comment;t&&a&&(t=t.replace(/^\n+/,"")),t&&(a&&!c||""===s[s.length-1]||s.push(""),s.push(bu(o(t),"")))}return s.join("\n")+"\n"}(this,e)}}function ed(e){if(Zl(e))return!0;throw new Error("Expected a YAML collection as document contents")}class td extends Error{constructor(e,t,n,s){super(),this.name=e,this.code=n,this.message=s,this.pos=t}}class nd extends td{constructor(e,t,n){super("YAMLParseError",e,t,n)}}class sd extends td{constructor(e,t,n){super("YAMLWarning",e,t,n)}}const rd=(e,t)=>n=>{if(-1===n.pos[0])return;n.linePos=n.pos.map((e=>t.linePos(e)));const{line:s,col:r}=n.linePos[0];n.message+=` at line ${s}, column ${r}`;let i=r-1,o=e.substring(t.lineStarts[s-1],t.lineStarts[s]).replace(/[\n\r]+$/,"");if(i>=60&&o.length>80){const e=Math.min(i-39,o.length-79);o="…"+o.substring(e),i-=e-1}if(o.length>80&&(o=o.substring(0,79)+"…"),s>1&&/^ *$/.test(o.substring(0,i))){let n=e.substring(t.lineStarts[s-2],t.lineStarts[s-1]);n.length>80&&(n=n.substring(0,79)+"…\n"),o=n+o}if(/[^ ]/.test(o)){let e=1;const t=n.linePos[1];t&&t.line===s&&t.col>r&&(e=Math.max(1,Math.min(t.col-r,80-i)));const a=" ".repeat(i)+"^".repeat(e);n.message+=`:\n\n${o}\n${a}\n`}};function id(e,{flow:t,indicator:n,next:s,offset:r,onError:i,parentIndent:o,startOnNewline:a}){let c=!1,l=a,u=a,h="",d="",p=!1,f=!1,m=null,E=null,T=null,_=null,A=null,g=null,C=null;for(const r of e)switch(f&&("space"!==r.type&&"newline"!==r.type&&"comma"!==r.type&&i(r.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),f=!1),m&&(l&&"comment"!==r.type&&"newline"!==r.type&&i(m,"TAB_AS_INDENT","Tabs are not allowed as indentation"),m=null),r.type){case"space":t||"doc-start"===n&&"flow-collection"===(null==s?void 0:s.type)||!r.source.includes("\t")||(m=r),u=!0;break;case"comment":{u||i(r,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");const e=r.source.substring(1)||" ";h?h+=d+e:h=e,d="",l=!1;break}case"newline":l?h?h+=r.source:c=!0:d+=r.source,l=!0,p=!0,(E||T)&&(_=r),u=!0;break;case"anchor":E&&i(r,"MULTIPLE_ANCHORS","A node can have at most one anchor"),r.source.endsWith(":")&&i(r.offset+r.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),E=r,null===C&&(C=r.offset),l=!1,u=!1,f=!0;break;case"tag":T&&i(r,"MULTIPLE_TAGS","A node can have at most one tag"),T=r,null===C&&(C=r.offset),l=!1,u=!1,f=!0;break;case n:(E||T)&&i(r,"BAD_PROP_ORDER",`Anchors and tags must be after the ${r.source} indicator`),g&&i(r,"UNEXPECTED_TOKEN",`Unexpected ${r.source} in ${t??"collection"}`),g=r,l="seq-item-ind"===n||"explicit-key-ind"===n,u=!1;break;case"comma":if(t){A&&i(r,"UNEXPECTED_TOKEN",`Unexpected , in ${t}`),A=r,l=!1,u=!1;break}default:i(r,"UNEXPECTED_TOKEN",`Unexpected ${r.type} token`),l=!1,u=!1}const b=e[e.length-1],k=b?b.offset+b.source.length:r;return f&&s&&"space"!==s.type&&"newline"!==s.type&&"comma"!==s.type&&("scalar"!==s.type||""!==s.source)&&i(s.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),m&&(l&&m.indent<=o||"block-map"===(null==s?void 0:s.type)||"block-seq"===(null==s?void 0:s.type))&&i(m,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:A,found:g,spaceBefore:c,comment:h,hasNewline:p,anchor:E,tag:T,newlineAfterProp:_,end:k,start:C??k}}function od(e){if(!e)return null;switch(e.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(e.source.includes("\n"))return!0;if(e.end)for(const t of e.end)if("newline"===t.type)return!0;return!1;case"flow-collection":for(const t of e.items){for(const e of t.start)if("newline"===e.type)return!0;if(t.sep)for(const e of t.sep)if("newline"===e.type)return!0;if(od(t.key)||od(t.value))return!0}return!1;default:return!0}}function ad(e,t,n){if("flow-collection"===(null==t?void 0:t.type)){const s=t.end[0];if(s.indent===e&&("]"===s.source||"}"===s.source)&&od(t)){n(s,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}}function cd(e,t,n){const{uniqueKeys:s}=e.options;if(!1===s)return!1;const r="function"==typeof s?s:(e,t)=>e===t||Wl(e)&&Wl(t)&&e.value===t.value;return t.some((e=>r(e.key,n)))}const ld="All mapping items must start at the same column";function ud(e,t,n,s){let r="";if(e){let i=!1,o="";for(const a of e){const{source:e,type:c}=a;switch(c){case"space":i=!0;break;case"comment":{n&&!i&&s(a,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");const t=e.substring(1)||" ";r?r+=o+t:r=t,o="";break}case"newline":r&&(o+=e),i=!0;break;default:s(a,"UNEXPECTED_TOKEN",`Unexpected ${c} at node end`)}t+=e.length}}return{comment:r,offset:t}}const hd="Block collections are not allowed within flow collections",dd=e=>e&&("block-map"===e.type||"block-seq"===e.type);function pd(e,t,n,s,r,i){const o="block-map"===n.type?function({composeNode:e,composeEmptyNode:t},n,s,r,i){var o;const a=new((null==i?void 0:i.nodeClass)??Zu)(n.schema);n.atRoot&&(n.atRoot=!1);let c=s.offset,l=null;for(const i of s.items){const{start:u,key:h,sep:d,value:p}=i,f=id(u,{indicator:"explicit-key-ind",next:h??(null==d?void 0:d[0]),offset:c,onError:r,parentIndent:s.indent,startOnNewline:!0}),m=!f.found;if(m){if(h&&("block-seq"===h.type?r(c,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in h&&h.indent!==s.indent&&r(c,"BAD_INDENT",ld)),!f.anchor&&!f.tag&&!d){l=f.end,f.comment&&(a.comment?a.comment+="\n"+f.comment:a.comment=f.comment);continue}(f.newlineAfterProp||od(h))&&r(h??u[u.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else(null==(o=f.found)?void 0:o.indent)!==s.indent&&r(c,"BAD_INDENT",ld);n.atKey=!0;const E=f.end,T=h?e(n,h,f,r):t(n,E,u,null,f,r);n.schema.compat&&ad(s.indent,h,r),n.atKey=!1,cd(n,a.items,T)&&r(E,"DUPLICATE_KEY","Map keys must be unique");const _=id(d??[],{indicator:"map-value-ind",next:p,offset:T.range[2],onError:r,parentIndent:s.indent,startOnNewline:!h||"block-scalar"===h.type});if(c=_.end,_.found){m&&("block-map"!==(null==p?void 0:p.type)||_.hasNewline||r(c,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),n.options.strict&&f.start<_.found.offset-1024&&r(T.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));const o=p?e(n,p,_,r):t(n,c,d,null,_,r);n.schema.compat&&ad(s.indent,p,r),c=o.range[2];const l=new Ku(T,o);n.options.keepSourceTokens&&(l.srcToken=i),a.items.push(l)}else{m&&r(T.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),_.comment&&(T.comment?T.comment+="\n"+_.comment:T.comment=_.comment);const e=new Ku(T);n.options.keepSourceTokens&&(e.srcToken=i),a.items.push(e)}}return l&&l<c&&r(l,"IMPOSSIBLE","Map comment with trailing content"),a.range=[s.offset,c,l??c],a}(e,t,n,s,i):"block-seq"===n.type?function({composeNode:e,composeEmptyNode:t},n,s,r,i){const o=new((null==i?void 0:i.nodeClass)??eh)(n.schema);n.atRoot&&(n.atRoot=!1),n.atKey&&(n.atKey=!1);let a=s.offset,c=null;for(const{start:i,value:l}of s.items){const u=id(i,{indicator:"seq-item-ind",next:l,offset:a,onError:r,parentIndent:s.indent,startOnNewline:!0});if(!u.found){if(!(u.anchor||u.tag||l)){c=u.end,u.comment&&(o.comment=u.comment);continue}l&&"block-seq"===l.type?r(u.end,"BAD_INDENT","All sequence items must start at the same column"):r(a,"MISSING_CHAR","Sequence item without - indicator")}const h=l?e(n,l,u,r):t(n,u.end,i,null,u,r);n.schema.compat&&ad(s.indent,l,r),a=h.range[2],o.items.push(h)}return o.range=[s.offset,a,c??a],o}(e,t,n,s,i):function({composeNode:e,composeEmptyNode:t},n,s,r,i){const o="{"===s.start.source,a=o?"flow map":"flow sequence",c=new((null==i?void 0:i.nodeClass)??(o?Zu:eh))(n.schema);c.flow=!0;const l=n.atRoot;l&&(n.atRoot=!1),n.atKey&&(n.atKey=!1);let u=s.offset+s.start.source.length;for(let i=0;i<s.items.length;++i){const l=s.items[i],{start:h,key:d,sep:p,value:f}=l,m=id(h,{flow:a,indicator:"explicit-key-ind",next:d??(null==p?void 0:p[0]),offset:u,onError:r,parentIndent:s.indent,startOnNewline:!1});if(!m.found){if(!(m.anchor||m.tag||p||f)){0===i&&m.comma?r(m.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`):i<s.items.length-1&&r(m.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${a}`),m.comment&&(c.comment?c.comment+="\n"+m.comment:c.comment=m.comment),u=m.end;continue}!o&&n.options.strict&&od(d)&&r(d,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(0===i)m.comma&&r(m.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`);else if(m.comma||r(m.start,"MISSING_CHAR",`Missing , between ${a} items`),m.comment){let e="";e:for(const t of h)switch(t.type){case"comma":case"space":break;case"comment":e=t.source.substring(1);break e;default:break e}if(e){let t=c.items[c.items.length-1];Ql(t)&&(t=t.value??t.key),t.comment?t.comment+="\n"+e:t.comment=e,m.comment=m.comment.substring(e.length+1)}}if(o||p||m.found){n.atKey=!0;const i=m.end,E=d?e(n,d,m,r):t(n,i,h,null,m,r);dd(d)&&r(E.range,"BLOCK_IN_FLOW",hd),n.atKey=!1;const T=id(p??[],{flow:a,indicator:"map-value-ind",next:f,offset:E.range[2],onError:r,parentIndent:s.indent,startOnNewline:!1});if(T.found){if(!o&&!m.found&&n.options.strict){if(p)for(const e of p){if(e===T.found)break;if("newline"===e.type){r(e,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}m.start<T.found.offset-1024&&r(T.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else f&&("source"in f&&f.source&&":"===f.source[0]?r(f,"MISSING_CHAR",`Missing space after : in ${a}`):r(T.start,"MISSING_CHAR",`Missing , or : between ${a} items`));const _=f?e(n,f,T,r):T.found?t(n,T.end,p,null,T,r):null;_?dd(f)&&r(_.range,"BLOCK_IN_FLOW",hd):T.comment&&(E.comment?E.comment+="\n"+T.comment:E.comment=T.comment);const A=new Ku(E,_);if(n.options.keepSourceTokens&&(A.srcToken=l),o){const e=c;cd(n,e.items,E)&&r(i,"DUPLICATE_KEY","Map keys must be unique"),e.items.push(A)}else{const e=new Zu(n.schema);e.flow=!0,e.items.push(A);const t=(_??E).range;e.range=[E.range[0],t[1],t[2]],c.items.push(e)}u=_?_.range[2]:T.end}else{const s=f?e(n,f,m,r):t(n,m.end,p,null,m,r);c.items.push(s),u=s.range[2],dd(f)&&r(s.range,"BLOCK_IN_FLOW",hd)}}const h=o?"}":"]",[d,...p]=s.end;let f=u;if(d&&d.source===h)f=d.offset+d.source.length;else{const e=a[0].toUpperCase()+a.substring(1);r(u,l?"MISSING_CHAR":"BAD_INDENT",l?`${e} must end with a ${h}`:`${e} in block collection must be sufficiently indented and end with a ${h}`),d&&1!==d.source.length&&p.unshift(d)}if(p.length>0){const e=ud(p,f,n.options.strict,r);e.comment&&(c.comment?c.comment+="\n"+e.comment:c.comment=e.comment),c.range=[s.offset,f,e.offset]}else c.range=[s.offset,f,f];return c}(e,t,n,s,i),a=o.constructor;return"!"===r||r===a.tagName?(o.tag=a.tagName,o):(r&&(o.tag=r),o)}function fd(e,t,n){const s=t.offset,r=function({offset:e,props:t},n,s){if("block-scalar-header"!==t[0].type)return s(t[0],"IMPOSSIBLE","Block scalar header not found"),null;const{source:r}=t[0],i=r[0];let o=0,a="",c=-1;for(let t=1;t<r.length;++t){const n=r[t];if(a||"-"!==n&&"+"!==n){const s=Number(n);!o&&s?o=s:-1===c&&(c=e+t)}else a=n}-1!==c&&s(c,"UNEXPECTED_TOKEN",`Block scalar header includes extra characters: ${r}`);let l=!1,u="",h=r.length;for(let e=1;e<t.length;++e){const r=t[e];switch(r.type){case"space":l=!0;case"newline":h+=r.source.length;break;case"comment":if(n&&!l){s(r,"MISSING_CHAR","Comments must be separated from other tokens by white space characters")}h+=r.source.length,u=r.source.substring(1);break;case"error":s(r,"UNEXPECTED_TOKEN",r.message),h+=r.source.length;break;default:{s(r,"UNEXPECTED_TOKEN",`Unexpected token in block scalar header: ${r.type}`);const e=r.source;e&&"string"==typeof e&&(h+=e.length)}}}return{mode:i,indent:o,chomp:a,comment:u,length:h}}(t,e.options.strict,n);if(!r)return{value:"",type:null,comment:"",range:[s,s,s]};const i=">"===r.mode?Eu.BLOCK_FOLDED:Eu.BLOCK_LITERAL,o=t.source?function(e){const t=e.split(/\n( *)/),n=t[0],s=n.match(/^( *)/),r=[(null==s?void 0:s[1])?[s[1],n.slice(s[1].length)]:["",n]];for(let e=1;e<t.length;e+=2)r.push([t[e],t[e+1]]);return r}(t.source):[];let a=o.length;for(let e=o.length-1;e>=0;--e){const t=o[e][1];if(""!==t&&"\r"!==t)break;a=e}if(0===a){const e="+"===r.chomp&&o.length>0?"\n".repeat(Math.max(1,o.length-1)):"";let n=s+r.length;return t.source&&(n+=t.source.length),{value:e,type:i,comment:r.comment,range:[s,n,n]}}let c=t.indent+r.indent,l=t.offset+r.length,u=0;for(let t=0;t<a;++t){const[s,i]=o[t];if(""!==i&&"\r"!==i){if(s.length<c){const e="Block scalars with more-indented leading empty lines must use an explicit indentation indicator";n(l+s.length,"MISSING_CHAR",e)}if(0===r.indent&&(c=s.length),u=t,0===c&&!e.atRoot){n(l,"BAD_INDENT","Block scalar values in collections must be indented")}break}0===r.indent&&s.length>c&&(c=s.length),l+=s.length+i.length+1}for(let e=o.length-1;e>=a;--e)o[e][0].length>c&&(a=e+1);let h="",d="",p=!1;for(let e=0;e<u;++e)h+=o[e][0].slice(c)+"\n";for(let e=u;e<a;++e){let[t,s]=o[e];l+=t.length+s.length+1;const a="\r"===s[s.length-1];if(a&&(s=s.slice(0,-1)),s&&t.length<c){const e=`Block scalar lines must not be less indented than their ${r.indent?"explicit indentation indicator":"first line"}`;n(l-s.length-(a?2:1),"BAD_INDENT",e),t=""}i===Eu.BLOCK_LITERAL?(h+=d+t.slice(c)+s,d="\n"):t.length>c||"\t"===s[0]?(" "===d?d="\n":p||"\n"!==d||(d="\n\n"),h+=d+t.slice(c)+s,d="\n",p=!0):""===s?"\n"===d?h+="\n":d="\n":(h+=d+s,d=" ",p=!1)}switch(r.chomp){case"-":break;case"+":for(let e=a;e<o.length;++e)h+="\n"+o[e][0].slice(c);"\n"!==h[h.length-1]&&(h+="\n");break;default:h+="\n"}const f=s+r.length+t.source.length;return{value:h,type:i,comment:r.comment,range:[s,f,f]}}function md(e,t,n){const{offset:s,type:r,source:i,end:o}=e;let a,c;const l=(e,t,r)=>n(s+e,t,r);switch(r){case"scalar":a=Eu.PLAIN,c=function(e,t){let n="";switch(e[0]){case"\t":n="a tab character";break;case",":n="flow indicator character ,";break;case"%":n="directive indicator character %";break;case"|":case">":n=`block scalar indicator ${e[0]}`;break;case"@":case"`":n=`reserved character ${e[0]}`}n&&t(0,"BAD_SCALAR_START",`Plain value cannot start with ${n}`);return Ed(e)}(i,l);break;case"single-quoted-scalar":a=Eu.QUOTE_SINGLE,c=function(e,t){"'"===e[e.length-1]&&1!==e.length||t(e.length,"MISSING_CHAR","Missing closing 'quote");return Ed(e.slice(1,-1)).replace(/''/g,"'")}(i,l);break;case"double-quoted-scalar":a=Eu.QUOTE_DOUBLE,c=function(e,t){let n="";for(let s=1;s<e.length-1;++s){const r=e[s];if("\r"!==r||"\n"!==e[s+1])if("\n"===r){const{fold:t,offset:r}=Td(e,s);n+=t,s=r}else if("\\"===r){let r=e[++s];const i=_d[r];if(i)n+=i;else if("\n"===r)for(r=e[s+1];" "===r||"\t"===r;)r=e[1+ ++s];else if("\r"===r&&"\n"===e[s+1])for(r=e[1+ ++s];" "===r||"\t"===r;)r=e[1+ ++s];else if("x"===r||"u"===r||"U"===r){const i={x:2,u:4,U:8}[r];n+=Ad(e,s+1,i,t),s+=i}else{const r=e.substr(s-1,2);t(s-1,"BAD_DQ_ESCAPE",`Invalid escape sequence ${r}`),n+=r}}else if(" "===r||"\t"===r){const t=s;let i=e[s+1];for(;" "===i||"\t"===i;)i=e[1+ ++s];"\n"===i||"\r"===i&&"\n"===e[s+2]||(n+=s>t?e.slice(t,s+1):r)}else n+=r}'"'===e[e.length-1]&&1!==e.length||t(e.length,"MISSING_CHAR",'Missing closing "quote');return n}(i,l);break;default:return n(e,"UNEXPECTED_TOKEN",`Expected a flow scalar value, but found: ${r}`),{value:"",type:null,comment:"",range:[s,s+i.length,s+i.length]}}const u=s+i.length,h=ud(o,u,t,n);return{value:c,type:a,comment:h.comment,range:[s,u,h.offset]}}function Ed(e){let t,n;try{t=new RegExp("(.*?)(?<![ \t])[ \t]*\r?\n","sy"),n=new RegExp("[ \t]*(.*?)(?:(?<![ \t])[ \t]*)?\r?\n","sy")}catch{t=/(.*?)[ \t]*\r?\n/sy,n=/[ \t]*(.*?)[ \t]*\r?\n/sy}let s=t.exec(e);if(!s)return e;let r=s[1],i=" ",o=t.lastIndex;for(n.lastIndex=o;s=n.exec(e);)""===s[1]?"\n"===i?r+=i:i="\n":(r+=i+s[1],i=" "),o=n.lastIndex;const a=/[ \t]*(.*)/sy;return a.lastIndex=o,s=a.exec(e),r+i+((null==s?void 0:s[1])??"")}function Td(e,t){let n="",s=e[t+1];for(;!(" "!==s&&"\t"!==s&&"\n"!==s&&"\r"!==s||"\r"===s&&"\n"!==e[t+2]);)"\n"===s&&(n+="\n"),s=e[(t+=1)+1];return n||(n=" "),{fold:n,offset:t}}const _d={0:"\0",a:"",b:"\b",e:"",f:"\f",n:"\n",r:"\r",t:"\t",v:"\v",N:"",_:" ",L:"\u2028",P:"\u2029"," ":" ",'"':'"',"/":"/","\\":"\\","\t":"\t"};function Ad(e,t,n,s){const r=e.substr(t,n),i=r.length===n&&/^[0-9a-fA-F]+$/.test(r)?parseInt(r,16):NaN;if(isNaN(i)){const r=e.substr(t-2,n+2);return s(t-2,"BAD_DQ_ESCAPE",`Invalid escape sequence ${r}`),r}return String.fromCodePoint(i)}function gd(e,t,n,s){const{value:r,type:i,comment:o,range:a}="block-scalar"===t.type?fd(e,t,s):md(t,e.options.strict,s),c=n?e.directives.tagName(n.source,(e=>s(n,"TAG_RESOLVE_FAILED",e))):null;let l,u;l=e.options.stringKeys&&e.atKey?e.schema[ql]:c?function(e,t,n,s,r){var i;if("!"===n)return e[ql];const o=[];for(const t of e.tags)if(!t.collection&&t.tag===n){if(!t.default||!t.test)return t;o.push(t)}for(const e of o)if(null==(i=e.test)?void 0:i.test(t))return e;const a=e.knownTags[n];if(a&&!a.collection)return e.tags.push(Object.assign({},a,{default:!1,test:void 0})),a;return r(s,"TAG_RESOLVE_FAILED",`Unresolved tag: ${n}`,"tag:yaml.org,2002:str"!==n),e[ql]}(e.schema,r,c,n,s):"scalar"===t.type?function({atKey:e,directives:t,schema:n},s,r,i){const o=n.tags.find((t=>{var n;return(!0===t.default||e&&"key"===t.default)&&(null==(n=t.test)?void 0:n.test(s))}))||n[ql];if(n.compat){const e=n.compat.find((e=>{var t;return e.default&&(null==(t=e.test)?void 0:t.test(s))}))??n[ql];if(o.tag!==e.tag){i(r,"TAG_RESOLVE_FAILED",`Value may be parsed as either ${t.tagString(o.tag)} or ${t.tagString(e.tag)}`,!0)}}return o}(e,r,t,s):e.schema[ql];try{const i=l.resolve(r,(e=>s(n??t,"TAG_RESOLVE_FAILED",e)),e.options);u=Wl(i)?i:new Eu(i)}catch(e){const i=e instanceof Error?e.message:String(e);s(n??t,"TAG_RESOLVE_FAILED",i),u=new Eu(r)}return u.range=a,u.source=r,i&&(u.type=i),c&&(u.tag=c),l.format&&(u.format=l.format),o&&(u.comment=o),u}function Cd(e,t,n){if(t){null===n&&(n=t.length);for(let s=n-1;s>=0;--s){let n=t[s];switch(n.type){case"space":case"comment":case"newline":e-=n.source.length;continue}for(n=t[++s];"space"===(null==n?void 0:n.type);)e+=n.source.length,n=t[++s];break}}return e}const bd={composeNode:kd,composeEmptyNode:Sd};function kd(e,t,n,s){const r=e.atKey,{spaceBefore:i,comment:o,anchor:a,tag:c}=n;let l,u=!0;switch(t.type){case"alias":l=function({options:e},{offset:t,source:n,end:s},r){const i=new pu(n.substring(1));""===i.source&&r(t,"BAD_ALIAS","Alias cannot be an empty string");i.source.endsWith(":")&&r(t+n.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);const o=t+n.length,a=ud(s,o,e.strict,r);i.range=[t,o,a.offset],a.comment&&(i.comment=a.comment);return i}(e,t,s),(a||c)&&s(t,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":l=gd(e,t,c,s),a&&(l.anchor=a.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":l=function(e,t,n,s,r){var i;const o=s.tag,a=o?t.directives.tagName(o.source,(e=>r(o,"TAG_RESOLVE_FAILED",e))):null;if("block-seq"===n.type){const{anchor:e,newlineAfterProp:t}=s,n=e&&o?e.offset>o.offset?e:o:e??o;n&&(!t||t.offset<n.offset)&&r(n,"MISSING_CHAR","Missing newline after block sequence props")}const c="block-map"===n.type?"map":"block-seq"===n.type?"seq":"{"===n.start.source?"map":"seq";if(!o||!a||"!"===a||a===Zu.tagName&&"map"===c||a===eh.tagName&&"seq"===c)return pd(e,t,n,r,a);let l=t.schema.tags.find((e=>e.tag===a&&e.collection===c));if(!l){const s=t.schema.knownTags[a];if(!s||s.collection!==c)return(null==s?void 0:s.collection)?r(o,"BAD_COLLECTION_TYPE",`${s.tag} used for ${c} collection, but expects ${s.collection}`,!0):r(o,"TAG_RESOLVE_FAILED",`Unresolved tag: ${a}`,!0),pd(e,t,n,r,a);t.schema.tags.push(Object.assign({},s,{default:!1})),l=s}const u=pd(e,t,n,r,a,l),h=(null==(i=l.resolve)?void 0:i.call(l,u,(e=>r(o,"TAG_RESOLVE_FAILED",e)),t.options))??u,d=Jl(h)?h:new Eu(h);return d.range=u.range,d.tag=a,(null==l?void 0:l.format)&&(d.format=l.format),d}(bd,e,t,n,s),a&&(l.anchor=a.source.substring(1));break;default:s(t,"UNEXPECTED_TOKEN","error"===t.type?t.message:`Unsupported token (type: ${t.type})`),l=Sd(e,t.offset,void 0,null,n,s),u=!1}if(a&&""===l.anchor&&s(a,"BAD_ALIAS","Anchor cannot be an empty string"),r&&e.options.stringKeys&&(!Wl(l)||"string"!=typeof l.value||l.tag&&"tag:yaml.org,2002:str"!==l.tag)){s(c??t,"NON_STRING_KEY","With stringKeys, all keys must be strings")}return i&&(l.spaceBefore=!0),o&&("scalar"===t.type&&""===t.source?l.comment=o:l.commentBefore=o),e.options.keepSourceTokens&&u&&(l.srcToken=t),l}function Sd(e,t,n,s,{spaceBefore:r,comment:i,anchor:o,tag:a,end:c},l){const u=gd(e,{type:"scalar",offset:Cd(t,n,s),indent:-1,source:""},a,l);return o&&(u.anchor=o.source.substring(1),""===u.anchor&&l(o,"BAD_ALIAS","Anchor cannot be an empty string")),r&&(u.spaceBefore=!0),i&&(u.comment=i,u.range[2]=c),u}function Nd(e){if("number"==typeof e)return[e,e+1];if(Array.isArray(e))return 2===e.length?e:[e[0],e[1]];const{offset:t,source:n}=e;return[t,t+("string"==typeof n?n.length:1)]}function Id(e){var t;let n="",s=!1,r=!1;for(let i=0;i<e.length;++i){const o=e[i];switch(o[0]){case"#":n+=(""===n?"":r?"\n\n":"\n")+(o.substring(1)||" "),s=!0,r=!1;break;case"%":"#"!==(null==(t=e[i+1])?void 0:t[0])&&(i+=1),s=!1;break;default:s||(r=!0),s=!1}}return{comment:n,afterEmptyLine:r}}class Dd{constructor(e={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(e,t,n,s)=>{const r=Nd(e);s?this.warnings.push(new sd(r,t,n)):this.errors.push(new nd(r,t,n))},this.directives=new ou({version:e.version||"1.2"}),this.options=e}decorate(e,t){const{comment:n,afterEmptyLine:s}=Id(this.prelude);if(n){const r=e.contents;if(t)e.comment=e.comment?`${e.comment}\n${n}`:n;else if(s||e.directives.docStart||!r)e.commentBefore=n;else if(Zl(r)&&!r.flow&&r.items.length>0){let e=r.items[0];Ql(e)&&(e=e.key);const t=e.commentBefore;e.commentBefore=t?`${n}\n${t}`:n}else{const e=r.commentBefore;r.commentBefore=e?`${n}\n${e}`:n}}t?(Array.prototype.push.apply(e.errors,this.errors),Array.prototype.push.apply(e.warnings,this.warnings)):(e.errors=this.errors,e.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:Id(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(e,t=!1,n=-1){for(const t of e)yield*this.next(t);yield*this.end(t,n)}*next(e){switch(e.type){case"directive":this.directives.add(e.source,((t,n,s)=>{const r=Nd(e);r[0]+=t,this.onError(r,"BAD_DIRECTIVE",n,s)})),this.prelude.push(e.source),this.atDirectives=!0;break;case"document":{const t=function(e,t,{offset:n,start:s,value:r,end:i},o){const a=Object.assign({_directives:t},e),c=new Jh(void 0,a),l={atKey:!1,atRoot:!0,directives:c.directives,options:c.options,schema:c.schema},u=id(s,{indicator:"doc-start",next:r??(null==i?void 0:i[0]),offset:n,onError:o,parentIndent:0,startOnNewline:!0});u.found&&(c.directives.docStart=!0,!r||"block-map"!==r.type&&"block-seq"!==r.type||u.hasNewline||o(u.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),c.contents=r?kd(l,r,u,o):Sd(l,u.end,s,null,u,o);const h=c.contents.range[2],d=ud(i,h,!1,o);return d.comment&&(c.comment=d.comment),c.range=[n,h,d.offset],c}(this.options,this.directives,e,this.onError);this.atDirectives&&!t.directives.docStart&&this.onError(e,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(t,!1),this.doc&&(yield this.doc),this.doc=t,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(e.source);break;case"error":{const t=e.source?`${e.message}: ${JSON.stringify(e.source)}`:e.message,n=new nd(Nd(e),"UNEXPECTED_TOKEN",t);this.atDirectives||!this.doc?this.errors.push(n):this.doc.errors.push(n);break}case"doc-end":{if(!this.doc){const t="Unexpected doc-end without preceding document";this.errors.push(new nd(Nd(e),"UNEXPECTED_TOKEN",t));break}this.doc.directives.docEnd=!0;const t=ud(e.end,e.offset+e.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),t.comment){const e=this.doc.comment;this.doc.comment=e?`${e}\n${t.comment}`:t.comment}this.doc.range[2]=t.offset;break}default:this.errors.push(new nd(Nd(e),"UNEXPECTED_TOKEN",`Unsupported token ${e.type}`))}}*end(e=!1,t=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(e){const e=Object.assign({_directives:this.directives},this.options),n=new Jh(void 0,e);this.atDirectives&&this.onError(t,"MISSING_CHAR","Missing directives-end indicator line"),n.range=[0,t,t],this.decorate(n,!1),yield n}}}function yd(e){switch(e){case void 0:case" ":case"\n":case"\r":case"\t":return!0;default:return!1}}const Od=new Set("0123456789ABCDEFabcdef"),Ld=new Set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()"),Rd=new Set(",[]{}"),Md=new Set(" ,[]{}\n\r\t"),vd=e=>!e||Md.has(e);class xd{constructor(){this.atEnd=!1,this.blockScalarIndent=-1,this.blockScalarKeep=!1,this.buffer="",this.flowKey=!1,this.flowLevel=0,this.indentNext=0,this.indentValue=0,this.lineEndPos=null,this.next=null,this.pos=0}*lex(e,t=!1){if(e){if("string"!=typeof e)throw TypeError("source is not a string");this.buffer=this.buffer?this.buffer+e:e,this.lineEndPos=null}this.atEnd=!t;let n=this.next??"stream";for(;n&&(t||this.hasChars(1));)n=yield*this.parseNext(n)}atLineEnd(){let e=this.pos,t=this.buffer[e];for(;" "===t||"\t"===t;)t=this.buffer[++e];return!t||"#"===t||"\n"===t||"\r"===t&&"\n"===this.buffer[e+1]}charAt(e){return this.buffer[this.pos+e]}continueScalar(e){let t=this.buffer[e];if(this.indentNext>0){let n=0;for(;" "===t;)t=this.buffer[++n+e];if("\r"===t){const t=this.buffer[n+e+1];if("\n"===t||!t&&!this.atEnd)return e+n+1}return"\n"===t||n>=this.indentNext||!t&&!this.atEnd?e+n:-1}if("-"===t||"."===t){const t=this.buffer.substr(e,3);if(("---"===t||"..."===t)&&yd(this.buffer[e+3]))return-1}return e}getLine(){let e=this.lineEndPos;return("number"!=typeof e||-1!==e&&e<this.pos)&&(e=this.buffer.indexOf("\n",this.pos),this.lineEndPos=e),-1===e?this.atEnd?this.buffer.substring(this.pos):null:("\r"===this.buffer[e-1]&&(e-=1),this.buffer.substring(this.pos,e))}hasChars(e){return this.pos+e<=this.buffer.length}setNext(e){return this.buffer=this.buffer.substring(this.pos),this.pos=0,this.lineEndPos=null,this.next=e,null}peek(e){return this.buffer.substr(this.pos,e)}*parseNext(e){switch(e){case"stream":return yield*this.parseStream();case"line-start":return yield*this.parseLineStart();case"block-start":return yield*this.parseBlockStart();case"doc":return yield*this.parseDocument();case"flow":return yield*this.parseFlowCollection();case"quoted-scalar":return yield*this.parseQuotedScalar();case"block-scalar":return yield*this.parseBlockScalar();case"plain-scalar":return yield*this.parsePlainScalar()}}*parseStream(){let e=this.getLine();if(null===e)return this.setNext("stream");if("\ufeff"===e[0]&&(yield*this.pushCount(1),e=e.substring(1)),"%"===e[0]){let t=e.length,n=e.indexOf("#");for(;-1!==n;){const s=e[n-1];if(" "===s||"\t"===s){t=n-1;break}n=e.indexOf("#",n+1)}for(;;){const n=e[t-1];if(" "!==n&&"\t"!==n)break;t-=1}const s=(yield*this.pushCount(t))+(yield*this.pushSpaces(!0));return yield*this.pushCount(e.length-s),this.pushNewline(),"stream"}if(this.atLineEnd()){const t=yield*this.pushSpaces(!0);return yield*this.pushCount(e.length-t),yield*this.pushNewline(),"stream"}return yield"",yield*this.parseLineStart()}*parseLineStart(){const e=this.charAt(0);if(!e&&!this.atEnd)return this.setNext("line-start");if("-"===e||"."===e){if(!this.atEnd&&!this.hasChars(4))return this.setNext("line-start");const e=this.peek(3);if(("---"===e||"..."===e)&&yd(this.charAt(3)))return yield*this.pushCount(3),this.indentValue=0,this.indentNext=0,"---"===e?"doc":"stream"}return this.indentValue=yield*this.pushSpaces(!1),this.indentNext>this.indentValue&&!yd(this.charAt(1))&&(this.indentNext=this.indentValue),yield*this.parseBlockStart()}*parseBlockStart(){const[e,t]=this.peek(2);if(!t&&!this.atEnd)return this.setNext("block-start");if(("-"===e||"?"===e||":"===e)&&yd(t)){const e=(yield*this.pushCount(1))+(yield*this.pushSpaces(!0));return this.indentNext=this.indentValue+1,this.indentValue+=e,yield*this.parseBlockStart()}return"doc"}*parseDocument(){yield*this.pushSpaces(!0);const e=this.getLine();if(null===e)return this.setNext("doc");let t=yield*this.pushIndicators();switch(e[t]){case"#":yield*this.pushCount(e.length-t);case void 0:return yield*this.pushNewline(),yield*this.parseLineStart();case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel=1,"flow";case"}":case"]":return yield*this.pushCount(1),"doc";case"*":return yield*this.pushUntil(vd),"doc";case'"':case"'":return yield*this.parseQuotedScalar();case"|":case">":return t+=(yield*this.parseBlockScalarHeader()),t+=(yield*this.pushSpaces(!0)),yield*this.pushCount(e.length-t),yield*this.pushNewline(),yield*this.parseBlockScalar();default:return yield*this.parsePlainScalar()}}*parseFlowCollection(){let e,t,n=-1;do{e=yield*this.pushNewline(),e>0?(t=yield*this.pushSpaces(!1),this.indentValue=n=t):t=0,t+=(yield*this.pushSpaces(!0))}while(e+t>0);const s=this.getLine();if(null===s)return this.setNext("flow");if(-1!==n&&n<this.indentNext&&"#"!==s[0]||0===n&&(s.startsWith("---")||s.startsWith("..."))&&yd(s[3])){if(!(n===this.indentNext-1&&1===this.flowLevel&&("]"===s[0]||"}"===s[0])))return this.flowLevel=0,yield"",yield*this.parseLineStart()}let r=0;for(;","===s[r];)r+=(yield*this.pushCount(1)),r+=(yield*this.pushSpaces(!0)),this.flowKey=!1;switch(r+=(yield*this.pushIndicators()),s[r]){case void 0:return"flow";case"#":return yield*this.pushCount(s.length-r),"flow";case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel+=1,"flow";case"}":case"]":return yield*this.pushCount(1),this.flowKey=!0,this.flowLevel-=1,this.flowLevel?"flow":"doc";case"*":return yield*this.pushUntil(vd),"flow";case'"':case"'":return this.flowKey=!0,yield*this.parseQuotedScalar();case":":{const e=this.charAt(1);if(this.flowKey||yd(e)||","===e)return this.flowKey=!1,yield*this.pushCount(1),yield*this.pushSpaces(!0),"flow"}default:return this.flowKey=!1,yield*this.parsePlainScalar()}}*parseQuotedScalar(){const e=this.charAt(0);let t=this.buffer.indexOf(e,this.pos+1);if("'"===e)for(;-1!==t&&"'"===this.buffer[t+1];)t=this.buffer.indexOf("'",t+2);else for(;-1!==t;){let e=0;for(;"\\"===this.buffer[t-1-e];)e+=1;if(e%2==0)break;t=this.buffer.indexOf('"',t+1)}const n=this.buffer.substring(0,t);let s=n.indexOf("\n",this.pos);if(-1!==s){for(;-1!==s;){const e=this.continueScalar(s+1);if(-1===e)break;s=n.indexOf("\n",e)}-1!==s&&(t=s-("\r"===n[s-1]?2:1))}if(-1===t){if(!this.atEnd)return this.setNext("quoted-scalar");t=this.buffer.length}return yield*this.pushToIndex(t+1,!1),this.flowLevel?"flow":"doc"}*parseBlockScalarHeader(){this.blockScalarIndent=-1,this.blockScalarKeep=!1;let e=this.pos;for(;;){const t=this.buffer[++e];if("+"===t)this.blockScalarKeep=!0;else if(t>"0"&&t<="9")this.blockScalarIndent=Number(t)-1;else if("-"!==t)break}return yield*this.pushUntil((e=>yd(e)||"#"===e))}*parseBlockScalar(){let e,t=this.pos-1,n=0;e:for(let s=this.pos;e=this.buffer[s];++s)switch(e){case" ":n+=1;break;case"\n":t=s,n=0;break;case"\r":{const e=this.buffer[s+1];if(!e&&!this.atEnd)return this.setNext("block-scalar");if("\n"===e)break}default:break e}if(!e&&!this.atEnd)return this.setNext("block-scalar");if(n>=this.indentNext){-1===this.blockScalarIndent?this.indentNext=n:this.indentNext=this.blockScalarIndent+(0===this.indentNext?1:this.indentNext);do{const e=this.continueScalar(t+1);if(-1===e)break;t=this.buffer.indexOf("\n",e)}while(-1!==t);if(-1===t){if(!this.atEnd)return this.setNext("block-scalar");t=this.buffer.length}}let s=t+1;for(e=this.buffer[s];" "===e;)e=this.buffer[++s];if("\t"===e){for(;"\t"===e||" "===e||"\r"===e||"\n"===e;)e=this.buffer[++s];t=s-1}else if(!this.blockScalarKeep)for(;;){let e=t-1,s=this.buffer[e];"\r"===s&&(s=this.buffer[--e]);const r=e;for(;" "===s;)s=this.buffer[--e];if(!("\n"===s&&e>=this.pos&&e+1+n>r))break;t=e}return yield"",yield*this.pushToIndex(t+1,!0),yield*this.parseLineStart()}*parsePlainScalar(){const e=this.flowLevel>0;let t,n=this.pos-1,s=this.pos-1;for(;t=this.buffer[++s];)if(":"===t){const t=this.buffer[s+1];if(yd(t)||e&&Rd.has(t))break;n=s}else if(yd(t)){let r=this.buffer[s+1];if("\r"===t&&("\n"===r?(s+=1,t="\n",r=this.buffer[s+1]):n=s),"#"===r||e&&Rd.has(r))break;if("\n"===t){const e=this.continueScalar(s+1);if(-1===e)break;s=Math.max(s,e-2)}}else{if(e&&Rd.has(t))break;n=s}return t||this.atEnd?(yield"",yield*this.pushToIndex(n+1,!0),e?"flow":"doc"):this.setNext("plain-scalar")}*pushCount(e){return e>0?(yield this.buffer.substr(this.pos,e),this.pos+=e,e):0}*pushToIndex(e,t){const n=this.buffer.slice(this.pos,e);return n?(yield n,this.pos+=n.length,n.length):(t&&(yield""),0)}*pushIndicators(){switch(this.charAt(0)){case"!":return(yield*this.pushTag())+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"&":return(yield*this.pushUntil(vd))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"-":case"?":case":":{const e=this.flowLevel>0,t=this.charAt(1);if(yd(t)||e&&Rd.has(t))return e?this.flowKey&&(this.flowKey=!1):this.indentNext=this.indentValue+1,(yield*this.pushCount(1))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators())}}return 0}*pushTag(){if("<"===this.charAt(1)){let e=this.pos+2,t=this.buffer[e];for(;!yd(t)&&">"!==t;)t=this.buffer[++e];return yield*this.pushToIndex(">"===t?e+1:e,!1)}{let e=this.pos+1,t=this.buffer[e];for(;t;)if(Ld.has(t))t=this.buffer[++e];else{if("%"!==t||!Od.has(this.buffer[e+1])||!Od.has(this.buffer[e+2]))break;t=this.buffer[e+=3]}return yield*this.pushToIndex(e,!1)}}*pushNewline(){const e=this.buffer[this.pos];return"\n"===e?yield*this.pushCount(1):"\r"===e&&"\n"===this.charAt(1)?yield*this.pushCount(2):0}*pushSpaces(e){let t,n=this.pos-1;do{t=this.buffer[++n]}while(" "===t||e&&"\t"===t);const s=n-this.pos;return s>0&&(yield this.buffer.substr(this.pos,s),this.pos=n),s}*pushUntil(e){let t=this.pos,n=this.buffer[t];for(;!e(n);)n=this.buffer[++t];return yield*this.pushToIndex(t,!1)}}class wd{constructor(){this.lineStarts=[],this.addNewLine=e=>this.lineStarts.push(e),this.linePos=e=>{let t=0,n=this.lineStarts.length;for(;t<n;){const s=t+n>>1;this.lineStarts[s]<e?t=s+1:n=s}if(this.lineStarts[t]===e)return{line:t+1,col:1};if(0===t)return{line:0,col:e};return{line:t,col:e-this.lineStarts[t-1]+1}}}}function Fd(e,t){for(let n=0;n<e.length;++n)if(e[n].type===t)return!0;return!1}function Pd(e){for(let t=0;t<e.length;++t)switch(e[t].type){case"space":case"comment":case"newline":break;default:return t}return-1}function Bd(e){switch(null==e?void 0:e.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function Ud(e){switch(e.type){case"document":return e.start;case"block-map":{const t=e.items[e.items.length-1];return t.sep??t.start}case"block-seq":return e.items[e.items.length-1].start;default:return[]}}function Hd(e){var t;if(0===e.length)return[];let n=e.length;e:for(;--n>=0;)switch(e[n].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;"space"===(null==(t=e[++n])?void 0:t.type););return e.splice(n,e.length)}function Gd(e){if("flow-seq-start"===e.start.type)for(const t of e.items)!t.sep||t.value||Fd(t.start,"explicit-key-ind")||Fd(t.sep,"map-value-ind")||(t.key&&(t.value=t.key),delete t.key,Bd(t.value)?t.value.end?Array.prototype.push.apply(t.value.end,t.sep):t.value.end=t.sep:Array.prototype.push.apply(t.start,t.sep),delete t.sep)}class $d{constructor(e){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new xd,this.onNewLine=e}*parse(e,t=!1){this.onNewLine&&0===this.offset&&this.onNewLine(0);for(const n of this.lexer.lex(e,t))yield*this.next(n);t||(yield*this.end())}*next(e){if(this.source=e,this.atScalar)return this.atScalar=!1,yield*this.step(),void(this.offset+=e.length);const t=function(e){switch(e){case"\ufeff":return"byte-order-mark";case"":return"doc-mode";case"":return"flow-error-end";case"":return"scalar";case"---":return"doc-start";case"...":return"doc-end";case"":case"\n":case"\r\n":return"newline";case"-":return"seq-item-ind";case"?":return"explicit-key-ind";case":":return"map-value-ind";case"{":return"flow-map-start";case"}":return"flow-map-end";case"[":return"flow-seq-start";case"]":return"flow-seq-end";case",":return"comma"}switch(e[0]){case" ":case"\t":return"space";case"#":return"comment";case"%":return"directive-line";case"*":return"alias";case"&":return"anchor";case"!":return"tag";case"'":return"single-quoted-scalar";case'"':return"double-quoted-scalar";case"|":case">":return"block-scalar-header"}return null}(e);if(t)if("scalar"===t)this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=t,yield*this.step(),t){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+e.length);break;case"space":this.atNewLine&&" "===e[0]&&(this.indent+=e.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=e.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=e.length}else{const t=`Not a YAML token: ${e}`;yield*this.pop({type:"error",offset:this.offset,message:t,source:e}),this.offset+=e.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){const e=this.peek(1);if("doc-end"!==this.type||e&&"doc-end"===e.type){if(!e)return yield*this.stream();switch(e.type){case"document":return yield*this.document(e);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(e);case"block-scalar":return yield*this.blockScalar(e);case"block-map":return yield*this.blockMap(e);case"block-seq":return yield*this.blockSequence(e);case"flow-collection":return yield*this.flowCollection(e);case"doc-end":return yield*this.documentEnd(e)}yield*this.pop()}else{for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source})}}peek(e){return this.stack[this.stack.length-e]}*pop(e){const t=e??this.stack.pop();if(t)if(0===this.stack.length)yield t;else{const e=this.peek(1);switch("block-scalar"===t.type?t.indent="indent"in e?e.indent:0:"flow-collection"===t.type&&"document"===e.type&&(t.indent=0),"flow-collection"===t.type&&Gd(t),e.type){case"document":e.value=t;break;case"block-scalar":e.props.push(t);break;case"block-map":{const n=e.items[e.items.length-1];if(n.value)return e.items.push({start:[],key:t,sep:[]}),void(this.onKeyLine=!0);if(!n.sep)return Object.assign(n,{key:t,sep:[]}),void(this.onKeyLine=!n.explicitKey);n.value=t;break}case"block-seq":{const n=e.items[e.items.length-1];n.value?e.items.push({start:[],value:t}):n.value=t;break}case"flow-collection":{const n=e.items[e.items.length-1];return void(!n||n.value?e.items.push({start:[],key:t,sep:[]}):n.sep?n.value=t:Object.assign(n,{key:t,sep:[]}))}default:yield*this.pop(),yield*this.pop(t)}if(!("document"!==e.type&&"block-map"!==e.type&&"block-seq"!==e.type||"block-map"!==t.type&&"block-seq"!==t.type)){const n=t.items[t.items.length-1];n&&!n.sep&&!n.value&&n.start.length>0&&-1===Pd(n.start)&&(0===t.indent||n.start.every((e=>"comment"!==e.type||e.indent<t.indent)))&&("document"===e.type?e.end=n.start:e.items.push({start:n.start}),t.items.splice(-1,1))}}else{const e="Tried to pop an empty stack";yield{type:"error",offset:this.offset,source:"",message:e}}}*stream(){switch(this.type){case"directive-line":return void(yield{type:"directive",offset:this.offset,source:this.source});case"byte-order-mark":case"space":case"comment":case"newline":return void(yield this.sourceToken);case"doc-mode":case"doc-start":{const e={type:"document",offset:this.offset,start:[]};return"doc-start"===this.type&&e.start.push(this.sourceToken),void this.stack.push(e)}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(e){if(e.value)return yield*this.lineEnd(e);switch(this.type){case"doc-start":return void(-1!==Pd(e.start)?(yield*this.pop(),yield*this.step()):e.start.push(this.sourceToken));case"anchor":case"tag":case"space":case"comment":case"newline":return void e.start.push(this.sourceToken)}const t=this.startBlockValue(e);t?this.stack.push(t):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(e){if("map-value-ind"===this.type){const t=Hd(Ud(this.peek(2)));let n;e.end?(n=e.end,n.push(this.sourceToken),delete e.end):n=[this.sourceToken];const s={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:t,key:e,sep:n}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=s}else yield*this.lineEnd(e)}*blockScalar(e){switch(this.type){case"space":case"comment":case"newline":return void e.props.push(this.sourceToken);case"scalar":if(e.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let e=this.source.indexOf("\n")+1;for(;0!==e;)this.onNewLine(this.offset+e),e=this.source.indexOf("\n",e)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(e){var t;const n=e.items[e.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,n.value){const t="end"in n.value?n.value.end:void 0,s=Array.isArray(t)?t[t.length-1]:void 0;"comment"===(null==s?void 0:s.type)?null==t||t.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else n.sep?n.sep.push(this.sourceToken):n.start.push(this.sourceToken);return;case"space":case"comment":if(n.value)e.items.push({start:[this.sourceToken]});else if(n.sep)n.sep.push(this.sourceToken);else{if(this.atIndentedComment(n.start,e.indent)){const s=e.items[e.items.length-2],r=null==(t=null==s?void 0:s.value)?void 0:t.end;if(Array.isArray(r))return Array.prototype.push.apply(r,n.start),r.push(this.sourceToken),void e.items.pop()}n.start.push(this.sourceToken)}return}if(this.indent>=e.indent){const t=!this.onKeyLine&&this.indent===e.indent,s=t&&(n.sep||n.explicitKey)&&"seq-item-ind"!==this.type;let r=[];if(s&&n.sep&&!n.value){const t=[];for(let s=0;s<n.sep.length;++s){const r=n.sep[s];switch(r.type){case"newline":t.push(s);break;case"space":break;case"comment":r.indent>e.indent&&(t.length=0);break;default:t.length=0}}t.length>=2&&(r=n.sep.splice(t[1]))}switch(this.type){case"anchor":case"tag":return void(s||n.value?(r.push(this.sourceToken),e.items.push({start:r}),this.onKeyLine=!0):n.sep?n.sep.push(this.sourceToken):n.start.push(this.sourceToken));case"explicit-key-ind":return n.sep||n.explicitKey?s||n.value?(r.push(this.sourceToken),e.items.push({start:r,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}):(n.start.push(this.sourceToken),n.explicitKey=!0),void(this.onKeyLine=!0);case"map-value-ind":if(n.explicitKey)if(n.sep)if(n.value)e.items.push({start:[],key:null,sep:[this.sourceToken]});else if(Fd(n.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:r,key:null,sep:[this.sourceToken]}]});else if(Bd(n.key)&&!Fd(n.sep,"newline")){const e=Hd(n.start),t=n.key,s=n.sep;s.push(this.sourceToken),delete n.key,delete n.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:e,key:t,sep:s}]})}else r.length>0?n.sep=n.sep.concat(r,this.sourceToken):n.sep.push(this.sourceToken);else if(Fd(n.start,"newline"))Object.assign(n,{key:null,sep:[this.sourceToken]});else{const e=Hd(n.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:e,key:null,sep:[this.sourceToken]}]})}else n.sep?n.value||s?e.items.push({start:r,key:null,sep:[this.sourceToken]}):Fd(n.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):n.sep.push(this.sourceToken):Object.assign(n,{key:null,sep:[this.sourceToken]});return void(this.onKeyLine=!0);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const t=this.flowScalar(this.type);return void(s||n.value?(e.items.push({start:r,key:t,sep:[]}),this.onKeyLine=!0):n.sep?this.stack.push(t):(Object.assign(n,{key:t,sep:[]}),this.onKeyLine=!0))}default:{const n=this.startBlockValue(e);if(n)return t&&"block-seq"!==n.type&&e.items.push({start:r}),void this.stack.push(n)}}}yield*this.pop(),yield*this.step()}*blockSequence(e){var t;const n=e.items[e.items.length-1];switch(this.type){case"newline":if(n.value){const t="end"in n.value?n.value.end:void 0,s=Array.isArray(t)?t[t.length-1]:void 0;"comment"===(null==s?void 0:s.type)?null==t||t.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else n.start.push(this.sourceToken);return;case"space":case"comment":if(n.value)e.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(n.start,e.indent)){const s=e.items[e.items.length-2],r=null==(t=null==s?void 0:s.value)?void 0:t.end;if(Array.isArray(r))return Array.prototype.push.apply(r,n.start),r.push(this.sourceToken),void e.items.pop()}n.start.push(this.sourceToken)}return;case"anchor":case"tag":if(n.value||this.indent<=e.indent)break;return void n.start.push(this.sourceToken);case"seq-item-ind":if(this.indent!==e.indent)break;return void(n.value||Fd(n.start,"seq-item-ind")?e.items.push({start:[this.sourceToken]}):n.start.push(this.sourceToken))}if(this.indent>e.indent){const t=this.startBlockValue(e);if(t)return void this.stack.push(t)}yield*this.pop(),yield*this.step()}*flowCollection(e){const t=e.items[e.items.length-1];if("flow-error-end"===this.type){let e;do{yield*this.pop(),e=this.peek(1)}while(e&&"flow-collection"===e.type)}else if(0===e.end.length){switch(this.type){case"comma":case"explicit-key-ind":return void(!t||t.sep?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken));case"map-value-ind":return void(!t||t.value?e.items.push({start:[],key:null,sep:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]}));case"space":case"comment":case"newline":case"anchor":case"tag":return void(!t||t.value?e.items.push({start:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken));case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const n=this.flowScalar(this.type);return void(!t||t.value?e.items.push({start:[],key:n,sep:[]}):t.sep?this.stack.push(n):Object.assign(t,{key:n,sep:[]}))}case"flow-map-end":case"flow-seq-end":return void e.end.push(this.sourceToken)}const n=this.startBlockValue(e);n?this.stack.push(n):(yield*this.pop(),yield*this.step())}else{const t=this.peek(2);if("block-map"===t.type&&("map-value-ind"===this.type&&t.indent===e.indent||"newline"===this.type&&!t.items[t.items.length-1].sep))yield*this.pop(),yield*this.step();else if("map-value-ind"===this.type&&"flow-collection"!==t.type){const n=Hd(Ud(t));Gd(e);const s=e.end.splice(1,e.end.length);s.push(this.sourceToken);const r={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:n,key:e,sep:s}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=r}else yield*this.lineEnd(e)}}flowScalar(e){if(this.onNewLine){let e=this.source.indexOf("\n")+1;for(;0!==e;)this.onNewLine(this.offset+e),e=this.source.indexOf("\n",e)+1}return{type:e,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(e){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;const t=Hd(Ud(e));return t.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:t,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;const t=Hd(Ud(e));return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:t,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(e,t){return"comment"===this.type&&(!(this.indent<=t)&&e.every((e=>"newline"===e.type||"space"===e.type)))}*documentEnd(e){"doc-mode"!==this.type&&(e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],"newline"===this.type&&(yield*this.pop()))}*lineEnd(e){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;default:e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],"newline"===this.type&&(yield*this.pop())}}}function qd(e,t={}){const{lineCounter:n,prettyErrors:s}=function(e){const t=!1!==e.prettyErrors;return{lineCounter:e.lineCounter||t&&new wd||null,prettyErrors:t}}(t),r=new $d(null==n?void 0:n.addNewLine),i=new Dd(t);let o=null;for(const t of i.compose(r.parse(e),!0,e.length))if(o){if("silent"!==o.options.logLevel){o.errors.push(new nd(t.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}}else o=t;return s&&n&&(o.errors.forEach(rd(e,n)),o.warnings.forEach(rd(e,n))),o}const Yd={name:"frontmatter",transform:e=>(e.beforeParse.tap(((e,t)=>{var n;const{content:s}=t;if(!/^---\r?\n/.test(s))return;const r=/\n---\r?\n/.exec(s);if(!r)return;const i=s.slice(4,r.index).trimEnd();let o;try{o=function(e,t,n){let s;const r=qd(e,n);if(!r)return null;if(r.warnings.forEach((e=>Uu(r.options.logLevel,e))),r.errors.length>0){if("silent"!==r.options.logLevel)throw r.errors[0];r.errors=[]}return r.toJS(Object.assign({reviver:s},n))}(i.replace(/\r?\n|\r/g,"\n")),(null==o?void 0:o.markmap)&&(o.markmap=function(e){if(!e)return;return["color","extraJs","extraCss"].forEach((t=>{null!=e[t]&&(e[t]=function(e){let t;"string"==typeof e?t=[e]:Array.isArray(e)&&(t=e.filter((e=>e&&"string"==typeof e)));return(null==t?void 0:t.length)?t:void 0}(e[t]))})),["duration","maxWidth","initialExpandLevel"].forEach((t=>{null!=e[t]&&(e[t]=function(e){if(isNaN(+e))return;return+e}(e[t]))})),e}(o.markmap))}catch{return}t.frontmatter=o,t.parserOptions={...t.parserOptions,...null==(n=null==o?void 0:o.markmap)?void 0:n.htmlParser},t.frontmatterInfo={lines:s.slice(0,r.index).split("\n").length+1,offset:r.index+r[0].length}})),{})};function jd(e,t){return"script"===t.type&&t.data.src?{...t,data:{...t.data,src:e.getFullUrl(t.data.src)}}:t}function Kd(e,t){return"stylesheet"===t.type&&t.data.href?{...t,data:{...t.data,href:e.getFullUrl(t.data.href)}}:t}const Vd="hljs",zd=["@highlightjs/cdn-assets@11.11.1/highlight.min.js"].map((e=>b(e))),Qd=["@highlightjs/cdn-assets@11.11.1/styles/default.min.css"].map((e=>k(e))),Wd={name:Vd,config:{versions:{hljs:"11.11.1"},preloadScripts:zd,styles:Qd},transform(e){var t,n,s;let r;const o=(null==(n=null==(t=Wd.config)?void 0:t.preloadScripts)?void 0:n.map((t=>jd(e.transformer.urlBuilder,t))))||[];let a=i;return e.parser.tap((t=>{t.set({highlight:(t,n)=>{a();const{hljs:s}=window;return s?s.highlightAuto(t,n?[n]:void 0).value:((r||(r=C(o)),r).then((()=>{e.retransform.call()})),t)}})})),e.beforeParse.tap(((e,t)=>{a=()=>{t.features[Vd]=!0}})),{styles:null==(s=Wd.config)?void 0:s.styles}}};const Xd="katex",Zd=["katex@0.16.18/dist/katex.min.js"].map((e=>b(e))),Jd=b("webfontloader@1.6.28/webfontloader.js");Jd.data.defer=!0;const ep={versions:{katex:"0.16.18",webfontloader:"1.6.28"},preloadScripts:Zd,scripts:[{type:"iife",data:{fn:e=>{window.WebFontConfig={custom:{families:["KaTeX_AMS","KaTeX_Caligraphic:n4,n7","KaTeX_Fraktur:n4,n7","KaTeX_Main:n4,n7,i4,i7","KaTeX_Math:i4,i7","KaTeX_Script","KaTeX_SansSerif:n4,n7,i4","KaTeX_Size1","KaTeX_Size2","KaTeX_Size3","KaTeX_Size4","KaTeX_Typewriter"]},active:()=>{e().refreshHook.call()}}},getParams:({getMarkmap:e})=>[e]}},Jd],styles:["katex@0.16.18/dist/katex.min.css"].map((e=>k(e))),resources:["katex@0.16.18/dist/fonts/KaTeX_AMS-Regular.woff2","katex@0.16.18/dist/fonts/KaTeX_Caligraphic-Bold.woff2","katex@0.16.18/dist/fonts/KaTeX_Caligraphic-Regular.woff2","katex@0.16.18/dist/fonts/KaTeX_Fraktur-Bold.woff2","katex@0.16.18/dist/fonts/KaTeX_Fraktur-Regular.woff2","katex@0.16.18/dist/fonts/KaTeX_Main-Bold.woff2","katex@0.16.18/dist/fonts/KaTeX_Main-BoldItalic.woff2","katex@0.16.18/dist/fonts/KaTeX_Main-Italic.woff2","katex@0.16.18/dist/fonts/KaTeX_Main-Regular.woff2","katex@0.16.18/dist/fonts/KaTeX_Math-BoldItalic.woff2","katex@0.16.18/dist/fonts/KaTeX_Math-Italic.woff2","katex@0.16.18/dist/fonts/KaTeX_SansSerif-Bold.woff2","katex@0.16.18/dist/fonts/KaTeX_SansSerif-Italic.woff2","katex@0.16.18/dist/fonts/KaTeX_SansSerif-Regular.woff2","katex@0.16.18/dist/fonts/KaTeX_Script-Regular.woff2","katex@0.16.18/dist/fonts/KaTeX_Size1-Regular.woff2","katex@0.16.18/dist/fonts/KaTeX_Size2-Regular.woff2","katex@0.16.18/dist/fonts/KaTeX_Size3-Regular.woff2","katex@0.16.18/dist/fonts/KaTeX_Size4-Regular.woff2","katex@0.16.18/dist/fonts/KaTeX_Typewriter-Regular.woff2"]};var tp,np={};var sp=function(){if(tp)return np;tp=1;var e=np.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(np,"__esModule",{value:!0});const n=e(t);function s(e,t){const n=e.src[t-1],s=e.src[t],o=e.src[t+1];if("$"!==s)return{can_open:!1,can_close:!1};let a=!1,c=!1;return"$"===n||"\\"===n||void 0!==n&&!r(n)&&i(n)||(a=!0),"$"===o||null!=o&&!r(o)&&i(o)||(c=!0),{can_open:a,can_close:c}}function r(e){return/^\s$/u.test(e)}function i(e){return/^[\w\d]$/u.test(e)}function o(e,t){const n=e.src[t-1],s=e.src[t],r=e.src[t+1],i=e.src[t+2];return"$"===s&&"$"!==n&&"\\"!==n&&"$"===r&&"$"!==i?{can_open:!0,can_close:!0}:{can_open:!1,can_close:!1}}function a(e,t){if("$"!==e.src[e.pos])return!1;const n=e.tokens.at(-1);if("html_inline"===(null==n?void 0:n.type)&&/^<\w+.+[^/]>$/.test(n.content))return!1;let r=s(e,e.pos);if(!r.can_open)return t||(e.pending+="$"),e.pos+=1,!0;let i,o=e.pos+1,a=o;for(;-1!==(a=e.src.indexOf("$",a));){for(i=a-1;"\\"===e.src[i];)i-=1;if((a-i)%2==1)break;a+=1}if(-1===a)return t||(e.pending+="$"),e.pos=o,!0;if(a-o==0)return t||(e.pending+="$$"),e.pos=o+1,!0;if(r=s(e,a),!r.can_close)return t||(e.pending+="$"),e.pos=o,!0;if(!t){const t=e.push("math_inline","math",0);t.markup="$",t.content=e.src.slice(o,a)}return e.pos=a+1,!0}function c(e,t){var n,s,r,i;if("$$"!==e.src.slice(e.pos,e.pos+2))return!1;if(!o(e,e.pos).can_open)return t||(e.pending+="$$"),e.pos+=2,!0;for(s=n=e.pos+2;-1!==(s=e.src.indexOf("$$",s));){for(i=s-1;"\\"===e.src[i];)i-=1;if((s-i)%2==1)break;s+=2}return-1===s?(t||(e.pending+="$$"),e.pos=n,!0):s-n==0?(t||(e.pending+="$$$$"),e.pos=n+2,!0):o(e,s).can_close?(t||((r=e.push("math_block","math",0)).block=!0,r.markup="$$",r.content=e.src.slice(n,s)),e.pos=s+2,!0):(t||(e.pending+="$$"),e.pos=n,!0)}function l(e,t){const n=e.src.slice(e.pos);if(!/^\n\\begin/.test(n))return!1;if(e.pos+=1,t)return!0;const s=n.split(/\n/g).slice(1);let r;const i=[];e:for(var o=0;o<s.length;++o){const e=s[o];for(const t of e.matchAll(/(\\begin|\\end)\s*\{([^{}]+)\}/g))if("\\begin"===t[1])i.push(t[2].trim());else if("\\end"===t[1]&&(i.pop(),!i.length)){r=o;break e}}if(void 0===r)return!1;const a=s.slice(0,r+1).reduce(((e,t)=>e+t.length),0)+r+1,c=e.push("math_inline_bare_block","math",0);return c.block=!0,c.markup="$$",c.content=n.slice(1,a),e.pos=e.pos+a,!0}function u(e,t,n,s){const r=e.tokens;for(let e=r.length-1;e>=0;e--){const i=r[e],o=[];if("html_block"!==i.type)continue;const a=i.content;for(const e of a.matchAll(s)){if(!e.groups)continue;const s=e.groups.html_before_math,r=e.groups.math,a=e.groups.html_after_math;s&&o.push({...i,type:"html_block",map:null,content:s}),r&&o.push({...i,type:t,map:null,content:r,markup:n,block:!0,tag:"math"}),a&&o.push({...i,type:"html_block",map:null,content:a})}o.length>0&&r.splice(e,1,...o)}return!0}function h(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")}return np.default=function(e,t){const s=(null==t?void 0:t.katex)??n.default,r=null==t?void 0:t.enableBareBlocks,i=null==t?void 0:t.enableMathBlockInHtml,o=null==t?void 0:t.enableMathInlineInHtml,d=null==t?void 0:t.enableFencedBlocks;e.inline.ruler.after("escape","math_inline",a),e.inline.ruler.after("escape","math_inline_block",c),r&&e.inline.ruler.before("text","math_inline_bare_block",l),e.block.ruler.after("blockquote","math_block",((e,t,n,s)=>!(!r||!function(e,t,n,s){const r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(!e.src.slice(r,i).match(/^\s*\\begin\s*\{([^{}]+)\}/))return!1;if(t>0){const n=e.bMarks[t-1]+e.tShift[t-1],s=e.eMarks[t-1],r=e.src.slice(n,s);if(!/^\s*$/.test(r))return!1}if(s)return!0;const o=[];let a,c=t,l=!1;e:for(;!(l||c>=n);c++){const t=e.bMarks[c]+e.tShift[c],n=e.eMarks[c];if(t<n&&e.tShift[c]<e.blkIndent)break;const s=e.src.slice(t,n);for(const r of s.matchAll(/(\\begin|\\end)\s*\{([^{}]+)\}/g))if("\\begin"===r[1])o.push(r[2].trim());else if("\\end"===r[1]&&(o.pop(),!o.length)){a=e.src.slice(t,n),l=!0;break e}}e.line=c+1;const u=e.push("math_block","math",0);return u.block=!0,u.content=(e.getLines(t,c,e.tShift[t],!0)+(a??"")).trim(),u.map=[t,e.line],u.markup="$$",!0}(e,t,n,s))||function(e,t,n,s){var r,i,o,a,c=!1,l=e.bMarks[t]+e.tShift[t],u=e.eMarks[t];if(l+2>u)return!1;if("$$"!==e.src.slice(l,l+2))return!1;l+=2;let h=e.src.slice(l,u);if(s)return!0;for("$$"===h.trim().slice(-2)&&(h=h.trim().slice(0,-2),c=!0),i=t;!(c||++i>=n||(l=e.bMarks[i]+e.tShift[i])<(u=e.eMarks[i])&&e.tShift[i]<e.blkIndent);)"$$"===e.src.slice(l,u).trim().slice(-2)?(o=e.src.slice(0,u).lastIndexOf("$$"),r=e.src.slice(l,o),c=!0):e.src.slice(l,u).trim().includes("$$")&&(o=e.src.slice(0,u).trim().indexOf("$$"),r=e.src.slice(l,o),c=!0);return e.line=i+1,(a=e.push("math_block","math",0)).block=!0,a.content=(h&&h.trim()?h+"\n":"")+e.getLines(t+1,i,e.tShift[t],!0)+(r&&r.trim()?r:""),a.map=[t,e.line],a.markup="$$",!0}(e,t,n,s)),{alt:["paragraph","reference","blockquote","list"]});const p=/(?<html_before_math>[\s\S]*?)\$\$(?<math>[\s\S]+?)\$\$(?<html_after_math>(?:(?!\$\$[\s\S]+?\$\$)[\s\S])*)/gm,f=/(?<html_before_math>[\s\S]*?)\$(?<math>.*?)\$(?<html_after_math>(?:(?!\$.*?\$)[\s\S])*)/gm;i&&e.core.ruler.push("math_block_in_html_block",(e=>u(e,"math_block","$$",p))),o&&e.core.ruler.push("math_inline_in_html_block",(e=>u(e,"math_inline","$",f)));const m=e=>{try{return`<p class="katex-block">${s.renderToString(e,{...t,displayMode:!0})}</p>`}catch(n){return(null==t?void 0:t.throwOnError)&&console.log(n),`<p class="katex-block katex-error" title="${h(e)}">${h(n+"")}</p>`}},E=(e,t)=>m(e[t].content)+"\n";if(e.renderer.rules.math_inline=(e,n)=>{const r=e[n].content;return(e=>{const n=/\\begin\{(align|equation|gather|cd|alignat)\}/gi.test(e);try{return s.renderToString(e,{...t,displayMode:n})}catch(n){return(null==t?void 0:t.throwOnError)&&console.log(n),`<span class="katex-error" title="${h(e)}">${h(n+"")}</span>`}})(r.length>2&&"`"===r[0]&&"`"===r[r.length-1]?r.slice(1,-1):r)},e.renderer.rules.math_inline_block=E,e.renderer.rules.math_inline_bare_block=E,e.renderer.rules.math_block=E,d){const t="math",n=e.renderer.rules.fence;e.renderer.rules.fence=function(e,s,r,i,o){const a=e[s];return a.info.trim().toLowerCase()===t&&d?m(a.content)+"\n":(null==n?void 0:n.call(this,e,s,r,i,o))||""}}},np}();const rp=(ip=en(sp)).default||ip;var ip;const op={name:Xd,config:ep,transform(e){var t,n,s,r;let o;const a=(null==(n=null==(t=op.config)?void 0:t.preloadScripts)?void 0:n.map((t=>jd(e.transformer.urlBuilder,t))))||[],c=(t,n)=>{const{katex:s}=window;return s?s.renderToString(t,{displayMode:n,throwOnError:!1}):((o||(o=C(a)),o).then((()=>{e.retransform.call()})),t)};let l=i;return e.parser.tap((e=>{e.use(rp),["math_block","math_inline"].forEach((t=>{e.renderer.rules[t]=(e,t)=>{l();return c(e[t].content,!!e[t].block)}}))})),e.beforeParse.tap(((e,t)=>{l=()=>{t.features[Xd]=!0}})),e.afterParse.tap(((e,t)=>{var n;const s=null==(n=t.frontmatter)?void 0:n.markmap;s&&["extraJs","extraCss"].forEach((e=>{var t,n;const r=s[e];var i,o,a;r&&(s[e]=(i=r,o=Xd,a=(null==(n=null==(t=op.config)?void 0:t.versions)?void 0:n.katex)||"",i.map((e=>{if("string"==typeof e&&!e.includes("://")){e.startsWith("npm:")||(e=`npm:${e}`);const t=4+o.length;e.startsWith(`npm:${o}/`)&&(e=`${e.slice(0,t)}@${a}${e.slice(t)}`)}return e}))))}))})),{styles:null==(s=op.config)?void 0:s.styles,scripts:null==(r=op.config)?void 0:r.scripts}}},ap=[Yd,op,Wd,{name:"npmUrl",transform:e=>(e.afterParse.tap(((t,n)=>{const{frontmatter:s}=n,r=null==s?void 0:s.markmap;r&&["extraJs","extraCss"].forEach((t=>{const n=r[t];n&&(r[t]=n.map((t=>t.startsWith("npm:")?e.transformer.urlBuilder.getFullUrl(t.slice(4)):t)))}))})),{})},Bl,{name:"sourceLines",transform(e){let t=0;return e.beforeParse.tap(((e,n)=>{var s;t=(null==(s=n.frontmatterInfo)?void 0:s.lines)||0})),e.parser.tap((e=>{e.renderer.renderAttrs=o(e.renderer.renderAttrs,((e,n)=>{if(n.block&&n.map){const e=n.map.map((e=>e+t));n.attrSet("data-lines",e.join(","))}return e(n)})),e.renderer.rules.fence&&(e.renderer.rules.fence=o(e.renderer.rules.fence,((e,n,s,...r)=>{let i=e(n,s,...r);const o=n[s];if(i.startsWith("<pre>")&&o.map){const e=o.map.map((e=>e+t));i=i.slice(0,4)+` data-lines="${e.join(",")}"`+i.slice(4)}return i})))})),{}}}];function cp(e){for(;!e.content&&1===e.children.length;)e=e.children[0];for(;1===e.children.length&&!e.children[0].content;)e={...e,children:e.children[0].children};return{...e,children:e.children.map(cp)}}e.Transformer=class{constructor(e=ap){this.assetsMap={},this.urlBuilder=new s,this.hooks={transformer:this,parser:new r,beforeParse:new r,afterParse:new r,retransform:new r},this.plugins=e.map((e=>"function"==typeof e?e():e));const t={};for(const{name:e,transform:n}of this.plugins)t[e]=n(this.hooks);this.assetsMap=t;const n=function(){const e=yl({html:!0,breaks:!0});return e.use(Ol).use(Ll).use(vl).use(Fl),e}();this.md=n,this.hooks.parser.call(n)}transform(e,t){var n;const s={content:e,features:{},parserOptions:t};this.hooks.beforeParse.call(this.md,s);let{content:r}=s;s.frontmatterInfo&&(r=r.slice(s.frontmatterInfo.offset));const i=this.md.render(r,{});this.hooks.afterParse.call(this.md,s);const o=cp(function(e,t){return n=na(e,t),function(e,t){const n=(e,s)=>t(e,(()=>{var t;return null==(t=e.children)?void 0:t.map((t=>n(t,e)))}),s);return n(e)}(n,((e,t)=>{const n={content:e.html,children:t()||[]};return e.data&&(n.payload={tag:e.tag,...e.data}),e.comments&&(e.comments.includes("foldAll")?n.payload={...n.payload,fold:2}:e.comments.includes("fold")&&(n.payload={...n.payload,fold:1})),n}));var n}(i,s.parserOptions));return o.content||(o.content=`${(null==(n=s.frontmatter)?void 0:n.title)||""}`),{...s,root:o}}resolveJS(e){return jd(this.urlBuilder,e)}resolveCSS(e){return Kd(this.urlBuilder,e)}getAssets(e){const t=[],n=[];e??(e=this.plugins.map((e=>e.name)));for(const s of e.map((e=>this.assetsMap[e])))s&&(s.styles&&t.push(...s.styles),s.scripts&&n.push(...s.scripts));return{styles:t.map((e=>this.resolveCSS(e))),scripts:n.map((e=>this.resolveJS(e)))}}getUsedAssets(e){const t=this.plugins.map((e=>e.name)).filter((t=>e[t]));return this.getAssets(t)}},e.builtInPlugins=ap,e.patchCSSItem=Kd,e.patchJSItem=jd,e.transformerVersions={"markmap-lib":"0.18.8"},Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}(this.markmap=this.markmap||{},window.katex);
//# sourceMappingURL=/sm/7caee7a16a4ada88abff37b1f6363aa0aca490a7844c1127d72c72770548163e.map