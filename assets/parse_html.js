function splitHtml(html, splitRegex, parentDeck, deckPrefix) {
    try {
        // 1. 创建DOM解析器
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 2. 初始化结果映射 - 键为牌组名称，值为该牌组下的内容
        const contentMap = {};

        // 3. 设置初始牌组
        let currentDeck = parentDeck || "Default";
        const isUsingPrefix = deckPrefix && deckPrefix.length > 0;
        let currentLevelDecks = [currentDeck];
        let lastLevel = 0;

        // 4. 创建分割正则表达式
        const regex = new RegExp(splitRegex);

        // 5. 初始化当前内容块
        let currentContent = "";

        // 6. 遍历HTML元素
        for (let element of doc.body.childNodes) {
            const elementText = element.textContent.trim();

            // 6.1 处理子牌组标识
            if (isUsingPrefix && elementText.startsWith(deckPrefix)) {
                // 如果当前内容块非空，保存到当前牌组
                if (currentContent.trim()) {
                    if (!contentMap[currentDeck]) {
                        contentMap[currentDeck] = [];
                    }
                    contentMap[currentDeck].push(currentContent);
                    currentContent = "";
                }

                // 计算子牌组层级
                const prefixLength = deckPrefix.length;

                // 计算前缀的重复次数
                let count = 0;
                let pos = 0;
                let text = elementText.trim();
                for (let i = 0; i < text.length; i++) {
                    if (text[i] === deckPrefix[0]) {
                        count++;
                    } else {
                        pos = i;
                        break;
                    }
                }

                // 计算层级 (前缀重复次数)
                const level = count - prefixLength + 1;

                // 提取牌组名称 (去除所有前缀字符)
                const deckName = text.substring(pos).trim();

                // 忽略空名称的牌组
                if (!deckName) {
                    continue;
                }

                // 构建牌组层级结构
                if (level === 1) {
                    // 一级牌组 - 直接从父牌组创建
                    currentLevelDecks = [parentDeck || "Default"];
                    currentLevelDecks.push(deckName);
                } else {
                    // 调整牌组层级
                    if (level > lastLevel + 1) {
                        // 不允许跳级
                        const validLevel = lastLevel + 1;
                        // 保留有效的父牌组路径
                        if (currentLevelDecks.length > validLevel) {
                            currentLevelDecks = currentLevelDecks.slice(0, validLevel);
                        }
                        // 添加新牌组
                        currentLevelDecks.push(deckName);
                    } else if (level <= lastLevel) {
                        // 同级或更浅层级
                        // 保留到上一级的路径
                        if (level <= currentLevelDecks.length) {
                            currentLevelDecks = currentLevelDecks.slice(0, level);
                        }
                        // 添加当前牌组，替换同级位置的牌组
                        currentLevelDecks.push(deckName);
                    } else { // level == lastLevel + 1
                        // 下一级子牌组，直接添加
                        currentLevelDecks.push(deckName);
                    }
                }

                // 确保没有空值，避免连续冒号
                currentLevelDecks = currentLevelDecks.filter(deck => deck && deck.trim() !== '');

                // 更新层级和当前牌组
                lastLevel = level;
                currentDeck = currentLevelDecks.join('::');

                continue; // 处理完牌组标识后继续下一个元素
            }

            // 6.2 检查元素是否匹配分割正则
            if (regex.test(elementText)) {
                // 如果当前内容非空，保存到当前牌组
                if (currentContent.trim()) {
                    if (!contentMap[currentDeck]) {
                        contentMap[currentDeck] = [];
                    }
                    contentMap[currentDeck].push(currentContent);
                    currentContent = "";
                }

                // 开始新的内容块，从当前元素开始
                currentContent = element.outerHTML;
            } else {
                // 如果内容块为空且当前元素不匹配分割条件，则开始新的内容块
                if (!currentContent) {
                    currentContent = element.outerHTML;
                } else {
                    // 否则将当前元素添加到现有内容块
                    currentContent += element.outerHTML;
                }
            }
        }

        // 7. 保存最后一个内容块（如果有的话）
        if (currentContent.trim()) {
            if (!contentMap[currentDeck]) {
                contentMap[currentDeck] = [];
            }
            contentMap[currentDeck].push(currentContent);
        }

        // 8. 返回结果映射
        return JSON.stringify(contentMap);
    } catch (e) {
        console.error(e);
        return null;
    }
}

function parseHtmlToParts(htmlContent) {
    try {
        // 创建DOM解析器
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // 获取所有一级子节点并转换为数组
        const parts = Array.from(doc.body.childNodes).map(element => element.outerHTML);

        return JSON.stringify(parts);
    } catch (e) {
        console.error(e);
        return "[]";
    }
}

function parseHtmlToCards(htmlContent, fieldPatterns, parentDeck, deckPrefix) {
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // --- 1. 清理和准备正则表达式 ---
        for (const field of Object.keys(fieldPatterns)) {
            if (!fieldPatterns[field]) {
                delete fieldPatterns[field];
            }
        }
        const fieldRegexes = Object.fromEntries(
            Object.entries(fieldPatterns).map(([key, value]) => [key, new RegExp(value)])
        );
        const fieldOrder = Object.keys(fieldPatterns);
        if (fieldOrder.length === 0) return JSON.stringify({}); // 没有字段模式，直接返回

        const firstField = fieldOrder[0];
        const firstFieldRegex = fieldRegexes[firstField];

        // --- 2. 阶段一：识别与分组 (将HTML元素分组为逻辑块) ---
        const logicalBlocks = [];
        let currentBlock = null;

        for (const element of doc.body.children) {
            const text = element.textContent.trim();
            const isDeckTag = deckPrefix && text.startsWith(deckPrefix);
            const isFirstField = firstFieldRegex.test(text);

            if (isDeckTag || isFirstField) {
                // 当遇到新的牌组或新的题干时，保存上一个块
                if (currentBlock) {
                    logicalBlocks.push(currentBlock);
                }
                // 并开始一个新块
                currentBlock = {
                    type: isDeckTag ? 'deck' : 'card',
                    elements: [element]
                };
            } else if (currentBlock) {
                // 如果不匹配，但当前有块，则将此元素追加到当前块
                currentBlock.elements.push(element);
            }
        }
        // 保存最后一个块
        if (currentBlock) {
            logicalBlocks.push(currentBlock);
        }

        // --- 3. 阶段二：构建与保存 (遍历逻辑块来创建卡片) ---
        const deckMap = {};
        let currentDeck = parentDeck || "Default";
        let currentLevelDecks = [currentDeck];
        let lastLevel = 0;

        for (const block of logicalBlocks) {
            if (block.type === 'deck') {
                const elementText = block.elements[0].textContent.trim();

                // (牌组层级处理逻辑保持不变，但更清晰)
                let count = 0;
                while (elementText[count] === deckPrefix[0]) {
                    count++;
                }
                const level = count;
                const deckName = elementText.substring(count).trim();

                if (!deckName) continue;

                if (level === 1) {
                    currentLevelDecks = [parentDeck || "Default", deckName];
                } else {
                    if (level >= currentLevelDecks.length) {
                        currentLevelDecks.push(deckName);
                    } else {
                        currentLevelDecks.length = level;
                        currentLevelDecks.push(deckName);
                    }
                }

                currentLevelDecks = currentLevelDecks.filter(Boolean);
                lastLevel = level;
                currentDeck = currentLevelDecks.join('::');

            } else if (block.type === 'card') {
                const card = {};
                let lastFieldFound = null;

                // 遍历这个卡片块中的所有HTML元素
                for (const element of block.elements) {
                    const text = element.textContent.trim();
                    let fieldFound = null;

                    // 检查当前元素属于哪个字段
                    for (const field of fieldOrder) {
                        if (fieldRegexes[field].test(text)) {
                            fieldFound = field;
                            break;
                        }
                    }

                    if (fieldFound) {
                        // 如果这是一个新字段，初始化它
                        if (!card[fieldFound]) {
                            card[fieldFound] = '';
                        }
                        card[fieldFound] += element.outerHTML;
                        lastFieldFound = fieldFound;
                    } else if (lastFieldFound) {
                        // 如果不属于任何新字段，则追加到上一个字段
                        card[lastFieldFound] += element.outerHTML;
                    }
                }

                // 补充缺失的字段为空字符串
                for (const field of fieldOrder) {
                    if (!card[field]) {
                        card[field] = '';
                    }
                }

                // 将构建好的卡片保存到对应的牌组
                if (!deckMap[currentDeck]) {
                    deckMap[currentDeck] = [];
                }
                deckMap[currentDeck].push(card);
            }
        }

        return JSON.stringify(deckMap);

    } catch (e) {
        console.error("Error in parseHtmlToCards:", e);
        return null;
    }
}

function html_convert_cloze(htmlContent, cloze_styles, text_colors, highlight_colors) {
    try {
        // 创建DOM解析器
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // 辅助函数：检查元素是否已经包含挖空标记
        function containsCloze(node) {
            const content = node.innerHTML || '';
            return content.includes('[[c') && content.includes('::');
        }

        // 处理节点的递归函数
        let clozeIndex = 1; // 初始化挖空序号
        function traverse(node) {
            if (node.nodeType === 1) { // 使用数值 1 代替 Node.ELEMENT_NODE
                // 如果节点已包含挖空标记，跳过该节点及其子节点
                if (containsCloze(node)) {
                    return;
                }

                const style = node.getAttribute('style');
                const colorAttr = node.getAttribute('color');
                const bgColorAttr = node.getAttribute('bgcolor');

                // 检查是否满足挖空条件
                if (cloze_styles.includes('text_color') && (
                    // 检查style属性中的文字颜色（更精确的匹配）
                    (style && text_colors.some(color => {
                        const colorLower = color.toLowerCase();
                        const colorUpper = color.toUpperCase();

                        // 使用更兼容的方法：分割CSS属性并逐个检查
                        const cssProperties = style.split(';').map(prop => prop.trim());

                        return cssProperties.some(prop => {
                            // 检查是否是 color 属性（不是 background-color）
                            const colorMatch = prop.match(/^color\s*:\s*(.+)$/i);
                            if (colorMatch) {
                                const propValue = colorMatch[1].trim();
                                return propValue.toLowerCase() === colorLower || propValue.toUpperCase() === colorUpper;
                            }
                            return false;
                        });
                    })) ||
                    // 检查直接的color属性
                    (colorAttr && text_colors.some(color => color.toLowerCase() === colorAttr.toLowerCase()))
                )) {
                    // 保留原始HTML格式，只包装挖空语法
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++; // 递增挖空序号
                    return; // 跳过子节点
                }

                if (cloze_styles.includes('text_highlight') && (
                    // 检查style属性中的背景颜色（更精确的匹配）
                    (style && highlight_colors.some(highlight => {
                        const highlightLower = highlight.toLowerCase();
                        const highlightUpper = highlight.toUpperCase();

                        // 使用CSS属性分割方法进行精确匹配
                        const cssProperties = style.split(';').map(prop => prop.trim());

                        return cssProperties.some(prop => {
                            // 检查是否是 background-color 属性
                            const bgColorMatch = prop.match(/^background-color\s*:\s*(.+)$/i);
                            if (bgColorMatch) {
                                const propValue = bgColorMatch[1].trim();
                                return propValue.toLowerCase() === highlightLower || propValue.toUpperCase() === highlightUpper;
                            }

                            // 检查是否是 background 属性（简写形式）
                            const bgMatch = prop.match(/^background\s*:\s*(.+)$/i);
                            if (bgMatch) {
                                const propValue = bgMatch[1].trim();
                                return propValue.toLowerCase() === highlightLower || propValue.toUpperCase() === highlightUpper;
                            }

                            return false;
                        });
                    })) ||
                    // 检查直接的bgcolor属性
                    (bgColorAttr && highlight_colors.some(highlight => highlight.toLowerCase() === bgColorAttr.toLowerCase()))
                )) {
                    // 保留原始HTML格式，只包装挖空语法
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++; // 递增挖空序号
                    return; // 跳过子节点
                }

                // 处理其他样式
                if (cloze_styles.includes('bold') && (node.tagName === 'B' || node.tagName === 'STRONG')) {
                    // 保留原始HTML格式，只包装挖空语法
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++; // 递增挖空序号
                    return; // 跳过子节点
                }
                if (cloze_styles.includes('italic') && (node.tagName === 'I' || node.tagName === 'EM')) {
                    // 保留原始HTML格式，只包装挖空语法
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++; // 递增挖空序号
                    return; // 跳过子节点
                }
                if (cloze_styles.includes('underline') && node.tagName === 'U') {
                    // 保留原始HTML格式，只包装挖空语法
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++; // 递增挖空序号
                    return; // 跳过子节点
                }
                if (cloze_styles.includes('strikeout') && (node.tagName === 'S' || node.tagName === 'DEL' || node.tagName === 'STRIKE')) {
                    // 保留原始HTML格式，只包装挖空语法
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++; // 递增挖空序号
                    return; // 跳过子节点
                }

                // 检查CSS样式属性
                if (style) {
                    // 检查font-weight属性（粗体）
                    if (cloze_styles.includes('bold') && (
                        style.includes('font-weight: bold') ||
                        style.includes('font-weight: 700') ||
                        style.includes('font-weight: 800') ||
                        style.includes('font-weight: 900') ||
                        style.includes('font-weight:bold') ||
                        style.includes('font-weight:700') ||
                        style.includes('font-weight:800') ||
                        style.includes('font-weight:900')
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }

                    // 检查font-style属性（斜体）
                    if (cloze_styles.includes('italic') && (
                        style.includes('font-style: italic') ||
                        style.includes('font-style: oblique') ||
                        style.includes('font-style:italic') ||
                        style.includes('font-style:oblique')
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }

                    // 检查text-decoration属性（下划线、删除线等）
                    if (cloze_styles.includes('underline') && (
                        style.includes('text-decoration: underline') ||
                        style.includes('text-decoration-line: underline') ||
                        style.includes('text-decoration:underline') ||
                        style.includes('text-decoration-line:underline')
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }

                    if (cloze_styles.includes('strikeout') && (
                        style.includes('text-decoration: line-through') ||
                        style.includes('text-decoration-line: line-through') ||
                        style.includes('text-decoration:line-through') ||
                        style.includes('text-decoration-line:line-through')
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }

                    // 检查text-decoration的复合值（如 "underline solid red"）
                    if (cloze_styles.includes('underline') && (
                        /text-decoration[^:]*:\s*[^;]*underline/i.test(style)
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }

                    if (cloze_styles.includes('strikeout') && (
                        /text-decoration[^:]*:\s*[^;]*line-through/i.test(style)
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }
                }
            }

            // 递归遍历子节点
            for (let child of Array.from(node.childNodes)) {
                traverse(child);
            }
        }

        // 开始遍历
        traverse(doc.body);

        // 返回处理后的HTML字符串
        return doc.body.innerHTML;
    } catch (e) {
        console.error(e);
        return e.toString();
    }
}

function html_convert_cloze_for_mubu(htmlContent, cloze_styles, text_colors, highlight_colors) {
    try {
        // 创建DOM解析器
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // 辅助函数：检查元素是否已经包含挖空标记
        function containsCloze(node) {
            const content = node.textContent || '';
            return content.includes('[[c') && content.includes('::');
        }

        // 处理节点的递归函数
        let clozeIndex = 1; // 初始化挖空序号
        function traverse(node) {
            // 如果节点已包含挖空标记，跳过该节点及其子节点
            if (node.nodeType === 1 && containsCloze(node)) {
                return;
            }

            if (node.nodeType === 1) { // 元素节点
                const classList = node.classList;
                const style = node.getAttribute('style');
                const colorAttr = node.getAttribute('color');
                const bgColorAttr = node.getAttribute('bgcolor');

                // 处理幕布的类名样式
                // 文本颜色
                if (cloze_styles.includes('text_color') && classList) {
                    const hasTextColor = Array.from(classList).some(cls =>
                        cls.startsWith('text-color-') &&
                        text_colors.includes(cls.replace('text-color-', ''))
                    );

                    if (hasTextColor && !containsCloze(node)) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return; // 跳过子节点
                    }
                }

                // 高亮背景
                if (cloze_styles.includes('text_highlight') && classList) {
                    const hasHighlight = Array.from(classList).some(cls =>
                        cls.startsWith('highlight-') &&
                        highlight_colors.includes(cls.replace('highlight-', ''))
                    );

                    if (hasHighlight && !containsCloze(node)) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return; // 跳过子节点
                    }
                }

                // 加粗
                if (cloze_styles.includes('bold') &&
                    (classList && classList.contains('bold') ||
                        node.tagName === 'B' ||
                        node.tagName === 'STRONG') &&
                    !containsCloze(node)) {
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++;
                    return; // 跳过子节点
                }

                // 斜体
                if (cloze_styles.includes('italic') &&
                    (classList && classList.contains('italic') ||
                        node.tagName === 'I' ||
                        node.tagName === 'EM') &&
                    !containsCloze(node)) {
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++;
                    return; // 跳过子节点
                }

                // 下划线
                if (cloze_styles.includes('underline') &&
                    (classList && classList.contains('underline') ||
                        node.tagName === 'U') &&
                    !containsCloze(node)) {
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++;
                    return; // 跳过子节点
                }

                // 删除线
                if (cloze_styles.includes('strikeout') &&
                    (classList && classList.contains('strikethrough') ||
                        node.tagName === 'S' ||
                        node.tagName === 'DEL' ||
                        node.tagName === 'STRIKE') &&
                    !containsCloze(node)) {
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++;
                    return; // 跳过子节点
                }

                // 处理内联样式
                if (cloze_styles.includes('text_color') && (
                    // 检查style属性中的文字颜色（更精确的匹配）
                    (style && text_colors.some(color => {
                        const colorLower = color.toLowerCase();
                        const colorUpper = color.toUpperCase();

                        // 使用更兼容的方法：分割CSS属性并逐个检查
                        const cssProperties = style.split(';').map(prop => prop.trim());

                        return cssProperties.some(prop => {
                            // 检查是否是 color 属性（不是 background-color）
                            const colorMatch = prop.match(/^color\s*:\s*(.+)$/i);
                            if (colorMatch) {
                                const propValue = colorMatch[1].trim();
                                return propValue.toLowerCase() === colorLower || propValue.toUpperCase() === colorUpper;
                            }
                            return false;
                        });
                    })) ||
                    // 检查直接的color属性
                    (colorAttr && text_colors.some(color =>
                        color.toLowerCase() === colorAttr.toLowerCase()))
                ) && !containsCloze(node)) {
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++;
                    return; // 跳过子节点
                }

                if (cloze_styles.includes('text_highlight') && (
                    // 检查style属性中的背景颜色（更精确的匹配）
                    (style && highlight_colors.some(highlight => {
                        const highlightLower = highlight.toLowerCase();
                        const highlightUpper = highlight.toUpperCase();

                        // 使用CSS属性分割方法进行精确匹配
                        const cssProperties = style.split(';').map(prop => prop.trim());

                        return cssProperties.some(prop => {
                            // 检查是否是 background-color 属性
                            const bgColorMatch = prop.match(/^background-color\s*:\s*(.+)$/i);
                            if (bgColorMatch) {
                                const propValue = bgColorMatch[1].trim();
                                return propValue.toLowerCase() === highlightLower || propValue.toUpperCase() === highlightUpper;
                            }

                            // 检查是否是 background 属性（简写形式）
                            const bgMatch = prop.match(/^background\s*:\s*(.+)$/i);
                            if (bgMatch) {
                                const propValue = bgMatch[1].trim();
                                return propValue.toLowerCase() === highlightLower || propValue.toUpperCase() === highlightUpper;
                            }

                            return false;
                        });
                    })) ||
                    // 检查直接的bgcolor属性
                    (bgColorAttr && highlight_colors.some(highlight =>
                        highlight.toLowerCase() === bgColorAttr.toLowerCase()))
                ) && !containsCloze(node)) {
                    const originalContent = node.innerHTML;
                    node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                    clozeIndex++;
                    return; // 跳过子节点
                }

                // 检查CSS样式属性（扩展样式检查）
                if (style && !containsCloze(node)) {
                    // 检查font-weight属性（粗体）
                    if (cloze_styles.includes('bold') && (
                        style.includes('font-weight: bold') ||
                        style.includes('font-weight: 700') ||
                        style.includes('font-weight: 800') ||
                        style.includes('font-weight: 900') ||
                        style.includes('font-weight:bold') ||
                        style.includes('font-weight:700') ||
                        style.includes('font-weight:800') ||
                        style.includes('font-weight:900')
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }

                    // 检查font-style属性（斜体）
                    if (cloze_styles.includes('italic') && (
                        style.includes('font-style: italic') ||
                        style.includes('font-style: oblique') ||
                        style.includes('font-style:italic') ||
                        style.includes('font-style:oblique')
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }

                    // 检查text-decoration属性（下划线、删除线等）
                    if (cloze_styles.includes('underline') && (
                        style.includes('text-decoration: underline') ||
                        style.includes('text-decoration-line: underline') ||
                        style.includes('text-decoration:underline') ||
                        style.includes('text-decoration-line:underline')
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }

                    if (cloze_styles.includes('strikeout') && (
                        style.includes('text-decoration: line-through') ||
                        style.includes('text-decoration-line: line-through') ||
                        style.includes('text-decoration:line-through') ||
                        style.includes('text-decoration-line:line-through')
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }

                    // 检查text-decoration的复合值（如 "underline solid red"）
                    if (cloze_styles.includes('underline') && (
                        /text-decoration[^:]*:\s*[^;]*underline/i.test(style)
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }

                    if (cloze_styles.includes('strikeout') && (
                        /text-decoration[^:]*:\s*[^;]*line-through/i.test(style)
                    )) {
                        const originalContent = node.innerHTML;
                        node.innerHTML = `[[c${clozeIndex}::${originalContent}]]`;
                        clozeIndex++;
                        return;
                    }
                }
            }

            // 递归遍历子节点
            for (let child of Array.from(node.childNodes)) {
                traverse(child);
            }
        }

        // 开始遍历
        traverse(doc.body);

        // 返回处理后的HTML字符串
        return doc.body.innerHTML;
    } catch (e) {
        console.error(e);
        return e.toString();
    }
}

function html_convert_cloze_for_markdown_mindmap(htmlContent, cloze_styles, text_colors, highlight_colors) {
    try {
        // 创建DOM解析器
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // 临时存储所有需要挖空的内容，稍后统一编号
        const clozesToProcess = [];

        // 收集所有需要挖空的内容
        function collectClozes(node) {
            if (node.nodeType === 3) { // 文本节点
                const text = node.textContent;
                let resultText = text;
                let modified = false;

                // 处理Markdown特定语法
                if (cloze_styles.includes('==xx==')) {
                    // 匹配 ==text== 格式
                    const highlightRegex = /==(.*?)==/g;
                    if (highlightRegex.test(resultText)) {
                        resultText = resultText.replace(highlightRegex, (match, p1) => {
                            clozesToProcess.push({ content: p1, placeholder: `__CLOZE_${clozesToProcess.length}__` });
                            return `__CLOZE_${clozesToProcess.length - 1}__`;
                        });
                        modified = true;
                    }
                }

                if (cloze_styles.includes('[[c1::xx]]')) {
                    // 将[[c数字::内容]]格式替换为临时占位符
                    const clozeRegex = /\[\[c\d+::(.*?)\]\]/g;
                    if (clozeRegex.test(resultText)) {
                        resultText = resultText.replace(clozeRegex, (match, p1) => {
                            clozesToProcess.push({ content: p1, placeholder: `__CLOZE_${clozesToProcess.length}__` });
                            return `__CLOZE_${clozesToProcess.length - 1}__`;
                        });
                        modified = true;
                    }
                }

                if (cloze_styles.includes('{{c1::xx}}')) {
                    // 将{{c数字::内容}}格式替换为临时占位符
                    const clozeRegex = /\{\{c\d+::(.*?)\}\}/g;
                    if (clozeRegex.test(resultText)) {
                        resultText = resultText.replace(clozeRegex, (match, p1) => {
                            clozesToProcess.push({ content: p1, placeholder: `__CLOZE_${clozesToProcess.length}__` });
                            return `__CLOZE_${clozesToProcess.length - 1}__`;
                        });
                        modified = true;
                    }
                }

                if (cloze_styles.includes('{{xx}}')) {
                    // 处理{{内容}}格式
                    const doubleBraceRegex = /\{\{([^}]*)\}\}/g;
                    if (doubleBraceRegex.test(resultText)) {
                        resultText = resultText.replace(doubleBraceRegex, (match, p1) => {
                            // 忽略已有的挖空格式
                            if (p1.includes('::')) return match;
                            clozesToProcess.push({ content: p1, placeholder: `__CLOZE_${clozesToProcess.length}__` });
                            return `__CLOZE_${clozesToProcess.length - 1}__`;
                        });
                        modified = true;
                    }
                }

                if (cloze_styles.includes('[[xx]]')) {
                    // 处理[[内容]]格式
                    const doubleBracketRegex = /\[\[([^\]]*)\]\]/g;
                    if (doubleBracketRegex.test(resultText)) {
                        resultText = resultText.replace(doubleBracketRegex, (match, p1) => {
                            // 忽略已有的挖空格式
                            if (p1.includes('::')) return match;
                            clozesToProcess.push({ content: p1, placeholder: `__CLOZE_${clozesToProcess.length}__` });
                            return `__CLOZE_${clozesToProcess.length - 1}__`;
                        });
                        modified = true;
                    }
                }

                if (modified) {
                    node.textContent = resultText;
                }
            }

            if (node.nodeType === 1) { // 元素节点
                const style = node.getAttribute('style');
                const colorAttr = node.getAttribute('color');
                const bgColorAttr = node.getAttribute('bgcolor');

                // 检查是否满足挖空条件
                if (cloze_styles.includes('text_color') && (
                    // 检查style属性中的颜色
                    (style && text_colors.some(color => style.includes(`color: ${color.toUpperCase()}`) || style.includes(`color: ${color.toLowerCase()}`))) ||
                    // 检查直接的color属性
                    (colorAttr && text_colors.some(color => color.toLowerCase() === colorAttr.toLowerCase()))
                )) {
                    clozesToProcess.push({ node, content: node.textContent, isElement: true });
                    return; // 跳过子节点
                }

                if (cloze_styles.includes('text_highlight') && (
                    // 检查style属性中的背景颜色
                    (style && highlight_colors.some(highlight => {
                        return (
                            style.includes(`background-color: ${highlight.toUpperCase()}`) ||
                            style.includes(`background-color: ${highlight.toLowerCase()}`) ||
                            style.includes(`background: ${highlight.toUpperCase()}`) ||
                            style.includes(`background: ${highlight.toLowerCase()}`)
                        );
                    })) ||
                    // 检查直接的bgcolor属性
                    (bgColorAttr && highlight_colors.some(highlight => highlight.toLowerCase() === bgColorAttr.toLowerCase()))
                )) {
                    clozesToProcess.push({ node, content: node.textContent, isElement: true });
                    return; // 跳过子节点
                }

                // 处理其他样式
                if (cloze_styles.includes('bold') && (node.tagName === 'B' || node.tagName === 'STRONG')) {
                    clozesToProcess.push({ node, content: node.textContent, isElement: true });
                    return; // 跳过子节点
                }
                if (cloze_styles.includes('italic') && (node.tagName === 'I' || node.tagName === 'EM')) {
                    clozesToProcess.push({ node, content: node.textContent, isElement: true });
                    return; // 跳过子节点
                }
                if (cloze_styles.includes('underline') && node.tagName === 'U') {
                    clozesToProcess.push({ node, content: node.textContent, isElement: true });
                    return; // 跳过子节点
                }
                if (cloze_styles.includes('strikeout') && (node.tagName === 'S' || node.tagName === 'DEL' || node.tagName === 'STRIKE')) {
                    clozesToProcess.push({ node, content: node.textContent, isElement: true });
                    return; // 跳过子节点
                }

                // 递归遍历子节点
                for (let child of Array.from(node.childNodes)) {
                    collectClozes(child);
                }
            } else {
                // 递归遍历非元素节点的子节点
                for (let child of Array.from(node.childNodes)) {
                    collectClozes(child);
                }
            }
        }

        // 应用挖空
        function applyClozes() {
            // 对收集到的所有挖空内容应用编号
            for (let i = 0; i < clozesToProcess.length; i++) {
                const cloze = clozesToProcess[i];
                const clozeIndex = i + 1; // 挖空编号从1开始

                if (cloze.isElement) {
                    // 处理元素节点
                    cloze.node.innerHTML = `<span id="${clozeIndex}" class="cloze activated" onclick='this.classList.toggle("activated")'>${cloze.content}</span>`;
                }
            }

            // 递归替换所有节点中的占位符
            function replacePlaceholders(node) {
                if (node.nodeType === 3) { // 文本节点
                    const text = node.textContent;
                    if (text.includes('__CLOZE_')) {
                        // 使用正则表达式找到所有占位符
                        const regex = /__CLOZE_(\d+)__/g;
                        let result = text;
                        let match;

                        // 重置正则搜索
                        regex.lastIndex = 0;

                        // 查找所有匹配项
                        while ((match = regex.exec(text)) !== null) {
                            const index = parseInt(match[1], 10);
                            if (index < clozesToProcess.length) {
                                const clozeContent = clozesToProcess[index].content;
                                const clozeIndex = index + 1;
                                result = result.replace(
                                    match[0],
                                    `<span id="${clozeIndex}" class="cloze activated" onclick='this.classList.toggle("activated")'>${clozeContent}</span>`
                                );
                            }
                        }

                        // 创建一个临时元素来替换文本节点
                        const temp = document.createElement('div');
                        temp.innerHTML = result;

                        // 将临时元素的子节点插入到原文本节点的位置
                        const parent = node.parentNode;
                        if (parent) {
                            while (temp.firstChild) {
                                parent.insertBefore(temp.firstChild, node);
                            }
                            parent.removeChild(node);
                        }
                    }
                } else if (node.nodeType === 1) { // 元素节点
                    // 处理元素节点的所有子节点
                    Array.from(node.childNodes).forEach(child => {
                        replacePlaceholders(child);
                    });
                }
            }

            // 替换所有占位符
            replacePlaceholders(doc.body);
        }

        // 第一步：收集所有挖空内容
        collectClozes(doc.body);

        // 第二步：应用挖空编号
        applyClozes();

        // 返回处理后的HTML字符串
        return doc.body.innerHTML;
    } catch (e) {
        console.error(e);
        return e.toString();
    }
}

function htmlToText(html) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    // 保存所有图片元素
    const images = Array.from(doc.body.getElementsByTagName('img'));
    const imagePlaceholders = images.map((img, index) => {
        // 用特殊标记替换图片
        const placeholder = `__IMG_${index}__`;
        img.outerHTML = placeholder;
        return {
            placeholder: placeholder,
            imgHTML: img.outerHTML
        };
    });

    // 保存所有表格元素
    const tables = Array.from(doc.body.getElementsByTagName('table'));
    const tablePlaceholders = tables.map((table, index) => {
        // 用特殊标记替换表格
        const placeholder = `__TABLE_${index}__`;
        table.outerHTML = placeholder;
        return {
            placeholder: placeholder,
            tableHTML: table.outerHTML
        };
    });

    // 获取纯文本内容
    let text = doc.body.textContent || doc.body.innerText;

    // 还原图片
    imagePlaceholders.forEach(item => {
        text = text.replace(item.placeholder, `<div class="image-placeholder">${item.imgHTML}</div>`);
    });

    // 还原表格
    tablePlaceholders.forEach(item => {
        text = text.replace(item.placeholder, item.tableHTML);
    });

    return text;
}

function convert_img_to_abs_path(html_content, base_dir) {
    try {
        // 创建DOM解析器
        const parser = new DOMParser();
        const doc = parser.parseFromString(html_content, 'text/html');

        // 获取所有图片元素
        const images = doc.querySelectorAll('img');

        // 处理每个图片的src属性
        images.forEach(img => {
            const src = img.getAttribute('src');

            // 检查是否是相对路径
            if (src && !src.match(/^(https?:\/\/|data:|file:\/\/|\/)/i)) {
                // 确保base_dir以斜杠结尾
                const baseWithSlash = base_dir.endsWith('/') ? base_dir : base_dir + '/';

                // 拼接成绝对路径
                const absolutePath = baseWithSlash + src;

                // 设置新的src属性
                img.setAttribute('src', absolutePath);
            }
        });

        // 返回处理后的HTML
        return doc.body.innerHTML;
    } catch (e) {
        console.error('转换图片路径时出错:', e);
        return html_content; // 出错时返回原始内容
    }
}

/**
 * Extract media files from HTML content with detailed context information
 * @param {string} html - HTML content to parse
 * @param {Array<string>} mediaTypeList - Array of media types to include
 * @returns {string} JSON string containing detailed media extraction results
 */
function extractMediaPathsFromHtml(html, mediaTypeList) {
    try {
        const mediaReferences = [];

        // Check which media types to include
        const includeLocalImage = mediaTypeList.includes('local_image');
        const includeNetworkImage = mediaTypeList.includes('network_image');
        const includeLocalAudio = mediaTypeList.includes('local_audio');
        const includeNetworkAudio = mediaTypeList.includes('network_audio');
        const includeLocalVideo = mediaTypeList.includes('local_video');
        const includeNetworkVideo = mediaTypeList.includes('network_video');

        // Extract images with context
        if (includeLocalImage || includeNetworkImage) {
            extractImagesFromHtmlWithContext(html, mediaReferences, includeNetworkImage, includeLocalImage);
        }

        // Extract audio files if requested
        if (includeLocalAudio || includeNetworkAudio) {
            extractAudioFromHtmlWithContext(html, mediaReferences, includeLocalAudio, includeNetworkAudio);
        }

        // Extract video files if requested
        if (includeLocalVideo || includeNetworkVideo) {
            extractVideoFromHtmlWithContext(html, mediaReferences, includeLocalVideo, includeNetworkVideo);
        }

        // Return both simple file list (for backward compatibility) and detailed context
        const result = {
            files: mediaReferences.map(ref => ref.filePath),
            references: mediaReferences
        };

        return JSON.stringify(result);
    } catch (e) {
        console.error('Error extracting media paths from HTML:', e);
        return JSON.stringify({ files: [], references: [] });
    }
}

/**
 * Extract image files from HTML content with detailed context information
 * @param {string} html - HTML content to parse
 * @param {Array} mediaReferences - Array to store detailed media reference objects
 * @param {boolean} includeNetworkImage - Whether to include network images
 * @param {boolean} includeLocalImage - Whether to include local images (including base64)
 */
function extractImagesFromHtmlWithContext(html, mediaReferences, includeNetworkImage, includeLocalImage = true) {
    try {
        // Use regex to match img tags with src attributes
        const imgRegex = /<img[^>]*src\s*=\s*["']([^"']*)["'][^>]*>/gi;
        let match;

        while ((match = imgRegex.exec(html)) !== null) {
            const src = match[1];
            const fullMatch = match[0];
            const matchStart = match.index;
            const matchEnd = match.index + fullMatch.length;

            if (src) {
                // Check if we should include network images
                if (!includeNetworkImage &&
                    (src.startsWith('http://') || src.startsWith('https://'))) {
                    continue;
                }

                // Handle base64 images (local images)
                if (src.startsWith('data:')) {
                    if (!includeLocalImage) {
                        continue;
                    }
                    // Extract base64 image information
                    const base64Match = src.match(/^data:image\/([^;]+);base64,(.+)$/);
                    if (!base64Match) {
                        continue; // Skip if not a valid base64 image
                    }

                    const imageFormat = base64Match[1];
                    const base64Data = base64Match[2];

                    // Generate a filename for the base64 image
                    const timestamp = Date.now();
                    const fileName = `base64_image_${timestamp}.${imageFormat}`;

                    // Create detailed reference object for base64 image
                    const reference = {
                        filePath: src, // Keep original data URL as filePath
                        fileName: fileName,
                        referenceType: 'img_src_base64',
                        fullMatch: fullMatch,
                        matchStart: matchStart,
                        matchEnd: matchEnd,
                        attributeValue: src,
                        tagName: 'img',
                        attributeName: 'src',
                        base64Data: base64Data,
                        imageFormat: imageFormat
                    };

                    // Check for duplicates based on exact match context
                    const isDuplicate = mediaReferences.some(ref =>
                        ref.filePath === src &&
                        ref.referenceType === 'img_src_base64' &&
                        ref.fullMatch === fullMatch
                    );

                    if (!isDuplicate) {
                        mediaReferences.push(reference);
                    }
                    continue;
                }

                // Check if we should include local images (non-network, non-base64)
                if (!includeLocalImage &&
                    !src.startsWith('http://') &&
                    !src.startsWith('https://') &&
                    !src.startsWith('data:')) {
                    continue;
                }

                // Create detailed reference object
                const reference = {
                    filePath: src,
                    fileName: src.split('/').pop() || src,
                    referenceType: 'img_src',
                    fullMatch: fullMatch,
                    matchStart: matchStart,
                    matchEnd: matchEnd,
                    attributeValue: src,
                    tagName: 'img',
                    attributeName: 'src'
                };

                // Check for duplicates based on exact match context
                const isDuplicate = mediaReferences.some(ref =>
                    ref.filePath === src &&
                    ref.referenceType === 'img_src' &&
                    ref.fullMatch === fullMatch
                );

                if (!isDuplicate) {
                    mediaReferences.push(reference);
                }
            }
        }
    } catch (e) {
        console.error('Error extracting images from HTML:', e);
    }
}

/**
 * Extract audio files from HTML content with detailed context information
 * @param {string} html - HTML content to parse
 * @param {Array} mediaReferences - Array to store detailed media reference objects
 * @param {boolean} includeLocalAudio - Whether to include local audio files
 * @param {boolean} includeNetworkAudio - Whether to include network audio files
 */
function extractAudioFromHtmlWithContext(html, mediaReferences, includeLocalAudio, includeNetworkAudio) {
    try {
        // Match Anki's [sound:filename] syntax
        const ankiSoundRegex = /\[sound:([^\]]+)\]/gi;
        let match;

        while ((match = ankiSoundRegex.exec(html)) !== null) {
            const filename = match[1];
            const fullMatch = match[0];
            const matchStart = match.index;
            const matchEnd = match.index + fullMatch.length;

            if (filename) {
                // Check file extension for audio formats
                const extension = getFileExtension(filename).toLowerCase();
                if (['.mp3', '.wav', '.ogg', '.m4a', '.aac', '.flac'].includes(extension)) {
                    // Check if we should include this audio file based on its type
                    const isNetworkAudio = filename.startsWith('http://') || filename.startsWith('https://');
                    if (isNetworkAudio && !includeNetworkAudio) {
                        continue;
                    }
                    if (!isNetworkAudio && !includeLocalAudio) {
                        continue;
                    }

                    const reference = {
                        filePath: filename,
                        fileName: filename.split('/').pop() || filename,
                        referenceType: 'anki_sound',
                        fullMatch: fullMatch,
                        matchStart: matchStart,
                        matchEnd: matchEnd,
                        attributeValue: filename,
                        tagName: 'anki_sound',
                        attributeName: 'sound'
                    };

                    const isDuplicate = mediaReferences.some(ref =>
                        ref.filePath === filename &&
                        ref.referenceType === 'anki_sound' &&
                        ref.fullMatch === fullMatch
                    );

                    if (!isDuplicate) {
                        mediaReferences.push(reference);
                    }
                }
            }
        }

        // Match audio tags with src attributes
        const audioRegex = /<audio[^>]*src\s*=\s*["']([^"']*)["'][^>]*>/gi;
        while ((match = audioRegex.exec(html)) !== null) {
            const src = match[1];
            const fullMatch = match[0];
            const matchStart = match.index;
            const matchEnd = match.index + fullMatch.length;

            if (src && !src.startsWith('data:')) {
                // Check if we should include this audio file based on its type
                const isNetworkAudio = src.startsWith('http://') || src.startsWith('https://');
                if (isNetworkAudio && !includeNetworkAudio) {
                    continue;
                }
                if (!isNetworkAudio && !includeLocalAudio) {
                    continue;
                }

                const reference = {
                    filePath: src,
                    fileName: src.split('/').pop() || src,
                    referenceType: 'audio_src',
                    fullMatch: fullMatch,
                    matchStart: matchStart,
                    matchEnd: matchEnd,
                    attributeValue: src,
                    tagName: 'audio',
                    attributeName: 'src'
                };

                const isDuplicate = mediaReferences.some(ref =>
                    ref.filePath === src &&
                    ref.referenceType === 'audio_src' &&
                    ref.fullMatch === fullMatch
                );

                if (!isDuplicate) {
                    mediaReferences.push(reference);
                }
            }
        }

        // Match source tags within audio elements
        const sourceRegex = /<source[^>]*src\s*=\s*["']([^"']*)["'][^>]*>/gi;
        while ((match = sourceRegex.exec(html)) !== null) {
            const src = match[1];
            const fullMatch = match[0];
            const matchStart = match.index;
            const matchEnd = match.index + fullMatch.length;

            if (src && !src.startsWith('data:')) {
                // Check file extension for audio formats
                const extension = getFileExtension(src).toLowerCase();
                if (['.mp3', '.wav', '.ogg', '.m4a', '.aac', '.flac'].includes(extension)) {
                    // Check if we should include this audio file based on its type
                    const isNetworkAudio = src.startsWith('http://') || src.startsWith('https://');
                    if (isNetworkAudio && !includeNetworkAudio) {
                        continue;
                    }
                    if (!isNetworkAudio && !includeLocalAudio) {
                        continue;
                    }

                    const reference = {
                        filePath: src,
                        fileName: src.split('/').pop() || src,
                        referenceType: 'source_src_audio',
                        fullMatch: fullMatch,
                        matchStart: matchStart,
                        matchEnd: matchEnd,
                        attributeValue: src,
                        tagName: 'source',
                        attributeName: 'src'
                    };

                    const isDuplicate = mediaReferences.some(ref =>
                        ref.filePath === src &&
                        ref.referenceType === 'source_src_audio' &&
                        ref.fullMatch === fullMatch
                    );

                    if (!isDuplicate) {
                        mediaReferences.push(reference);
                    }
                }
            }
        }
    } catch (e) {
        console.error('Error extracting audio from HTML:', e);
    }
}

/**
 * Extract video files from HTML content with detailed context information
 * @param {string} html - HTML content to parse
 * @param {Array} mediaReferences - Array to store detailed media reference objects
 * @param {boolean} includeLocalVideo - Whether to include local video files
 * @param {boolean} includeNetworkVideo - Whether to include network video files
 */
function extractVideoFromHtmlWithContext(html, mediaReferences, includeLocalVideo, includeNetworkVideo) {
    try {
        // Match Anki's [sound:filename] syntax (also used for video files)
        const ankiSoundRegex = /\[sound:([^\]]+)\]/gi;
        let match;

        while ((match = ankiSoundRegex.exec(html)) !== null) {
            const filename = match[1];
            const fullMatch = match[0];
            const matchStart = match.index;
            const matchEnd = match.index + fullMatch.length;

            if (filename) {
                // Check file extension for video formats
                const extension = getFileExtension(filename).toLowerCase();
                if (['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'].includes(extension)) {
                    // Check if we should include this video file based on its type
                    const isNetworkVideo = filename.startsWith('http://') || filename.startsWith('https://');
                    if (isNetworkVideo && !includeNetworkVideo) {
                        continue;
                    }
                    if (!isNetworkVideo && !includeLocalVideo) {
                        continue;
                    }

                    const reference = {
                        filePath: filename,
                        fileName: filename.split('/').pop() || filename,
                        referenceType: 'anki_sound_video',
                        fullMatch: fullMatch,
                        matchStart: matchStart,
                        matchEnd: matchEnd,
                        attributeValue: filename,
                        tagName: 'anki_sound',
                        attributeName: 'sound'
                    };

                    const isDuplicate = mediaReferences.some(ref =>
                        ref.filePath === filename &&
                        ref.referenceType === 'anki_sound_video' &&
                        ref.fullMatch === fullMatch
                    );

                    if (!isDuplicate) {
                        mediaReferences.push(reference);
                    }
                }
            }
        }

        // Match video tags with src attributes
        const videoRegex = /<video[^>]*src\s*=\s*["']([^"']*)["'][^>]*>/gi;
        while ((match = videoRegex.exec(html)) !== null) {
            const src = match[1];
            const fullMatch = match[0];
            const matchStart = match.index;
            const matchEnd = match.index + fullMatch.length;

            if (src && !src.startsWith('data:')) {
                // Check if we should include this video file based on its type
                const isNetworkVideo = src.startsWith('http://') || src.startsWith('https://');
                if (isNetworkVideo && !includeNetworkVideo) {
                    continue;
                }
                if (!isNetworkVideo && !includeLocalVideo) {
                    continue;
                }

                const reference = {
                    filePath: src,
                    fileName: src.split('/').pop() || src,
                    referenceType: 'video_src',
                    fullMatch: fullMatch,
                    matchStart: matchStart,
                    matchEnd: matchEnd,
                    attributeValue: src,
                    tagName: 'video',
                    attributeName: 'src'
                };

                const isDuplicate = mediaReferences.some(ref =>
                    ref.filePath === src &&
                    ref.referenceType === 'video_src' &&
                    ref.fullMatch === fullMatch
                );

                if (!isDuplicate) {
                    mediaReferences.push(reference);
                }
            }
        }

        // Match source tags within video elements
        const sourceRegex = /<source[^>]*src\s*=\s*["']([^"']*)["'][^>]*>/gi;
        while ((match = sourceRegex.exec(html)) !== null) {
            const src = match[1];
            const fullMatch = match[0];
            const matchStart = match.index;
            const matchEnd = match.index + fullMatch.length;

            if (src && !src.startsWith('data:')) {
                // Check file extension for video formats
                const extension = getFileExtension(src).toLowerCase();
                if (['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'].includes(extension)) {
                    // Check if we should include this video file based on its type
                    const isNetworkVideo = src.startsWith('http://') || src.startsWith('https://');
                    if (isNetworkVideo && !includeNetworkVideo) {
                        continue;
                    }
                    if (!isNetworkVideo && !includeLocalVideo) {
                        continue;
                    }

                    const reference = {
                        filePath: src,
                        fileName: src.split('/').pop() || src,
                        referenceType: 'source_src_video',
                        fullMatch: fullMatch,
                        matchStart: matchStart,
                        matchEnd: matchEnd,
                        attributeValue: src,
                        tagName: 'source',
                        attributeName: 'src'
                    };

                    const isDuplicate = mediaReferences.some(ref =>
                        ref.filePath === src &&
                        ref.referenceType === 'source_src_video' &&
                        ref.fullMatch === fullMatch
                    );

                    if (!isDuplicate) {
                        mediaReferences.push(reference);
                    }
                }
            }
        }
    } catch (e) {
        console.error('Error extracting video from HTML:', e);
    }
}

/**
 * Helper function to get file extension from a path
 * @param {string} filename - File path or name
 * @returns {string} File extension including the dot
 */
function getFileExtension(filename) {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : '';
}



/**
 * Replace media file references in HTML content using detailed context information
 * @param {string} htmlContent - HTML content to process
 * @param {Object} fileMapping - Object mapping original file paths to new URLs
 * @param {Array} mediaReferences - Optional array of detailed media references from extraction
 * @returns {string} Updated HTML content with replaced URLs
 */
function replaceMediaReferencesInHtml(htmlContent, fileMapping, mediaReferences = null) {
    try {
        let updatedContent = htmlContent;

        if (mediaReferences && Array.isArray(mediaReferences)) {
            // Use detailed context information for precise replacement
            return replaceMediaReferencesWithContext(updatedContent, fileMapping, mediaReferences);
        } else {
            // Fallback to pattern-based replacement for backward compatibility
            return replaceMediaReferencesWithPatterns(updatedContent, fileMapping);
        }
    } catch (e) {
        console.error('Error replacing media references in HTML:', e);
        return htmlContent; // Return original content on error
    }
}

/**
 * Replace media references using detailed context information for precise matching
 * @param {string} htmlContent - HTML content to process
 * @param {Object} fileMapping - Object mapping original file paths to new URLs
 * @param {Array} mediaReferences - Array of detailed media references
 * @returns {string} Updated HTML content
 */
function replaceMediaReferencesWithContext(htmlContent, fileMapping, mediaReferences) {
    let updatedContent = htmlContent;
    let offsetAdjustment = 0; // Track position changes due to replacements

    // Create a mapping from filename to full path for easier lookup
    const fileNameToPathMapping = {};
    for (const [fullPath, newUrl] of Object.entries(fileMapping)) {
        const fileName = fullPath.split('/').pop() || fullPath;
        fileNameToPathMapping[fileName] = { fullPath, newUrl };
    }

    // Sort references by position (descending) to maintain correct positions during replacement
    const sortedReferences = mediaReferences
        .filter(ref => {
            // Try exact match first, then filename match
            return fileMapping.hasOwnProperty(ref.filePath) ||
                   fileNameToPathMapping.hasOwnProperty(ref.fileName || ref.filePath.split('/').pop());
        })
        .sort((a, b) => b.matchStart - a.matchStart);

    for (const ref of sortedReferences) {
        // Try exact path match first, then filename match
        let newUrl = fileMapping[ref.filePath];
        if (!newUrl) {
            const fileName = ref.fileName || ref.filePath.split('/').pop();
            const mapping = fileNameToPathMapping[fileName];
            newUrl = mapping ? mapping.newUrl : null;
        }
        if (!newUrl) continue;

        const adjustedStart = ref.matchStart + offsetAdjustment;
        const adjustedEnd = ref.matchEnd + offsetAdjustment;

        // Verify the match is still valid at the adjusted position
        const currentMatch = updatedContent.substring(adjustedStart, adjustedEnd);
        if (currentMatch !== ref.fullMatch) {
            // Position has shifted, try to find the match nearby
            const searchStart = Math.max(0, adjustedStart - 50);
            const searchEnd = Math.min(updatedContent.length, adjustedEnd + 50);
            const searchArea = updatedContent.substring(searchStart, searchEnd);
            const matchIndex = searchArea.indexOf(ref.fullMatch);

            if (matchIndex === -1) {
                console.warn('Could not find expected match for replacement:', ref.fullMatch);
                continue;
            }

            // Update positions
            const newStart = searchStart + matchIndex;
            const newEnd = newStart + ref.fullMatch.length;

            // Create replacement based on reference type
            const replacement = createReplacementForReference(ref, newUrl);

            // Perform replacement
            updatedContent = updatedContent.substring(0, newStart) +
                           replacement +
                           updatedContent.substring(newEnd);

            // Update offset adjustment
            offsetAdjustment += replacement.length - ref.fullMatch.length;
        } else {
            // Direct replacement at expected position
            const replacement = createReplacementForReference(ref, newUrl);

            updatedContent = updatedContent.substring(0, adjustedStart) +
                           replacement +
                           updatedContent.substring(adjustedEnd);

            // Update offset adjustment
            offsetAdjustment += replacement.length - ref.fullMatch.length;
        }
    }

    return updatedContent;
}

/**
 * Create appropriate replacement string based on reference type
 * @param {Object} ref - Media reference object
 * @param {string} newUrl - New URL to replace with
 * @returns {string} Replacement string
 */
function createReplacementForReference(ref, newUrl) {
    switch (ref.referenceType) {
        case 'img_src':
            return ref.fullMatch.replace(
                new RegExp(`src\\s*=\\s*["']${escapeRegExp(ref.attributeValue)}["']`, 'i'),
                `src="${newUrl}"`
            );
        case 'img_src_base64':
            // For base64 images, replace the entire data URL with the new URL
            return ref.fullMatch.replace(
                new RegExp(`src\\s*=\\s*["']${escapeRegExp(ref.attributeValue)}["']`, 'i'),
                `src="${newUrl}"`
            );
        case 'audio_src':
            return ref.fullMatch.replace(
                new RegExp(`src\\s*=\\s*["']${escapeRegExp(ref.attributeValue)}["']`, 'i'),
                `src="${newUrl}"`
            );
        case 'video_src':
            return ref.fullMatch.replace(
                new RegExp(`src\\s*=\\s*["']${escapeRegExp(ref.attributeValue)}["']`, 'i'),
                `src="${newUrl}"`
            );
        case 'source_src_audio':
        case 'source_src_video':
            return ref.fullMatch.replace(
                new RegExp(`src\\s*=\\s*["']${escapeRegExp(ref.attributeValue)}["']`, 'i'),
                `src="${newUrl}"`
            );
        case 'anki_sound':
        case 'anki_sound_video':
            return `[sound:${newUrl}]`;
        default:
            // Generic attribute replacement
            return ref.fullMatch.replace(
                new RegExp(`${ref.attributeName}\\s*=\\s*["']${escapeRegExp(ref.attributeValue)}["']`, 'i'),
                `${ref.attributeName}="${newUrl}"`
            );
    }
}

/**
 * Fallback pattern-based replacement for backward compatibility
 * @param {string} htmlContent - HTML content to process
 * @param {Object} fileMapping - Object mapping original file paths to new URLs
 * @returns {string} Updated HTML content
 */
function replaceMediaReferencesWithPatterns(htmlContent, fileMapping) {
    let updatedContent = htmlContent;

    // Process each file mapping
    for (const [originalPath, newUrl] of Object.entries(fileMapping)) {
        // Extract filename from the original path
        const fileName = originalPath.split('/').pop() || originalPath;

        // Replace img tag src attributes - match both exact filename and filename at end of path
        const imgRegex = new RegExp(
            `<img([^>]*)\\s+src\\s*=\\s*["']([^"']*(?:^|/)${escapeRegExp(fileName)})["']([^>]*)>`,
            'gi'
        );
        updatedContent = updatedContent.replace(imgRegex, `<img$1 src="${newUrl}"$3>`);

        // Also try exact filename match for img tags
        const imgExactRegex = new RegExp(
            `<img([^>]*)\\s+src\\s*=\\s*["']${escapeRegExp(fileName)}["']([^>]*)>`,
            'gi'
        );
        updatedContent = updatedContent.replace(imgExactRegex, `<img$1 src="${newUrl}"$2>`);

        // Replace Anki [sound:filename] syntax
        const ankiSoundRegex = new RegExp(`\\[sound:${escapeRegExp(fileName)}\\]`, 'gi');
        updatedContent = updatedContent.replace(ankiSoundRegex, `[sound:${newUrl}]`);

        // Replace audio tag src attributes
        const audioRegex = new RegExp(
            `<audio([^>]*)\\s+src\\s*=\\s*["']([^"']*(?:^|/)${escapeRegExp(fileName)})["']([^>]*)>`,
            'gi'
        );
        updatedContent = updatedContent.replace(audioRegex, `<audio$1 src="${newUrl}"$3>`);

        // Also try exact filename match for audio tags
        const audioExactRegex = new RegExp(
            `<audio([^>]*)\\s+src\\s*=\\s*["']${escapeRegExp(fileName)}["']([^>]*)>`,
            'gi'
        );
        updatedContent = updatedContent.replace(audioExactRegex, `<audio$1 src="${newUrl}"$2>`);

        // Replace video tag src attributes
        const videoRegex = new RegExp(
            `<video([^>]*)\\s+src\\s*=\\s*["']([^"']*(?:^|/)${escapeRegExp(fileName)})["']([^>]*)>`,
            'gi'
        );
        updatedContent = updatedContent.replace(videoRegex, `<video$1 src="${newUrl}"$3>`);

        // Also try exact filename match for video tags
        const videoExactRegex = new RegExp(
            `<video([^>]*)\\s+src\\s*=\\s*["']${escapeRegExp(fileName)}["']([^>]*)>`,
            'gi'
        );
        updatedContent = updatedContent.replace(videoExactRegex, `<video$1 src="${newUrl}"$2>`);

        // Replace source tag src attributes (for audio/video elements)
        const sourceRegex = new RegExp(
            `<source([^>]*)\\s+src\\s*=\\s*["']([^"']*(?:^|/)${escapeRegExp(fileName)})["']([^>]*)>`,
            'gi'
        );
        updatedContent = updatedContent.replace(sourceRegex, `<source$1 src="${newUrl}"$3>`);

        // Also try exact filename match for source tags
        const sourceExactRegex = new RegExp(
            `<source([^>]*)\\s+src\\s*=\\s*["']${escapeRegExp(fileName)}["']([^>]*)>`,
            'gi'
        );
        updatedContent = updatedContent.replace(sourceExactRegex, `<source$1 src="${newUrl}"$2>`);

        // Replace any href attributes that might reference media files
        const hrefRegex = new RegExp(
            `<a([^>]*)\\s+href\\s*=\\s*["']([^"']*(?:^|/)${escapeRegExp(fileName)})["']([^>]*)>`,
            'gi'
        );
        updatedContent = updatedContent.replace(hrefRegex, `<a$1 href="${newUrl}"$3>`);

        // Also try exact filename match for href attributes
        const hrefExactRegex = new RegExp(
            `<a([^>]*)\\s+href\\s*=\\s*["']${escapeRegExp(fileName)}["']([^>]*)>`,
            'gi'
        );
        updatedContent = updatedContent.replace(hrefExactRegex, `<a$1 href="${newUrl}"$2>`);
    }

    return updatedContent;
}

/**
 * Helper function to escape special regex characters
 * @param {string} string - String to escape
 * @returns {string} Escaped string
 */
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}



window.splitHtml = splitHtml;
window.parseHtmlToCards = parseHtmlToCards;
window.html_convert_cloze = html_convert_cloze;
window.html_convert_cloze_for_mubu = html_convert_cloze_for_mubu;
window.html_convert_cloze_for_markdown_mindmap = html_convert_cloze_for_markdown_mindmap;
window.parseHtmlToParts = parseHtmlToParts;
window.htmlToText = htmlToText;
window.convert_img_to_abs_path = convert_img_to_abs_path;
window.extractMediaPathsFromHtml = extractMediaPathsFromHtml;
window.replaceMediaReferencesInHtml = replaceMediaReferencesInHtml;