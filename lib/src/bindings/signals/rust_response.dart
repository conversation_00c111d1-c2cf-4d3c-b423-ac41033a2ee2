// ignore_for_file: type=lint, type=warning
part of 'signals.dart';


@immutable
class RustResponse {
  /// An async broadcast stream that listens for signals from Rust.
  /// It supports multiple subscriptions.
  /// Make sure to cancel the subscription when it's no longer needed,
  /// such as when a widget is disposed.
  static final rustSignalStream =
      _rustResponseStreamController.stream.asBroadcastStream();
        
  /// The latest signal value received from Rust.
  /// This is updated every time a new signal is received.
  /// It can be null if no signals have been received yet.
  static RustSignalPack<RustResponse>? latestRustSignal = null;

  const RustResponse({
    required this.interactionId,
    required this.status,
    required this.message,
    required this.data,
  });

  static RustResponse deserialize(BinaryDeserializer deserializer) {
    deserializer.increaseContainerDepth();
    final instance = RustResponse(
      interactionId: deserializer.deserializeString(),
      status: deserializer.deserializeString(),
      message: deserializer.deserializeString(),
      data: deserializer.deserializeString(),
    );
    deserializer.decreaseContainerDepth();
    return instance;
  }

  static RustResponse bincodeDeserialize(Uint8List input) {
    final deserializer = BincodeDeserializer(input);
    final value = RustResponse.deserialize(deserializer);
    if (deserializer.offset < input.length) {
      throw Exception('Some input bytes were not read');
    }
    return value;
  }

  final String interactionId;
  final String status;
  final String message;
  final String data;

  RustResponse copyWith({
    String? interactionId,
    String? status,
    String? message,
    String? data,
  }) {
    return RustResponse(
      interactionId: interactionId ?? this.interactionId,
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  void serialize(BinarySerializer serializer) {
    serializer.increaseContainerDepth();
    serializer.serializeString(interactionId);
    serializer.serializeString(status);
    serializer.serializeString(message);
    serializer.serializeString(data);
    serializer.decreaseContainerDepth();
  }

  Uint8List bincodeSerialize() {
      final serializer = BincodeSerializer();
      serialize(serializer);
      return serializer.bytes;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;

    return other is RustResponse
      && interactionId == other.interactionId
      && status == other.status
      && message == other.message
      && data == other.data;
  }

  @override
  int get hashCode => Object.hash(
        interactionId,
        status,
        message,
        data,
      );

  @override
  String toString() {
    String? fullString;

    assert(() {
      fullString = '$runtimeType('
        'interactionId: $interactionId, '
        'status: $status, '
        'message: $message, '
        'data: $data'
        ')';
      return true;
    }());

    return fullString ?? 'RustResponse';
  }
}

final _rustResponseStreamController =
    StreamController<RustSignalPack<RustResponse>>();
