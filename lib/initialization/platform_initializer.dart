import 'dart:io';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:launch_at_startup/launch_at_startup.dart';
import 'package:local_notifier/local_notifier.dart';
import 'package:window_size/window_size.dart';
import 'package:anki_guru/controllers/common.dart';

/// Platform-specific initialization module
/// Handles desktop-specific setup like notifications, startup, and window configuration
class PlatformInitializer {
  /// Initialize platform-specific features
  /// This should be called after controllers are initialized
  static Future<void> initialize(SettingController settingController) async {
    logger.i('Initializing platform-specific features...');
    
    // Only initialize desktop features on desktop platforms
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      await _initializeDesktopFeatures(settingController);
    }
    
    logger.i('Platform-specific initialization completed');
  }
  
  /// Initialize desktop-specific features
  static Future<void> _initializeDesktopFeatures(SettingController settingController) async {
    final packageInfo = await PackageInfo.fromPlatform();
    
    // Setup launch at startup
    await _setupLaunchAtStartup(packageInfo);
    
    // Setup local notifications
    await _setupLocalNotifications();
    
    // Set window title
    _setWindowTitle(settingController, packageInfo);
  }
  
  /// Setup launch at startup functionality
  static Future<void> _setupLaunchAtStartup(PackageInfo packageInfo) async {
    try {
      launchAtStartup.setup(
        appName: packageInfo.appName,
        appPath: Platform.resolvedExecutable,
        // Set packageName parameter to support MSIX.
        packageName: 'top.kevin2li.guru',
      );
      logger.i('Launch at startup configured');
    } catch (e) {
      logger.e('Failed to setup launch at startup: $e');
    }
  }
  
  /// Setup local notifications
  static Future<void> _setupLocalNotifications() async {
    try {
      await localNotifier.setup(
        appName: 'PDF Guru Anki',
        // The parameter shortcutPolicy only works on Windows
        shortcutPolicy: ShortcutPolicy.requireCreate,
      );
      logger.i('Local notifications configured');
    } catch (e) {
      logger.e('Failed to setup local notifications: $e');
    }
  }
  
  /// Set application window title
  static void _setWindowTitle(SettingController settingController, PackageInfo packageInfo) {
    try {
      if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        setWindowTitle("PDF Guru Anki - v${settingController.version}");
        logger.i('Window title set');
      }
    } catch (e) {
      logger.e('Failed to set window title: $e');
    }
  }
}
