import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:app_links/app_links.dart';
import 'package:share_handler/share_handler.dart';
import 'package:win32_registry/win32_registry.dart';
import 'package:rinf/rinf.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/common/paywall_controller.dart';
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';

/// App lifecycle management module
/// Handles app lifecycle events, deep links, shared media, and service management
class AppLifecycleManager {
  static bool _hasCheckedLicense = false;
  static late AppLinks _appLinks;
  static StreamSubscription<Uri>? _linkSubscription;
  static late AppLifecycleListener _listener;
  
  /// Initialize app lifecycle management
  /// This should be called from MyApp's initState
  static void initialize() {
    logger.i('Initializing app lifecycle management...');
    
    // Setup app lifecycle listener for cleanup
    _setupAppLifecycleListener();
    
    logger.i('App lifecycle management initialized');
  }
  
  /// Setup app lifecycle listener for proper cleanup
  static void _setupAppLifecycleListener() {
    _listener = AppLifecycleListener(
      onExitRequested: () async {
        logger.i('应用退出请求，正在终止所有服务...');
        try {
          await _performCleanupOnExit();
          logger.i('所有服务已成功终止');
          return AppExitResponse.exit;
        } catch (e) {
          logger.e('应用退出时终止服务出错: $e');
          await _emergencyCleanup();
          return AppExitResponse.exit;
        }
      },
    );
  }
  
  /// Perform cleanup operations on app exit
  static Future<void> _performCleanupOnExit() async {
    // Terminate all services
    await terminateAllServices();
    
    // Platform-specific cleanup
    if (Platform.isWindows) {
      await _performWindowsCleanup();
    }
    
    // Close logging system
    await LoggerService.close().catchError((e) {
      logger.e('关闭日志系统时出错: $e');
    });
    
    // Release Rust resources
    safelyFinalizeRust();
    
    // Give logging system time to complete writes
    await Future.delayed(const Duration(milliseconds: 300));
  }
  
  /// Perform Windows-specific cleanup
  static Future<void> _performWindowsCleanup() async {
    try {
      final settingController = Get.find<SettingController>();
      final ports = [settingController.pyServerPort.value];
      
      for (final port in ports) {
        try {
          final result = await Process.run(
            'cmd.exe',
            [
              '/c',
              'for /f "tokens=5" %a in (\'netstat -ano ^| findstr :$port\') do taskkill /F /PID %a'
            ],
            runInShell: true,
          );
          
          if (result.exitCode == 0 && result.stdout.isNotEmpty) {
            logger.i('端口 $port 已被强制释放: ${result.stdout}');
          }
        } catch (e) {
          logger.e('尝试释放端口 $port 时出错: $e');
        }
      }
    } catch (e) {
      logger.e('Windows平台终止进程时出错: $e');
    }
  }
  
  /// Emergency cleanup when normal cleanup fails
  static Future<void> _emergencyCleanup() async {
    try {
      await LoggerService.close().catchError((_) {});
      safelyFinalizeRust();
      await Future.delayed(const Duration(milliseconds: 300));
    } catch (e) {
      // Silent failure in emergency cleanup
    }
  }
  
  /// Initialize post-frame callback operations
  /// This should be called from MyApp's initState after addPostFrameCallback
  static Future<void> initializePostFrameOperations({
    SharedMedia? initialMedia,
    required LicenseController licenseController,
    required VideoNoteController videoNoteController,
    required ImageCardController imageCardController,
    required SettingController settingController,
  }) async {
    if (!_hasCheckedLicense) {
      logger.i('Initializing post-frame operations...');
      
      // Initialize license controller
      await _initializeLicenseController(licenseController);
      
      // Process initial shared media if any
      if (initialMedia != null) {
        await _processSharedMedia(initialMedia, imageCardController);
      }
      
      // Initialize various services
      await _initializeServices();
      
      // Auto-start Anki if configured
      await _autoStartAnki(settingController);
      
      _hasCheckedLicense = true;
      logger.i('Post-frame operations completed');
    }
  }

  /// Initialize license controller based on platform
  static Future<void> _initializeLicenseController(LicenseController licenseController) async {
    if (!Platform.isIOS) {
      await licenseController.checkLicenseQuick();
      await licenseController.updateLicenseInfo();
      if (licenseController.isActivated.value) {
        await licenseController.checkLicenseRemote();
      }
    } else {
      // iOS platform - initialize PaywallController from local subscription info
      Future.microtask(() {
        Get.put(PaywallController());
        logger.i('iOS平台：已从本地存储加载内购信息，激活状态: ${licenseController.isActivated.value}');
      });
    }
  }

  /// Process shared media from external sources
  static Future<void> _processSharedMedia(SharedMedia sharedMedia, ImageCardController imageCardController) async {
    if (sharedMedia.attachments != null && sharedMedia.attachments!.isNotEmpty) {
      var haveImage = false;
      for (var attachment in sharedMedia.attachments!) {
        if (attachment?.type == SharedAttachmentType.image) {
          haveImage = true;
          final imageBytes = await File(attachment?.path ?? "").readAsBytes();
          imageCardController.imageCards.add(ImageCard(
            imageData: imageBytes,
            noteController: TextEditingController(),
            tagController: TextEditingController(),
            clozeController: TextEditingController(),
          ));
        }
      }
      if (haveImage) {
        await Get.toNamed('/image_card');
      }
    }
  }

  /// Initialize various services
  static Future<void> _initializeServices() async {
    initDeepLinks();
    initPlatformState();
    initApiServer();
    initHttpServer();
    if (Platform.isWindows) {
      registerURLScheme("guru2");
    }
  }

  /// Auto-start Anki if configured
  static Future<void> _autoStartAnki(SettingController settingController) async {
    if (settingController.autoStartAnki.value) {
      final ankiPath = settingController.ankiPath.value;
      if (ankiPath.isNotEmpty) {
        await Process.run(ankiPath, [], runInShell: true);
      }
    }
  }

  /// Initialize deep links
  static Future<void> initDeepLinks() async {
    _appLinks = AppLinks();

    // Handle initial link if the app was launched with a link
    final uri = await _appLinks.getInitialLink();
    logger.d('Initial link: $uri');
    if (uri != null) {
      processLink(uri);
    }

    // Handle links while app is running
    _linkSubscription = _appLinks.uriLinkStream.listen((uri) async {
      processLink(uri);
    });
  }

  /// Initialize platform state for mobile platforms
  static Future<void> initPlatformState() async {
    if (Platform.isIOS || Platform.isAndroid) {
      final handler = ShareHandlerPlatform.instance;
      await handler.getInitialSharedMedia();

      handler.sharedMediaStream.listen((SharedMedia media) async {
        logger.d('Shared media: $media');
        final imageCardController = Get.find<ImageCardController>();
        await _processSharedMedia(media, imageCardController);
      });
    }
  }

  /// Register URL scheme for Windows platform
  static Future<void> registerURLScheme(String scheme) async {
    String appPath = Platform.resolvedExecutable;
    String seekPath = PathUtils.join([PathUtils(appPath).parent, 'seek.exe']);
    String protocolRegKey = 'Software\\Classes\\$scheme';
    RegistryValue protocolRegValue = const RegistryValue.string(
      'URL Protocol',
      '',
    );
    String protocolCmdRegKey = 'shell\\open\\command';
    RegistryValue protocolCmdRegValue = RegistryValue.string(
      '',
      '"$seekPath" "%1"',
    );

    final regKey = Registry.currentUser.createKey(protocolRegKey);
    regKey.createValue(protocolRegValue);
    regKey.createKey(protocolCmdRegKey).createValue(protocolCmdRegValue);
  }

  /// Dispose lifecycle manager resources
  static void dispose() {
    _linkSubscription?.cancel();
    _listener.dispose();
  }

  /// Safe wrapper for finalizing Rust resources
  static void safelyFinalizeRust() {
    try {
      finalizeRust();
      logger.i('Rust资源已安全释放');
    } catch (e, stack) {
      logger.e('释放Rust资源时出错: $e\n$stack');
    }
  }
}
