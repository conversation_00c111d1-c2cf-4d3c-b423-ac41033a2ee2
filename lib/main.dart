import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_handler/share_handler.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:toastification/toastification.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'routes/app_pages.dart';
import 'theme/app_theme.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';
import 'package:anki_guru/translations/app_translations.dart';

// Import initialization modules
import 'initialization/core_initializer.dart';
import 'initialization/controller_initializer.dart';
import 'initialization/localization_initializer.dart';
import 'initialization/rust_signal_handler.dart';
import 'initialization/platform_initializer.dart';
import 'initialization/app_lifecycle_manager.dart';



void main(List<String> args) async {
  // Step 1: Initialize core system components (REQUIRED for logger)
  await CoreInitializer.initialize();

  // Step 2: Initialize localization system (moved here to avoid SettingController dependency)
  await LocalizationInitializer.initialize();

  // Step 3: Initialize all GetX controllers
  final controllerRefs = ControllerInitializer.initializeControllers();

  // Step 4: Setup Rust signal handlers
  RustSignalHandler.initializeSignalHandlers(
    controllerRefs.messageController,
    controllerRefs.progressController,
    controllerRefs.webviewController,
    controllerRefs.ankiConnectController,
  );

  // Step 5: Initialize platform-specific features
  await PlatformInitializer.initialize(controllerRefs.settingController);

  // Step 6: Launch the application - TEMPORARY DEBUG VERSION
  const initialRoute = "/main";
  logger.i('initialRoute: $initialRoute');
  runApp(const MyApp(
    initialRoute: initialRoute,
  ));
  // runApp(const DebugApp());
}

class MyApp extends StatefulWidget {
  final String initialRoute;
  final SharedMedia? initialMedia;

  const MyApp({
    super.key,
    this.initialRoute = '/main',
    this.initialMedia,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final licenseController = Get.put(LicenseController());
  final settingController = Get.find<SettingController>();
  final videoNoteController = Get.find<VideoNoteController>();
  final imageCardController = Get.find<ImageCardController>();
  final localeController = Get.find<LocaleController>();

  @override
  void initState() {
    super.initState();

    // Initialize app lifecycle management
    AppLifecycleManager.initialize();

    // Ensure language is correctly applied
    final savedLanguage = settingController.language.value;
    if (savedLanguage.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        localeController.changeLocale(savedLanguage);
        // Ensure GetX updates UI
        Get.updateLocale(localeController.getLocaleForLanguage(savedLanguage));
      });
    }

    // Initialize post-frame operations
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await AppLifecycleManager.initializePostFrameOperations(
        initialMedia: widget.initialMedia,
        licenseController: licenseController,
        videoNoteController: videoNoteController,
        imageCardController: imageCardController,
        settingController: settingController,
      );
    });
  }

  @override
  void dispose() {
    AppLifecycleManager.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      String targetTheme = settingController.theme.value;
      if (settingController.theme.value == 'system') {
        targetTheme = settingController.getSystemTheme();
      }
      var themeMode = ThemeMode.light;
      if (targetTheme == 'dark') {
        themeMode = ThemeMode.dark;
      }

      // 调试当前语言设置
      final currentLocale = Get.locale;
      final localeController = Get.find<LocaleController>();
      logger.d('Current locale in MyApp: $currentLocale');
      logger.d('Current language from controller: ${settingController.language.value}');
      logger.d('Current locale controller language: ${localeController.currentLanguage.value}');

      return ShadApp.custom(
          theme: ShadThemeData(
            brightness: Brightness.light,
            colorScheme: const ShadSlateColorScheme.light(),
            // colorScheme: const ShadBlueColorScheme.light(),
            // colorScheme: ShadColorScheme.fromName('blue'),
          ),
          darkTheme: ShadThemeData(
            brightness: Brightness.dark,
            colorScheme: const ShadSlateColorScheme.dark(),
          ),
          themeMode: themeMode,
          appBuilder: (context) {
            ThemeData theme = convertToMaterialTheme(ShadTheme.of(context));
            theme = theme.copyWith(
              iconTheme: theme.iconTheme.copyWith(size: 18),
            );

            return GetMaterialApp(
              title: 'PDF Guru Anki',
              theme: theme,
              // darkTheme: theme,
              locale: const Locale('zh', 'CN'),
              translations: AppTranslations(),
              fallbackLocale: const Locale('en', 'US'),
              translationsKeys: AppTranslations().keys,
              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
                FlutterQuillLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('en', 'US'),
                Locale('zh', 'CN'),
                Locale('zh', 'TW'),
                Locale('ja', 'JP'),
              ],
              themeMode: themeMode,
              initialRoute: widget.initialRoute,
              getPages: AppPages.routes,
              builder: (context, child) {
                return ToastificationWrapper(
                  child: ShadToaster(child: child!),
                );
              },
            );
          });
    });
  }
}

// Simple debug app to isolate splash screen issues
class DebugApp extends StatelessWidget {
  const DebugApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Debug App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const DebugPage(),
    );
  }
}

class DebugPage extends StatefulWidget {
  const DebugPage({super.key});

  @override
  State<DebugPage> createState() => _DebugPageState();
}

class _DebugPageState extends State<DebugPage> {
  int _counter = 0;
  String _statusMessage = 'App is running successfully!';
  bool _isLoading = false;

  void _incrementCounter() {
    setState(() {
      _counter++;
      _statusMessage = 'Button pressed $_counter times';
    });
  }

  void _simulateAsyncOperation() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Running async operation...';
    });

    // Simulate some async work
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
      _statusMessage = 'Async operation completed successfully!';
    });
  }

  void _navigateToSecondPage() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SecondDebugPage()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('Debug Page'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 64,
              ),
              const SizedBox(height: 20),
              const Text(
                'Flutter App Debug Mode',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                _statusMessage,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Text(
                'Counter: $_counter',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 30),
              if (_isLoading)
                const CircularProgressIndicator()
              else
                Column(
                  children: [
                    ElevatedButton(
                      onPressed: _incrementCounter,
                      child: const Text('Increment Counter'),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: _simulateAsyncOperation,
                      child: const Text('Test Async Operation'),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: _navigateToSecondPage,
                      child: const Text('Navigate to Second Page'),
                    ),
                  ],
                ),
              const SizedBox(height: 30),
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Debug Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10),
                      Text('✓ Flutter framework loaded'),
                      Text('✓ Material Design components working'),
                      Text('✓ State management functional'),
                      Text('✓ Navigation system ready'),
                      Text('✓ Async operations supported'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ),
    );
  }
}

class SecondDebugPage extends StatelessWidget {
  const SecondDebugPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Second Debug Page'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.navigation,
                color: Colors.blue,
                size: 64,
              ),
              SizedBox(height: 20),
              Text(
                'Navigation Test Successful!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20),
              Text(
                'This page confirms that navigation between screens is working properly.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 30),
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Navigation Test Results',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10),
                      Text('✓ Route navigation working'),
                      Text('✓ AppBar back button functional'),
                      Text('✓ Page transitions smooth'),
                      Text('✓ State preservation working'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}


