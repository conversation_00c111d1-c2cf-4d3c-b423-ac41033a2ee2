import 'package:get/get.dart';
import 'package:anki_guru/pages/home/<USER>';
import 'package:anki_guru/pages/anki/llm_card/index.dart';
import 'package:anki_guru/pages/anki/text_card/index.dart';
import 'package:anki_guru/pages/anki/pdf_card/index.dart';
import 'package:anki_guru/pages/anki/image_card/image_card.dart';
import 'package:anki_guru/pages/anki/image_card/components/config.dart';
import 'package:anki_guru/pages/anki/image_card/editor.dart';
import 'package:anki_guru/pages/pdf_viewer/pdf_viewer.dart';
import 'package:anki_guru/pages/video_notes/index.dart';
import 'package:anki_guru/pages/video_notes/config.dart';
import 'package:anki_guru/pages/video_notes/ocr.dart';
import 'package:anki_guru/pages/video_notes/editor.dart';
import 'package:anki_guru/pages/video_notes/cloze.dart';
import 'package:anki_guru/pages/video_notes/bili_login.dart';
import 'package:anki_guru/pages/home/<USER>/about_page.dart';
import 'package:anki_guru/pages/home/<USER>/activate_page.dart';
import 'package:anki_guru/pages/home/<USER>/user_center.dart';
import 'package:anki_guru/pages/home/<USER>/anki_config.dart';
import 'package:anki_guru/controllers/common/license_controller.dart';
import 'package:flutter/material.dart';
import 'package:anki_guru/pages/toolbox/rotate.dart';
import 'package:anki_guru/pages/toolbox/extract.dart';
import 'package:anki_guru/pages/toolbox/merge.dart';
import 'package:anki_guru/pages/toolbox/ocr.dart';
import 'package:anki_guru/pages/anki/sync/sync.dart';
import 'package:anki_guru/pages/anki/sync/config.dart';
import 'package:anki_guru/pages/anki/tts/index.dart';
import 'package:anki_guru/pages/anki/mindmap/index.dart';
import 'package:anki_guru/pages/anki/ocr/card_ocr/index.dart';
import 'package:anki_guru/pages/anki/excel_card/index.dart';
import 'package:anki_guru/pages/anki/media_card/index.dart';
import 'package:anki_guru/pages/anki/wereader_card/index.dart';
import 'package:anki_guru/pages/anki/mubu_card/index.dart';
import 'package:anki_guru/pages/anki/vocab_card/index.dart';
import 'package:anki_guru/pages/anki/word_card/index.dart';
import 'package:anki_guru/pages/anki/flash_notes/index.dart';
import 'package:anki_guru/pages/anki/card_media_manager/index.dart';
import 'package:anki_guru/pages/toolbox/reorder.dart';
import 'package:anki_guru/pages/toolbox/bookmark.dart';
import 'package:anki_guru/pages/toolbox/split.dart';
import 'package:anki_guru/pages/toolbox/delete.dart';
import 'package:anki_guru/pages/toolbox/scale.dart';
import 'package:anki_guru/pages/toolbox/encrypt.dart';
import 'package:anki_guru/pages/toolbox/crop.dart';
import 'package:anki_guru/pages/toolbox/insert.dart';
import 'package:anki_guru/pages/toolbox/watermark.dart';
import 'package:anki_guru/pages/toolbox/permission.dart';
import 'package:anki_guru/pages/toolbox/expand.dart';
import 'package:anki_guru/pages/toolbox/cut.dart';
import 'package:anki_guru/pages/toolbox/combine.dart';
import 'package:anki_guru/pages/toolbox/background.dart';
import 'package:anki_guru/pages/toolbox/meta.dart';
import 'package:anki_guru/pages/toolbox/page_number.dart';
import 'package:anki_guru/pages/toolbox/convert/pdf2img.dart';
import 'package:anki_guru/pages/toolbox/convert/img2pdf.dart';
import 'package:anki_guru/pages/toolbox/convert/pdf2img_pdf.dart';
import 'package:anki_guru/pages/toolbox/convert/epub2pdf.dart';
import 'package:anki_guru/pages/toolbox/convert/mobi2pdf.dart';
import 'package:anki_guru/pages/toolbox/convert/docx2pdf.dart';
import 'package:anki_guru/pages/toolbox/convert/pdf2docx.dart';
import 'package:anki_guru/pages/toolbox/convert/ofd2pdf.dart';
import 'package:anki_guru/pages/toolbox/convert/docx2html.dart';
import 'package:anki_guru/pages/toolbox/convert/md2html.dart';
import 'package:anki_guru/pages/toolbox/convert/html2pdf.dart';
import 'package:anki_guru/pages/toolbox/convert/md2pdf.dart';
import 'package:anki_guru/pages/toolbox/annotation.dart';
import 'package:anki_guru/pages/anki/pdf_note/index.dart';
import 'package:anki_guru/pages/anki/deck_manager/index.dart';
import 'package:anki_guru/pages/anki/markdown_card/index.dart';
import 'package:anki_guru/pages/home/<USER>/paywall.dart';
import 'package:anki_guru/pages/anki/ocr/image_ocr/index.dart';
import 'dart:io';

class AppPages {
  static final routes = [
    GetPage(
      name: '/main',
      page: () => const MainPage(),
      transition: Transition.cupertino,
      transitionDuration: const Duration(milliseconds: 500),
    ),
    // Anki制卡
    /// 文本制卡
    GetPage(
      name: '/text_card',
      page: () => const TextCard(),
      transition: Transition.cupertino,
      transitionDuration: const Duration(milliseconds: 500),
      middlewares: [LicenseMiddleware()],
    ),

    /// AI制卡
    GetPage(
      name: '/llm_card',
      page: () => const LLMCardPage(),
      transition: Transition.cupertino,
      transitionDuration: const Duration(milliseconds: 500),
      middlewares: [LicenseMiddleware()],
    ),

    /// PDF制卡
    GetPage(
      name: '/pdf_card',
      page: () => const PdfCard(),
      transition: Transition.cupertino,
      transitionDuration: const Duration(milliseconds: 500),
      middlewares: [LicenseMiddleware()],
    ),

    /// 图片制卡
    GetPage(
      name: '/image_card',
      page: () => const ImageCardPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    GetPage(
      name: '/image_card/config',
      page: () => const ImageCardConfig(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// 闪念笔记
    GetPage(
      name: '/flash_notes',
      page: () => const FlashNotesPage(),
      transition: Transition.cupertino,
      transitionDuration: const Duration(milliseconds: 500),
      middlewares: [LicenseMiddleware()],
    ),

    /// 导图制卡
    GetPage(
      name: '/mindmap_card',
      page: () => const MindmapCardPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// Excel制卡
    GetPage(
      name: '/excel_card',
      page: () => const ExcelCardPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// Markdown制卡
    GetPage(
      name: '/markdown_card',
      page: () => const MarkdownCardPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// Word制卡
    GetPage(
      name: '/word_card',
      page: () => const WordCardPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    // 多媒体制卡
    GetPage(
      name: '/media_card',
      page: () => const MediaCardPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    // 微信读书制卡
    GetPage(
      name: '/wereader_card',
      page: () => const WeReaderCardPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    // 幕布制卡
    GetPage(
      name: '/mubu_card',
      page: () => const MubuCardPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    // 单词制卡
    GetPage(
      name: '/vocab_card',
      page: () => const VocabCardPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    // 卡片图片管理
    GetPage(
      name: '/card_media_manager',
      page: () => const CardMediaManagerPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// Anki同步
    GetPage(
      name: '/toolbox/sync',
      page: () => const AnkiSyncPage(),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    GetPage(
      name: '/toolbox/sync/config',
      page: () => const AnkiSyncConfig(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// Anki卡片朗读
    GetPage(
      name: '/card_tts',
      page: () => const CardTTSPage(),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// Anki卡片OCR
    GetPage(
      name: '/card_ocr',
      page: () => const CardOCRPage(),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// 图片OCR
    GetPage(
      name: '/image_ocr',
      page: () => const ImageOCRPage(),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// 牌组管理
    GetPage(
      name: '/deck_manager',
      page: () => const DeckManagerPage(),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// PDF笔记
    GetPage(
      name: '/pdf_note',
      page: () => const PDFNotePage(),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),

    /// 视频笔记
    GetPage(
      name: '/video_note',
      page: () => const VideoNotePage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    GetPage(
      name: '/bilibili-login',
      page: () => const BiliLoginPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    GetPage(
      name: '/video-settings',
      page: () => const VideoSettingsPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    GetPage(
      name: '/image_editor',
      page: () => const ImageCloze(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    GetPage(
      name: '/video_note/ocr',
      page: () {
        // 检查是否有图片路径参数
        final arguments = Get.arguments;
        final String? imagePath =
            arguments != null && arguments is Map<String, dynamic>
                ? arguments['imagePath'] as String?
                : null;

        return VideoOCRPage(imagePath: imagePath);
      },
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    GetPage(
      name: '/video_note/annot',
      page: () => const ImageEditorPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    GetPage(
      name: '/video_note/cloze',
      page: () => const VideoImageCloze(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
      middlewares: [LicenseMiddleware()],
    ),
    GetPage(
      name: '/video_note/seek',
      page: () => const VideoNotePage(),
      transitionDuration: const Duration(milliseconds: 0),
      transition: Transition.noTransition,
      middlewares: [LicenseMiddleware()],
    ),

    /// 系统设置
    GetPage(
      name: '/about',
      page: () => const AboutPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
    ),
    GetPage(
      name: '/activate',
      page: () => ActivatePage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
    ),
    GetPage(
      name: '/user_center',
      page: () => UserCenterPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
    ),
    GetPage(
      name: '/anki_config',
      page: () => const AnkiConfig(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
    ),

    /// 工具箱
    /// 旋转PDF
    GetPage(
      name: '/toolbox/rotate',
      page: () => const PDFRotatePage(),
      transition: Transition.cupertino,
    ),

    /// 重排
    GetPage(
      name: '/toolbox/reorder',
      page: () => const PDFReorderPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
    ),

    /// OCR
    GetPage(
      name: '/toolbox/ocr',
      page: () => const PDFOCRPage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
    ),

    /// 注释
    GetPage(
      name: '/toolbox/annotation',
      page: () => const PDFRotatePage(),
      transitionDuration: const Duration(milliseconds: 500),
      transition: Transition.cupertino,
    ),

    /// 提取
    GetPage(
      name: '/toolbox/extract',
      page: () => const PDFExtractPage(),
      transition: Transition.cupertino,
    ),

    /// 合并
    GetPage(
      name: '/toolbox/merge',
      page: () => const PDFMergePage(),
      transition: Transition.cupertino,
    ),

    /// 拆分
    GetPage(
      name: '/toolbox/split',
      page: () => const PDFSplitPage(),
      transition: Transition.cupertino,
    ),

    /// 删除
    GetPage(
      name: '/toolbox/delete',
      page: () => const PDFDeletePage(),
      transition: Transition.cupertino,
    ),

    /// 恢复权限
    GetPage(
      name: '/toolbox/recover_permission',
      page: () => const PDFPermissionPage(),
      transition: Transition.cupertino,
    ),

    /// 缩放
    GetPage(
      name: '/toolbox/scale',
      page: () => const PDFScalePage(),
      transition: Transition.cupertino,
    ),

    /// 裁剪
    GetPage(
      name: '/toolbox/crop',
      page: () => const PDFCropPage(),
      transition: Transition.cupertino,
    ),

    /// 分割
    GetPage(
      name: '/toolbox/cut',
      page: () => const PDFCutPage(),
      transition: Transition.cupertino,
    ),

    /// 组合
    GetPage(
      name: '/toolbox/combine',
      page: () => const PDFCombinePage(),
      transition: Transition.cupertino,
    ),

    /// 延展
    GetPage(
      name: '/toolbox/expand',
      page: () => const PDFExpandPage(),
      transition: Transition.cupertino,
    ),

    /// 插入
    GetPage(
      name: '/toolbox/insert',
      page: () => const PDFInsertPage(),
      transition: Transition.cupertino,
    ),

    /// 背景
    GetPage(
      name: '/toolbox/background',
      page: () => const PDFBackgroundPage(),
      transition: Transition.cupertino,
    ),

    /// 元数据
    GetPage(
      name: '/toolbox/meta',
      page: () => const PDFMetaPage(),
      transition: Transition.cupertino,
    ),

    /// 页码
    GetPage(
      name: '/toolbox/page_number',
      page: () => const PDFPageNumberPage(),
      transition: Transition.cupertino,
    ),

    /// 书签
    GetPage(
      name: '/toolbox/bookmark',
      page: () => const PDFBookmarkPage(),
      transition: Transition.cupertino,
    ),

    /// 批注
    GetPage(
      name: '/toolbox/annot',
      page: () => const PDFAnnotationPage(),
      transition: Transition.cupertino,
    ),

    /// PDF转图片
    GetPage(
      name: '/toolbox/convert_pdf_to_img',
      page: () => const PDF2ImgPage(),
      transition: Transition.cupertino,
    ),

    /// 图片转PDF
    GetPage(
      name: '/toolbox/convert_img_to_pdf',
      page: () => const IMG2PDFPage(),
      transition: Transition.cupertino,
    ),

    /// PDF转图片型
    GetPage(
      name: '/toolbox/convert_pdf_to_img_pdf',
      page: () => const PDF2ImgPDFPage(),
      transition: Transition.cupertino,
    ),

    /// EPUB转PDF
    GetPage(
      name: '/toolbox/convert_epub_to_pdf',
      page: () => const EPUB2PDFPage(),
      transition: Transition.cupertino,
    ),

    /// MOBI转PDF
    GetPage(
      name: '/toolbox/convert_mobi_to_pdf',
      page: () => const MOBI2PDFPage(),
      transition: Transition.cupertino,
    ),

    /// OFD转PDF
    GetPage(
      name: '/toolbox/convert_ofd_to_pdf',
      page: () => const OFD2PDFPage(),
      transition: Transition.cupertino,
    ),

    /// DOCX转PDF
    GetPage(
      name: '/toolbox/convert_docx_to_pdf',
      page: () => const DOCX2PDFPage(),
      transition: Transition.cupertino,
    ),

    /// PDF转DOCX
    GetPage(
      name: '/toolbox/convert_pdf_to_docx',
      page: () => const PDF2DOCXPage(),
      transition: Transition.cupertino,
    ),

    /// DOCX转HTML
    GetPage(
      name: '/toolbox/convert_docx_to_html',
      page: () => const DOCX2HTMLPage(),
      transition: Transition.cupertino,
    ),

    /// MD转HTML
    GetPage(
      name: '/toolbox/convert_md_to_html',
      page: () => const MD2HTMLPage(),
      transition: Transition.cupertino,
    ),

    /// HTML转PDF
    GetPage(
      name: '/toolbox/convert_html_to_pdf',
      page: () => const HTML2PDFPage(),
      transition: Transition.cupertino,
    ),

    /// Markdown转PDF
    GetPage(
      name: '/toolbox/convert_md_to_pdf',
      page: () => const MD2PDFPage(),
      transition: Transition.cupertino,
    ),

    /// 加密
    GetPage(
      name: '/toolbox/encrypt',
      page: () => const PDFEncryptPage(),
      transition: Transition.cupertino,
    ),

    /// 水印
    GetPage(
      name: '/toolbox/watermark',
      page: () => const PDFWatermarkPage(),
      transition: Transition.cupertino,
    ),

    GetPage(
      name: '/paywall',
      page: () => const PaywallPage(),
      transition: Transition.rightToLeft,
    ),
  ];
}

class LicenseMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final licenseController = Get.find<LicenseController>();
    if (!licenseController.isActivated.value) {
      if (Platform.isIOS) {
        return const RouteSettings(name: '/paywall');
      } else {
        return const RouteSettings(name: '/activate');
      }
    }
    return null;
  }
}
