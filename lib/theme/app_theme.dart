import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

/// The [AppTheme] defines light and dark themes for the app.
///
/// Theme setup for FlexColorScheme package v8.
/// Use same major flex_color_scheme package version. If you use a
/// lower minor version, some properties may not be supported.
/// In that case, remove them after copying this theme to your
/// app or upgrade package to version 8.0.2.
///
/// Use in [MaterialApp] like this:
///
/// MaterialApp(
///  theme: AppTheme.light,
///  darkTheme: AppTheme.dark,
///  :
/// );
sealed class AppTheme {
  // The defined light theme.
  static ThemeData light = FlexThemeData.light(
    scheme: FlexScheme.blue,
    visualDensity: FlexColorScheme.comfortablePlatformDensity,
    cupertinoOverrideTheme: const CupertinoThemeData(applyThemeToAll: true),
  ).copyWith(
    cardColor: const Color(0xFFFFFFFF),
    scaffoldBackgroundColor: const Color(0xFFFAFAFA),
  );
  // The defined dark theme.
  static ThemeData dark = FlexThemeData.dark(
    scheme: FlexScheme.blue,
    visualDensity: FlexColorScheme.comfortablePlatformDensity,
    cupertinoOverrideTheme: const CupertinoThemeData(applyThemeToAll: true),
  );
}

ThemeData convertToMaterialTheme(ShadThemeData themeData) {
  var mTheme = ThemeData(
    fontFamily: themeData.textTheme.family,
    extensions: themeData.extensions,
    colorScheme: ColorScheme(
      brightness: themeData.brightness,
      primary: themeData.colorScheme.primary,
      onPrimary: themeData.colorScheme.primaryForeground,
      secondary: themeData.colorScheme.secondary,
      onSecondary: themeData.colorScheme.secondaryForeground,
      error: themeData.colorScheme.destructive,
      onError: themeData.colorScheme.destructiveForeground,
      // Keep deprecated members for backwards compatibility
      // ignore: deprecated_member_use
      background: themeData.colorScheme.background,
      // ignore: deprecated_member_use
      onBackground: themeData.colorScheme.foreground,
      surface: themeData.colorScheme.card,
      onSurface: themeData.colorScheme.cardForeground,
    ),
    scaffoldBackgroundColor: themeData.colorScheme.background,
    brightness: themeData.brightness,
    dividerTheme: DividerThemeData(
      color: themeData.colorScheme.border,
      thickness: 1,
    ),
    textSelectionTheme: TextSelectionThemeData(
      cursorColor: themeData.colorScheme.primary,
      selectionColor: themeData.colorScheme.selection,
      selectionHandleColor: themeData.colorScheme.primary,
    ),
  );
  return mTheme;
}
