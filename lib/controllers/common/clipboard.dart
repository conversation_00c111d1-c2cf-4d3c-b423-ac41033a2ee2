import 'dart:convert';
import 'dart:io';

import 'package:get/get.dart';
import 'package:super_clipboard/super_clipboard.dart';
import 'dart:typed_data';
import 'package:flutter/services.dart';

class ClipboardController extends GetxController {
  final clipboard = SystemClipboard.instance;

  bool isClipboardAvailable() {
    if (clipboard == null) {
      return false; // Clipboard API is not supported on this platform.
    }
    return true;
  }

  Future<void> copyText(String text) async {
    final item = DataWriterItem();
    item.add(Formats.plainText(text));
    await clipboard?.write([item]);
  }

  Future<void> copyImage(Uint8List image, String type) async {
    if (Platform.isAndroid) {
      const platform = MethodChannel('samples.flutter.dev/battery');
      platform.invokeMethod('setClipboardData', {
        'type': 'image',
        'mimeType': 'image/jpeg',
        'content': base64Encode(image),
      });
    } else {
      final item = DataWriterItem();
      if (type == "png") {
        item.add(Formats.png(image));
      } else if (type == "jpeg") {
        item.add(Formats.jpeg(image));
      } else {
        item.add(Formats.png(image));
      }
      await clipboard?.write([item]);
    }
  }

  Future<void> copyImageBase64(String base64Str) async {
    final Uint8List bytes = Uint8List.fromList(base64.decode(base64Str));
    await copyImage(bytes, "png");
  }

  Future<void> copyHtml(String html) async {
    final item = DataWriterItem();
    item.add(Formats.htmlText(html));
    await clipboard?.write([item]);
  }

  Future<void> copyMultipleItems(List<dynamic> data) async {
    final item = DataWriterItem();
    for (var ele in data) {
      item.add(ele);
    }
    await clipboard?.write([item]);
  }
}
