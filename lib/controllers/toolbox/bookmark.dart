import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'dart:convert';
import 'package:path/path.dart' as path;

class PDFBookmarkPageController extends GetxController {
  // 基本数据
  final exportFormatList = [
    {"label": 'txt', "value": 'txt'},
    {"label": 'json', "value": 'json'},
  ];
  // 表单参数
  final bookmarkFile = "".obs;
  final exportFormat = 'txt'.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'import');

  Future<void> deleteBookmarks(String path, String outputPath) async {
    //Loads an existing PDF document
    PdfDocument document =
        PdfDocument(inputBytes: File(path).readAsBytesSync());

    PdfBookmarkBase bookmark = document.bookmarks;
    for (var i = bookmark.count - 1; i >= 0; i--) {
      bookmark.removeAt(i);
    }
    File(outputPath).writeAsBytes(await document.save());
    document.dispose();
  }

  Future<void> importBookmarks(
      String path, String bookmarkPath, String outputPath) async {
    final document = PdfDocument(inputBytes: File(path).readAsBytesSync());
    final lines = await File(bookmarkPath).readAsLines();

    // 用于跟踪当前父级书签的栈结构
    final List<PdfBookmark> bookmarkStack = [];
    PdfBookmark? currentParent;
    int lastIndentLevel = -1;

    for (final line in lines) {
      // 解析缩进层级和内容
      final indentMatch = RegExp(r'^\t*').firstMatch(line);
      final indentLevel = indentMatch?.group(0)?.length ?? 0;
      final content = line.substring(indentMatch?.end ?? 0);

      // 提取页码（使用正则匹配最后一个数字）
      final pageMatch = RegExp(r'(\d+)\s*$').firstMatch(content);
      int pageNumber = pageMatch != null ? int.parse(pageMatch.group(1)!) : 1;
      final title = content.replaceAll(RegExp(r'\s*\d+\s*$'), '').trim();

      // 验证页码有效性
      if (pageNumber < 1 || pageNumber > document.pages.count) {
        pageNumber = 1; // 无效页码设为首页
      }

      // 创建目标页面（注意页码从0开始）
      final page = document.pages[pageNumber - 1];
      final destination = PdfDestination(page, Offset(0, 0));

      // 根据缩进层级调整父级书签
      if (indentLevel > lastIndentLevel) {
        if (currentParent != null) {
          bookmarkStack.add(currentParent);
        }
      } else if (indentLevel < lastIndentLevel) {
        final popCount = lastIndentLevel - indentLevel;
        for (var i = 0; i < popCount; i++) {
          if (bookmarkStack.isNotEmpty) {
            bookmarkStack.removeLast();
          }
        }
      }

      // 获取当前父级书签
      currentParent = bookmarkStack.isNotEmpty ? bookmarkStack.last : null;

      // 添加新书签
      final newBookmark = currentParent == null
          ? document.bookmarks.add(title)
          : currentParent.add(title);

      newBookmark.destination = destination;
      currentParent = newBookmark;
      lastIndentLevel = indentLevel;
    }

    File(outputPath).writeAsBytes(await document.save());
    document.dispose();
  }

  Future<void> exportBookmarks(
      String path, String format, String outputPath) async {
    //Loads an existing PDF document
    PdfDocument document =
        PdfDocument(inputBytes: File(path).readAsBytesSync());
    PdfBookmarkBase bookmark = document.bookmarks;
    if (format == "txt") {
      String text = "";
      void processBookmark(PdfBookmarkBase bookmark, int indentLevel) {
        for (var i = 0; i < bookmark.count; i++) {
          final cur = bookmark[i];
          // 根据缩进级别添加对应数量的制表符
          String pageNum = "";
          if (cur.destination != null) {
            pageNum =
                (document.pages.indexOf(cur.destination!.page) + 1).toString();
          }
          text += "${'\t' * indentLevel}${cur.title} $pageNum\n";
          // 递归处理子书签，缩进级别+1
          if (cur.count > 0) {
            processBookmark(cur, indentLevel + 1);
          }
        }
      }

      // 从顶级书签开始处理，初始缩进级别为0
      processBookmark(bookmark, 0);
      File(outputPath).writeAsString(text);
      document.dispose();
    } else if (format == "json") {
      List<dynamic>? result = [];
      void processBookmark(
          PdfBookmarkBase bookmark, Map<String, dynamic> parent) {
        for (var i = 0; i < bookmark.count; i++) {
          final cur = bookmark[i];
          String pageNum = "";
          if (cur.destination != null) {
            pageNum =
                (document.pages.indexOf(cur.destination!.page) + 1).toString();
          }
          // 创建当前书签节点
          final node = {
            'title': cur.title,
            'page': pageNum,
            'children': <Map<String, dynamic>>[]
          };
          // 将节点添加到父级
          parent['children'].add(node);
          // 递归处理子书签
          if (cur.count > 0) {
            processBookmark(cur, node);
          }
        }
      }

      // 创建根节点
      final root = {'children': []};
      // 从顶级书签开始处理
      processBookmark(bookmark, root);
      result = root['children'];
      File(outputPath).writeAsString(jsonEncode(result));
      document.dispose();
    }
  }

  /// 获取PDF所有页码对应的子牌组路径映射
  ///
  /// 参数:
  /// * [pdfPath] - PDF文件路径
  ///
  /// 返回:
  /// * [Map<int, String>] - 页码到书签路径的映射，键为页码（从1开始），值为书签路径
  Future<Map<int, String>> getPageSubdeckMap(String pdfPath) async {
    Map<int, String> pageToSubdeck = {};

    // 获取PDF文件名作为顶级标题
    final pdfTitle = path.basenameWithoutExtension(pdfPath);
    print('PDF标题: $pdfTitle');

    try {
      // 加载PDF文档
      final document = PdfDocument(inputBytes: File(pdfPath).readAsBytesSync());

      // 处理书签
      final List<(int, String)> pagePaths = _extractBookmarkPaths(document);
      print('找到${pagePaths.length}个书签');

      // 如果没有找到任何书签，返回空映射
      if (pagePaths.isEmpty) {
        print('未能从PDF中提取出有效书签');
        document.dispose();
        return pageToSubdeck;
      }

      // 按页码排序
      pagePaths.sort((a, b) => a.$1.compareTo(b.$1));

      // 创建一个临时的书签数组，添加书签页码和路径
      final List<(int, String)> bookmarkPages = [];

      // 处理第一个书签之前的页码
      if (pagePaths.isNotEmpty && pagePaths.first.$1 > 1) {
        // 如果第一个书签不在第1页，为第1页创建默认书签
        bookmarkPages.add((1, pagePaths.first.$2));
      }

      // 添加所有书签
      for (final pagePath in pagePaths) {
        bookmarkPages.add(pagePath);
      }

      // 填充页码映射
      final totalPages = document.pages.count;
      for (int page = 1; page <= totalPages; page++) {
        // 找到最近的前一个书签
        int? closestBookmarkIdx;
        for (int idx = 0; idx < bookmarkPages.length; idx++) {
          if (bookmarkPages[idx].$1 <= page) {
            closestBookmarkIdx = idx;
          } else {
            break; // 已经超过当前页码
          }
        }

        if (closestBookmarkIdx != null) {
          pageToSubdeck[page] = bookmarkPages[closestBookmarkIdx].$2;
        }
      }

      // 打印一些示例结果
      final sampleCount = 5.clamp(0, pageToSubdeck.length);
      print('生成的页码-子牌组路径映射示例:');
      int count = 0;
      pageToSubdeck.forEach((page, path) {
        if (count < sampleCount) {
          print('  示例 ${count + 1}: 第${page}页 -> "$path"');
          count++;
        }
      });

      document.dispose();
    } catch (e) {
      print('处理PDF书签时出错: $e');
    }

    return pageToSubdeck;
  }

  /// 从PDF文档中提取所有书签路径
  List<(int, String)> _extractBookmarkPaths(PdfDocument document) {
    final List<(int, String)> pagePaths = [];
    final bookmarks = document.bookmarks;

    // 处理根书签
    if (bookmarks.count == 0) {
      print('PDF没有书签');
      return pagePaths;
    }

    // 处理所有书签
    _processBookmarks(bookmarks, [], pagePaths, document);

    return pagePaths;
  }

  /// 递归处理书签及其子书签
  void _processBookmarks(PdfBookmarkBase bookmark, List<String> currentPath,
      List<(int, String)> pagePaths, PdfDocument document) {
    for (int i = 0; i < bookmark.count; i++) {
      final current = bookmark[i];
      final title = current.title;

      if (title.isEmpty) {
        continue; // 跳过没有标题的书签
      }

      // 创建当前路径副本并添加当前书签标题
      final path = List<String>.from(currentPath)..add(title);

      // 如果有目标页面，添加到路径列表
      if (current.destination != null) {
        final pageNum = document.pages.indexOf(current.destination!.page) + 1;
        final fullPath = path.join('::');
        print('添加书签路径: 第${pageNum}页 -> "$fullPath"');
        pagePaths.add((pageNum, fullPath));
      }

      // 递归处理子书签
      if (current.count > 0) {
        _processBookmarks(current, path, pagePaths, document);
      }
    }
  }

  /// 获取指定页码的子牌组路径
  ///
  /// 参数:
  /// * [pageMap] - 页码到子牌组路径的映射
  /// * [parentDeck] - 父牌组名称
  /// * [isCreateSubdeck] - 是否创建子牌组
  /// * [maxLevel] - 子牌组最大层级
  /// * [pageNum] - 页码
  ///
  /// 返回:
  /// * [String] - 最终的牌组路径
  String getSubdeckByBookmark({
    required Map<int, String> pageMap,
    required String parentDeck,
    required bool isCreateSubdeck,
    required int maxLevel,
    required int pageNum,
  }) {
    if (!isCreateSubdeck) {
      return parentDeck;
    }

    final fullName = pageMap[pageNum] ?? '';
    if (fullName.isEmpty) {
      return parentDeck;
    }

    // 根据maxLevel截取子牌组层级
    final subdeckParts = fullName.split('::');
    final truncatedSubdeck =
        maxLevel > 0 ? subdeckParts.take(maxLevel).join('::') : fullName;

    return '$parentDeck::$truncatedSubdeck';
  }

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      progressController.updateProgress(
          status: "running",
          current: 10,
          total: 100,
          message: 'toolbox.common.processing'.tr);
      for (String filePath in selectedFilePaths) {
        final pathUtils = PathUtils(filePath);
        String outputPath = "";
        if (tabController.selected == 'import') {
          if (bookmarkFile.value.isEmpty) {
            showToastNotification(
              context,
              'toolbox.common.failure'.tr,
              'toolbox.bookmark.bookmarkFileEmpty'.tr,
              type: "error",
            );
            return;
          }
          outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_${'toolbox.bookmark.importBookmarks'.tr}",
            outputDir: outputDir.value,
          );
          await importBookmarks(filePath, bookmarkFile.value, outputPath);
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
      status: "completed",
      message: 'toolbox.common.fileProcessSuccess'.tr,
      current: 100.0,
      total: 100.0,
          );
        } else if (tabController.selected == 'export') {
          outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_${'toolbox.bookmark.exportBookmarks'.tr}",
            suffix: ".${exportFormat.value}",
            outputDir: outputDir.value,
          );
          await exportBookmarks(filePath, exportFormat.value, outputPath);
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
      status: "completed",
      message: 'toolbox.common.fileProcessSuccess'.tr,
      current: 100.0,
      total: 100.0,
          );
        } else if (tabController.selected == 'delete') {
          outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_${'toolbox.bookmark.deleteBookmarks'.tr}",
            suffix: ".pdf",
            outputDir: outputDir.value,
          );
          await deleteBookmarks(filePath, outputPath);
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
      status: "completed",
      message: 'toolbox.common.fileProcessSuccess'.tr,
      current: 100.0,
      total: 100.0,
          );
        }
      }
    } catch (e) {
      logger.e(e.toString());
      progressController.updateProgress(
      status: "error",
      message: e.toString(),
      current: 0.0,
      total: 100.0,
    );
    }
  }
}
