import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class PDFDeletePageController extends GetxController {
  // 表单参数
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'custom');

  Future<void> deleteCustomPages(
      String path, String pageRange, String outputPath) async {
    final PdfDocument document = PdfDocument(
      inputBytes: File(path).readAsBytesSync(),
    );
    final List<int> pages = parsePageRange(pageRange, document.pages.count);
    pages.sort((a, b) => b.compareTo(a));

    for (int pageNum in pages) {
      document.pages.removeAt(pageNum);
    }
    File(outputPath).writeAsBytesSync(await document.save());
    document.dispose();
  }

  Future<void> deleteBlankPages(String path, String outputPath) async {
    final PdfDocument document = PdfDocument(
      inputBytes: File(path).readAsBytesSync(),
    );
    for (var i = document.pages.count - 1; i >= 0; i--) {
      final page = document.pages[i];
      if (page.isBlank != null && page.isBlank == true) {
        document.pages.removeAt(i);
      }
    }
    File(outputPath).writeAsBytesSync(await document.save());
    document.dispose();
  }

  void submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      if (tabController.selected == "custom") {
        for (String filePath in selectedFilePaths) {
          final pathUtils = PathUtils(filePath);
          String outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_${'toolbox.delete.stem_append'.tr}",
            outputDir: outputDir.value,
          );
          await deleteCustomPages(filePath, pageRange.value, outputPath);
          progressController.outputPath.value = outputPath;
        }
      } else if (tabController.selected == "blank") {
        for (String filePath in selectedFilePaths) {
          final pathUtils = PathUtils(filePath);
          String outputPath = await pathUtils.convertPath(
            outputMode.value,
            stem_append: "_${'toolbox.delete.blankTab'.tr}",
            outputDir: outputDir.value,
          );
          await deleteBlankPages(filePath, outputPath);
          progressController.outputPath.value = outputPath;
        }
      }
      progressController.updateProgress(status: "completed", message: 'toolbox.background.completed'.tr);
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "toolbox.common.error_with_msg".trParams({'error': e.toString()}),
      );
    }
  }
}
