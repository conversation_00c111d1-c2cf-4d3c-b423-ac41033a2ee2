import 'dart:io';
import 'dart:convert';
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';

import 'dart:math';
import 'package:flutter/services.dart';

class PDFWatermarkPageController extends GetxController {
  // 基本数据
  final watermarkTypeList = [
    {'value': 'text', 'label': 'toolbox.watermark.typeOptions.text'.tr},
    {'value': 'image', 'label': 'toolbox.watermark.typeOptions.image'.tr},
  ];
  final watermarkRemoveTypeList = [
    {
      'value': 'type',
      'label': 'toolbox.watermark.watermarkRemoveTypeList.type'.tr
    },
    {
      'value': 'mask',
      'label': 'toolbox.watermark.watermarkRemoveTypeList.mask'.tr
    },
    {
      'value': 'content',
      'label': 'toolbox.watermark.watermarkRemoveTypeList.content'.tr
    },
    {
      'value': 'image',
      'label': 'toolbox.watermark.watermarkRemoveTypeList.image'.tr
    },
    {
      'value': 'edit_text',
      'label': 'toolbox.watermark.watermarkRemoveTypeList.edit_text'.tr
    },
    {
      'value': 'path',
      'label': 'toolbox.watermark.watermarkRemoveTypeList.path'.tr
    },
    {
      'value': 'pixel',
      'label': 'toolbox.watermark.watermarkRemoveTypeList.pixel'.tr
    },
  ];
  final fontFamilyList = [
    {
      'value': 'SourceHanSansSC',
      'label': 'toolbox.watermark.fontFamilyList.sourceHanSansSC'.tr
    },
    {'value': 'helvetica', 'label': 'Helvetica'},
    {'value': 'courier', 'label': 'Courier'},
    {'value': 'times', 'label': 'Times New Roman'},
    {'value': 'symbol', 'label': 'Symbol'},
    {'value': 'zapfdingbats', 'label': 'ZapfDingbats'},
  ];
  final stepOptionList = [
    {'value': 'step1', 'label': 'toolbox.watermark.stepOptions.step1'.tr},
    {'value': 'step2', 'label': 'toolbox.watermark.stepOptions.step2'.tr},
  ];

  // 表单参数
  // 添加水印
  final watermarkType = 'text'.obs;
  final watermarkText = ''.obs;
  final advancedMode = false.obs;
  final rotate = Rx<double>(-30.0);
  final opacity = 0.5.obs;
  final fontSize = 40.0.obs;
  final fontColor = const Color(0xFF000000).obs;
  final useCustomFont = false.obs;
  final customFontFilePath = ''.obs;
  final fontName = 'SourceHanSansSC'.obs;
  final xOffset = 0.0.obs;
  final yOffset = 0.0.obs;
  final lineSpacing = 0.0.obs;
  final spacing = 0.0.obs;
  final numRows = 1.obs;
  final watermarkImage = ''.obs;
  final scale = 1.0.obs;
  // 去除水印
  final step = "step1".obs;
  final expandMode = false.obs;
  final watermarkRemoveType = 'type'.obs;
  final watermarkIndex = "".obs;
  final watermarkPage = "".obs;
  final watermarkTextToRemove = "".obs;
  final upperBounds = "255".obs;
  final lowerBounds = "128".obs;
  final targetColor = const Color(0xFFFFFFFF).obs;
  final isLimitRegin = false.obs;
  // 公共
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;

  // 错误信息
  final userPasswordError = ''.obs;
  final permissionPasswordError = ''.obs;
  final outputDirError = ''.obs;

  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'add');

  @override
  void onInit() {
    super.onInit();

    // 监听水印去除类型变化，重置检测状态
    watermarkRemoveType.listen((type) {
      if (type != 'type') {
        resetDetectionState();
      }
    });

    // 监听步骤变化，在切换到步骤1时重置检测状态
    step.listen((currentStep) {
      if (currentStep == 'step1') {
        resetDetectionState();
      }
    });
  }

  /// 添加文本水印到PDF文档
  Future<void> addTextWatermark(
      {required String inputFilePath,
      required String outputFilePath,
      required String text,
      required double opacity,
      required double rotationAngle,
      required double fontSize,
      required String hexColor}) async {
    try {
      // 打开PDF文档
      final File inputFile = File(inputFilePath);
      if (!inputFile.existsSync()) {
        throw Exception('toolbox.watermark.fileNotExist'.tr);
      }

      // 读取PDF文档
      final PdfDocument document =
          PdfDocument(inputBytes: await inputFile.readAsBytes());

      // 解析页码范围
      List<int> targetPages = [];
      if (pageRange.value.isNotEmpty) {
        targetPages = parsePageRange(pageRange.value, document.pages.count);
      } else {
        targetPages = List.generate(document.pages.count, (index) => index);
      }

      // 解析颜色
      Color textColor = Color(int.parse(hexColor.replaceFirst('#', '0xFF')));

      // 创建字体
      PdfFont font;
      if (customFontFilePath.value.isNotEmpty &&
          File(customFontFilePath.value).existsSync()) {
        // 使用自定义字体
        final fontData = File(customFontFilePath.value).readAsBytesSync();
        font = PdfTrueTypeFont(fontData, fontSize);
      } else {
        // 使用标准字体
        final fontFamily = await getPdfFontFamily(fontName.value);
        if (fontFamily is PdfCjkFontFamily) {
          font = PdfCjkStandardFont(fontFamily, fontSize);
        } else if (fontFamily is PdfTrueTypeFont) {
          font = fontFamily;
        } else {
          font = PdfStandardFont(fontFamily as PdfFontFamily, fontSize);
        }
      }
      logger.i('字体: $font');

      // 计算文本尺寸以便对齐
      final Size textSize = font.measureString(text);

      // 为指定的页面添加水印
      for (int pageIndex in targetPages) {
        // 获取页面图形上下文
        PdfGraphics graphics = document.pages[pageIndex].graphics;

        // 获取页面尺寸
        final pageSize = document.pages[pageIndex].size;

        // 创建画笔和画刷
        final watermarkPen =
            PdfPen(PdfColor(textColor.red, textColor.green, textColor.blue));
        final watermarkBrush = PdfSolidBrush(
            PdfColor(textColor.red, textColor.green, textColor.blue));

        // 计算旋转角度（弧度）
        final double rotationRadian = rotationAngle * pi / 180.0;

        // 创建水印网格布局
        // 首先计算沿旋转方向每个水印占用的距离
        final double spaceAlongRotation = textSize.width + spacing.value;
        // 计算垂直于旋转方向每个水印占用的距离
        final double spacePerpendicularToRotation =
            textSize.height + lineSpacing.value;

        // 计算页面对角线长度，确保水印能覆盖整个页面
        final double diagonalLength =
            sqrt(pow(pageSize.width, 2) + pow(pageSize.height, 2));

        // 计算需要多少行和列的水印才能覆盖整个页面（包括页面外部分）
        int totalRows = numRows.value;
        int totalCols = spacing.value > 0
            ? (diagonalLength / spaceAlongRotation).ceil() * 2
            : 1;

        // 计算水印网格的起始位置（中心点）
        final double centerX = pageSize.width / 2;
        final double centerY = pageSize.height / 2;

        // 为每行和每列添加水印
        for (int row = 0; row < totalRows; row++) {
          for (int col = 0; col < totalCols; col++) {
            // 保存当前图形状态
            PdfGraphicsState state = graphics.save();

            // 设置透明度
            graphics.setTransparency(opacity);

            // 计算水印在网格中的位置
            // 计算当前行和列的偏移
            double rowOffset =
                (row - totalRows / 2) * spacePerpendicularToRotation;
            double colOffset = (col - totalCols / 2) * spaceAlongRotation;

            // 偶数行的水印偏移半个水印宽度，形成交错排列
            // 只有当有多行且spacing>0时才应用交错布局
            if (row % 2 == 1 && totalRows > 1 && spacing.value > 0) {
              colOffset += spaceAlongRotation / 2;
            }

            // 计算旋转后的偏移坐标（顺着旋转角度方向和垂直于旋转角度方向）
            // 旋转方向上的偏移
            double offsetAlongRotation = colOffset;
            // 垂直于旋转方向的偏移
            double offsetPerpendicularToRotation = rowOffset;

            // 计算最终的水印中心点坐标
            double watermarkCenterX = centerX +
                offsetAlongRotation * cos(rotationRadian) -
                offsetPerpendicularToRotation * sin(rotationRadian) +
                xOffset.value;
            double watermarkCenterY = centerY +
                offsetAlongRotation * sin(rotationRadian) +
                offsetPerpendicularToRotation * cos(rotationRadian) +
                yOffset.value;

            // 计算水印左上角坐标
            double watermarkX = watermarkCenterX - textSize.width / 2;
            double watermarkY = watermarkCenterY - textSize.height / 2;

            // 设置旋转，围绕水印中心点旋转
            graphics.translateTransform(watermarkCenterX, watermarkCenterY);
            graphics.rotateTransform(rotationAngle);
            graphics.translateTransform(-watermarkCenterX, -watermarkCenterY);

            // 绘制文本水印
            graphics.drawString(text, font,
                pen: watermarkPen,
                brush: watermarkBrush,
                bounds: Rect.fromLTWH(
                    watermarkX, watermarkY, textSize.width, textSize.height));

            // 恢复图形状态
            graphics.restore(state);
          }
        }
      }

      // 保存并释放PDF文档
      File(outputFilePath).writeAsBytes(await document.save());
      document.dispose();
    } catch (e) {
      throw Exception('${'watermark.error.addTextWatermarkFailed'.tr}: $e');
    }
  }

  /// 添加图片水印到PDF文档
  Future<void> addImageWatermark({
    required String inputFilePath,
    required String outputFilePath,
    required String imagePath,
    required double opacity,
  }) async {
    try {
      // 打开PDF文档
      final File inputFile = File(inputFilePath);
      if (!inputFile.existsSync()) {
        throw Exception('输入文件不存在');
      }

      // 读取PDF文档
      final PdfDocument document =
          PdfDocument(inputBytes: await inputFile.readAsBytes());

      // 解析页码范围
      List<int> targetPages = [];
      if (pageRange.value.isNotEmpty) {
        targetPages = parsePageRange(pageRange.value, document.pages.count);
      } else {
        targetPages = List.generate(document.pages.count, (index) => index);
      }

      // 读取图片文件
      final File imageFile = File(imagePath);
      if (!imageFile.existsSync()) {
        throw Exception('toolbox.watermark.watermarkImageNotExist'.tr);
      }

      // 读取图片数据
      Uint8List imageBytes;
      try {
        imageBytes = await imageFile.readAsBytes();
        if (imageBytes.isEmpty) {
          throw Exception('toolbox.watermark.imageDataEmpty'.tr);
        }
        logger.i('图片大小: ${imageBytes.length} 字节');
      } catch (e) {
        throw Exception('toolbox.watermark.readImageDataFailed'.tr);
      }

      // 创建PdfBitmap对象
      PdfBitmap? imageBitmap;
      try {
        // 首先尝试直接加载图片
        imageBitmap = PdfBitmap(imageBytes);
        logger.i('图片尺寸: ${imageBitmap.width} x ${imageBitmap.height}');
      } catch (e) {
        logger.e('直接创建PdfBitmap失败: $e, 尝试使用Base64方式');
        // 如果直接创建PdfBitmap失败，尝试使用Base64方式
        try {
          String imageBase64 = base64Encode(imageBytes);
          imageBitmap = PdfBitmap.fromBase64String(imageBase64);
          logger.i(
              '通过Base64创建图片成功, 尺寸: ${imageBitmap.width} x ${imageBitmap.height}');
        } catch (e2) {
          logger.e('通过Base64创建PdfBitmap也失败: $e2');
          throw Exception('toolbox.watermark.createWatermarkImageFailed'.tr);
        }
      }

      // 应用缩放因子
      double imageWidth = imageBitmap.width.toDouble() * scale.value;
      double imageHeight = imageBitmap.height.toDouble() * scale.value;
      logger.i('应用缩放后图片尺寸: $imageWidth x $imageHeight, 缩放比例: ${scale.value}');

      // 为指定的页面添加水印
      for (int pageIndex in targetPages) {
        logger.i('处理页面: ${pageIndex + 1}');
        // 获取页面
        final PdfPage page = document.pages[pageIndex];

        // 获取页面图形上下文
        PdfGraphics graphics = page.graphics;
        final pageSize = page.size;
        logger.i('页面尺寸: ${pageSize.width} x ${pageSize.height}');

        // 简化处理 - 使用文档中推荐的方法添加单个水印
        // 保存图形状态
        PdfGraphicsState state = graphics.save();

        // 设置透明度
        graphics.setTransparency(opacity);

        // 计算水印位置（居中）
        double watermarkX = (pageSize.width - imageWidth) / 2 + xOffset.value;
        double watermarkY = (pageSize.height - imageHeight) / 2 + yOffset.value;

        if (numRows.value > 1 || spacing.value > 0) {
          // 多行水印模式 (网格布局)
          logger.i('使用网格布局添加水印, 行数: ${numRows.value}, 间距: ${spacing.value}');

          final double rotationRadian = rotate.value * pi / 180.0;
          // 空间计算
          final double spaceAlongRotation = imageWidth + spacing.value;
          final double spacePerpendicularToRotation =
              imageHeight + lineSpacing.value;

          // 计算页面对角线长度
          final double diagonalLength =
              sqrt(pow(pageSize.width, 2) + pow(pageSize.height, 2));

          int totalRows = numRows.value;
          int totalCols = spacing.value > 0
              ? (diagonalLength / spaceAlongRotation).ceil() * 2
              : 1;

          // 网格中心点
          final double centerX = pageSize.width / 2;
          final double centerY = pageSize.height / 2;

          // 绘制水印网格
          for (int row = 0; row < totalRows; row++) {
            for (int col = 0; col < totalCols; col++) {
              PdfGraphicsState gridState = graphics.save();

              // 计算偏移
              double rowOffset =
                  (row - totalRows / 2) * spacePerpendicularToRotation;
              double colOffset = (col - totalCols / 2) * spaceAlongRotation;

              // 交错布局
              if (row % 2 == 1 && totalRows > 1 && spacing.value > 0) {
                colOffset += spaceAlongRotation / 2;
              }

              // 计算旋转后坐标
              double offsetAlongRotation = colOffset;
              double offsetPerpendicularToRotation = rowOffset;

              // 计算水印中心点
              double watermarkCenterX = centerX +
                  offsetAlongRotation * cos(rotationRadian) -
                  offsetPerpendicularToRotation * sin(rotationRadian) +
                  xOffset.value;
              double watermarkCenterY = centerY +
                  offsetAlongRotation * sin(rotationRadian) +
                  offsetPerpendicularToRotation * cos(rotationRadian) +
                  yOffset.value;

              // 计算左上角坐标
              double imgX = watermarkCenterX - imageWidth / 2;
              double imgY = watermarkCenterY - imageHeight / 2;

              // 设置旋转
              graphics.translateTransform(watermarkCenterX, watermarkCenterY);
              graphics.rotateTransform(rotate.value);
              graphics.translateTransform(-watermarkCenterX, -watermarkCenterY);

              // 绘制图片
              graphics.drawImage(imageBitmap,
                  Rect.fromLTWH(imgX, imgY, imageWidth, imageHeight));

              graphics.restore(gridState);
            }
          }
        } else {
          // 单水印模式
          logger.i('使用单水印模式, 位置: $watermarkX, $watermarkY');

          // 设置旋转中心点为水印中心
          double centerX = watermarkX + imageWidth / 2;
          double centerY = watermarkY + imageHeight / 2;

          // 应用旋转
          graphics.translateTransform(centerX, centerY);
          graphics.rotateTransform(rotate.value);
          graphics.translateTransform(-centerX, -centerY);

          // 绘制图片
          graphics.drawImage(imageBitmap,
              Rect.fromLTWH(watermarkX, watermarkY, imageWidth, imageHeight));
        }

        // 恢复图形状态
        graphics.restore(state);
      }

      // 提前捕获可能的保存错误
      try {
        // 保存到输出文件
        final outputBytes = await document.save();
        await File(outputFilePath).writeAsBytes(outputBytes);
        logger.i('PDF保存成功: $outputFilePath, 文件大小: ${outputBytes.length} 字节');
      } catch (e) {
        logger.e('保存PDF文件失败: $e');
        throw Exception('toolbox.watermark.savePdfFailed'.tr);
      } finally {
        // 释放资源
        document.dispose();
      }
    } catch (e) {
      logger.e('添加图片水印失败: $e');
      throw Exception('watermark.error.addImageWatermarkFailed'.tr);
    }
  }

  /// 获取PDF字体系列
  Future<dynamic> getPdfFontFamily(String fontName) async {
    switch (fontName.toLowerCase()) {
      case 'sourcehansanssc':
        final ByteData fontData =
            await rootBundle.load('assets/fonts/SourceHanSansSC-Normal.ttf');
        return PdfTrueTypeFont(fontData.buffer.asUint8List(), fontSize.value);
      case 'helvetica':
        return PdfFontFamily.helvetica;
      case 'courier':
        return PdfFontFamily.courier;
      case 'times':
        return PdfFontFamily.timesRoman;
      case 'symbol':
        return PdfFontFamily.symbol;
      case 'zapfdingbats':
        return PdfFontFamily.zapfDingbats;
      default:
        return PdfFontFamily.helvetica;
    }
  }

  /// 计算旋转后的尺寸
  Size calculateRotatedSize(Size originalSize, double angleDegrees) {
    // 将角度转换为弧度
    double angleRadians = angleDegrees * (pi / 180.0);

    // 取角度的绝对值
    angleRadians = angleRadians.abs();

    // 计算旋转后的宽度和高度
    double rotatedWidth = originalSize.width * cos(angleRadians).abs() +
        originalSize.height * sin(angleRadians).abs();
    double rotatedHeight = originalSize.width * sin(angleRadians).abs() +
        originalSize.height * cos(angleRadians).abs();

    return Size(rotatedWidth, rotatedHeight);
  }

  /// 通过遮罩去除水印
  Future<void> removeWatermarkByMask({
    required String inputFilePath,
    required String outputFilePath,
  }) async {
    try {
      // 打开PDF文档
      final File inputFile = File(inputFilePath);
      if (!inputFile.existsSync()) {
        throw Exception('toolbox.watermark.fileNotExist'.tr);
      }

      // 读取PDF文档
      final PdfDocument document =
          PdfDocument(inputBytes: await inputFile.readAsBytes());

      // 解析水印页码范围
      List<int> watermarkPages = [];
      if (watermarkPage.value.isNotEmpty) {
        watermarkPages =
            parsePageRange(watermarkPage.value, document.pages.count);
      } else {
        // 如果未指定水印页码，默认使用第一页
        watermarkPages = [0];
      }

      logger.i('水印页: ${watermarkPages.map((p) => p + 1).join(", ")}');

      // 遍历所有水印页，获取指定索引的矩形注释信息
      Map<int, List<Rect>> pageAnnotationRects = {};
      Map<int, List<int>> annotationsToRemove = {};

      for (int pageIndex in watermarkPages) {
        if (pageIndex >= document.pages.count) {
          continue;
        }

        PdfPage page = document.pages[pageIndex];
        if (page.annotations.count <= 0) {
          continue;
        }

        // 收集该页上需要的注释位置信息和要删除的注释索引
        List<Rect> annotationRects = [];
        List<int> removeIndices = [];

        // 收集页面上所有矩形注释
        for (int i = 0; i < page.annotations.count; i++) {
          PdfAnnotation annotation = page.annotations[i];
          // 仅处理矩形注释
          if (annotation is PdfRectangleAnnotation) {
            annotationRects.add(annotation.bounds);
            removeIndices.add(i);
            logger.i('找到页 ${pageIndex + 1} 上的矩形注释 $i: ${annotation.bounds}');
          }
        }

        if (annotationRects.isNotEmpty) {
          pageAnnotationRects[pageIndex] = annotationRects;
          annotationsToRemove[pageIndex] = removeIndices;
        }
      }

      if (pageAnnotationRects.isEmpty) {
        // throw Exception('在指定页面上未找到有效的矩形注释');
        throw Exception('toolbox.watermark.noValidRectangleAnnotation'.tr);
      }

      // 根据expandMode确定要处理的目标页面
      List<int> targetPages = [];
      if (expandMode.value) {
        // 扩展模式：处理pageRange中指定的所有页面
        targetPages = parsePageRange(pageRange.value, document.pages.count);
        logger.i('扩展模式开启，目标处理页: ${targetPages.map((p) => p + 1).join(", ")}');
      } else {
        // 默认模式：仅处理有矩形注释的页面
        targetPages = pageAnnotationRects.keys.toList();
        logger.i('默认模式，仅处理有矩形注释的页面: ${targetPages.map((p) => p + 1).join(", ")}');
      }

      // 使用直接绘制的方法处理每个目标页面
      for (int pageIndex in targetPages) {
        if (pageIndex >= document.pages.count) {
          continue;
        }

        PdfPage page = document.pages[pageIndex];
        List<Rect> rectsToApply = [];

        // 确定要应用的矩形区域
        if (pageAnnotationRects.containsKey(pageIndex)) {
          // 如果是水印页本身，使用该页上的矩形
          rectsToApply = pageAnnotationRects[pageIndex]!;
        } else {
          // 如果是其他页，使用扩展模式下的所有水印页的矩形
          for (int refPageIndex in watermarkPages) {
            if (pageAnnotationRects.containsKey(refPageIndex)) {
              rectsToApply.addAll(pageAnnotationRects[refPageIndex]!);
            }
          }
        }

        if (rectsToApply.isEmpty) {
          logger.i('页 ${pageIndex + 1} 没有需要处理的矩形区域');
          continue;
        }

        // 获取页面的图形上下文，直接在页面上绘制白色矩形
        PdfGraphics graphics = page.graphics;

        // 创建白色填充画刷
        PdfSolidBrush whiteBrush = PdfSolidBrush(PdfColor(255, 255, 255));

        // 为每个矩形区域添加白色遮罩
        for (Rect rect in rectsToApply) {
          // 直接绘制白色矩形
          graphics.drawRectangle(
            brush: whiteBrush,
            bounds: rect,
          );
          logger.i('页 ${pageIndex + 1} 添加矩形遮罩: $rect');
        }
      }

      // 删除所有用于标记的矩形注释
      for (int pageIndex in annotationsToRemove.keys) {
        PdfPage page = document.pages[pageIndex];
        // 必须从后向前删除，避免索引变化导致删除错误
        List<int> indices = annotationsToRemove[pageIndex]!;
        indices.sort((a, b) => b.compareTo(a)); // 从大到小排序

        for (int index in indices) {
          if (index < page.annotations.count) {
            page.annotations.remove(page.annotations[index]);
            logger.i('从页 ${pageIndex + 1} 删除了矩形注释 $index');
          }
        }
      }

      // 保存处理后的文档
      try {
        logger.i('准备保存处理后的文档...');
        final outputBytes = await document.save();
        logger.i('文档已保存到内存，大小: ${outputBytes.length} 字节');
        await File(outputFilePath).writeAsBytes(outputBytes);
        logger.i('PDF保存成功: $outputFilePath, 文件大小: ${outputBytes.length} 字节');
      } catch (e) {
        logger.e('保存PDF文件失败: $e');
        throw Exception("$e");
      } finally {
        document.dispose();
      }
    } catch (e) {
      logger.e('通过遮罩去除水印失败: $e');
      throw Exception("$e");
    }
  }

  /// 通过类型检测水印
  Future<String> detectWatermarkByType({
    required String inputFilePath,
    required String outputPath,
  }) async {
    try {
      logger.i('开始检测水印: $inputFilePath, 页码: ${watermarkPage.value}');

      final data = {
        "input_path": inputFilePath,
        "page_range":
            watermarkPage.value.isNotEmpty ? watermarkPage.value : '1',
        "watermark_type": "type", // 指定水印类型为默认类型
        "wm_index": <String>[], // 空数组，检测阶段不需要索引
        "output_path": outputPath, // 使用传入的输出路径
      };

      final response =
          await messageController.request(data, "pdf/detect_watermark");

      if (response.status == "success") {
        logger.i('水印检测成功: ${response.data}');
        return response.data;
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('水印检测失败: $e');
      throw Exception("$e");
    }
  }

  /// 通过类型去除水印
  Future<void> removeWatermarkByType({
    required String inputFilePath,
    required String outputFilePath,
    required String pageRange,
    required List<int> watermarkIndices,
  }) async {
    try {
      logger.i('开始去除水印: $inputFilePath -> $outputFilePath');
      logger.i('页码范围: $pageRange, 水印索引: $watermarkIndices');

      final data = {
        "input_path": inputFilePath,
        "page_range": pageRange,
        "watermark_type": "type", // 指定水印类型为默认类型
        "wm_index": watermarkIndices.map((i) => i.toString()).toList(),
        "output_path": outputFilePath,
      };

      final response =
          await messageController.request(data, "pdf/remove_watermark");

      if (response.status == "success") {
        logger.i('水印去除成功');
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('水印去除失败: $e');
      throw Exception("$e");
    }
  }

  /// 重置检测状态
  void resetDetectionState() {
    watermarkIndex.value = '';
  }

  /// 主提交函数 - 根据选中的标签页路由到相应的处理函数
  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.fileSelect.error'.tr,
        type: "error",
      );
      return;
    }

    if (tabController.selected == "add") {
      await submitAddWatermark(context);
    } else if (tabController.selected == "remove") {
      await submitRemoveWatermark(context);
    }
  }

  /// 处理添加水印的提交逻辑
  Future<void> submitAddWatermark(BuildContext context) async {
    // 记录当前参数
    logger.i("水印类型: ${watermarkType.value}");
    if (watermarkType.value == 'image') {
      logger.i("水印图片路径: ${watermarkImage.value}");
      logger.i("缩放比例: ${scale.value}");

      // 验证图片路径
      if (watermarkImage.value.isEmpty) {
        showToastNotification(
          context,
          'toolbox.common.failure'.tr,
          'toolbox.watermark.watermarkImageRequired'.tr,
          type: "error",
        );
        return;
      }

      final imageFile = File(watermarkImage.value);
      if (!imageFile.existsSync()) {
        showToastNotification(
          context,
          'toolbox.common.failure'.tr,
          'toolbox.watermark.watermarkImageNotExist'.tr,
          type: "error",
        );
        return;
      }
    } else {
      logger.i("${'watermark.log.watermarkText'.tr}: ${watermarkText.value}");
      logger.i("${'watermark.log.font'.tr}: ${fontName.value}");
      logger.i("${'watermark.log.fontSize'.tr}: ${fontSize.value}");

      // 验证文本
      if (watermarkText.value.isEmpty) {
        showToastNotification(
          context,
          'toolbox.common.failure'.tr,
          'toolbox.watermark.watermarkTextRequired'.tr,
          type: "error",
        );
        return;
      }
    }

    logger.i("透明度: ${opacity.value}");
    logger.i("旋转角度: ${rotate.value}");
    logger.i("行数: ${numRows.value}");
    logger.i("间距: ${spacing.value}");
    logger.i("行间距: ${lineSpacing.value}");

    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        logger.i("处理文件: $inputFile");

        // 确定输出文件路径
        final pathUtils = PathUtils(inputFile);
        String outputFilePath = await pathUtils.convertPath(
          outputMode.value,
          stem_append: 'watermark.filename.watermarkAdded'.tr,
          outputDir: outputDir.value,
        );
        logger.i("输出文件路径: $outputFilePath");

        // 根据水印类型执行相应操作
        if (watermarkType.value == 'text') {
          await addTextWatermark(
              inputFilePath: inputFile,
              outputFilePath: outputFilePath,
              text: watermarkText.value,
              opacity: opacity.value,
              rotationAngle: rotate.value,
              fontSize: fontSize.value,
              hexColor: fontColor.value.hex);
        } else {
          await addImageWatermark(
              inputFilePath: inputFile,
              outputFilePath: outputFilePath,
              imagePath: watermarkImage.value,
              opacity: opacity.value);
        }

        // 更新进度
        progressController.outputPath.value = outputFilePath;
        progressController.updateProgress(
            status: "processing",
            current: (i + 1).toDouble(),
            total: selectedFilePaths.length.toDouble(),
            message:
                "${'toolbox.common.process.running'.tr}: ${pathUtils.name}");
      }
      progressController.updateProgress(
      status: "completed",
      message: 'toolbox.common.process.completed'.tr,
      current: 100.0,
      total: 100.0,
    );
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
      status: "error",
      message: "${'toolbox.common.process.failed'.tr}: $e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  /// 处理去除水印的提交逻辑
  Future<void> submitRemoveWatermark(BuildContext context) async {
    // 记录当前参数
    logger.i("水印去除类型: ${watermarkRemoveType.value}");
    logger.i("水印所在页: ${watermarkPage.value}");


    // 统一的水印去除类型处理
    switch (watermarkRemoveType.value) {
      case 'mask':
        await processWatermarkRemovalByMask(context);
        break;
      case 'type':
        await processWatermarkRemovalByType(context);
        break;
      case 'content':
        await processWatermarkRemovalByContent(context);
        break;
      case 'image':
        await processWatermarkRemovalByImage(context);
        break;
      case 'edit_text':
        await processWatermarkRemovalByEditText(context);
        break;
      case 'path':
        await processWatermarkRemovalByPath(context);
        break;
      case 'pixel':
        await processWatermarkRemovalByPixel(context);
        break;
      default:
        throw Exception('toolbox.watermark.notSupport'.tr);
    }
  }

  /// 处理遮罩水印去除
  Future<void> processWatermarkRemovalByMask(BuildContext context) async {
    await executeDirectWatermarkRemoval(context);
  }

  /// 处理类型水印去除的分步骤逻辑
  Future<void> processWatermarkRemovalByType(BuildContext context) async {
    if (step.value == "step1") {
          // 验证输入
    if (watermarkPage.value.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.watermark.watermarkPageRequired'.tr,
        type: "error",
      );
      return;
    }
      // 步骤1: 检测水印索引
      await executeWatermarkDetection(context);
    } else if (step.value == "step2") {
      // 步骤2: 执行水印去除
      await executeWatermarkRemoval(context);
    }
  }

  /// 处理内容水印去除
  Future<void> processWatermarkRemovalByContent(BuildContext context) async {
    if (step.value == "step1") {
          // 验证输入
    if (watermarkPage.value.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.watermark.watermarkPageRequired'.tr,
        type: "error",
      );
      return;
    }
      // 步骤1: 检测内容水印索引
      await executeWatermarkDetectionByContent(context);
    } else if (step.value == "step2") {
      // 步骤2: 执行内容水印去除
      await executeWatermarkRemovalByContent(context);
    }
  }

  /// 处理图片水印去除
  Future<void> processWatermarkRemovalByImage(BuildContext context) async {
    if (step.value == "step1") {
          // 验证输入
    if (watermarkPage.value.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.watermark.watermarkPageRequired'.tr,
        type: "error",
      );
      return;
    }
      // 步骤1: 检测图片水印索引
      await executeWatermarkDetectionByImage(context);
    } else if (step.value == "step2") {
      // 步骤2: 执行图片水印去除
      await executeWatermarkRemovalByImage(context);
    }
  }

  /// 处理可编辑文字水印去除
  Future<void> processWatermarkRemovalByEditText(BuildContext context) async {
    // 可编辑文字水印直接去除，不需要检测步骤
    await executeWatermarkRemovalByEditText(context);
  }

  /// 处理路径水印去除
  Future<void> processWatermarkRemovalByPath(BuildContext context) async {
    if (step.value == "step1") {
          // 验证输入
    if (watermarkPage.value.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.watermark.watermarkPageRequired'.tr,
        type: "error",
      );
      return;
    }
      // 步骤1: 检测路径水印索引
      await executeWatermarkDetectionByPath(context);
    } else if (step.value == "step2") {
      // 步骤2: 执行路径水印去除
      await executeWatermarkRemovalByPath(context);
    }
  }

  /// 处理像素水印去除
  Future<void> processWatermarkRemovalByPixel(BuildContext context) async {
    if (step.value == "step1") {
          // 验证输入
    if (watermarkPage.value.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.watermark.watermarkPageRequired'.tr,
        type: "error",
      );
      return;
    }
      // 步骤1: 检测像素水印索引
      await executeWatermarkDetectionByPixel(context);
    } else if (step.value == "step2") {
      // 步骤2: 执行像素水印去除
      await executeWatermarkRemovalByPixel(context);
    }
  }

  /// 执行水印检测（步骤1）
  Future<void> executeWatermarkDetection(BuildContext context) async {
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 只处理第一个文件进行检测
      if (selectedFilePaths.isNotEmpty) {
        final inputFile = selectedFilePaths.first;
        logger.i("检测文件: $inputFile");

        // 为检测操作生成临时输出路径
        final pathUtils = PathUtils(inputFile);
        final tempOutputPath = await pathUtils.convertPath(outputMode.value,
            stem_append: 'watermark.filename.watermarkIndexDetected'.tr, outputDir: outputDir.value);

        // 执行检测 - 仅调用原生函数，不解析结果
        await detectWatermarkByType(
          inputFilePath: inputFile,
          outputPath: tempOutputPath,
        );

        // 设置输出路径以便在进度对话框中显示
        if (selectedFilePaths.isNotEmpty) {
          final pathUtils = PathUtils(selectedFilePaths.first);
          final tempOutputPath = await pathUtils.convertPath(outputMode.value,
              stem_append: 'watermark.filename.watermarkIndexDetected'.tr, outputDir: outputDir.value);
          progressController.outputPath.value = tempOutputPath;
        }
        progressController.updateProgress(
      status: "completed",
      message: 'watermark.completion.detectionCompleted'.tr,
      current: 100.0,
      total: 100.0,
    );
      }
    } catch (e) {
      logger.e("水印检测失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "${'toolbox.watermark.detectWatermarkFailed'.tr}: $e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  /// 执行水印去除（步骤2）
  Future<void> executeWatermarkRemoval(BuildContext context) async {
    // 获取用户输入的水印索引
    List<int> watermarkIndices = [];
    if (watermarkIndex.value.isNotEmpty) {
      try {
        watermarkIndices = watermarkIndex.value
            .split(',')
            .where((s) => s.trim().isNotEmpty)
            .map((s) => int.parse(s.trim()))
            .toList();
      } catch (e) {
        showToastNotification(
          context,
          'toolbox.common.failure'.tr,
          'toolbox.watermark.invalidWatermarkIndex'.tr,
          type: "error",
        );
        return;
      }
    }

    if (watermarkIndices.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.watermark.watermarkIndexRequired'.tr,
        type: "error",
      );
      return;
    }

    await executeDirectWatermarkRemoval(context,
        watermarkIndices: watermarkIndices);
  }

  /// 直接执行水印去除（用于非type类型或type类型的步骤2）
  Future<void> executeDirectWatermarkRemoval(BuildContext context,
      {List<int>? watermarkIndices}) async {
    // 检查桌面环境
    if (!PathUtils.isDesktop) {
      showToastNotification(context, 'toolbox.common.failure'.tr,
          'toolbox.watermark.desktopOnlyFeature'.tr,
          type: "error");
      return;
    }

    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        logger.i("处理文件: $inputFile");

        // 确定输出文件路径
        final pathUtils = PathUtils(inputFile);
        String outputFilePath = await pathUtils.convertPath(
          outputMode.value,
          stem_append: 'watermark.filename.watermarkRemoved'.tr,
          outputDir: outputDir.value,
        );
        logger.i("输出文件路径: $outputFilePath");

        // 根据去水印类型执行相应操作
        if (watermarkRemoveType.value == 'type' && watermarkIndices != null) {
          // 使用指定的水印索引执行type类型去除
          String pageRangeStr =
              pageRange.value.isNotEmpty ? pageRange.value : 'all';

          await removeWatermarkByType(
            inputFilePath: inputFile,
            outputFilePath: outputFilePath,
            pageRange: pageRangeStr,
            watermarkIndices: watermarkIndices,
          );
        } else {
          // 其他类型的水印去除（目前只支持mask类型）
          if (watermarkRemoveType.value == 'mask') {
            await removeWatermarkByMask(
              inputFilePath: inputFile,
              outputFilePath: outputFilePath,
            );
          } else {
            throw Exception('toolbox.watermark.notSupport'.tr);
          }
        }

        // 更新进度
        progressController.updateProgress(
            status: "processing",
            current: (i + 1).toDouble(),
            total: selectedFilePaths.length.toDouble(),
            message: "${'toolbox.common.processing'.tr}: ${pathUtils.name}");
        progressController.outputPath.value = outputFilePath;
      }

      progressController.updateProgress(
      status: "completed",
      message: 'toolbox.common.process.completed'.tr,
      current: 100.0,
      total: 100.0,
    );
    } catch (e) {
      logger.e("水印去除失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "${'toolbox.common.process.failed'.tr}: $e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  // ============================================================================
  // 内容水印检测和去除方法
  // ============================================================================

  /// 通过内容检测水印
  Future<String> detectWatermarkByContent({
    required String inputFilePath,
    required String outputPath,
  }) async {
    try {
      logger.i('开始检测内容水印: $inputFilePath, 页码: ${watermarkPage.value}');

      final data = {
        "input_path": inputFilePath,
        "page_range":
            watermarkPage.value.isNotEmpty ? watermarkPage.value : '1',
        "watermark_type": "content", // 指定水印类型
        "wm_index": <String>[], // 空数组，检测阶段不需要索引
        "output_path": outputPath, // 使用传入的输出路径
      };

      final response =
          await messageController.request(data, "pdf/detect_watermark");

      if (response.status == "success") {
        logger.i('内容水印检测成功: ${response.data}');
        return response.data;
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('内容水印检测失败: $e');
      throw Exception('toolbox.watermark.detectWatermarkFailed'.tr);
    }
  }

  /// 通过内容去除水印
  Future<void> removeWatermarkByContent({
    required String inputFilePath,
    required String outputFilePath,
    required String pageRange,
    required List<int> watermarkIndices,
  }) async {
    try {
      logger.i('开始去除内容水印: $inputFilePath -> $outputFilePath');
      logger.i('页码范围: $pageRange, 水印索引: $watermarkIndices');

      final data = {
        "input_path": inputFilePath,
        "page_range": pageRange,
        "watermark_type": "content", // 指定水印类型
        "wm_index": watermarkIndices.map((i) => i.toString()).toList(),
        "output_path": outputFilePath,
      };

      final response =
          await messageController.request(data, "pdf/remove_watermark");

      if (response.status == "success") {
        logger.i('内容水印去除成功');
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('内容水印去除失败: $e');
      throw Exception('toolbox.watermark.removeWatermarkFailed'.tr);
    }
  }

  /// 执行内容水印检测（步骤1）
  Future<void> executeWatermarkDetectionByContent(BuildContext context) async {
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        final pathUtils = PathUtils(inputFile);
        logger.i("检测内容水印文件: $inputFile");

        // 为检测操作生成临时输出路径
        final tempOutputPath = await pathUtils.convertPath(outputMode.value,
            stem_append: "_识别水印索引", outputDir: outputDir.value);

        // 执行检测
        await detectWatermarkByContent(
          inputFilePath: inputFile,
          outputPath: tempOutputPath,
        );

        // 更新进度
        progressController.updateProgress(
            status: "processing",
            current: (i + 1).toDouble(),
            total: selectedFilePaths.length.toDouble(),
            message: "${'watermark.progress.detectingContentWatermark'.tr}: ${pathUtils.name}");
      }

      // 检测完成
      // 设置输出路径以便在进度对话框中显示
      if (selectedFilePaths.isNotEmpty) {
        final pathUtils = PathUtils(selectedFilePaths.first);
        final tempOutputPath = await pathUtils.convertPath(outputMode.value,
            stem_append: "_识别水印索引", outputDir: outputDir.value);

        progressController.outputPath.value = tempOutputPath;
      }
      progressController.updateProgress(
      status: "completed",
      message: 'watermark.completion.detectionCompleted'.tr,
      current: 100.0,
      total: 100.0,
    );
    } catch (e) {
      logger.e("内容水印检测失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "${'watermark.error.contentWatermarkDetectionFailed'.tr}: $e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  /// 执行内容水印去除（步骤2）
  Future<void> executeWatermarkRemovalByContent(BuildContext context) async {
    // 获取用户输入的水印索引
    List<int> watermarkIndices = [];
    if (watermarkIndex.value.isNotEmpty) {
      try {
        watermarkIndices = watermarkIndex.value
            .split(',')
            .where((s) => s.trim().isNotEmpty)
            .map((s) => int.parse(s.trim()))
            .toList();
      } catch (e) {
        // 处理解析错误
        watermarkIndices = [];
      }
    }
    await executeDirectWatermarkRemovalByContent(context,
        watermarkIndices: watermarkIndices);
  }

  /// 直接执行内容水印去除
  Future<void> executeDirectWatermarkRemovalByContent(BuildContext context,
      {List<int>? watermarkIndices}) async {
    // 检查桌面环境
    if (!PathUtils.isDesktop) {
      showToastNotification(context, 'toolbox.common.failure'.tr,
          'toolbox.watermark.desktopOnlyFeature'.tr,
          type: "error");
      return;
    }

    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        logger.i("处理内容水印文件: $inputFile");

        // 确定输出文件路径
        final pathUtils = PathUtils(inputFile);
        final outputFilePath = await pathUtils.convertPath(
          outputMode.value,
          stem_append: "_去水印版",
          outputDir: outputDir.value,
        );

        logger.i("输出文件路径: $outputFilePath");

        // 使用用户指定的页码范围（跳过步骤1参数验证）
        String pageRangeStr =
            pageRange.value.isNotEmpty ? pageRange.value : 'all';

        await removeWatermarkByContent(
          inputFilePath: inputFile,
          outputFilePath: outputFilePath,
          pageRange: pageRangeStr,
          watermarkIndices: watermarkIndices ?? [],
        );

        // 更新进度
        progressController.updateProgress(
            status: "processing",
            current: (i + 1).toDouble(),
            total: selectedFilePaths.length.toDouble(),
            message: "${'watermark.progress.processingContentWatermark'.tr}: ${pathUtils.name}");
        progressController.outputPath.value = outputFilePath;
      }

      progressController.updateProgress(
      status: "completed",
      message: 'toolbox.common.process.completed'.tr,
      current: 100.0,
      total: 100.0,
    );
    } catch (e) {
      logger.e("内容水印去除失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "${'toolbox.common.process.failed'.tr}: $e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  // ============================================================================
  // 图片水印检测和去除方法
  // ============================================================================

  /// 通过图片检测水印
  Future<String> detectWatermarkByImage({
    required String inputFilePath,
    required String outputPath,
  }) async {
    try {
      logger.i('开始检测图片水印: $inputFilePath, 页码: ${watermarkPage.value}');

      final data = {
        "input_path": inputFilePath,
        "page_range":
            watermarkPage.value.isNotEmpty ? watermarkPage.value : '1',
        "watermark_type": "image", // 指定水印类型
        "wm_index": <String>[], // 空数组，检测阶段不需要索引
        "output_path": outputPath, // 使用传入的输出路径
      };

      final response =
          await messageController.request(data, "pdf/detect_watermark");

      if (response.status == "success") {
        logger.i('图片水印检测成功: ${response.data}');
        return response.data;
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('图片水印检测失败: $e');
      throw Exception("$e");
    }
  }

  /// 通过图片去除水印
  Future<void> removeWatermarkByImage({
    required String inputFilePath,
    required String outputFilePath,
    required String pageRange,
    required List<int> watermarkIndices,
  }) async {
    try {
      logger.i('开始去除图片水印: $inputFilePath -> $outputFilePath');
      logger.i('页码范围: $pageRange, 水印索引: $watermarkIndices');

      final data = {
        "input_path": inputFilePath,
        "page_range": pageRange,
        "watermark_type": "image", // 指定水印类型
        "wm_index": watermarkIndices.map((i) => i.toString()).toList(),
        "output_path": outputFilePath,
      };

      final response =
          await messageController.request(data, "pdf/remove_watermark");

      if (response.status == "success") {
        logger.i('图片水印去除成功');
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('图片水印去除失败: $e');
      throw Exception("$e");
    }
  }

  /// 执行图片水印检测（步骤1）
  Future<void> executeWatermarkDetectionByImage(BuildContext context) async {
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        final pathUtils = PathUtils(inputFile);
        logger.i("检测图片水印文件: $inputFile");

        // 为检测操作生成临时输出路径
        final tempOutputPath = await pathUtils.convertPath(outputMode.value,
            stem_append: "_识别水印索引", suffix: "", outputDir: outputDir.value);

        // 执行检测
        await detectWatermarkByImage(
          inputFilePath: inputFile,
          outputPath: tempOutputPath,
        );

        // 更新进度
        progressController.updateProgress(
            status: "processing",
            current: (i + 1).toDouble(),
            total: selectedFilePaths.length.toDouble(),
            message: "${'watermark.progress.detectingImageWatermark'.tr}: ${pathUtils.name}");
      }

      // 检测完成
      // 设置输出路径以便在进度对话框中显示
      if (selectedFilePaths.isNotEmpty) {
        final pathUtils = PathUtils(selectedFilePaths.first);
        final tempOutputPath = await pathUtils.convertPath(outputMode.value,
            stem_append: "_识别水印索引", suffix: "", outputDir: outputDir.value);

        progressController.outputPath.value = tempOutputPath;
      }
      progressController.updateProgress(
      status: "completed",
      message: 'watermark.completion.imageWatermarkDetectionCompleted'.tr,
      current: 100.0,
      total: 100.0,
    );
    } catch (e) {
      logger.e("图片水印检测失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "${'watermark.error.imageWatermarkDetectionFailed'.tr}: $e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  /// 执行图片水印去除（步骤2）
  Future<void> executeWatermarkRemovalByImage(BuildContext context) async {
    // 获取用户输入的水印索引
    List<int> watermarkIndices = [];
    if (watermarkIndex.value.isNotEmpty) {
      try {
        watermarkIndices = watermarkIndex.value
            .split(',')
            .where((s) => s.trim().isNotEmpty)
            .map((s) => int.parse(s.trim()))
            .toList();
      } catch (e) {
        // 处理解析错误
        watermarkIndices = [];
      }
    }
    await executeDirectWatermarkRemovalByImage(context,
        watermarkIndices: watermarkIndices);
  }

  /// 直接执行图片水印去除
  Future<void> executeDirectWatermarkRemovalByImage(BuildContext context,
      {List<int>? watermarkIndices}) async {
    // 检查桌面环境
    if (!PathUtils.isDesktop) {
      showToastNotification(context, 'toolbox.common.failure'.tr,
          'toolbox.watermark.desktopOnlyFeature'.tr,
          type: "error");
      return;
    }

    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        logger.i("处理图片水印文件: $inputFile");

        // 确定输出文件路径
        final pathUtils = PathUtils(inputFile);
        final outputFilePath = await pathUtils.convertPath(
          outputMode.value,
          stem_append: "_去水印版",
          outputDir: outputDir.value,
        );

        logger.i("输出文件路径: $outputFilePath");

        // 使用用户指定的页码范围（跳过步骤1参数验证）
        String pageRangeStr =
            pageRange.value.isNotEmpty ? pageRange.value : 'all';

        await removeWatermarkByImage(
          inputFilePath: inputFile,
          outputFilePath: outputFilePath,
          pageRange: pageRangeStr,
          watermarkIndices: watermarkIndices ?? [],
        );

        // 更新进度
        progressController.updateProgress(
            status: "processing",
            current: (i + 1).toDouble(),
            total: selectedFilePaths.length.toDouble(),
            message: "${'watermark.progress.processingImageWatermark'.tr}: ${pathUtils.name}");
        progressController.outputPath.value = outputFilePath;
      }

      progressController.updateProgress(
      status: "completed",
      message: 'toolbox.common.process.completed'.tr,
      current: 100.0,
      total: 100.0,
    );
    } catch (e) {
      logger.e("图片水印去除失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "${'toolbox.common.process.failed'.tr}: $e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  // ============================================================================
  // 可编辑文字水印去除方法
  // ============================================================================

  /// 通过可编辑文字去除水印
  Future<void> removeWatermarkByEditText({
    required String inputFilePath,
    required String outputFilePath,
    required String pageRange,
    required String watermarkText,
  }) async {
    try {
      logger.i('开始去除可编辑文字水印: $inputFilePath -> $outputFilePath');
      logger.i('页码范围: $pageRange, 水印文字: $watermarkText');

      final data = {
        "input_path": inputFilePath,
        "page_range": pageRange,
        "watermark_type": "text", // 指定水印类型
        "wm_text": watermarkText,
        "output_path": outputFilePath,
      };

      final response =
          await messageController.request(data, "pdf/remove_watermark");

      if (response.status == "success") {
        logger.i('可编辑文字水印去除成功');
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('可编辑文字水印去除失败: $e');
      throw Exception('toolbox.watermark.removeWatermarkFailed'.tr);
    }
  }

  /// 执行可编辑文字水印去除
  Future<void> executeWatermarkRemovalByEditText(BuildContext context) async {
    // 检查桌面环境
    if (!PathUtils.isDesktop) {
      showToastNotification(context, 'toolbox.common.failure'.tr,
          'toolbox.watermark.desktopOnlyFeature'.tr,
          type: "error");
      return;
    }

    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        logger.i("处理可编辑文字水印文件: $inputFile");

        // 确定输出文件路径
        final pathUtils = PathUtils(inputFile);
        final outputFilePath = await pathUtils.convertPath(
          outputMode.value,
          stem_append: "_去水印版",
          outputDir: outputDir.value,
        );

        logger.i("输出文件路径: $outputFilePath");

        // 使用用户指定的页码范围（跳过步骤1参数验证）
        String pageRangeStr =
            pageRange.value.isNotEmpty ? pageRange.value : 'all';

        // 获取水印文字（这里需要从UI获取，暂时使用默认值）
        String watermarkText = watermarkTextToRemove.value.isNotEmpty
            ? watermarkTextToRemove.value
            : 'watermark.default.watermarkText'.tr; // 默认水印文字

        await removeWatermarkByEditText(
          inputFilePath: inputFile,
          outputFilePath: outputFilePath,
          pageRange: pageRangeStr,
          watermarkText: watermarkText,
        );

        // 更新进度
        progressController.updateProgress(
            status: "processing",
            current: (i + 1).toDouble(),
            total: selectedFilePaths.length.toDouble(),
            message: "${'watermark.progress.processingEditableTextWatermark'.tr}: ${pathUtils.name}");
        progressController.outputPath.value = outputFilePath;
      }

      progressController.updateProgress(
      status: "completed",
      message: 'toolbox.common.process.completed'.tr,
      current: 100.0,
      total: 100.0,
    );
    } catch (e) {
      logger.e("可编辑文字水印去除失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "${'toolbox.common.process.failed'.tr}: $e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  // ============================================================================
  // 路径水印检测和去除方法
  // ============================================================================

  /// 通过路径检测水印
  Future<String> detectWatermarkByPath({
    required String inputFilePath,
    required String outputPath,
  }) async {
    try {
      logger.i('开始检测路径水印: $inputFilePath, 页码: ${watermarkPage.value}');

      final data = {
        "input_path": inputFilePath,
        "page_range":
            watermarkPage.value.isNotEmpty ? watermarkPage.value : '1',
        "watermark_type": "path", // 指定水印类型
        "wm_index": <String>[], // 空数组，检测阶段不需要索引
        "output_path": outputPath, // 使用传入的输出路径
      };

      final response =
          await messageController.request(data, "pdf/detect_watermark");

      if (response.status == "success") {
        logger.i('路径水印检测成功: ${response.data}');
        return response.data;
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('路径水印检测失败: $e');
      throw Exception("$e");
    }
  }

  // ============================================================================
  // 像素水印检测和去除方法
  // ============================================================================

  /// 通过像素检测水印
  Future<String> detectWatermarkByPixel({
    required String inputFilePath,
    required String outputPath,
  }) async {
    try {
      logger.i('开始检测像素水印: $inputFilePath, 页码: ${watermarkPage.value}');

      // 像素水印检测使用PDF转图片的方式进行分析
      final data = {
        "path": inputFilePath,
        "output_path": outputPath,
        "output_format": "png", // 使用PNG格式以保持图像质量
        "page_range": watermarkPage.value.isNotEmpty ? watermarkPage.value : '1',
        "dpi": 300, // 高分辨率以便进行像素级分析
        "is_gray": true, // 保持彩色以便分析颜色信息
        "show_progress": false, // 不显示进度对话框，因为这是检测步骤
      };

      final response =
          await messageController.request(data, "pdf_to_image");

      if (response.status == "success") {
        logger.i('像素水印检测成功: ${response.data}');
        // 返回转换后的图片信息，用户可以查看这些图片来识别水印
        return response.data;
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('像素水印检测失败: $e');
      // 保留原始错误信息以便用户了解具体问题
      throw Exception("$e");
    }
  }

  /// 通过路径去除水印
  Future<void> removeWatermarkByPath({
    required String inputFilePath,
    required String outputFilePath,
    required String pageRange,
    required String watermarkIndices,
  }) async {
    try {
      logger.i('开始去除路径水印: $inputFilePath -> $outputFilePath');
      logger.i('页码范围: $pageRange, 水印索引: $watermarkIndices');

      final data = {
        "input_path": inputFilePath,
        "page_range": pageRange,
        "watermark_type": "path", // 指定水印类型
        "wm_index_str": watermarkIndices,
        "output_path": outputFilePath,
      };

      final response =
          await messageController.request(data, "pdf/remove_watermark");

      if (response.status == "success") {
        logger.i('路径水印去除成功');
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('路径水印去除失败: $e');
      throw Exception("$e");
    }
  }

  /// 执行路径水印检测（步骤1）
  Future<void> executeWatermarkDetectionByPath(BuildContext context) async {
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        final pathUtils = PathUtils(inputFile);
        logger.i("检测路径水印文件: $inputFile");

        // 为检测操作生成临时输出路径
        final detectionPathUtils = PathUtils(inputFile);
        final tempOutputPath = await detectionPathUtils.convertPath(
            outputMode.value,
            stem_append: "_识别水印索引",
            outputDir: outputDir.value);

        // 执行检测
        await detectWatermarkByPath(
          inputFilePath: inputFile,
          outputPath: tempOutputPath,
        );

        // 更新进度
        progressController.updateProgress(
            status: "processing",
            current: (i + 1).toDouble(),
            total: selectedFilePaths.length.toDouble(),
            message: "${'watermark.progress.detectingPathWatermark'.tr}: ${pathUtils.name}");
      }

      // 检测完成
      // 设置输出路径以便在进度对话框中显示
      if (selectedFilePaths.isNotEmpty) {
        final pathUtils = PathUtils(selectedFilePaths.first);
        final tempOutputPath = await pathUtils.convertPath(outputMode.value,
            stem_append: "_识别水印索引", outputDir: outputDir.value);

        progressController.outputPath.value = tempOutputPath;
      }
      progressController.updateProgress(
      status: "completed",
      message: 'watermark.completion.pathWatermarkDetectionCompleted'.tr,
      current: 100.0,
      total: 100.0,
    );
    } catch (e) {
      logger.e("路径水印检测失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "$e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  /// 执行路径水印去除（步骤2）
  Future<void> executeWatermarkRemovalByPath(BuildContext context) async {
    await executeDirectWatermarkRemovalByPath(context);
  }

  /// 执行像素水印检测（步骤1）
  Future<void> executeWatermarkDetectionByPixel(BuildContext context) async {
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        final pathUtils = PathUtils(inputFile);
        logger.i("检测像素水印文件: $inputFile");

        // 为检测操作生成临时输出路径
        final tempOutputPath = await pathUtils.convertPath(outputMode.value,
            stem_append: "", suffix: "", outputDir: outputDir.value);

        // 执行检测
        await detectWatermarkByPixel(
          inputFilePath: inputFile,
          outputPath: tempOutputPath,
        );

        // 更新进度
        progressController.updateProgress(
            status: "processing",
            current: (i + 1).toDouble(),
            total: selectedFilePaths.length.toDouble(),
            message: "${'watermark.progress.convertingPdfToImage'.tr}: ${pathUtils.name}");
      }

      // 检测完成
      // 设置输出路径以便在进度对话框中显示
      if (selectedFilePaths.isNotEmpty) {
        final pathUtils = PathUtils(selectedFilePaths.first);
        final tempOutputPath = await pathUtils.convertPath(outputMode.value,
            stem_append: "", outputDir: outputDir.value);

        progressController.outputPath.value = tempOutputPath;
      }
      progressController.updateProgress(
      status: "completed",
      message: 'watermark.completion.pdfToImageCompleted'.tr,
      current: 100.0,
      total: 100.0,
    );
    } catch (e) {
      logger.e("像素水印检测失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "$e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  /// 直接执行路径水印去除
  Future<void> executeDirectWatermarkRemovalByPath(BuildContext context) async {
    // 检查桌面环境
    if (!PathUtils.isDesktop) {
      showToastNotification(context, 'toolbox.common.failure'.tr,
          'toolbox.watermark.desktopOnlyFeature'.tr,
          type: "error");
      return;
    }

    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        logger.i("处理路径水印文件: $inputFile");

        // 确定输出文件路径
        final pathUtils = PathUtils(inputFile);
        final outputFilePath = await pathUtils.convertPath(
          outputMode.value,
          stem_append: "_去水印版",
          outputDir: outputDir.value,
        );

        logger.i("输出文件路径: $outputFilePath");

        // 使用用户指定的页码范围（跳过步骤1参数验证）
        String pageRangeStr =
            pageRange.value.isNotEmpty ? pageRange.value : 'all';

        // 将水印索引转换为字符串格式（从watermarkIndex获取）
        String watermarkIndicesStr = watermarkIndex.value.isNotEmpty
            ? watermarkIndex.value
            : "1"; // 默认第一个索引

        await removeWatermarkByPath(
          inputFilePath: inputFile,
          outputFilePath: outputFilePath,
          pageRange: pageRangeStr,
          watermarkIndices: watermarkIndicesStr,
        );

        // 更新进度
        progressController.updateProgress(
            status: "processing",
            current: (i + 1).toDouble(),
            total: selectedFilePaths.length.toDouble(),
            message: "${'watermark.progress.processingPathWatermark'.tr}: ${pathUtils.name}");
        progressController.outputPath.value = outputFilePath;
      }

      progressController.updateProgress(
      status: "completed",
      message: 'toolbox.common.process.completed'.tr,
      current: 100.0,
      total: 100.0,
    );
    } catch (e) {
      logger.e("路径水印去除失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "${'toolbox.common.process.failed'.tr}: $e",
      current: 0.0,
      total: 100.0,
    );
    }
  }

  // ============================================================================
  // 像素水印去除方法
  // ============================================================================

  /// 通过像素去除水印
  Future<void> removeWatermarkByPixel({
    required String inputFilePath,
    required String outputFilePath,
    required String pageRange,
    int thresholdBelow = 128,
    int thresholdHigh = 255,
    String thresholdTarget = "#ffffff",
    bool isLimitRegion = false,
    int dpi = 300,
  }) async {
    try {
      logger.i('开始去除像素水印: $inputFilePath -> $outputFilePath');
      logger.i(
          '页码范围: $pageRange, 阈值: $thresholdBelow-$thresholdHigh, 目标颜色: $thresholdTarget');

      final data = {
        "input_path": inputFilePath,
        "page_range": pageRange,
        "watermark_type": "pixel", // 指定水印类型
        "threshold_below": thresholdBelow,
        "threshold_high": thresholdHigh,
        "threshold_target": thresholdTarget,
        "is_limit_region": isLimitRegion,
        "dpi": dpi,
        "output_path": outputFilePath,
      };

      final response =
          await messageController.request(data, "pdf/remove_watermark");

      if (response.status == "success") {
        logger.i('像素水印去除成功');
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      logger.e('像素水印去除失败: $e');
      throw Exception("$e");
    }
  }

  /// 执行像素水印去除
  Future<void> executeWatermarkRemovalByPixel(BuildContext context) async {
    // 检查桌面环境
    if (!PathUtils.isDesktop) {
      showToastNotification(context, 'toolbox.common.failure'.tr,
          'toolbox.watermark.desktopOnlyFeature'.tr,
          type: "error");
      return;
    }

    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      // 处理每个选定的文件
      for (int i = 0; i < selectedFilePaths.length; i++) {
        final inputFile = selectedFilePaths[i];
        logger.i("处理像素水印文件: $inputFile");

        // 确定输出文件路径
        final pathUtils = PathUtils(inputFile);
        final outputFilePath = await pathUtils.convertPath(
          outputMode.value,
          stem_append: "_去水印版",
          outputDir: outputDir.value,
        );

        logger.i("输出文件路径: $outputFilePath");

        // 使用用户指定的页码范围（跳过步骤1参数验证）
        String pageRangeStr =
            pageRange.value.isNotEmpty ? pageRange.value : 'all';

        await removeWatermarkByPixel(
          inputFilePath: inputFile,
          outputFilePath: outputFilePath,
          pageRange: pageRangeStr,
          thresholdBelow: int.parse(lowerBounds.value),
          thresholdHigh: int.parse(upperBounds.value),
          thresholdTarget: targetColor.value.hex,
          isLimitRegion: isLimitRegin.value,
          dpi: 300,
        );

        // 更新进度
        progressController.updateProgress(
          status: "processing",
          current: (i + 1).toDouble(),
          total: selectedFilePaths.length.toDouble(),
          message: "${'watermark.progress.processingPixelWatermark'.tr}: ${pathUtils.name}"
        );
        progressController.outputPath.value = outputFilePath;
      }
      progressController.updateProgress(
        status: "completed",
        message: 'toolbox.common.process.completed'.tr,
        current: 100.0,
        total: 100.0,
      );
    } catch (e) {
      logger.e("像素水印去除失败: $e");
      progressController.updateProgress(
      status: "error",
      message: "${'toolbox.common.process.failed'.tr}: $e",
      current: 0.0,
      total: 100.0,
    );
    }
  }
}
