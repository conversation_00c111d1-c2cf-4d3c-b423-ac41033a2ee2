import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFCropPageController extends GetxController {
  // 基础数据
  late final List<Map<String, String>> cropModeList;
  late final List<Map<String, String>> outputFormatList;
  late final List<Map<String, String>> unitList;

  // 表单参数
  final cropMode = "annotate".obs;
  final outputFormat = "pdf".obs;
  final expandMode = false.obs;
  final keepPaperSize = false.obs;
  final keepAnnotation = true.obs;
  final unit = "pt".obs;
  final top = 0.0.obs;
  final bottom = 0.0.obs;
  final left = 0.0.obs;
  final right = 0.0.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputPath = ''.obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'annotate');

  @override
  void onInit() {
    super.onInit();

    // Initialize lists with i18n translations
    cropModeList = [
      {"value": "annotate", "label": 'toolbox.cropTypeOptions.annotate'.tr},
      {"value": "margin", "label": 'toolbox.cropTypeOptions.margin'.tr},
    ];

    outputFormatList = [
      {"value": "pdf", "label": "PDF"},
      {"value": "png", "label": "PNG"},
    ];

    unitList = [
      {"value": "pt", "label": "pt"},
      {"value": "cm", "label": "cm"},
      {"value": "mm", "label": "mm"},
    ];
  }

  Future<void> submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);
    try {
      for (String filePath in selectedFilePaths) {
        progressController.updateProgress(
          status: "running",
          message: "${'toolbox.common.processing'.tr}: ${PathUtils(filePath).name}",
        );
        String outputPath = "";
        if (outputFormat.value == "pdf") {
          outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "_${'toolbox.crop.stem_append'.tr}",
            suffix: ".pdf",
            outputDir: outputDir.value,
          );
        } else if (outputFormat.value == "png") {
          outputPath = await PathUtils(filePath).convertPath(
            outputMode.value,
            stem_append: "_${'toolbox.crop.stem_append'.tr}",
            suffix: "",
            outputDir: outputDir.value,
          );
        }
        final data = {
          'crop_type': cropMode.value,
          'expand_mode': expandMode.value,
          'top': top.value,
          'bottom': bottom.value,
          'left': left.value,
          'right': right.value,
          'unit': unit.value,
          'keep_annotation': keepAnnotation.value,
          'keep_page_size': keepPaperSize.value,
          'output_format': outputFormat.value,
          'input_path': filePath,
          'page_range': pageRange.value,
          'output_path': outputPath,
          'show_progress': true,
        };
        final resp = await messageController.request(data, 'pdf/crop');
        logger.w("resp: $resp");
        if (resp.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'toolbox.background.completed'.tr,
          );
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
          return;
        }
      }
      progressController.updateProgress(status: "completed", message: "");
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "toolbox.common.error_with_msg".trParams({'error': e.toString()}),
      );
    }
  }
}
