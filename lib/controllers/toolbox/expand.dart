import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class PDFExpandController extends GetxController {
  // 基础数据
  final expandModeList = [
    {"value": "blank", "label": 'toolbox.expand.mode.blank'.tr},
    {"value": "file", "label": 'toolbox.expand.mode.file'.tr},
  ];
  final unitList = [
    {"value": "pt", "label": 'toolbox.expand.unit.pt'.tr},
    {"value": "ratio", "label": 'toolbox.expand.unit.ratio'.tr},
  ];
  final directionList = [
    {"value": "top", "label": 'toolbox.expand.direction.top'.tr},
    {"value": "bottom", "label": 'toolbox.expand.direction.bottom'.tr},
    {"value": "left", "label": 'toolbox.expand.direction.left'.tr},
    {"value": "right", "label": 'toolbox.expand.direction.right'.tr},
  ];

  // 表单参数
  final expandMode = 'blank'.obs;
  final top = 0.0.obs;
  final bottom = 0.0.obs;
  final left = 0.0.obs;
  final right = 0.0.obs;
  final unit = 'pt'.obs;
  final bgFilePath = ''.obs;
  final direction = 'right'.obs;
  final pageRange = ''.obs;
  final outputMode = "same".obs;
  final outputDir = ''.obs;
  final selectedFilePaths = <String>[].obs;
  // 错误
  final outputDirError = ''.obs;
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final tabController = ShadTabsController(value: 'custom');

  void submit(BuildContext context) async {
    if (selectedFilePaths.isEmpty) {
      showToastNotification(
        context,
        'toolbox.common.failure'.tr,
        'toolbox.common.selectPdfFiles'.tr,
        type: "error",
      );
      return;
    }
    progressController.reset(
      showOutputHint: true,
      numberButtons: 2,
    );
    progressController.showProgressDialog(context);

    try {
      if (selectedFilePaths.isEmpty) {
        progressController.updateProgress(
          status: "error", message: 'toolbox.common.selectPdfFiles'.tr,
        );
        return;
      }
      for (String filePath in selectedFilePaths) {
        progressController.updateProgress(
          status: "running",
          message: "${'toolbox.common.process.running'.tr}: ${PathUtils(filePath).name}",
        );
        // Determine output path
        String outputPath = await PathUtils(filePath).convertPath(
          outputMode.value,
          stem_append: "_${'toolbox.expand.fileNameAppend'.tr}",
          suffix: ".pdf",
          outputDir: outputDir.value,
        );

        final data = {
          'expand_mode': expandMode.value,
          'input_path': filePath,
          'output_path': outputPath,
          'bg_path': bgFilePath.value,
          'direction': direction.value,
          'unit': unit.value,
          'top': top.value,
          'bottom': bottom.value,
          'left': left.value,
          'right': right.value,
          'page_range': pageRange.value,
          'show_progress': true,
        };

        final resp = await messageController.request(data, 'pdf/expand');
        logger.w("resp: $resp");
        if (resp.status == "success") {
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(
            status: "completed", message: 'toolbox.common.process.completed'.tr,
          );
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
          return;
        }
      }
    } catch (e) {
      logger.e("submit error: $e");
      progressController.updateProgress(
        status: "error", message: "${'toolbox.common.process.failed'.tr}: $e",
      );
    }
  }
}
