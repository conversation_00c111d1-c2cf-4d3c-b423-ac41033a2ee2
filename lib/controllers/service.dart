import 'package:shelf/shelf.dart' as shelf;
import 'package:shelf/shelf_io.dart' as shelf_io;
import 'package:shelf_router/shelf_router.dart';
import 'package:shelf_web_socket/shelf_web_socket.dart' as shelf_web_socket;
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dart:async' show Future;
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';
import 'package:media_kit/media_kit.dart'; // Provides [Player], [Media], [Playlist] etc.
import 'package:media_kit_video/media_kit_video.dart'; // Provides [VideoController]
import 'dart:io';
import 'package:path/path.dart' as p;
import 'package:shelf_cors_headers/shelf_cors_headers.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:http/http.dart' as http;

Future<void> processLink(Uri uri) async {
  final settingController = Get.find<SettingController>();
  final videoNoteController = Get.find<VideoNoteController>();
  final _storage = StorageManager();
  final currentRoute = Get.currentRoute;
  logger.d('Current route: $currentRoute');
  logger.d('onAppLink: $uri');
  final queryParams = uri.queryParameters;
  logger.i('queryParams: $queryParams');
  var category = queryParams['category'] ?? "video";
  if (category == "video") {
    const targetRoute = '/video_note';
    if (currentRoute.isNotEmpty && currentRoute != targetRoute) {
      await Get.toNamed(targetRoute);
    }
    var videoUrl = "";
    var t = 0;
    if (queryParams.containsKey('path')) {
      videoUrl = queryParams['path']!;
      logger.d('deep link videoUrl: $videoUrl');
      logger.d('current videoUrl: ${videoNoteController.getCurrentVideoUrl()}');
      if (_storage.read(
              StorageBox.videoNotes, VideoNotesStorageKeys.useRelativePath) ??
          false) {
        final rootDir = _storage.read(
                StorageBox.videoNotes, VideoNotesStorageKeys.rootDir) ??
            "";
        if (videoUrl.isNotEmpty && !videoUrl.startsWith('http')) {
          videoUrl = p.join(rootDir, videoUrl);
        }
      }
      // 检查是否需要URL解码
      final usePathEncoding = _storage.read(
              StorageBox.videoNotes, VideoNotesStorageKeys.usePathEncoding, true) ??
          true;
      if (usePathEncoding) {
        try {
          videoUrl = Uri.decodeComponent(videoUrl);
          logger.d('URL解码后: $videoUrl');
        } catch (e) {
          logger.w('URL解码失败，使用原始URL: $e');
        }
      }

      if (queryParams.containsKey("t")) {
        final timeStr = queryParams['t']!;
        // 检查是否为时间格式 (包含:)
        if (timeStr.contains(':')) {
          final parts = timeStr.split(':').map(int.parse).toList();
          if (parts.length == 2) {
            // mm:ss 格式
            t = parts[0] * 60 + parts[1];
          } else if (parts.length == 3) {
            // hh:mm:ss 格式
            t = parts[0] * 3600 + parts[1] * 60 + parts[2];
          }
        } else {
          // 纯数字格式（秒数）
          t = int.parse(timeStr);
        }
        logger.d('t: $t seconds');
      }

      // 检查默认播放器设置
      final defaultPlayer = videoNoteController.defaultPlayer.value;
      logger.i('默认播放器设置: $defaultPlayer');

      if (defaultPlayer == "browser" && videoUrl.startsWith('http')) {
        // 浏览器模式：通过WebSocket发送命令到浏览器扩展
        logger.i('使用浏览器模式处理时间戳链接: $videoUrl, 时间戳: $t秒');

        try {
          final wsManager = Get.find<WebSocketManager>();

          // 检查WebSocket连接状态
          if (!wsManager.isConnected.value || !wsManager.isBrowserExtensionAvailable()) {
            logger.w('WebSocket未连接或浏览器扩展不可用，显示连接提示');
            showLocalNotification(
              'service.browser.extensionRequired'.tr,
              'service.browser.extensionInstallPrompt'.tr
            );
            return;
          }

          // 发送浏览器导航命令（使用智能导航避免不必要的页面刷新）
          wsManager.navigateToVideoTimestamp(videoUrl, t);
          logger.i('已发送浏览器智能导航命令: $videoUrl, 时间戳: $t秒');

          // 显示成功通知
          showLocalNotification(
            'service.browser.openingVideo'.tr,
            'service.browser.videoWillOpen'.tr
          );

        } catch (e) {
          logger.e('浏览器模式处理失败: $e');
          showLocalNotification(
            'service.browser.navigationFailed'.tr,
            'service.browser.cannotOpenVideo'.tr
          );
        }
      } else {
        // 内置播放器模式：使用原有逻辑
        logger.i('使用内置播放器模式处理时间戳链接: $videoUrl, 时间戳: $t秒');

        if (videoUrl.isNotEmpty) {
          videoNoteController.addToPlaylist(videoUrl);
        }

        if (videoNoteController.getCurrentVideoUrl() != videoUrl) {
          await videoNoteController.player.value.open(Media(
            videoUrl,
          ));
        }
        if (t > 0) {
          await videoNoteController.player.value.seek(Duration(seconds: t));
        }
      }
    }
  } else if (category == "pdf") {
    final path = queryParams['path']!;
    final page = queryParams['page'] ?? "1";
    if (settingController.pdfReaderPath.value.isEmpty) {
      // 调用默认程序打开PDF
      openFileBySystemDefault(path);
    } else {
      final appName =
          PathUtils(settingController.pdfReaderPath.value).stem.toLowerCase();
      if (Platform.isWindows) {
        List<String> args = [];
        if (appName.contains('foxit')) {
          args = ['/A', 'page=$page', '"$path"'];
        } else if (appName.contains('adobe')) {
          args = ['/A', 'page=$page', 'OpenActions', '"$path"'];
        } else if (appName.contains('sumatra')) {
          args = ['"$path"', '-page', page];
        } else if (appName.contains('pdfannotator')) {
          args = ['/page=$page', '"$path"'];
        } else if (appName.contains('PDFXCview'.toLowerCase())) {
          args = ['/A', 'page=$page', '"$path"'];
        } else if (appName.contains('okular')) {
          args = ['-p', page, '"$path"'];
        } else if (appName.contains('chrome') ||
            appName.contains('msedge') ||
            appName.contains('firefox')) {
          args = [
            'file:///${Uri.encodeFull(path.replaceAll("\\", "/"))}#page=$page'
          ];
        }
        logger.w('Using args: ${settingController.pdfReaderPath.value} $args');
        await Process.run(
          settingController.pdfReaderPath.value,
          args,
          runInShell: true,
        );
      } else if (Platform.isMacOS) {
        List<String> args;
        if (appName.contains('foxit')) {
          args = ['"$path"'];
        } else if (appName.contains('chrome') ||
            appName.contains('msedge') ||
            appName.contains('firefox')) {
          if (!path.startsWith('/')) {
            args = ['file:///${Uri.encodeFull(path)}#page=$page'];
          } else {
            args = ['file://${Uri.encodeFull(path)}#page=$page'];
          }
        } else {
          args = ['"$path"'];
        }
        logger.w('Using args: ${settingController.pdfReaderPath.value} $args');
        await Process.run(
          settingController.pdfReaderPath.value,
          args,
          runInShell: true,
        );
      } else if (Platform.isLinux) {
        String args;
        switch (appName) {
          case 'okular':
          case 'evince':
            args = '-p $page "$path"';
          default:
            args = '"$path"';
        }
        logger.w('Using args: $args');
        await Process.run(
          settingController.pdfReaderPath.value,
          args.split(' '),
          runInShell: true,
        );
      }
    }
  } else if (category == "zotero") {
    final path = queryParams['path']!;
    openFileBySystemDefault(path);
  } else {
    final path = queryParams['path']!;
    openFileBySystemDefault(path);
  }
}

class VideoNoteApi {
  Future<shelf.Response> _messages(shelf.Request request) async {
    return shelf.Response.ok('[]');
  }

  // By exposing a [Router] for an object, it can be mounted in other routers.
  Router get router {
    final router = Router();

    // A handler can have more that one route.
    router.get('/messages', _messages);
    router.get('/messages/', _messages);

    // This nested catch-all, will only catch /api/.* when mounted above.
    // Notice that ordering if annotated handlers and mounts is significant.
    router.all('/<ignored|.*>',
        (shelf.Request request) => shelf.Response.notFound('null'));

    return router;
  }
}

// 初始化API服务器
Process? _apiServerProcess;
HttpServer? _httpServer;

Future<void> initApiServer() async {
  if (Platform.isIOS || Platform.isAndroid) {
    return;
  }

  final settingController = Get.find<SettingController>();
  final port = settingController.pyServerPort.value;

  logger.i('Initializing API Server on port $port');

  // Check if a compatible server is already running
  if (await _isApiServerRunning(port)) {
    logger.i('Compatible API Server is already running on port $port');
    return;
  }

  // Determine server executable path
  String serverPath = _getServerExecutablePath();

  // Check if server executable exists
  if (!File(serverPath).existsSync()) {
    logger.w('API Server executable not found at: $serverPath');
    logger.i('Will wait for manual server startup or check again later');

    // Wait a bit and check if a manual server becomes available
    await _waitForManualServer(port);
    return;
  }

  // Start the server
  await _startManagedServer(serverPath, port);
}

/// Check if a compatible API server is running on the specified port
Future<bool> _isApiServerRunning(int port) async {
  try {
    // Test basic connectivity with timeout
    final response = await http.get(
      Uri.parse('http://127.0.0.1:$port/'),
    ).timeout(const Duration(seconds: 5));

    if (response.statusCode != 200) {
      return false;
    }

    // Verify it's our API server by checking the response format
    try {
      final data = jsonDecode(response.body);
      if (data['message']?.toString().contains('PDF Guru API') == true &&
          data['status'] == 'ok') {
        logger.d('Verified compatible API server is running');
        return true;
      }
    } catch (e) {
      logger.d('Server response format not recognized: $e');
    }

    return false;
  } catch (e) {
    logger.d('API Server health check failed: $e');
    return false;
  }
}

/// Get the expected server executable path for the current platform
String _getServerExecutablePath() {
  if (Platform.isMacOS) {
    return "/Library/Application Support/PDF Guru Anki2/server/server";
  } else if (Platform.isWindows) {
    String appPath = Platform.resolvedExecutable;
    return PathUtils.join([PathUtils(appPath).parent, 'server', 'server.exe']);
  } else if (Platform.isLinux) {
    String appPath = Platform.resolvedExecutable;
    return PathUtils.join([PathUtils(appPath).parent, 'server', 'server']);
  }
  return "";
}

/// Wait for a manually started server to become available
Future<void> _waitForManualServer(int port) async {
  logger.i('Waiting for manual server startup...');

  // Wait up to 30 seconds for a manual server to start
  for (int i = 0; i < 30; i++) {
    await Future.delayed(const Duration(seconds: 1));

    if (await _isApiServerRunning(port)) {
      logger.i('Manual API server detected and verified');
      return;
    }
  }

  logger.w('No manual server detected after 30 seconds');
  logger.w('PDF processing features may not work until server is started');
}

/// Start a managed server process
Future<void> _startManagedServer(String serverPath, int port) async {
  try {
    logger.i('Starting managed API server: $serverPath');

    _apiServerProcess = await Process.start(
        serverPath, ["-p", port.toString()]);

    // Monitor process output
    _apiServerProcess!.stdout.transform(utf8.decoder).listen((data) {
      logger.d('API Server stdout: $data');
    });
    _apiServerProcess!.stderr.transform(utf8.decoder).listen((data) {
      logger.e('API Server stderr: $data');
    });

    // Wait for server to start and verify it's working
    bool serverReady = false;
    for (int i = 0; i < 10; i++) {
      await Future.delayed(const Duration(seconds: 1));

      if (await _isApiServerRunning(port)) {
        serverReady = true;
        break;
      }
    }

    if (serverReady) {
      logger.i('Managed API Server started successfully on port $port');
    } else {
      logger.e('Managed API Server failed to start properly');
      _apiServerProcess?.kill();
      _apiServerProcess = null;
    }

  } catch (e) {
    logger.e('Failed to start managed API Server: $e');
    _apiServerProcess = null;
  }
}

// 终止API服务器进程
Future<void> terminateApiServer() async {
  final settingController = Get.find<SettingController>();
  final int port = settingController.pyServerPort.value;

  // First, try to terminate managed process if it exists
  if (_apiServerProcess != null) {
    logger.i('Terminating managed API server process...');
    await _terminateManagedServer();
  }

  // Also check for any remaining servers on the port (manual or orphaned)
  await _ensurePortIsClear(port);
}

/// Terminate the managed server process
Future<void> _terminateManagedServer() async {
  if (_apiServerProcess == null) return;

  try {
    logger.i('Terminating managed server process...');

    if (Platform.isWindows) {
      // Windows platform - try graceful kill first, then force
      bool killed = _apiServerProcess!.kill();
      if (!killed) {
        // Force kill by PID
        final pid = _apiServerProcess!.pid;
        await Process.run('taskkill', ['/F', '/PID', '$pid'], runInShell: true);
      }
    } else {
      // Unix-like platforms
      if (!_apiServerProcess!.kill()) {
        _apiServerProcess!.kill(ProcessSignal.sigkill);
      }

      // Wait for process to terminate
      await _apiServerProcess!.exitCode.timeout(
        const Duration(seconds: 3),
        onTimeout: () {
          _apiServerProcess!.kill(ProcessSignal.sigkill);
          return -1;
        },
      );
    }

    logger.i('Managed server process terminated');
  } catch (e) {
    logger.e('Error terminating managed server: $e');
  } finally {
    _apiServerProcess = null;
  }
}

/// Ensure the specified port is clear of any processes
Future<void> _ensurePortIsClear(int port) async {
  try {
    logger.d('Ensuring port $port is clear...');

    if (Platform.isWindows) {
      // Kill any process using the port on Windows
      await Process.run(
        'cmd.exe',
        ['/c', 'for /f "tokens=5" %a in (\'netstat -ano ^| findstr :$port\') do taskkill /F /PID %a'],
        runInShell: true,
      );
    } else {
      // Kill any process using the port on Unix-like systems
      try {
        final result = await Process.run('lsof', ['-ti:$port']);
        if (result.stdout.toString().trim().isNotEmpty) {
          final pids = result.stdout.toString().trim().split('\n');
          for (final pid in pids) {
            if (pid.isNotEmpty) {
              await Process.run('kill', ['-9', pid]);
            }
          }
        }
      } catch (e) {
        // lsof might not be available, ignore
        logger.d('lsof not available or no processes found: $e');
      }
    }

    logger.d('Port $port cleanup completed');
  } catch (e) {
    logger.w('Error clearing port $port: $e');
  }
}

// 初始化HTTP服务器
Future<void> initHttpServer() async {
  try {
    final settingController = Get.find<SettingController>();
    final guruServiceRouter = Router();

    // 现有的HTTP路由
    guruServiceRouter.get("/", (shelf.Request request) {
      return shelf.Response.ok('Welcome to PDF Guru Anki API Server');
    });
    guruServiceRouter.get("/status", (shelf.Request request) {
      return shelf.Response.ok(
        jsonEncode({
          'status': 'running',
          'timestamp': DateTime.now().toIso8601String()
        }),
        headers: {'content-type': 'application/json'},
      );
    });
    guruServiceRouter.get("/version", (shelf.Request request) async {
      final packageInfo = await PackageInfo.fromPlatform();
      return shelf.Response.ok(
        jsonEncode({
          'version': packageInfo.version,
          'buildNumber': packageInfo.buildNumber,
        }),
        headers: {'content-type': 'application/json'},
      );
    });
    guruServiceRouter.get("/screenshot_card", (shelf.Request request) async {
      final imageCardController = Get.find<ImageCardController>();
      final currentRoute = Get.currentRoute;
      const targetRoute = '/image_card';
      if (currentRoute != targetRoute) {
        await Get.toNamed(targetRoute);
      }
      await imageCardController.loadImageFromClipboard();
      return shelf.Response.ok(
        jsonEncode({
          'status': 'success',
        }),
        headers: {'content-type': 'application/json'},
      );
    });
    guruServiceRouter.get("/guru_jump", (shelf.Request request) async {
      final url = request.url.queryParameters['url'] ??
          request.url.queryParameters['uri'];
      if (url == null) {
        return shelf.Response.badRequest(
          body: jsonEncode({'error': 'Missing url or uri parameter'}),
          headers: {'content-type': 'application/json'},
        );
      }
      logger.d('Processing guru_jump GET with url: $url');
      final rawUrl = Uri.decodeComponent(url); // 先进行URL解码
      processLink(Uri.parse(rawUrl));

      return shelf.Response.ok(
        '''
        <!DOCTYPE html>
        <html>
        <head>
          <script>
            window.close();
          </script>
        </head>
        <body>
        </body>
        </html>
        ''',
        headers: {'content-type': 'text/html; charset=utf-8'},
      );
    });
    guruServiceRouter.post("/guru_jump", (shelf.Request request) async {
      final contentType = request.headers['content-type'];
      String? urlParam;

      // 处理 JSON 格式的请求体
      if (contentType?.contains('application/json') == true) {
        final requestBody = await request.readAsString();
        final jsonBody = jsonDecode(requestBody);
        urlParam = jsonBody['url'] ?? jsonBody['uri'];
        if (urlParam == null) {
          return shelf.Response.badRequest(
            body: jsonEncode(
                {'error': 'Missing url or uri parameter in JSON body'}),
            headers: {'content-type': 'application/json'},
          );
        }
      }
      // 处理表单格式的请求体
      else if (contentType?.contains('application/x-www-form-urlencoded') ==
          true) {
        final formData = await request.readAsString();
        final params = Uri.splitQueryString(formData);
        urlParam = params['url'] ?? params['uri'];
        if (urlParam == null) {
          return shelf.Response.badRequest(
            body: jsonEncode(
                {'error': 'Missing url or uri parameter in form data'}),
            headers: {'content-type': 'application/json'},
          );
        }
      } else {
        return shelf.Response.badRequest(
          body: jsonEncode({'error': 'Unsupported content type'}),
          headers: {'content-type': 'application/json'},
        );
      }

      logger.d('Processing guru_jump POST with url: $urlParam');
      final rawUrl = Uri.decodeComponent(urlParam); // 先进行URL解码
      processLink(Uri.parse(rawUrl));

      return shelf.Response.ok(
        jsonEncode({'status': 'success'}),
        headers: {'content-type': 'application/json'},
      );
    });

    // 添加WebSocket支持
    guruServiceRouter.get('/connect', (shelf.Request request) {
      final timestamp = DateTime.now().toIso8601String();
      final clientIP = request.headers['x-forwarded-for'] ??
                      request.headers['x-real-ip'] ??
                      'localhost';

      logger.i('[$timestamp] WebSocket连接请求 - 客户端IP: $clientIP, User-Agent: ${request.headers['user-agent']}');

      return shelf_web_socket.webSocketHandler((webSocket, String? protocol) {
        final connectionTimestamp = DateTime.now().toIso8601String();
        // 连接建立
        logger.i('[$connectionTimestamp] WebSocket连接已建立 - 客户端: $clientIP, 协议: $protocol');

        // 注册WebSocket连接到管理器
        final wsManager = Get.find<WebSocketManager>();
        wsManager.addConnection(webSocket);

        logger.i('[$connectionTimestamp] WebSocket连接已注册到管理器，当前连接数: ${wsManager.connectionCount.value}');

        // 处理接收到的消息
        webSocket.stream.listen((message) {
          logger.d('收到WebSocket消息: $message');
          try {
            final data = jsonDecode(message);
            // 根据消息类型处理不同请求
            if (data['type'] == 'guru_jump' && data['url'] != null) {
              final rawUrl = Uri.decodeComponent(data['url']);
              processLink(Uri.parse(rawUrl));
              webSocket.sink.add(
                  jsonEncode({'status': 'success', 'action': 'guru_jump'}));
            }
            // 视频截图处理 (旧格式，保持兼容性)
            else if (data['type'] == 'video_screenshot' &&
                data['image'] != null) {
              final imageData = data['image'];
              final videoNoteController = Get.find<VideoNoteController>();
              // 将Base64编码的图像保存到剪贴板
              videoNoteController.saveScreenshotFromBase64(imageData);
              webSocket.sink.add(jsonEncode({
                'status': 'success',
                'action': 'video_screenshot',
                'message': 'service.screenshot.saved'.tr
              }));
            }

            // 视频时间戳处理 (旧格式，保持兼容性)
            else if (data['type'] == 'video_timestamp' && data['timestamp'] != null) {
              final videoNoteController = Get.find<VideoNoteController>();
              final clipboardController = Get.find<ClipboardController>();

              // 构建时间戳链接
              final timestamp = data['timestamp'];
              final videoUrl = data['url'] ?? '';
              final linkText = videoNoteController.buildTimestampLink(timestamp, videoUrl);

              clipboardController.copyText(linkText);
              webSocket.sink.add(jsonEncode({
                'status': 'success',
                'action': 'video_timestamp',
                'message': 'service.timestamp.linkCopiedToClipboard'.tr
              }));

              if (videoNoteController.isAutoPaste.value) {
                AnkiConnectController().simulatePaste();
              }
            }

            // 处理统一报告格式
            else if (data['type'] == 'report') {
              final reportType = data['reportType'] ?? '';
              final status = data['status'] ?? '';
              final action = data['action'] ?? '';
              final reportData = data['data'] ?? {};
              final message = data['message'] ?? '';

              logger.i('收到统一报告: reportType=$reportType, status=$status, action=$action');

              // 根据报告类型和状态处理
              if (reportType == 'browser_navigation') {
                _handleBrowserNavigationReport(status, action, reportData, message);
              } else if (reportType == 'screenshot') {
                _handleScreenshotReport(status, action, reportData, message);
              } else if (reportType == 'timestamp') {
                _handleTimestampReport(status, action, reportData, message);
              } else if (reportType == 'video_info') {
                _handleVideoInfoReport(status, action, reportData, message);
              } else {
                logger.w('未知的报告类型: $reportType');
              }
            }

            // 处理时间戳链接
            else if (data['type'] == 'timestamp_link' && data['link'] != null) {
              final link = data['link'];
              final clipboardController = Get.find<ClipboardController>();
              clipboardController.copyText(link);

              webSocket.sink.add(jsonEncode({
                'status': 'success',
                'action': 'timestamp_link',
                'link': link,
                'message': 'service.timestamp.linkCopiedToClipboard'.tr
              }));

              // 显示通知
              Get.snackbar(
                'service.timestamp.linkCopied'.tr,
                'service.timestamp.linkSaved'.tr,
                duration: const Duration(seconds: 2),
                snackPosition: SnackPosition.BOTTOM,
              );
            }
            // 可以添加更多消息类型处理
            else if (data['type'] == 'screenshot_card') {
              // 异步处理截图卡片
              _handleScreenshotCard().then((_) {
                webSocket.sink.add(jsonEncode(
                    {'status': 'success', 'action': 'screenshot_card'}));
              }).catchError((error) {
                webSocket.sink.add(jsonEncode(
                    {'status': 'error', 'message': error.toString()}));
              });
            }
            // 处理浏览器扩展识别响应
            else if (data['type'] == 'identify_response') {
              final timestamp = DateTime.now().toIso8601String();
              logger.i('[$timestamp] 收到浏览器扩展识别响应: ${data['source']}');

              // 确认这是浏览器扩展连接
              final wsManager = Get.find<WebSocketManager>();
              wsManager.confirmBrowserExtensionConnection(webSocket, data);

              webSocket.sink.add(jsonEncode({
                'status': 'success',
                'action': 'identify_response',
                'message': 'service.browser.extensionIdentified'.tr
              }));
            }
            // 浏览器视频控制命令
            else if (data['type'] == 'browser_control') {
              _handleBrowserControl(data, webSocket);
            } else {
              logger.w('收到未知消息类型: ${data['type']}');
              webSocket.sink.add(jsonEncode(
                  {'status': 'error', 'message': 'Unknown command'}));
            }
          } catch (e) {
            logger.e('处理WebSocket消息时出错: $e');
            webSocket.sink
                .add(jsonEncode({'status': 'error', 'message': e.toString()}));
          }
        }, onDone: () {
          final disconnectTimestamp = DateTime.now().toIso8601String();
          logger.i('[$disconnectTimestamp] WebSocket连接已关闭 - 客户端: $clientIP');
          // 从管理器中移除连接
          try {
            final wsManager = Get.find<WebSocketManager>();
            wsManager.removeConnection(webSocket);
            logger.i('[$disconnectTimestamp] WebSocket连接已从管理器移除，剩余连接数: ${wsManager.connectionCount.value}');
          } catch (e) {
            logger.e('[$disconnectTimestamp] 移除WebSocket连接时出错: $e');
          }
        }, onError: (error) {
          final errorTimestamp = DateTime.now().toIso8601String();
          logger.e('[$errorTimestamp] WebSocket错误 - 客户端: $clientIP, 错误: $error');
          // 从管理器中移除连接
          try {
            final wsManager = Get.find<WebSocketManager>();
            wsManager.removeConnection(webSocket);
            logger.i('[$errorTimestamp] 错误连接已从管理器移除，剩余连接数: ${wsManager.connectionCount.value}');
          } catch (e) {
            logger.e('[$errorTimestamp] 移除WebSocket连接时出错: $e');
          }
        });
      })(request);
    });

    guruServiceRouter.all('/<ignored|.*>',
        (shelf.Request request) => shelf.Response.notFound('null'));

    // 配置CORS头部
    final corsHeadersOptions = {
      ACCESS_CONTROL_ALLOW_ORIGIN: '*',
      ACCESS_CONTROL_ALLOW_METHODS: 'GET, POST, OPTIONS',
      ACCESS_CONTROL_ALLOW_HEADERS:
          'Origin, Content-Type, Accept, Authorization',
      ACCESS_CONTROL_ALLOW_CREDENTIALS: 'true',
      ACCESS_CONTROL_MAX_AGE: '86400',
    };

    // 创建请求处理管道
    final app = shelf.Pipeline()
        .addMiddleware(corsHeaders(headers: corsHeadersOptions))
        .addMiddleware(shelf.logRequests())
        .addHandler(guruServiceRouter.call);

    final serverPort = settingController.serverPort.value;
    final timestamp = DateTime.now().toIso8601String();

    logger.i('[$timestamp] 启动HTTP/WebSocket服务器 - 端口: $serverPort');

    _httpServer = await shelf_io.serve(app, 'localhost', serverPort);

    // 启用内容压缩
    _httpServer!.autoCompress = true;

    logger.i('[$timestamp] HTTP/WebSocket服务器启动成功！');
    logger.i('[$timestamp] 服务器地址: http://${_httpServer!.address.host}:${_httpServer!.port}');
    logger.i('[$timestamp] WebSocket端点: ws://${_httpServer!.address.host}:${_httpServer!.port}/connect');
    logger.i('[$timestamp] 等待浏览器扩展连接...');
  } catch (e, stackTrace) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] 初始化HTTP/WebSocket服务器时出错: $e');
    logger.e('[$timestamp] 错误堆栈: $stackTrace');

    // 检查是否是端口被占用的错误
    if (e.toString().contains('Address already in use') ||
        e.toString().contains('port') ||
        e.toString().contains('bind')) {
      final settingController = Get.find<SettingController>();
      logger.e('[$timestamp] 端口 ${settingController.serverPort.value} 可能已被占用');
      logger.e('[$timestamp] 请检查是否有其他应用程序正在使用此端口');
      logger.e('[$timestamp] 或者尝试更改设置中的服务器端口');
    }
  }
}

// 辅助方法处理截图卡片
Future<void> _handleScreenshotCard() async {
  final imageCardController = Get.find<ImageCardController>();
  final currentRoute = Get.currentRoute;
  const targetRoute = '/image_card';
  if (currentRoute != targetRoute) {
    await Get.toNamed(targetRoute);
  }
  await imageCardController.loadImageFromClipboard();
}

// 处理浏览器控制命令
void _handleBrowserControl(Map<String, dynamic> data, dynamic webSocket) {
  try {
    final command = data['command'];
    logger.i('处理浏览器控制命令: $command');

    // 获取WebSocket管理器并广播命令到浏览器扩展
    try {
      final wsManager = Get.find<WebSocketManager>();
      wsManager.sendToBrowserExtension(data);

      webSocket.sink.add(jsonEncode({
        'status': 'success',
        'action': 'browser_control',
        'command': command,
        'message': 'service.browser.commandSent'.tr
      }));
    } catch (e) {
      logger.e('发送浏览器控制命令失败: $e');
      webSocket.sink.add(jsonEncode({
        'status': 'error',
        'action': 'browser_control',
        'message': 'service.browser.cannotConnect'.tr
      }));
    }
  } catch (e) {
    logger.e('处理浏览器控制命令时出错: $e');
    webSocket.sink.add(jsonEncode({
      'status': 'error',
      'action': 'browser_control',
      'message': e.toString()
    }));
  }
}

/// 检查WebSocket服务器是否正在运行
Future<bool> isWebSocketServerRunning() async {
  try {
    final settingController = Get.find<SettingController>();
    final port = settingController.serverPort.value;
    final timestamp = DateTime.now().toIso8601String();

    logger.i('[$timestamp] 检查WebSocket服务器状态 - 端口: $port');

    // 检查HTTP服务器是否存在
    if (_httpServer == null) {
      logger.w('[$timestamp] HTTP服务器实例为null');
      return false;
    }

    // 尝试连接到状态端点
    final response = await http.get(
      Uri.parse('http://localhost:$port/status'),
    ).timeout(const Duration(seconds: 3));

    if (response.statusCode == 200) {
      logger.i('[$timestamp] WebSocket服务器正在运行 - 状态码: ${response.statusCode}');
      return true;
    } else {
      logger.w('[$timestamp] WebSocket服务器响应异常 - 状态码: ${response.statusCode}');
      return false;
    }
  } catch (e) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] 检查WebSocket服务器状态失败: $e');
    return false;
  }
}

/// 获取WebSocket服务器详细状态
Map<String, dynamic> getWebSocketServerStatus() {
  try {
    final settingController = Get.find<SettingController>();
    final wsManager = Get.find<WebSocketManager>();
    final timestamp = DateTime.now().toIso8601String();

    final status = {
      'timestamp': timestamp,
      'server_running': _httpServer != null,
      'server_port': settingController.serverPort.value,
      'server_address': _httpServer?.address.toString(),
      'websocket_connections': wsManager.connectionCount.value,
      'browser_extensions': wsManager.getConnectionInfo(),
    };

    logger.d('[$timestamp] WebSocket服务器状态: $status');
    return status;
  } catch (e) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] 获取WebSocket服务器状态失败: $e');
    return {
      'timestamp': timestamp,
      'error': e.toString(),
      'server_running': false,
    };
  }
}

// 终止HTTP服务器
Future<void> terminateHttpServer() async {
  if (_httpServer != null) {
    logger.i('正在终止HTTP/WebSocket服务器...');
    try {
      final settingController = Get.find<SettingController>();
      final int port = settingController.serverPort.value;

      await _httpServer!.close(force: true).timeout(const Duration(seconds: 3),
          onTimeout: () {
        logger.w('等待HTTP/WebSocket服务器关闭超时');
        return;
      });

      // 在Windows平台上，额外确保端口被释放
      if (Platform.isWindows) {
        logger.i('Windows平台：额外检查端口 $port 是否被释放');
        try {
          // 使用taskkill命令终止占用指定端口的进程
          final result = await Process.run(
            'cmd.exe',
            [
              '/c',
              'for /f "tokens=5" %a in (\'netstat -ano ^| findstr :$port\') do taskkill /F /PID %a'
            ],
            runInShell: true,
          );

          if (result.exitCode == 0 && result.stdout.isNotEmpty) {
            logger.i('端口 $port 已被强制释放: ${result.stdout}');
          }
        } catch (e) {
          logger.e('尝试释放端口 $port 时出错: $e');
        }
      }

      logger.i('HTTP/WebSocket服务器已终止');
    } catch (e) {
      logger.e('终止HTTP/WebSocket服务器时出错: $e');
    } finally {
      // 确保即使出错也将服务器引用设置为null
      _httpServer = null;
    }
  }
}

// 终止所有服务
Future<void> terminateAllServices() async {
  logger.i('正在终止所有服务...');
  try {
    // 使用Future.wait同时终止所有服务，提高效率
    await Future.wait([
      terminateApiServer(),
      terminateHttpServer(),
    ]);
    logger.i('所有服务已成功终止');

    // 在Windows平台上，进行额外的端口检查和清理
    if (Platform.isWindows) {
      final settingController = Get.find<SettingController>();
      final ports = [
        settingController.serverPort.value,
        settingController.pyServerPort.value
      ];

      logger.i('Windows平台：额外检查端口 $ports 是否被释放');

      // 延迟一小段时间，确保之前的终止操作有时间生效
      await Future.delayed(const Duration(milliseconds: 500));

      for (final port in ports) {
        try {
          // 使用同步方法检查并终止任何仍然占用这些端口的进程
          final result = Process.runSync(
            'cmd.exe',
            [
              '/c',
              'for /f "tokens=5" %a in (\'netstat -ano ^| findstr :$port\') do taskkill /F /PID %a'
            ],
            runInShell: true,
          );

          if (result.exitCode == 0 && result.stdout.isNotEmpty) {
            logger.i('端口 $port 已被强制释放: ${result.stdout}');
          }
        } catch (e) {
          logger.e('尝试释放端口 $port 时出错: $e');
        }
      }

      // 尝试终止可能的Python进程
      try {
        final result = Process.runSync(
          'taskkill',
          ['/F', '/IM', 'python.exe'],
          runInShell: true,
        );
        logger.i('已尝试终止所有Python进程: ${result.stdout}');
      } catch (e) {
        logger.e('尝试终止Python进程时出错: $e');
      }
    }
  } catch (e) {
    logger.e('终止服务时出错: $e');
    // 确保即使出现错误，也尝试单独终止每个服务
    try {
      await terminateApiServer();
    } catch (e) {
      logger.e('终止API服务器时出错: $e');
    }

    try {
      await terminateHttpServer();
    } catch (e) {
      logger.e('终止HTTP服务器时出错: $e');
    }
  }
}

/// 处理浏览器导航报告
void _handleBrowserNavigationReport(String status, String action, Map<String, dynamic> data, String message) {
  if (status == 'success') {
    if (action == 'open_video_with_timestamp') {
      showLocalNotification(
        'service.video.openedInBrowser'.tr,
        message.isNotEmpty ? message : 'service.video.openedAndJumped'.tr
      );
    } else if (action == 'seek_to_timestamp') {
      showLocalNotification(
        'service.video.jumpedToTime'.tr,
        message.isNotEmpty ? message : 'service.video.jumpedToTimestamp'.tr
      );
    }
    logger.i('浏览器导航成功: $action, 数据: $data');
  } else if (status == 'error') {
    showLocalNotification(
      'service.browser.navigationFailed'.tr,
      message.isNotEmpty ? message : 'service.browser.cannotOpenVideo'.tr
    );
    logger.e('浏览器导航失败: $action, 错误: $message');
  }
}

/// 处理截图报告
void _handleScreenshotReport(String status, String action, Map<String, dynamic> data, String message) {
  if (status == 'success') {
    if (action == 'take_screenshot') {
      final videoNoteController = Get.find<VideoNoteController>();
      final base64Image = data['image'] ?? '';

      if (base64Image.isNotEmpty) {
        // 处理截图数据 (使用现有的截图处理逻辑)
        videoNoteController.saveScreenshotFromBase64(base64Image);
        logger.i('截图数据处理成功');
      } else {
        logger.w('截图数据为空');
        showLocalNotification(
          'service.screenshot.failed'.tr,
          'service.screenshot.dataEmpty'.tr
        );
      }
    }
  } else if (status == 'error') {
    showLocalNotification(
      'service.screenshot.failed'.tr,
      message.isNotEmpty ? message : 'service.screenshot.browserFailed'.tr
    );
    logger.e('截图失败: $action, 错误: $message');
  }
}

/// 处理时间戳报告
void _handleTimestampReport(String status, String action, Map<String, dynamic> data, String message) {
  if (status == 'success') {
    if (action == 'generate_timestamp_link') {
      final videoNoteController = Get.find<VideoNoteController>();
      final clipboardController = Get.find<ClipboardController>();

      final videoUrl = data['url'] ?? '';
      final timestamp = data['timestamp'] ?? '';
      final title = data['title'] ?? '';

      logger.i('收到浏览器扩展时间戳数据: URL=$videoUrl, 时间=$timestamp, 标题=$title');

      // 使用与本地视频相同的逻辑生成时间戳链接
      final linkText = videoNoteController.buildTimestampLink(timestamp, videoUrl);

      clipboardController.copyText(linkText);

      // 自动粘贴功能 (与本地视频保持一致)
      if (videoNoteController.isAutoPaste.value) {
        AnkiConnectController().simulatePaste();
      }

      showLocalNotification(
        'service.timestamp.linkCopied'.tr,
        message.isNotEmpty ? message : 'service.timestamp.linkCopiedToClipboard'.tr
      );
    }
  } else if (status == 'error') {
    showLocalNotification(
      'service.timestamp.generationFailed'.tr,
      message.isNotEmpty ? message : 'service.timestamp.browserExtensionFailed'.tr
    );
    logger.e('时间戳生成失败: $action, 错误: $message');
  }
}

/// 处理视频信息报告
void _handleVideoInfoReport(String status, String action, Map<String, dynamic> data, String message) {
  if (status == 'success') {
    logger.i('视频信息获取成功: $action, 数据: $data');
    // 可以在这里处理视频信息，比如显示视频标题、时长等
  } else if (status == 'error') {
    showLocalNotification(
      'service.video.infoRetrievalFailed'.tr,
      message.isNotEmpty ? message : 'service.video.cannotRetrieveInfo'.tr
    );
    logger.e('视频信息获取失败: $action, 错误: $message');
  }
}
