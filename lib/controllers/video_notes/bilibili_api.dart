import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';
import 'dart:async';

/// 视频清晰度代码
class VideoQuality {
  static const int q240P = 6; // 240P 极速
  static const int q360P = 16; // 360P 流畅
  static const int q480P = 32; // 480P 清晰
  static const int q720P = 64; // 720P 高清
  static const int q720P60 = 74; // 720P60 高帧率
  static const int q1080P = 80; // 1080P 高清
  static const int q1080PP = 112; // 1080P+ 高码率
  static const int q1080P60 = 116; // 1080P60 高帧率
  static const int q4K = 120; // 4K 超清
}

class BilibiliService extends GetxController {
  final dio = Dio();
  final settingController = Get.find<SettingController>();

  // API URLs
  static const String _baseUrl = 'https://api.bilibili.com';
  static const String _passportBaseUrl = 'https://passport.bilibili.com';

  // 登录相关
  static const String _generateQRUrl =
      '$_passportBaseUrl/x/passport-login/web/qrcode/generate';
  static const String _pollQRStatusUrl =
      '$_passportBaseUrl/x/passport-login/web/qrcode/poll';
  static const String _loginInfoUrl = '$_baseUrl/x/web-interface/nav';

  // 视频信息相关
  static const String _videoInfoUrl = '$_baseUrl/x/web-interface/view';
  static const String _videoPartsUrl = '$_baseUrl/x/player/pagelist';
  static const String _videoPlayUrl = '$_baseUrl/x/player/wbi/playurl';

  // 添加 GetX 响应式变量
  final isLoggedIn = false.obs;
  final isLoading = false.obs;
  final loginStatus = ''.obs;
  final userInfo = Rxn<Map<String, dynamic>>();

  // 存储相关
  final _storage = StorageManager();
  static const String _cookieKey = 'bilibili_cookie';

  @override
  void onInit() {
    super.onInit();
    // 初始化时检查登录状态
    _initLoginStatus();
  }

  Future<void> _initLoginStatus() async {
    final cookie = _storage.read(StorageBox.videoNotes, _cookieKey, '');
    if (cookie.isNotEmpty) {
      isLoggedIn.value = await checkLoginStatus();
      if (isLoggedIn.value) {
        await _updateUserInfo();
      }
    }
  }

  /// 检查登录状态并获取用户信息
  Future<bool> checkLoginStatus() async {
    try {
      final cookie = getCookie() ?? "";
      logger.d('cookie: $cookie');
      final response = await dio.get(
        _loginInfoUrl,
        options: Options(
          headers: {
            'cookie': cookie,
            'referer': 'https://www.bilibili.com',
            'user-agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36',
          },
          validateStatus: (status) => true,
          followRedirects: false,
        ),
      );

      logger.d('Check login status response: ${response.data}');

      if (response.data['code'] == 0) {
        final data = response.data['data'];
        if (data['isLogin']) {
          userInfo.value = data; // 直接存储完整的用户信息
          return true;
        }
      }
      userInfo.value = null;
      return false;
    } catch (e) {
      logger.e('Check login status error: $e');
      userInfo.value = null;
      return false;
    }
  }

  /// 更新用户信息
  Future<void> _updateUserInfo() async {
    try {
      final response = await dio.get(
        _loginInfoUrl,
        options: Options(
          validateStatus: (status) => true,
          followRedirects: false,
        ),
      );

      if (response.data['code'] == 0) {
        userInfo.value = response.data['data'];
      }
    } catch (e) {
      logger.e('Update user info error: $e');
    }
  }

  /// 获取存储的cookie
  String? getCookie() {
    return _storage.read(StorageBox.videoNotes, _cookieKey, '');
  }

  /// 保存cookie
  void saveCookie(String cookie) {
    _storage.write(StorageBox.videoNotes, _cookieKey, cookie);
  }

  /// 清除登录状态
  Future<void> logout() async {
    _storage.remove(StorageBox.videoNotes, _cookieKey);
    isLoggedIn.value = false;
    userInfo.value = null;
    loginStatus.value = '';
  }

  /// 开始登录流程
  Future<void> startLogin(String qrcodeKey) async {
    try {
      isLoading.value = true;
      loginStatus.value = '等待扫码...';

      // 开始轮询扫码状态
      while (true) {
        final response = await dio.get(
          _pollQRStatusUrl,
          queryParameters: {'qrcode_key': qrcodeKey},
          options: Options(
            validateStatus: (status) => true,
            followRedirects: false,
            receiveDataWhenStatusError: true,
          ),
        );

        final status = response.data;
        final code = status['data']['code'];

        if (code == 0) {
          // 登录成功，从响应头中获取cookie
          final cookies = response.headers['set-cookie'];
          if (cookies != null) {
            final cookieString = cookies.join('; ');
            logger.d('登录成功，cookie: $cookieString');
            saveCookie(cookieString);
            isLoggedIn.value = true;
            await _updateUserInfo();
          }
          loginStatus.value = '登录成功';
          logger.i('登录成功');
          break;
        } else if (code == 86038) {
          loginStatus.value = '二维码已过期，请重试';
          break;
        } else if (code == 86090) {
          loginStatus.value = '已扫码，请在手机上确认';
        } else if (code == 86101) {
          loginStatus.value = '等待扫码...';
        }

        await Future.delayed(const Duration(seconds: 2));
      }
    } catch (e) {
      logger.e('Login error: $e');
      loginStatus.value = '登录失败: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// 生成二维码登录链接
  Future<Map<String, dynamic>> generateQRCode() async {
    try {
      final response = await dio.get(_generateQRUrl);
      logger.d('Generate QR response: ${response.data}');

      if (response.data['code'] == 0) {
        return {
          'url': response.data['data']['url'],
          'qrcode_key': response.data['data']['qrcode_key'],
        };
      } else {
        throw Exception('获取二维码失败: ${response.data['message']}');
      }
    } catch (e) {
      logger.e('Generate QR error: $e');
      rethrow;
    }
  }

  /// 轮询二维码扫描状态
  ///
  /// 返回状态码说明：
  /// - 86101：未扫码
  /// - 86090：已扫码未确认
  /// - 86038：二维码已过期
  /// - 0：成功
  Future<Map<String, dynamic>> pollQRStatus(String qrcodeKey) async {
    try {
      final response = await dio.get(
        _pollQRStatusUrl,
        queryParameters: {'qrcode_key': qrcodeKey},
      );

      logger.d('Poll QR status response: ${response.data}');

      if (response.data['code'] == 0) {
        // 从 data 中获取真正的状态码
        return {
          'code': response.data['data']['code'],
          'message': response.data['data']['message'],
          'data': response.data['data'],
        };
      } else {
        throw Exception('请求失败: ${response.data['message']}');
      }
    } catch (e) {
      logger.e('Poll QR status error: $e');
      rethrow;
    }
  }

  // 提取视频ID
  Future<String?> extractBiliVideoId(String url) async {
    try {
      // 直接匹配BV号或av号
      final bvRegex = RegExp(r'BV[a-zA-Z0-9]{10}');
      final avRegex = RegExp(r'av[0-9]+');

      // 如果是直接的bilibili.com链接
      if (url.contains('bilibili.com')) {
        final bvMatch = bvRegex.firstMatch(url);
        if (bvMatch != null) {
          return bvMatch.group(0);
        }

        final avMatch = avRegex.firstMatch(url);
        if (avMatch != null) {
          return avMatch.group(0);
        }
      }

      // 如果是短链接，需要请求获取重定向URL
      if (url.contains('b23.tv')) {
        final dio = Dio();
        final response = await dio.get(
          url,
          options: Options(
            followRedirects: false,
            validateStatus: (status) => status != null && status < 400,
          ),
        );

        // 从重定向响应中获取目标URL
        final location = response.headers.value('location');
        if (location != null) {
          logger.d('Redirected URL: $location');
          // 递归调用以解析重定向后的URL
          return extractBiliVideoId(location);
        }
      }

      return null;
    } catch (e) {
      logger.e('Error extracting video ID: $e');
      return null;
    }
  }

  /// 获取视频详细信息
  Future<Map<String, dynamic>?> getBiliVideoInfo(
      {String? bvid, String? aid}) async {
    try {
      // 构建请求URL
      final url = Uri.parse(_videoInfoUrl).replace(queryParameters: {
        if (bvid != null) 'bvid': bvid,
        if (aid != null) 'aid': aid,
      });

      // 使用dio发送请求
      final response = await dio.get(url.toString());
      logger.d('Get video info response: ${response.data}');

      // 检查返回码
      if (response.data['code'] == 0) {
        return response.data['data'];
      } else {
        logger.e('获取视频信息失败: ${response.data['message']}');
        return null;
      }
    } catch (e) {
      logger.e('获取视频信息时发生错误: $e');
      return null;
    }
  }

  /// 获取 img_key 和 sub_key
  Future<Map<String, String>> getWbiKeys() async {
    final resp = await dio.get('https://api.bilibili.com/x/web-interface/nav');
    final data = resp.data['data'];
    final wbiImg = data['wbi_img'];

    final imgUrl = wbiImg['img_url'] as String;
    final subUrl = wbiImg['sub_url'] as String;

    // 从URL中提取文件名作为key
    final imgKey = imgUrl.split('/').last.split('.').first;
    final subKey = subUrl.split('/').last.split('.').first;

    return {'img_key': imgKey, 'sub_key': subKey};
  }

  /// 获取视频流URL (FLV/MP4格式)
  Future<Map<String, dynamic>?> getBiliVideoStream({
    String? bvid,
    String? aid,
    required String cid,
    int qn = 80,
    int fnval = 1,
    String? cookie,
  }) async {
    try {
      // 构建基础参数 - 确保所有值都是字符串类型
      final params = {
        if (bvid != null) 'bvid': bvid,
        if (aid != null) 'avid': aid,
        'cid': cid,
        'qn': qn.toString(),
        'fnval': fnval.toString(),
        'fnver': '0',
        'fourk': '1',
        'platform': 'html5',
        'high_quality': '1',
      };
      logger.i('params: $params');

      // 添加WBI签名 - 修改这部分以确保数据格式正确
      final wbi_keys = await getWbiKeys();
      final data = {
        "params": params, // 修改这里，使用正确的键名
        "img_key": wbi_keys['img_key'],
        "sub_key": wbi_keys['sub_key'],
        "show_progress": false,
      };
      final messageController = Get.find<MessageController>();
      final resp = await messageController.request(data, "wbi_sign");
      if (resp.status == "error") {
        logger.e(resp.message);
        return null;
      }

      logger.i('signedParams: $resp.data');
      // 解析签名参数
      final signedParams = resp.data;
      logger.i('signedParams: $signedParams');

      // 将原始参数和签名参数合并
      final Uri baseUrl = Uri.parse(_videoPlayUrl);
      final Map<String, String> allParams = {
        ...params, // 原始参数
        ...Uri.splitQueryString(signedParams), // 签名参数 (wts和w_rid)
      };

      final url = baseUrl.replace(queryParameters: allParams);
      logger.i('Final URL: $url');

      final response = await dio.get(url.toString(),
          options: Options(headers: {
            'referer': 'https://www.bilibili.com',
            'user-agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36',
            if (cookie != null) 'cookie': cookie,
          }));

      logger.d('Get video stream response: ${response.data}');

      if (response.data['code'] == 0) {
        final data = response.data['data'];

        // 检查是否有可用的视频URL
        if (data['durl'] != null && data['durl'].isNotEmpty) {
          return {
            'urls': data['durl'].map((item) => item['url'].toString()).toList(),
            'quality': data['quality'],
            'format': data['format'],
            'timelength': data['timelength'],
            'accept_description': data['accept_description'],
            'accept_quality': data['accept_quality'],
          };
        }
      }

      logger.e('获取视频流失败: ${response.data}');
      return null;
    } catch (e, stackTrace) {
      logger.e('获取视频流时发生错误: $e');
      logger.e('Stack trace: $stackTrace'); // 添加堆栈跟踪以便调试
      return null;
    }
  }
}
