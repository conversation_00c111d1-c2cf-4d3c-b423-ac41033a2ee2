/// 提示词生成器工具类
///
/// 负责生成各种类型卡片的提示词，将提示词生成逻辑与控制器分离
class PromptGenerator {
  // 获取卡片类型的中文名称
  static String getCardTypeDisplayName(String cardType) {
    switch (cardType) {
      case "qa":
        return "问答";
      case "cloze":
        return "填空";
      case "choice":
        return "单选题";
      case "multi_choice":
        return "多选题";
      case "judge":
        return "判断题";
      default:
        return "问答";
    }
  }

  // 获取卡片难度的中文名称
  static String getDifficultyDisplayName(String difficultyLevel) {
    switch (difficultyLevel) {
      case "none":
        return "无";
      case "basic":
        return "基础";
      case "medium":
        return "中等";
      case "advanced":
        return "高级";
      default:
        return "中等";
    }
  }

  // 为卡片难度生成说明文本
  static String _getDifficultyInstructions(
      {List<String> difficulties = const [
        "none",
        "basic",
        "medium",
        "advanced"
      ]}) {
    String result = "";
    if (difficulties.contains("none")) {
      return "";
    }

    result += """
## 难度级别说明
""";

    for (var difficulty in difficulties) {
      if (difficulty == "basic") {
        result += """
### 基础难度标准
- **聚焦**：基本概念、核心定义、简单事实、基础术语。
- **表述**：使用直接、明确、易于理解的语言。
- **问题**：简单明了，不拐弯抹角，通常考察回忆和基本理解。
- **答案**：简短清晰，直击要点。
- **适用对象**：初学者或对该领域入门的学习者。
""";
      } else if (difficulty == "medium") {
        result += """
### 中等难度标准
- **聚焦**：概念的应用、比较异同、解释原因、简单分析、理解过程或机制。
- **表述**：语言可能更细致，可能需要一定的推理或联系多个信息点。
- **问题**：可能涉及"为什么"、"如何"，或要求在不同情境下应用知识，或者对信息进行初步分类或组织。
- **答案**：可能需要更详细的解释，或包含多个步骤/方面。
- **适用对象**：对基础知识有一定掌握，希望加深理解和应用能力的学习者。
""";
      } else if (difficulty == "advanced") {
        result += """
### 高级难度标准
- **聚焦**：复杂分析、综合评估、信息整合、解决非常规问题、批判性思维、提出见解。
- **表述**：语言精确、专业，可能包含特定领域的术语和抽象概念。
- **问题**：可能要求进行深入分析、评估不同观点、设计解决方案、或从多个角度解释复杂现象。
- **答案**：通常是结构化的、论证充分的，可能需要引用证据或原理，展示深层次的理解和洞察力。
- **适用对象**：对领域有较深理解，寻求挑战性问题以巩固和扩展专业知识的学习者或研究者。
""";
      }
    }
    return result;
  }

  // 为每种卡片类型生成详细的说明文本
  static String _getCardTypeInstructions(
      {required List<String> cardTypes,
      String clozeMode = "free_guess",
      bool isAnswerCloze = true,
      bool isPerClozePerCard = false}) {
    String result = """
## 卡片类型说明
""";

    for (var cardType in cardTypes) {
      switch (cardType) {
        case "qa":
          {
            if (isAnswerCloze) {
              result += """
### 问答卡片说明(qa)
**设计原则：**
- 问题应具有明确的指向性，避免模糊或多解的表述
- 答案应包含完整的知识点，但避免冗余信息
- 一个卡片只聚焦于一个知识点，便于记忆和复习
- `answer`字段必须使用`[[c1::挖空内容]]`语法标记关键内容
""";
            } else {
              result += """
### 问答卡片说明(qa)
**设计原则：**
- 问题应具有明确的指向性，避免模糊或多解的表述
- 答案应包含完整的知识点，但避免冗余信息
- 一个卡片只聚焦于一个知识点，便于记忆和复习
""";
            }
            break;
          }
        case "cloze":
          result += """
### 填空卡片说明(cloze)
**设计原则：**
- 挖空部分应选择关键词或短语，而非整句话
- 上下文应提供足够信息，使填空有明确指向
- 可以在一个句子中创建多个填空，编号应递增（c1, c2, c3...）
- 挖空内容必须使用 [[c1::挖空内容]] 语法
""";
          break;
        case "choice":
          result += """
### 单选题卡片说明(choice)
**设计原则：**
- 问题应明确具体，避免模糊表述
- 选项数量通常为4-5个，应有合理的干扰项
- 选项间应有明显区别，避免过于相似导致混淆
- 正确答案应只有一个，避免有争议的选项
""";
          break;
        case "multi_choice":
          result += """
### 多选题卡片说明(multi_choice)
**设计原则：**
- 问题中应明确指出需要选择多个答案
- 正确答案应有2个或以上，用||分隔
- 选项应有明确的区分度，避免模糊不清
- 解析应说明每个正确选项为何正确
""";
          break;
        case "judge":
          result += """
### 判断题卡片说明(judge)
**设计原则：**
- 陈述句应明确具体，避免模糊或有歧义的表述
- 应基于明确的事实或概念，而非主观观点
- 避免使用"总是"、"从不"等极端词汇
- 解析应提供充分的依据，说明为什么判断是对或错
""";
          break;
        default:
          break;
      }
    }
    return result;
  }

  // 生成输出格式说明文本
  static String _getOutputFormat(
      {required List<String> cardTypes, bool isAnswerCloze = true}) {
    String baseFormat = """
## 输出格式
**严格要求**：仅输出一个标准的JSON对象，不包含任何Markdown标记、解释性文字或其他额外文本。JSON结构如下：

```json
[
""";

    // 根据卡片类型添加对应的示例
    for (var cardType in cardTypes) {
      switch (cardType) {
        case "qa":
          {
            if (!isAnswerCloze) {
              baseFormat += """
  // 问答示例
  {
    "type": "qa",
    "fields": {
      "question": "问题内容",
      "answer": "答案内容",
      "options": "",
      "hint": "提示信息",
      "remark": ""
    },
    "tags": ["关键词1", "关键词2", "关键词3"]
  },
""";
            } else {
              baseFormat += """
  // 问答示例
  {
    "type": "qa",
    "fields": {
      "question": "问题内容",
      "answer": "[c1::关键信息1]]和[[c2::关键信息2]]等答案内容。", // 使用[[c1::xx]]语法标记关键内容
      "options": "",
      "hint": "提示信息",
      "remark": ""
    },
    "tags": ["关键词1", "关键词2", "关键词3"]
  },
""";
            }
          }
          break;
        case "cloze":
          baseFormat += """
  // 填空示例
  {
    "type": "cloze",
    "fields": {
      "question": "[[c1::关键信息1]]和[[c2::关键信息2]]等填空内容。", // 使用[[c1::xx]]标记填空,c1,c2为填空序号
      "answer": "",
      "options": "",
      "remark": ""
    },
    "tags": ["关键词1", "关键词2", "填空"]
  },
""";
          break;
        case "choice":
          baseFormat += """
  // 单选示例
  {
    "type": "choice",
    "fields": {
      "question": "问题内容",
      "answer": "1", // 正确答案序号（从1开始）
      "options": "选项1||选项2||选项3",
      "remark": ""
    },
    "tags": ["关键词1", "关键词2", "选择题"]
  },
""";
          break;
        case "multi_choice":
          baseFormat += """
  // 多选示例
  {
    "type": "multi_choice",
    "fields": {
      "question": "问题内容",
      "answer": "1||2", // 正确答案序号（从1开始），多个答案用||分隔
      "options": "选项1||选项2||选项3||选项4",
      "remark": ""
    },
    "tags": ["关键词1", "关键词2", "多选题"]
  },
""";
          break;
        case "judge":
          baseFormat += """
  // 判断示例
  {
    "type": "judge",
    "fields": {
      "question": "陈述内容",
      "answer": "1", // 1表示正确，2表示错误
      "options": "对||错",
      "remark": ""
    },
    "tags": ["关键词1", "关键词2", "判断题"]
  },
""";
          break;
      }
    }

    // 移除最后一个示例后的逗号
    if (cardTypes.isNotEmpty) {
      baseFormat = baseFormat.substring(0, baseFormat.length - 1);
    }

    baseFormat += """
]
```
**关键点**：
*   `tags` 数组应包含3-5个精准概括该卡片核心知识点的关键词。
*   `type` 必须是 """;

    // 根据参数中的卡片类型列出可选值
    if (cardTypes.isEmpty) {
      baseFormat += "`qa`, `cloze`, `choice`, `multi_choice`, `judge`";
    } else {
      List<String> typeOptions = cardTypes.map((type) => "`$type`").toList();
      baseFormat += typeOptions.join(", ");
    }

    baseFormat += " 之一。\n\n";

    return baseFormat;
  }

  /// 生成提示词
  ///
  /// 根据指定的参数生成适用于不同卡片类型的提示词
  ///
  /// 参数：
  /// - cardTypes: 卡片类型列表
  /// - userPrompt: 用户自定义提示
  /// - difficulty: 卡片难度设置
  static Future<String> generatePrompt({
    required List<String> cardTypes,
    required String userPrompt,
    required List<String> difficulties,
    String clozeMode = "free_guess",
    bool isAutoTag = true,
    bool isPerClozePerCard = true,
    bool isAnswerCloze = true,
  }) async {
    // 构建主要提示词部分
    String titleAndDescription = """
# Anki学习卡片生成器 

## 角色
你是一个专业的Anki学习卡片生成助手。你的任务是根据用户提供的学习材料，精准地提取核心知识点，并为这些知识点生成高质量、多样化的Anki卡片。

## 核心任务
接收用户提供的学习材料 `{{学习材料}}`，并完成以下操作：
1.  **识别核心知识点**：仔细分析材料，识别出所有关键概念、定义、重要事实、流程步骤、关键人物/事件等。确保没有遗漏。
2.  **生成多样化卡片**：针对每一个识别出的核心知识点，尝试生成下面【输出格式】部分要求的指定类型的卡片。
    *   **覆盖优先**：首要目标是确保所有识别出的核心知识点都被至少一种卡片覆盖。
    *   **多样性目标**：在覆盖所有知识点的基础上，力求为每个核心知识点创建尽可能多的适用卡片类型。如果某个知识点不适合某种卡片类型（例如，一个复杂的概念很难用判断题表达），则可以跳过该类型，但应在其他类型中体现。  
""";
    if (!difficulties.contains("none")) {
      titleAndDescription += """
4.  **卡片难度**：所有卡片难度统一设置为：${difficulties.map((difficulty) => getDifficultyDisplayName(difficulty)).join("、")} （请参考下面的"难度级别说明"）
""";
    }

    // 使用新函数生成输出格式说明
    String outputFormat =
        _getOutputFormat(cardTypes: cardTypes, isAnswerCloze: isAnswerCloze);

    // 为每种卡片类型添加说明
    String cardTypeInstructions = _getCardTypeInstructions(
        cardTypes: cardTypes,
        clozeMode: clozeMode,
        isAnswerCloze: isAnswerCloze,
        isPerClozePerCard: isPerClozePerCard);

    // 卡片难度说明
    final difficultyInstructions =
        _getDifficultyInstructions(difficulties: difficulties);

    // 质量要求
    String qualityRequirements = """
## 质量标准 (必须遵守)
1.  **知识点准确性**：所有卡片内容必须准确无误，与学习材料一致。
2.  **单一焦点**：每个卡片严格聚焦于一个明确的核心知识点或一个紧密相关的概念组合。
3.  **问题清晰度**：问题表述清晰、无歧义，让用户能准确理解考察点。
4.  **答案质量**：答案准确、简洁、完整。
5.  **填空精准性**：填空题的挖空部分必须是关键信息，上下文要充分。
6.  **选项合理性 (选择题)**：干扰项需具有迷惑性但又明显错误或不完全正确，与正确答案有清晰的区分度。
7.  **判断题客观性**：判断题的陈述应基于客观事实或公认定义。
8.  **全面覆盖**：尽最大努力确保学习材料中的所有核心知识点都有对应的卡片。
9.  **标签相关性**：`tags` 必须是与卡片知识点高度相关的关键词。
10. **格式合规**：严格遵守JSON输出格式，所有字段名和卡片类型标识符准确无误。
---
请严格按照以上要求处理以下学习材料：

`{{学习材料}}`

""";

    // 合并所有部分生成最终提示词
    String finalPrompt = titleAndDescription +
        outputFormat +
        cardTypeInstructions +
        difficultyInstructions +
        qualityRequirements;

    if (userPrompt.isNotEmpty) {
      finalPrompt += """

## 用户特殊要求
$userPrompt
""";
    }

    return finalPrompt;
  }

  /// 获取默认系统提示词
  static String getDefaultSystemPrompt() {
    return """
# Anki学习卡片生成器 

## 角色
你是一个专业的Anki学习卡片生成助手。你的任务是根据用户提供的学习材料，精准地提取核心知识点，并为这些知识点生成高质量、多样化的Anki卡片。

## 核心任务
接收用户提供的学习材料 `{{学习材料}}`，并完成以下操作：
1.  **语言一致性**：所有生成的卡片内容（问题、答案、选项、解释、标签等）的语言**必须**与用户提供的 `{{学习材料}}` 的语言完全一致。
2.  **识别核心知识点**：仔细分析材料，识别出所有关键概念、定义、重要事实、流程步骤、关键人物/事件等。确保没有遗漏。
3.  **生成多样化卡片**：针对每一个识别出的核心知识点，尝试生成以下5种类型的Anki卡片：问答(qa)、填空(cloze)、单选题(choice)、多选题(multi_choice)、判断题(judge)。
    *   **覆盖优先**：首要目标是确保所有识别出的核心知识点都被至少一种卡片覆盖。
    *   **多样性目标**：在覆盖所有知识点的基础上，力求为每个核心知识点创建尽可能多的适用卡片类型。如果某个知识点不适合某种卡片类型（例如，一个复杂的概念很难用判断题表达），则可以跳过该类型，但应在其他类型中体现。
4.  **卡片数量**：生成的卡片总数应与学习材料中核心知识点的数量和多样性相匹配。优先确保每个知识点都有高质量的代表性卡片，而不是追求数量。
5.  **卡片难度**：所有卡片难度统一设置为：**基础**。 （请参考下面的"难度级别说明"，如果需要其他难度，请修改此处的设置，例如"中等"或"高级"）

""";
  }
}
