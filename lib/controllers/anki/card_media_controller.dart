import 'dart:convert';
import 'dart:io';
import 'package:anki_guru/controllers/common.dart';
import 'package:dio/dio.dart' as dio;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

/// 图片压缩参数配置类
/// 管理图片压缩相关的参数设置
class CompressionParams extends GetxController {
  /// 选中的文件路径列表
  final selectedFilePaths = <String>[].obs;

  /// 压缩源类型：deck（牌组）或 file（文件）
  final source = "deck".obs;

  /// 压缩质量（1-100）
  final quality = "80".obs;

  /// 输出模式：same（同目录）或 custom（自定义目录）
  final outputMode = "same".obs;

  /// 自定义输出目录路径
  final outputDir = ''.obs;

  /// 获取源类型选项列表
  List<Map<String, String>> get sourceTypeList => [
        {"label": 'anki.card_media_manager.deck'.tr, "value": "deck"},
        {"label": 'anki.card_media_manager.file'.tr, "value": "file"},
      ];
}

/// 图片上传参数配置类
/// 管理图片上传到图床相关的参数设置
class UploadParams extends GetxController {
  /// PicGo 服务地址
  final picgoAddress = 'http://127.0.0.1:36677'.obs;
}

/// 图片扫描参数配置类
/// 管理图片扫描相关的参数设置
class ScanParams extends GetxController {
  /// 输出目录路径
  final outputDir = ''.obs;
}

/// 卡片图片管理控制器
///
/// 提供以下功能：
/// 1. 图片压缩 - 支持文件模式和牌组模式的图片压缩
/// 2. 图片上传 - 将Anki卡片中的图片上传到图床服务
/// 3. 图片扫描 - 扫描并导出牌组中的所有媒体文件
///
/// 该控制器遵循anki-guru的设计模式：
/// - 使用PathUtils进行路径操作
/// - 使用progressController.updateProgress进行错误处理
/// - 支持多用户和多平台操作
class CardMediaManagerController extends GetxController {
  /// 获取媒体类型选项列表
  List<Map<String, String>> get mediaTypeOptions => [
        {
          "label": 'anki.card_media_manager.media_type_local_image'.tr,
          "value": "local_image"
        },
        {
          "label": 'anki.card_media_manager.media_type_local_audio'.tr,
          "value": "local_audio"
        },
        {
          "label": 'anki.card_media_manager.media_type_local_video'.tr,
          "value": "local_video"
        },
        {
          "label": 'anki.card_media_manager.media_type_network_image'.tr,
          "value": "network_image"
        },
        {
          "label": 'anki.card_media_manager.media_type_network_audio'.tr,
          "value": "network_audio"
        },
        {
          "label": 'anki.card_media_manager.media_type_network_video'.tr,
          "value": "network_video"
        },
      ];

  /// 当前选中的父级牌组
  final parentDeck = ''.obs;
  final mediaTypeList = ["local_image"].obs;

  /// 标签页控制器，管理压缩/上传/扫描三个标签页
  final tabController = ShadTabsController(value: 'compression');

  /// 参数控制器实例
  final compressionParams = Get.put(CompressionParams());
  final uploadParams = Get.put(UploadParams());
  final scanParams = Get.put(ScanParams());

  /// 依赖的通用控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final ankiConnectController = Get.find<AnkiConnectController>();
  final webviewController = Get.find<WebviewController>();

  @override
  void onInit() async {
    super.onInit();

    // 检查 AnkiConnect 数据是否已加载
    if (ankiConnectController.modelList.isEmpty ||
        ankiConnectController.parentDeckList.isEmpty) {
      await ankiConnectController.resetAnkiConnectData();
    }

    // 设置初始值
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }
    scanParams.outputDir.value = await PathUtils.downloadDir;
  }

  @override
  void dispose() {
    tabController.dispose();
    compressionParams.dispose();
    uploadParams.dispose();
    scanParams.dispose();
    super.dispose();
  }

  /// 压缩图片
  ///
  /// 根据选择的源类型（文件或牌组）执行图片压缩操作
  ///
  /// [context] - 用于显示进度对话框的上下文
  ///
  /// 支持两种模式：
  /// - 文件模式：压缩用户选择的特定文件
  /// - 牌组模式：压缩指定牌组中所有卡片的图片（原地压缩）
  Future<void> compressImages(BuildContext context) async {
    try {
      // 验证质量参数
      final quality = int.tryParse(compressionParams.quality.value);
      if (quality == null || quality < 1 || quality > 100) {
        showToastNotification(
            context, "anki.card_media_manager.invalid_quality".tr, "",
            type: "error");
        return;
      }

      // 根据源类型处理
      if (compressionParams.source.value == "file") {
        progressController.reset(
          showOutputHint: true,
          numberButtons: 2,
        );
        progressController.showProgressDialog(context);
        await _compressFileMode(quality);
      } else if (compressionParams.source.value == "deck") {
        progressController.reset(
          showOutputHint: false,
          numberButtons: 0,
        );
        progressController.showProgressDialog(context);
        await _compressDeckModeSimplified(quality);
      } else {
        progressController.updateProgress(
          status: "error",
          message: "anki.card_media_manager.invalid_source_mode".tr,
        );
      }
    } catch (e) {
      logger.e("Image compression failed: $e");
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.compression_failed"
              .trParams({'error': e.toString()}),
      );
    }
  }

  /// 上传图片到图床
  ///
  /// 将指定牌组中的所有媒体文件上传到PicGo图床服务
  /// 上传成功后会自动更新卡片中的媒体引用为图床URL
  ///
  /// [context] - 用于显示进度对话框的上下文
  ///
  /// 功能特性：
  /// - 支持图片、音频、视频文件上传（根据参数配置）
  /// - 支持网络图片处理（根据参数配置）
  /// - 自动更新卡片中的媒体引用
  /// - 提供详细的上传进度反馈
  Future<void> uploadImages(BuildContext context) async {
    try {
      progressController.reset(
        showOutputHint: false,
        numberButtons: 0,
      );
      progressController.showProgressDialog(context);

      // 验证PicGo地址
      if (uploadParams.picgoAddress.value.isEmpty) {
        progressController.updateProgress(
          status: "error",
          message: "anki.card_media_manager.picgo_address_cannot_empty".tr,
        );
        return;
      }

      progressController.updateProgress(
        status: "running",
        message: "anki.card_media_manager.scanning_deck".tr,
        current: 0,
        total: 100,
      );

      await _uploadImagesSimplified();
    } catch (e) {
      logger.e("Image upload failed: $e");
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.upload_failed"
              .trParams({'error': e.toString()}),
      );
    }
  }

  /// 简化的上传图片流程
  ///
  /// 新的简化工作流程：
  /// 1. 迭代牌组中的所有笔记
  /// 2. 逐字段处理每个笔记以查找媒体文件
  /// 3. 上传找到的媒体文件到PicGo
  /// 4. 更新字段中的媒体引用为新URL
  /// 5. 批量更新有变化的笔记
  Future<void> _uploadImagesSimplified() async {
    // 初始化媒体路径缓存 (0-10%)
    progressController.updateProgress(
      status: "running",
      message: "anki.card_media_manager.initializing".tr,
      current: 0,
      total: 100,
    );

    if (!await _initializeMediaPath()) {
      return;
    }

    // 验证牌组选择并获取笔记 (10-20%)
    progressController.updateProgress(
      status: "running",
      message: "anki.card_media_manager.getting_deck_cards".tr,
      current: 10,
      total: 100,
    );

    final notes = await _validateAndGetDeckCards(parentDeck.value);
    if (notes == null) return;

    logger.i("Found ${notes.length} notes in deck: ${parentDeck.value}");
    // 收集所有需要上传的媒体文件 (20-40%)
    final allMediaFiles = <String>{};
    final noteMediaMapping = <String, Map<String, List<String>>>{};
    final allMediaReferences = <Map<String, dynamic>>[];

    for (int i = 0; i < notes.length; i++) {
      final note = notes[i];
      final noteId = note['noteId']?.toString() ?? i.toString();
      final fields = note['fields'] as Map<String, dynamic>? ?? {};
      final noteFieldMedia = <String, List<String>>{};

      // 逐字段处理笔记以查找媒体文件
      for (final fieldEntry in fields.entries) {
        final fieldName = fieldEntry.key;
        final fieldValue = fieldEntry.value;
        if (fieldValue is Map && fieldValue.containsKey('value')) {
          final htmlContent = fieldValue['value'] as String? ?? '';

          // 使用webview controller提取媒体文件和上下文
          final extractionResult =
              await webviewController.extractMediaPathsFromHtmlWithContext(
            htmlContent,
            mediaTypeList: mediaTypeList.toList(),
          );

          final fieldMediaFiles =
              (extractionResult['files'] as List<dynamic>? ?? [])
                  .map((e) => e.toString())
                  .toList();
          final fieldReferences =
              (extractionResult['references'] as List<dynamic>? ?? [])
                  .cast<Map<String, dynamic>>();

          // 转换为完整路径并验证文件存在
          final validMediaFiles = <String>[];
          for (final relativePath in fieldMediaFiles) {
            // 检查是否为网络图片
            if (relativePath.startsWith('http://') ||
                relativePath.startsWith('https://')) {
              // 网络图片直接添加，无需本地文件验证
              if (mediaTypeList.contains('network_image') ||
                  mediaTypeList.contains('network_audio') ||
                  mediaTypeList.contains('network_video')) {
                allMediaFiles.add(relativePath);
                validMediaFiles.add(relativePath);
              }
            } else if (relativePath.startsWith('data:')) {
              // Base64图片直接添加，无需本地文件验证
              if (mediaTypeList.contains('local_image')) {
                allMediaFiles.add(relativePath);
                validMediaFiles.add(relativePath);
              }
            } else {
              // 本地文件需要构建完整路径并验证存在性
              final fullPath = _buildMediaPath(relativePath);
              if (fullPath != null && File(fullPath).existsSync()) {
                allMediaFiles.add(fullPath);
                validMediaFiles.add(fullPath);
              }
            }
          }

          if (validMediaFiles.isNotEmpty) {
            noteFieldMedia[fieldName] = validMediaFiles;

            // 添加字段上下文到引用
            for (final ref in fieldReferences) {
              final enhancedRef = Map<String, dynamic>.from(ref);
              enhancedRef['noteId'] = noteId;
              enhancedRef['fieldName'] = fieldName;
              enhancedRef['fieldContent'] = htmlContent;
              allMediaReferences.add(enhancedRef);
              logger.d(
                  "Created media reference: ${ref['filePath']} (type: ${ref['referenceType']}) for note $noteId field $fieldName");
            }
          }
        }
      }

      if (noteFieldMedia.isNotEmpty) {
        noteMediaMapping[noteId] = noteFieldMedia;
      }

      // 更新进度
      const progressRange = 20.0; // 20-40%
      final currentProgress = 20.0 + (progressRange * (i + 1) / notes.length);
      progressController.updateProgress(
        status: "running",
        message: "anki.card_media_manager.scanning_cards".trParams({
            'current': (i + 1).toString(),
            'total': notes.length.toString(),
          }),
          current: currentProgress,
          total: 100.0,
      );
    }

    if (allMediaFiles.isEmpty) {
      progressController.updateProgress(
        status: "success",
        message: "anki.card_media_manager.no_media_found".tr,
      );
      return;
    }

    logger.i("Found ${allMediaFiles.length} unique media files to upload");

    // 上传媒体文件到PicGo (40-80%)
    final uploadedFiles = <String, String>{};
    Map<String, int> uploadResults;
    try {
      uploadResults = await _uploadMediaFilesToPicGoSimplified(
          allMediaFiles.toList(), uploadedFiles, 40.0, 80.0);
    } catch (e) {
      logger.e("Upload process failed: $e");
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.upload_failed"
            .trParams({'error': e.toString()}),
      );
      return;
    }

    if (uploadedFiles.isEmpty) {
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.upload_failed_no_files_uploaded".tr,
      );
      return;
    }

    // 调试：打印文件映射信息
    logger.i("File mapping created with ${uploadedFiles.length} entries:");
    uploadedFiles.forEach((key, value) {
      logger.i("  $key -> $value");
    });

    // 批量更新笔记中的媒体引用 (80-100%)
    await _updateNotesMediaReferencesSimplified(
        notes, uploadedFiles, allMediaReferences, 80.0, 100.0, uploadResults);
  }

  /// 简化的PicGo上传处理
  Future<Map<String, int>> _uploadMediaFilesToPicGoSimplified(
      List<String> mediaFiles,
      Map<String, String> uploadedFiles,
      double progressStart,
      double progressEnd) async {
    final dioClient = dio.Dio();
    var successCount = 0;
    var failedCount = 0;

    for (int i = 0; i < mediaFiles.length; i++) {
      final filePath = mediaFiles[i];
      final fileName = _getFileNameFromPath(filePath);

      try {
        // 计算上传进度
        final uploadProgressRange = progressEnd - progressStart;
        final currentUploadProgress =
            progressStart + (uploadProgressRange * (i + 1) / mediaFiles.length);

        progressController.updateProgress(
          status: "running",
          message: "anki.card_media_manager.uploading_file".trParams({
              'current': (i + 1).toString(),
              'total': mediaFiles.length.toString(),
              'filename': fileName,
            }),
            current: currentUploadProgress,
            total: 100.0,
        );

        // 检查文件类型并处理
        if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
          // 处理网络图片：下载到临时文件后上传
          await _handleNetworkImageUpload(
              filePath, fileName, uploadedFiles, dioClient);
          successCount++;
        } else if (filePath.startsWith('data:')) {
          // 处理base64图片：转换为临时文件后上传
          await _handleBase64ImageUpload(
              filePath, fileName, uploadedFiles, dioClient);
          successCount++;
        } else {
          // 处理本地文件：直接上传
          await _handleLocalFileUpload(
              filePath, fileName, uploadedFiles, dioClient);
          successCount++;
        }
      } catch (e) {
        failedCount++;
        logger.e("Error uploading $fileName: $e");
      }
    }

    logger.i(
        "Upload completed: $successCount successful, $failedCount failed out of ${mediaFiles.length} files");

    // 如果所有文件都上传失败，抛出异常
    if (successCount == 0 && failedCount > 0) {
      throw Exception("All $failedCount file(s) failed to upload");
    }

    return {
      'success': successCount,
      'failed': failedCount,
      'total': mediaFiles.length,
    };
  }

  /// 处理网络图片上传
  Future<void> _handleNetworkImageUpload(String networkUrl, String fileName,
      Map<String, String> uploadedFiles, dio.Dio dioClient) async {
    // 下载网络图片到临时文件
    final tempDir = Directory.systemTemp;
    final tempFile =
        File(PathUtils.join([tempDir.path, 'anki_guru_temp_$fileName']));

    try {
      // 下载图片
      final response = await dioClient.download(networkUrl, tempFile.path);
      if (response.statusCode == 200) {
        // 上传临时文件到PicGo，但使用原始网络URL作为映射键
        await _uploadFileToPicGoWithOriginalUrl(
            tempFile.path, fileName, networkUrl, uploadedFiles, dioClient);
      } else {
        throw Exception(
            'Failed to download network image: HTTP ${response.statusCode}');
      }
    } finally {
      // 清理临时文件
      if (tempFile.existsSync()) {
        tempFile.deleteSync();
      }
    }
  }

  /// 处理base64图片上传
  Future<void> _handleBase64ImageUpload(String base64DataUrl, String fileName,
      Map<String, String> uploadedFiles, dio.Dio dioClient) async {
    // 解析base64数据URL
    final base64Match =
        RegExp(r'^data:image\/([^;]+);base64,(.+)$').firstMatch(base64DataUrl);
    if (base64Match == null) {
      throw Exception('Invalid base64 data URL format');
    }

    final imageFormat = base64Match.group(1)!;
    final base64Data = base64Match.group(2)!;

    // 生成临时文件名
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final tempFileName = 'anki_guru_base64_$timestamp.$imageFormat';
    final tempDir = Directory.systemTemp;
    final tempFile = File(PathUtils.join([tempDir.path, tempFileName]));

    try {
      // 将base64数据转换为字节并写入临时文件
      final bytes = base64Decode(base64Data);
      await tempFile.writeAsBytes(bytes);

      // 上传临时文件到PicGo，使用原始base64 URL作为映射键
      await _uploadFileToPicGoWithOriginalUrl(
          tempFile.path, tempFileName, base64DataUrl, uploadedFiles, dioClient);
    } finally {
      // 清理临时文件
      if (tempFile.existsSync()) {
        tempFile.deleteSync();
      }
    }
  }

  /// 处理本地文件上传
  Future<void> _handleLocalFileUpload(String filePath, String fileName,
      Map<String, String> uploadedFiles, dio.Dio dioClient) async {
    await _uploadFileToPicGo(filePath, fileName, uploadedFiles, dioClient);
  }

  /// 上传文件到PicGo
  Future<void> _uploadFileToPicGo(String filePath, String fileName,
      Map<String, String> uploadedFiles, dio.Dio dioClient) async {
    await _uploadFileToPicGoWithOriginalUrl(
        filePath, fileName, filePath, uploadedFiles, dioClient);
  }

  /// 上传文件到PicGo，使用指定的原始URL作为映射键
  Future<void> _uploadFileToPicGoWithOriginalUrl(
      String filePath,
      String fileName,
      String originalUrl,
      Map<String, String> uploadedFiles,
      dio.Dio dioClient) async {
    // 准备JSON请求数据
    final requestData = {
      'list': [filePath],
    };

    // 发送上传请求到PicGo
    final response = await dioClient.post(
      '${uploadParams.picgoAddress.value}/upload',
      data: requestData,
      options: dio.Options(
        headers: {
          'Content-Type': 'application/json',
        },
        sendTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
      ),
    );

    if (response.statusCode == 200 && response.data['success'] == true) {
      final result = response.data['result'];
      if (result is List && result.isNotEmpty) {
        final uploadedUrl = result[0] as String;
        // 使用原始URL作为映射键（对于网络图片，这是原始网络URL；对于本地文件，这是文件路径）
        uploadedFiles[originalUrl] = uploadedUrl;

        // 根据文件类型添加适当的映射
        if (originalUrl.startsWith('http://') ||
            originalUrl.startsWith('https://')) {
          // 网络图片：添加原始文件名的映射（去掉临时文件前缀）
          final originalFileName = _getFileNameFromPath(originalUrl);
          uploadedFiles[originalFileName] = uploadedUrl;
          logger.i(
              "Successfully uploaded network image: $originalFileName -> $uploadedUrl (from: $originalUrl)");
        } else if (originalUrl.startsWith('data:')) {
          // Base64图片：添加生成的文件名映射
          final generatedFileName = _getFileNameFromPath(originalUrl);
          uploadedFiles[generatedFileName] = uploadedUrl;
          logger.i(
              "Successfully uploaded base64 image: $generatedFileName -> $uploadedUrl (from base64 data)");
        } else {
          // 本地文件：添加文件名映射
          uploadedFiles[fileName] = uploadedUrl;
          logger
              .i("Successfully uploaded local file: $fileName -> $uploadedUrl");
        }
      } else {
        throw Exception("Invalid response format - ${response.data}");
      }
    } else {
      throw Exception("HTTP ${response.statusCode} - ${response.data}");
    }
  }

  /// 从路径中提取文件名
  String _getFileNameFromPath(String path) {
    if (path.startsWith('http://') || path.startsWith('https://')) {
      // 对于网络URL，提取最后一部分作为文件名
      final uri = Uri.parse(path);
      final segments = uri.pathSegments;
      if (segments.isNotEmpty) {
        return segments.last;
      } else {
        // 如果无法从URL提取文件名，生成一个默认名称
        return 'network_image_${DateTime.now().millisecondsSinceEpoch}.jpg';
      }
    } else if (path.startsWith('data:')) {
      // 对于base64数据URL，解析格式并生成文件名
      final base64Match =
          RegExp(r'^data:image\/([^;]+);base64,').firstMatch(path);
      if (base64Match != null) {
        final imageFormat = base64Match.group(1)!;
        return 'base64_image_${DateTime.now().millisecondsSinceEpoch}.$imageFormat';
      } else {
        return 'base64_image_${DateTime.now().millisecondsSinceEpoch}.jpg';
      }
    } else {
      // 对于本地路径，使用PathUtils
      return PathUtils(path).name;
    }
  }

  /// 简化的笔记媒体引用更新
  Future<void> _updateNotesMediaReferencesSimplified(
      List<Map<String, dynamic>> notes,
      Map<String, String> uploadedFiles,
      List<Map<String, dynamic>> allMediaReferences,
      double progressStart,
      double progressEnd,
      Map<String, int> uploadResults) async {
    var updatedCount = 0;
    final notesToUpdate = <Map<String, dynamic>>[];

    for (int i = 0; i < notes.length; i++) {
      final note = notes[i];
      final noteId = note['noteId']?.toString() ?? i.toString();
      final fields = note['fields'] as Map<String, dynamic>? ?? {};
      var hasUpdates = false;
      final updatedFields = <String, String>{};

      // 计算更新进度
      final updateProgressRange = progressEnd - progressStart;
      final currentUpdateProgress =
          progressStart + (updateProgressRange * (i + 1) / notes.length);

      progressController.updateProgress(
        status: "running",
        message: "anki.card_media_manager.updating_note_references".trParams({
            'current': (i + 1).toString(),
            'total': notes.length.toString(),
          }),
          current: currentUpdateProgress,
          total: 100.0,
      );

      // 获取此笔记的媒体引用
      final noteReferences =
          allMediaReferences.where((ref) => ref['noteId'] == noteId).toList();

      // 遍历所有字段
      for (final fieldEntry in fields.entries) {
        final fieldName = fieldEntry.key;
        final fieldValue = fieldEntry.value;

        if (fieldValue is Map && fieldValue.containsKey('value')) {
          var htmlContent = fieldValue['value'] as String? ?? '';

          // 过滤此字段的引用
          final fieldReferences = noteReferences
              .where((ref) => ref['fieldName'] == fieldName)
              .toList();

          if (fieldReferences.isNotEmpty) {
            logger.d(
                "Processing field $fieldName for note $noteId with ${fieldReferences.length} references");
            for (final ref in fieldReferences) {
              logger.d(
                  "  Reference: ${ref['filePath']} (type: ${ref['referenceType']})");
            }

            final updatedContent = await webviewController
                .replaceMediaReferencesInHtml(htmlContent, uploadedFiles,
                    mediaReferences: fieldReferences);

            if (updatedContent != htmlContent) {
              updatedFields[fieldName] = updatedContent;
              hasUpdates = true;
              logger.i("Field $fieldName content updated for note $noteId");
              logger.d("Original: $htmlContent");
              logger.d("Updated:  $updatedContent");
            } else {
              logger.w(
                  "No content changes detected for field $fieldName in note $noteId");
            }
          }
        }
      }

      // 如果有更新，添加到批量更新列表
      if (hasUpdates) {
        notesToUpdate.add({
          'id': note['noteId'],
          'fields': updatedFields,
        });
        updatedCount++;
      }
    }

    // 批量更新所有有变化的笔记
    int failedUpdateCount = 0;
    if (notesToUpdate.isNotEmpty) {
      logger.i(
          "Batch updating ${notesToUpdate.length} notes with new media URLs");

      for (final noteData in notesToUpdate) {
        try {
          await ankiConnectController.updateNote(noteData);
        } catch (e) {
          logger.e("Failed to update note ${noteData['id']}: $e");
          updatedCount--; // 减少成功计数
          failedUpdateCount++; // 增加失败计数
        }
      }
    }

    // 显示最终结果
    // 根据结果确定状态：如果所有更新都失败，则为错误状态
    final status =
        (updatedCount == 0 && failedUpdateCount > 0) ? "error" : "completed";

    progressController.updateProgress(
        status: status,
        message: "anki.card_media_manager.upload_completed".trParams({
          'success': uploadResults['success'].toString(),
          'total': uploadResults['total'].toString(),
          'failed': uploadResults['failed'].toString(),
        }),
        current: status == "error" ? 0.0 : 100.0,
        total: 100.0,
    );

    logger.i("Updated $updatedCount notes with new media URLs");
  }

  /// 扫描图片
  ///
  /// 扫描指定牌组中的所有媒体文件并复制到输出目录
  ///
  /// [context] - 用于显示进度对话框的上下文
  ///
  /// 功能特性：
  /// - 扫描牌组中的所有媒体文件（图片、音频、视频）
  /// - 将文件复制到指定的输出目录
  /// - 自动处理文件名冲突（添加序号后缀）
  /// - 不包含网络图片（仅处理本地文件）
  Future<void> scanImages(BuildContext context) async {
    try {
      // 确保输出目录存在
      final outputDir = Directory(scanParams.outputDir.value);
      if (!outputDir.existsSync()) {
        outputDir.createSync(recursive: true);
      }

      await _scanImagesSimplified();
    } catch (e) {
      logger.e("Image scan failed: $e");
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.scan_failed"
            .trParams({'error': e.toString()}),
      );
    }
  }

  /// 简化的图片扫描流程
  ///
  /// 新的简化工作流程：
  /// 1. 迭代牌组中的所有笔记
  /// 2. 逐字段处理每个笔记以查找媒体文件
  /// 3. 复制找到的媒体文件到输出目录
  /// 4. 无需更新字段引用（扫描操作）
  Future<void> _scanImagesSimplified() async {
    // 初始化媒体路径缓存 (0-10%)
    progressController.updateProgress(
      status: "running",
      message: "anki.card_media_manager.initializing".tr,
      current: 0,
      total: 100,
    );

    if (!await _initializeMediaPath()) {
      return;
    }

    // 验证牌组选择并获取笔记 (10-20%)
    progressController.updateProgress(
      status: "running",
      message: "anki.card_media_manager.getting_deck_cards".tr,
      current: 10,
      total: 100,
    );

    final notes = await _validateAndGetDeckCards(parentDeck.value);
    if (notes == null) return;

    logger.i("Found ${notes.length} notes in deck: ${parentDeck.value}");

    // 收集所有需要复制的媒体文件 (20-70%)
    final allMediaFiles = <String>{};

    for (int i = 0; i < notes.length; i++) {
      final note = notes[i];
      final fields = note['fields'] as Map<String, dynamic>? ?? {};

      // 逐字段处理笔记以查找媒体文件
      for (final fieldEntry in fields.entries) {
        final fieldValue = fieldEntry.value;
        if (fieldValue is Map && fieldValue.containsKey('value')) {
          final htmlContent = fieldValue['value'] as String? ?? '';

          // 使用webview controller提取媒体文件
          // 扫描模式可以包含本地和网络媒体文件
          final extractionResult =
              await webviewController.extractMediaPathsFromHtmlWithContext(
            htmlContent,
            mediaTypeList: mediaTypeList.toList(),
          );

          final fieldMediaFiles =
              (extractionResult['files'] as List<dynamic>? ?? [])
                  .map((e) => e.toString())
                  .toList();

          // 处理媒体文件：本地文件验证存在性，网络文件准备下载
          for (final relativePath in fieldMediaFiles) {
            if (relativePath.startsWith('http://') ||
                relativePath.startsWith('https://')) {
              // 网络文件：检查是否包含网络媒体类型
              if (mediaTypeList.any((type) => type.startsWith('network_'))) {
                allMediaFiles.add(relativePath);
              }
            } else {
              // 本地文件：构建完整路径并验证存在性
              final fullPath = _buildMediaPath(relativePath);
              if (fullPath != null && File(fullPath).existsSync()) {
                allMediaFiles.add(fullPath);
              }
            }
          }
        }
      }

      // 更新进度
      const progressRange = 50.0; // 20-70%
      final currentProgress = 20.0 + (progressRange * (i + 1) / notes.length);
      progressController.updateProgress(
        status: "running",
        message: "anki.card_media_manager.scanning_cards".trParams({
            'current': (i + 1).toString(),
            'total': notes.length.toString(),
          }),
          current: currentProgress,
          total: 100.0,
      );
    }

    if (allMediaFiles.isEmpty) {
      progressController.updateProgress(
        status: "success",
        message: "anki.card_media_manager.no_media_found".tr,
      );
      return;
    }

    logger.i("Found ${allMediaFiles.length} unique media files to copy");

    // 复制媒体文件到输出目录 (70-100%)
    await _copyMediaFilesToDirectory(
        allMediaFiles.toList(), scanParams.outputDir.value, 70.0, 100.0);
  }

  Future<void> submit(BuildContext context) async {
    if (tabController.selected == "compression") {
      await compressImages(context);
    } else if (tabController.selected == "upload") {
      await uploadImages(context);
    } else if (tabController.selected == "scan") {
      progressController.reset(
        showOutputHint: true,
        numberButtons: 2,
      );
      progressController.showProgressDialog(context);
      await scanImages(context);
    }
  }

  /// 文件模式压缩
  Future<void> _compressFileMode(int quality) async {
    // 验证输入
    if (compressionParams.selectedFilePaths.isEmpty) {
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.no_files_selected".tr,
      );
      return;
    }

    // 验证输入文件并生成有效文件列表
    final inputPaths = <String>[];

    for (final inputPath in compressionParams.selectedFilePaths) {
      final file = File(inputPath);
      if (!file.existsSync()) {
        logger.w("File not found: $inputPath");
        continue;
      }

      inputPaths.add(inputPath);
    }

    if (inputPaths.isEmpty) {
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.no_valid_files".tr,
      );
      return;
    }

    // Validate output_dir for custom mode
    if (compressionParams.outputMode.value == "custom" &&
        compressionParams.outputDir.value.isEmpty) {
      progressController.updateProgress(
        status: "error",
        message: "common.fileSelect.directoryCannotBeEmpty".tr,
      );
      return;
    }

    // 准备请求数据
    final data = {
      "input_paths": inputPaths,
      "quality": quality,
      "format": "jpeg", // 默认使用JPEG格式
      "keep_metadata": false,
      "output_mode": compressionParams.outputMode.value,
      "output_dir": compressionParams.outputDir.value.isNotEmpty
          ? compressionParams.outputDir.value
          : null,
      "show_progress": true,
      "progress_start": 0.0, // 文件模式使用完整进度范围
      "progress_end": 100.0,
    };

    // 发送请求到Rust后端
    final resp = await messageController.request(data, "image/compress");

    if (resp.status == "success") {
      // 解析压缩结果
      final results = jsonDecode(resp.data) as List;
      final successCount = results.where((r) => r['success'] == true).length;
      final failedCount = results.length - successCount;
      logger.i(results);
      final totalCount = results.length;
      progressController.outputPath.value = results[0]['output_path'];

      // 根据结果确定状态：如果所有压缩都失败，则为错误状态
      final status =
          (successCount == 0 && failedCount > 0) ? "error" : "completed";

      progressController.updateProgress(
          status: status,
          message: "anki.card_media_manager.compression_completed".trParams({
            'success': successCount.toString(),
            'total': totalCount.toString(),
          }),
          current: status == "error" ? 0.0 : 100.0,
          total: 100.0,
      );

      // Results processed successfully
    } else {
      progressController.updateProgress(
        status: "error",
        message: resp.message.isNotEmpty
              ? resp.message
              : "anki.card_media_manager.compression_failed".tr
      );
    }
  }

  /// 牌组模式压缩处理（简化版本）
  ///
  /// 新的简化工作流程：
  /// 1. 迭代牌组中的所有笔记
  /// 2. 逐字段处理每个笔记以查找图片
  /// 3. 压缩找到的图片文件
  /// 4. 更新字段中的媒体引用指向压缩后的文件
  /// 5. 批量更新有变化的笔记
  Future<void> _compressDeckModeSimplified(int quality) async {
    try {
      // 初始化媒体路径缓存 (0-10%)
      progressController.updateProgress(
        status: "running",
        message: "anki.card_media_manager.initializing".tr,
        current: 0,
        total: 100,
      );

      if (!await _initializeMediaPath()) {
        return;
      }

      // 验证牌组选择并获取笔记 (10-20%)
      progressController.updateProgress(
        status: "running",
        message: "anki.card_media_manager.getting_deck_cards".tr,
        current: 10,
        total: 100,
      );

      final notes = await _validateAndGetDeckCards(parentDeck.value);
      if (notes == null) return;

      logger.i("Found ${notes.length} notes in deck: ${parentDeck.value}");

      // 收集所有需要压缩的图片文件和媒体引用 (20-40%)
      final allImageFiles = <String>{};
      final noteImageMapping = <String, Map<String, List<String>>>{};
      final allMediaReferences = <Map<String, dynamic>>[];

      for (int i = 0; i < notes.length; i++) {
        final note = notes[i];
        final noteId = note['noteId']?.toString() ?? i.toString();
        final fields = note['fields'] as Map<String, dynamic>? ?? {};
        final noteFieldImages = <String, List<String>>{};

        // 逐字段处理笔记以查找图片
        for (final fieldEntry in fields.entries) {
          final fieldName = fieldEntry.key;
          final fieldValue = fieldEntry.value;
          if (fieldValue is Map && fieldValue.containsKey('value')) {
            final htmlContent = fieldValue['value'] as String? ?? '';

            // 使用webview controller提取图片和上下文
            final extractionResult =
                await webviewController.extractMediaPathsFromHtmlWithContext(
              htmlContent,
              mediaTypeList: ['local_image'], // 压缩模式只处理本地图片
            );

            final fieldImageFiles =
                (extractionResult['files'] as List<dynamic>? ?? [])
                    .map((e) => e.toString())
                    .toList();
            final fieldReferences =
                (extractionResult['references'] as List<dynamic>? ?? [])
                    .cast<Map<String, dynamic>>();

            // 转换为完整路径并验证文件存在
            final validImageFiles = <String>[];
            for (final relativePath in fieldImageFiles) {
              final fullPath = _buildMediaPath(relativePath);
              if (fullPath != null && File(fullPath).existsSync()) {
                allImageFiles.add(fullPath);
                validImageFiles.add(fullPath);
              }
            }

            if (validImageFiles.isNotEmpty) {
              noteFieldImages[fieldName] = validImageFiles;

              // 添加字段上下文到引用
              for (final ref in fieldReferences) {
                final enhancedRef = Map<String, dynamic>.from(ref);
                enhancedRef['noteId'] = noteId;
                enhancedRef['fieldName'] = fieldName;
                enhancedRef['fieldContent'] = htmlContent;
                allMediaReferences.add(enhancedRef);
              }
            }
          }
        }

        if (noteFieldImages.isNotEmpty) {
          noteImageMapping[noteId] = noteFieldImages;
        }

        // 更新进度
        const progressRange = 20.0; // 20-40%
        final currentProgress = 20.0 + (progressRange * (i + 1) / notes.length);
        progressController.updateProgress(
          status: "running",
          message: "anki.card_media_manager.scanning_cards".trParams({
              'current': (i + 1).toString(),
              'total': notes.length.toString(),
            }),
            current: currentProgress,
            total: 100.0,
        );
      }

      if (allImageFiles.isEmpty) {
        progressController.updateProgress(
          status: "success",
          message: "anki.card_media_manager.no_media_found".tr,
        );
        return;
      }

      logger.i("Found ${allImageFiles.length} unique images to compress");

      // 压缩图片文件 (40-80%)
      progressController.updateProgress(
        status: "running",
        message: "anki.card_media_manager.compressing_images".tr,
        current: 40,
        total: 100,
      );

      final data = {
        "input_paths": allImageFiles.toList(),
        "quality": quality,
        "format": "jpeg", // 默认使用JPEG格式
        "keep_metadata": false,
        "output_mode": "same", // 创建压缩版本文件
        "output_dir": null,
        "show_progress": true,
        "progress_start": 40.0,
        "progress_end": 80.0,
      };

      final resp = await messageController.request(data, "image/compress");

      if (resp.status == "success") {
        final results = jsonDecode(resp.data) as List;
        final successCount = results.where((r) => r['success'] == true).length;
        final totalCount = results.length;

        logger.i(
            "Compression completed: $successCount/$totalCount files compressed successfully");

        // 构建文件映射：原文件路径 -> 压缩文件路径
        final compressionMapping = <String, String>{};
        int actualCompressedCount = 0;
        for (final result in results) {
          if (result['success'] == true) {
            final inputPath = result['input_path'] as String;
            final outputPath = result['output_path'] as String;

            // 获取文件名用于映射
            final inputFileName = PathUtils(inputPath).name;
            final outputFileName = PathUtils(outputPath).name;

            compressionMapping[inputPath] = outputPath;
            compressionMapping[inputFileName] = outputFileName;
            actualCompressedCount++; // 记录实际压缩的文件数量

            logger.d("Mapped: $inputFileName -> $outputFileName");
          }
        }

        if (compressionMapping.isNotEmpty) {
          // 更新笔记中的图片引用 (80-100%)
          await _updateNotesImageReferencesAfterCompression(
              notes,
              compressionMapping,
              allMediaReferences,
              80.0,
              100.0,
              actualCompressedCount);
        } else {
          // 根据结果确定状态：如果所有压缩都失败，则为错误状态
          final failedCount = totalCount - successCount;
          final status =
              (successCount == 0 && failedCount > 0) ? "error" : "completed";

          progressController.updateProgress(
            status: status,
            message: "anki.card_media_manager.deck_compression_completed"
                .trParams({
                  'success': successCount.toString(),
                  'total': totalCount.toString(),
                }),
            current: status == "error" ? 0.0 : 100.0,
            total: 100.0,
          );
        }
      } else {
        progressController.updateProgress(
          status: "error",
          message: resp.message.isNotEmpty
                ? resp.message
                : "anki.card_media_manager.compression_failed".tr
        );
      }
    } catch (e) {
      logger.e("Deck compression failed: $e");
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.deck_compression_failed"
              .trParams({'error': e.toString()}),
      );
    }
  }

  /// 获取牌组中的所有卡片
  Future<List<Map<String, dynamic>>> _getDeckCards(String deckName) async {
    try {
      final notes = await ankiConnectController.findNotes('deck:"$deckName"');
      return List<Map<String, dynamic>>.from(notes);
    } catch (e) {
      logger.e("Failed to get deck cards: $e");
      return [];
    }
  }

  /// 构建媒体文件的完整路径
  String? _buildMediaPath(String relativePath) {
    try {
      final ankiMediaPath = _cachedMediaPath;
      if (ankiMediaPath == null || ankiMediaPath.isEmpty) {
        logger.w("Anki media path not available");
        return null;
      }

      final fullPath = PathUtils.join([ankiMediaPath, relativePath]);
      return fullPath;
    } catch (e) {
      logger.e("Failed to build media path: $e");
      return null;
    }
  }

  // 缓存的媒体路径
  String? _cachedMediaPath;

  /// 初始化媒体路径缓存
  Future<bool> _initializeMediaPath() async {
    try {
      _cachedMediaPath = await ankiConnectController.getMediaDir();
      logger.i("Anki media path: $_cachedMediaPath");
      return true;
    } catch (e) {
      logger.e("Failed to get Anki media path: $e");
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.failed_to_get_media_path".tr,
      );
      return false;
    }
  }

  /// 验证牌组选择并获取卡片
  Future<List<Map<String, dynamic>>?> _validateAndGetDeckCards(
      String deckName) async {
    if (deckName.isEmpty) {
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.no_deck_selected".tr,
      );
      return null;
    }

    final cards = await _getDeckCards(deckName);
    if (cards.isEmpty) {
      progressController.updateProgress(
        status: "error",
        message: "anki.card_media_manager.no_cards_found".tr,
      );
      return null;
    }

    return cards;
  }

  /// 复制媒体文件到指定目录
  Future<void> _copyMediaFilesToDirectory(
      List<String> mediaFiles, String outputDir,
      [double progressStart = 0.0, double progressEnd = 100.0]) async {
    var successCount = 0;
    var failedCount = 0;

    for (int i = 0; i < mediaFiles.length; i++) {
      final filePath = mediaFiles[i];
      final fileName = PathUtils(filePath).name;

      try {
        // 计算复制进度（在指定范围内）
        final copyProgressRange = progressEnd - progressStart;
        final currentCopyProgress =
            progressStart + (copyProgressRange * (i + 1) / mediaFiles.length);

        // 更新复制进度
        progressController.updateProgress(
          status: "running",
          message: "anki.card_media_manager.copying_file".trParams({
              'current': (i + 1).toString(),
              'total': mediaFiles.length.toString(),
              'filename': fileName,
            }),
            current: currentCopyProgress,
            total: 100.0,
        );

        // 构建输出文件路径
        final outputFilePath = PathUtils.join([outputDir, fileName]);

        // 如果文件已存在，添加序号后缀
        var finalOutputPath = outputFilePath;
        var counter = 1;
        while (File(finalOutputPath).existsSync()) {
          final pathUtils = PathUtils(fileName);
          final nameWithoutExt = pathUtils.stem;
          final extension = pathUtils.extension;
          finalOutputPath = PathUtils.join(
              [outputDir, '${nameWithoutExt}_$counter$extension']);
          counter++;
        }

        // 复制或下载文件
        if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
          // 网络文件：下载到本地
          await _downloadNetworkFile(filePath, finalOutputPath);
          successCount++;
          logger.i("Successfully downloaded: $fileName -> $finalOutputPath");
        } else {
          // 本地文件：直接复制
          await File(filePath).copy(finalOutputPath);
          successCount++;
          logger.i("Successfully copied: $fileName -> $finalOutputPath");
        }
      } catch (e) {
        failedCount++;
        logger.e("Error copying $fileName: $e");
      }
    }

    // 显示最终结果
    progressController.outputPath.value = outputDir;

    // 根据结果确定状态：如果所有文件都失败，则为错误状态
    final status =
        (successCount == 0 && failedCount > 0) ? "error" : "completed";

    progressController.updateProgress(
        status: status,
        message: "anki.card_media_manager.scan_completed".trParams({
          'success': successCount.toString(),
          'failed': failedCount.toString(),
          'total': mediaFiles.length.toString(),
        }),
        current: status == "error" ? 0.0 : 100.0,
        total: 100.0,
    );
  }

  /// 压缩后更新笔记中的图片引用
  Future<void> _updateNotesImageReferencesAfterCompression(
      List<Map<String, dynamic>> notes,
      Map<String, String> compressionMapping,
      List<Map<String, dynamic>> allMediaReferences,
      double progressStart,
      double progressEnd,
      int actualCompressedCount) async {
    var updatedCount = 0;
    final notesToUpdate = <Map<String, dynamic>>[];

    for (int i = 0; i < notes.length; i++) {
      final note = notes[i];
      final noteId = note['noteId']?.toString() ?? i.toString();
      final fields = note['fields'] as Map<String, dynamic>? ?? {};
      var hasUpdates = false;
      final updatedFields = <String, String>{};

      // 计算更新进度
      final updateProgressRange = progressEnd - progressStart;
      final currentUpdateProgress =
          progressStart + (updateProgressRange * (i + 1) / notes.length);

      progressController.updateProgress(
        status: "running",
        message: "anki.card_media_manager.updating_note_references".trParams({
            'current': (i + 1).toString(),
            'total': notes.length.toString(),
          }),
          current: currentUpdateProgress,
          total: 100.0,
      );

      // 获取此笔记的媒体引用
      final noteReferences =
          allMediaReferences.where((ref) => ref['noteId'] == noteId).toList();

      // 遍历所有字段
      for (final fieldEntry in fields.entries) {
        final fieldName = fieldEntry.key;
        final fieldValue = fieldEntry.value;

        if (fieldValue is Map && fieldValue.containsKey('value')) {
          var htmlContent = fieldValue['value'] as String? ?? '';

          // 过滤此字段的引用
          final fieldReferences = noteReferences
              .where((ref) => ref['fieldName'] == fieldName)
              .toList();

          if (fieldReferences.isNotEmpty) {
            final updatedContent = await webviewController
                .replaceMediaReferencesInHtml(htmlContent, compressionMapping,
                    mediaReferences: fieldReferences);

            if (updatedContent != htmlContent) {
              updatedFields[fieldName] = updatedContent;
              hasUpdates = true;
              logger.d("Field $fieldName content updated for note $noteId");
            }
          }
        }
      }

      // 如果有更新，添加到批量更新列表
      if (hasUpdates) {
        notesToUpdate.add({
          'id': note['noteId'],
          'fields': updatedFields,
        });
        updatedCount++;
      }
    }

    // 批量更新所有有变化的笔记
    int failedUpdateCount = 0;
    if (notesToUpdate.isNotEmpty) {
      logger.i(
          "Batch updating ${notesToUpdate.length} notes with compressed image references");

      for (final noteData in notesToUpdate) {
        try {
          await ankiConnectController.updateNote(noteData);
        } catch (e) {
          logger.e("Failed to update note ${noteData['id']}: $e");
          updatedCount--; // 减少成功计数
          failedUpdateCount++; // 增加失败计数
        }
      }
    }

    // 显示最终结果
    // 根据结果确定状态：如果所有更新都失败，则为错误状态
    final status =
        (updatedCount == 0 && failedUpdateCount > 0) ? "error" : "completed";

    progressController.updateProgress(
        status: status,
        message:
            "anki.card_media_manager.deck_compression_completed_with_updates"
                .trParams({
          'compressed': actualCompressedCount.toString(),
          'updated': updatedCount.toString(),
          'total': notes.length.toString(),
        }),
        current: status == "error" ? 0.0 : 100.0,
        total: 100.0,
    );

    logger.i(
        "Compression completed: $actualCompressedCount images compressed, $updatedCount notes updated");
  }

  /// 下载网络文件到本地
  Future<void> _downloadNetworkFile(String url, String outputPath) async {
    try {
      final dioClient = dio.Dio();

      // 设置超时时间
      dioClient.options.connectTimeout = const Duration(seconds: 30);
      dioClient.options.receiveTimeout = const Duration(seconds: 60);

      // 下载文件
      final response = await dioClient.download(url, outputPath);

      if (response.statusCode == 200) {
        logger.i("Successfully downloaded file from $url to $outputPath");
      } else {
        throw Exception("HTTP ${response.statusCode}: Failed to download file");
      }
    } catch (e) {
      logger.e("Error downloading file from $url: $e");
      rethrow;
    }
  }
}
