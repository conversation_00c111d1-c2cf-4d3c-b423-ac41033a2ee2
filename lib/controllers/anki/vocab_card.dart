import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:dio/dio.dart';

class DictParams extends GetxController {}

class PDFParams extends GetxController {}

class TXTParams extends GetxController {
  final sourceType = "file".obs;
  final txtContent = ''.obs;
}

class VocabCardPageController extends GetxController {
  // 已有数据
  final tabController = ShadTabsController(value: 'dict');
  final parentDeck = ''.obs;
  final tags = <String>[].obs;
  final selectedFilePaths = <String>[].obs;
  // 表单参数
  final dictParams = Get.put(DictParams());
  final pdfParams = Get.put(PDFParams());
  final txtParams = Get.put(TXTParams());
  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final ankiConnectController = Get.find<AnkiConnectController>();

  @override
  void onInit() async {
    super.onInit();
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }
  }

  @override
  void onClose() {
    dictParams.dispose();
    pdfParams.dispose();
    txtParams.dispose();
    super.onClose();
  }

  /// Query word data for a batch of words from the API server
  /// Returns a list of word data maps from the API response
  Future<List<Map<String, dynamic>>> _queryWordBatch(
      List<String> batchWords) async {
    final dio = Dio();

    // Make direct HTTP request to Python API server
    final response = await dio.post(
      'http://localhost:8000/words/details',
      data: {"words": batchWords},
      options: Options(
        headers: {'Content-Type': 'application/json'},
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
      ),
    );

    if (response.statusCode == 200) {
      final responseData = response.data;
      if (responseData['code'] == 0 && responseData['data'] != null) {
        final batchWordData = responseData['data'] as List<dynamic>;
        return batchWordData.cast<Map<String, dynamic>>();
      } else {
        throw Exception("API返回错误: ${responseData['msg'] ?? 'Unknown error'}");
      }
    } else {
      throw Exception("HTTP请求失败: ${response.statusCode}");
    }
  }

  void submit(BuildContext context) async {
    if (tabController.selected == "txt") {
      if (selectedFilePaths.isEmpty) {
        showToastNotification(
          context,
          "失败",
          "请选择txt文件",
          type: "error",
        );
        return;
      }

      try {
        // Initialize progress
        final exportMode = settingController.getExportCardMode();
        progressController.reset(
          showOutputHint: exportMode == "apkg",
          numberButtons: exportMode == "apkg" ? 2 : 0,
        );
        progressController.showProgressDialog(context);

        // Process each selected file
        final allWords = <String>[];
        for (final filePath in selectedFilePaths) {
          try {
            progressController.updateProgress(
              status: "running",
              message: "读取文件: ${PathUtils(filePath).name}",
              current: 0.0,
              total: 100.0,
            );

            // Read words from file
            final file = File(filePath);
            if (!file.existsSync()) {
              throw Exception("文件不存在: $filePath");
            }

            final content = await file.readAsString();
            final words = content
                .split('\n')
                .map((line) => line.trim())
                .where((word) => word.isNotEmpty)
                .toList();

            allWords.addAll(words);
          } catch (e) {
            logger.e("读取文件失败: $filePath - $e");
            progressController.updateProgress(
              status: "error",
              message: "读取文件失败: ${PathUtils(filePath).name} - $e",
              current: 0.0,
              total: 100.0,
            );
            return;
          }
        }

        if (allWords.isEmpty) {
          progressController.updateProgress(
      status: "error",
      message: "未找到有效单词",
      current: 0.0,
      total: 100.0,
    );
          return;
        }

        // Remove duplicates and prepare for batch processing
        final uniqueWords = allWords.toSet().toList();
        const batchSize = 100;
        final totalBatches = (uniqueWords.length / batchSize).ceil();

        progressController.updateProgress(
          status: "running",
          message: "准备处理 ${uniqueWords.length} 个单词，分 $totalBatches 批次",
          current: 0.0,
          total: 100.0,
        );

        // Process words in batches
        final allWordData = <Map<String, dynamic>>[];

        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
          final startIndex = batchIndex * batchSize;
          final endIndex =
              (startIndex + batchSize).clamp(0, uniqueWords.length);
          final batchWords = uniqueWords.sublist(startIndex, endIndex);

          progressController.updateProgress(
            status: "running",
            current: batchIndex.toDouble(),
            total: totalBatches.toDouble(),
            message:
                "查询第 ${batchIndex + 1}/$totalBatches 批次: ${batchWords.length} 个单词",
          );

          try {
            // Query word data for this batch using the extracted method
            final batchWordData = await _queryWordBatch(batchWords);
            allWordData.addAll(batchWordData);
          } catch (e) {
            logger.e("批次 ${batchIndex + 1} 查询失败: $e");
            progressController.updateProgress(
      status: "error",
      message: "第 ${batchIndex + 1} 批次查询失败: $e",
      current: 0.0,
      total: 100.0,
    );
            return;
          }

          // Small delay between batches to avoid overwhelming the API
          if (batchIndex < totalBatches - 1) {
            await Future.delayed(const Duration(milliseconds: 500));
          }
        }

        if (allWordData.isEmpty) {
          progressController.updateProgress(
      status: "error",
      message: "未找到单词数据",
      current: 0.0,
      total: 100.0,
    );
          return;
        }

        progressController.updateProgress(
          status: "running",
          message: "生成Anki卡片: ${allWordData.length} 个单词",
          current: 0.0,
          total: 100.0,
        );

        // Generate Anki cards
        final notes = <AnkiNote>[];
        final deckName =
            parentDeck.value.isNotEmpty ? parentDeck.value : "Guru导入";

        for (int i = 0; i < allWordData.length; i++) {
          final wordData = allWordData[i];

          progressController.updateProgress(
            status: "running",
            current: i.toDouble(),
            total: allWordData.length.toDouble(),
            message: "生成卡片: ${wordData['word'] ?? '未知单词'}",
          );

          try {
            // Create note using Kevin Vocab Card template
            final note = AnkiNote(
              deckName: deckName,
              modelName: "Kevin Vocab Card v2",
              fields: [
                wordData['word'] ?? '', // Word field
                jsonEncode(wordData), // Data field (JSON string)
                '', // Note field (empty for now)
              ],
              tags: tags.toList(),
            );

            notes.add(note);
          } catch (e) {
            logger.e("生成卡片失败: ${wordData['word']} - $e");
          }
        }

        if (notes.isEmpty) {
          progressController.updateProgress(
      status: "error",
      message: "未生成有效卡片",
      current: 0.0,
      total: 100.0,
    );
          return;
        }

        // Import cards to Anki
        progressController.updateProgress(
          status: "running",
          message: "导入Anki卡片...",
          current: 0.0,
          total: 100.0,
        );

        await AnkiConnectController().generateAndImportCards(notes);

        progressController.updateProgress(
          status: "completed",
          message: "成功生成 ${notes.length} 张词汇卡片",
          current: 100.0,
          total: 100.0,
        );
      } catch (e) {
        logger.e("submit error: $e");
        progressController.updateProgress(
          status: "error",
          message: "操作失败: $e",
          current: 0.0,
          total: 100.0,
        );
      }
    }
  }
}
