import 'dart:math';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/ocr/components/ocr_image.dart';
import 'package:anki_guru/controllers/anki/ocr/components/ocr_overlay_painter.dart';
import 'package:anki_guru/controllers/anki/ocr/components/text_edit_dialog.dart';
import 'package:anki_guru/controllers/anki/ocr/ocr_parser.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';

/// OCR图像查看器，用于显示OCR处理后的图像及其结果
class OcrImageViewer extends StatefulWidget {
  /// OCR图像对象
  final OcrImage image;

  /// 复制文本后的回调（可选）
  final Function(String text)? onTextCopied;

  /// 选择状态变化的回调（可选）
  final Function(bool isAllSelected)? onSelectionChanged;

  /// 导出文本后的回调（可选）
  final Function(String filePath)? onTextExported;

  /// 自定义工具按钮
  final List<Widget>? extraTools;

  /// 是否使用Card包装
  final bool useCard;

  const OcrImageViewer({
    super.key,
    required this.image,
    this.onTextCopied,
    this.onSelectionChanged,
    this.onTextExported,
    this.extraTools,
    this.useCard = true,
  });

  @override
  State<OcrImageViewer> createState() => _OcrImageViewerState();
}

class _OcrImageViewerState extends State<OcrImageViewer> {
  // 变换控制器，控制图像的缩放平移
  final TransformationController _transformationController =
      TransformationController();

  // 视图大小
  Size _widgetSize = Size.zero;

  // 交互状态
  bool _isDragging = false;
  bool _isSelectMode = true;
  Set<int> _processedIndices = {};
  List<Offset> _panPath = [];

  /// 获取当前选中状态
  bool get _isAllSelected => widget.image.isAllSelected;

  @override
  void initState() {
    super.initState();
    resetTransformation();
  }

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  /// 重置变换
  void resetTransformation() {
    _transformationController.value = Matrix4.identity();
  }

  /// 处理点击事件
  void handleTap(Offset localPosition) {
    if (!widget.image.isProcessed || widget.image.image == null) return;

    final tappedPoint = _convertToImageCoordinates(localPosition);

    for (int i = 0; i < widget.image.textBlocks.length; i++) {
      final block = widget.image.textBlocks[i];
      if (_isPointInBlock(block, tappedPoint)) {
        setState(() {
          final currentIndices = List<int>.from(widget.image.selectedIndices);
          if (currentIndices.contains(i)) {
            currentIndices.remove(i);
          } else {
            currentIndices.add(i);
          }
          widget.image.selectedIndices = currentIndices;

          // 通知选择状态变化
          widget.onSelectionChanged?.call(_isAllSelected);
        });
        break;
      }
    }
  }

  /// 开始拖动
  void startPan(Offset localPosition) {
    if (!widget.image.isProcessed || widget.image.image == null) return;

    setState(() {
      _isDragging = true;
      _panPath = [localPosition];
      _processedIndices = {};

      final imagePoint = _convertToImageCoordinates(localPosition);

      // 确定拖动模式（选中或取消选中）
      _isSelectMode = true;
      for (int i = 0; i < widget.image.textBlocks.length; i++) {
        if (widget.image.selectedIndices.contains(i) &&
            _isPointInBlock(widget.image.textBlocks[i], imagePoint)) {
          _isSelectMode = false;
          break;
        }
      }
    });
  }

  /// 更新拖动
  void updatePan(Offset localPosition) {
    if (!_isDragging || !widget.image.isProcessed || widget.image.image == null)
      return;

    _panPath.add(localPosition);
    final imagePoint = _convertToImageCoordinates(localPosition);

    bool stateChanged = false;
    final updatedIndices = List<int>.from(widget.image.selectedIndices);

    for (int i = 0; i < widget.image.textBlocks.length; i++) {
      if (_processedIndices.contains(i)) continue;

      final block = widget.image.textBlocks[i];
      if (_isPointInBlock(block, imagePoint)) {
        if (_isSelectMode) {
          if (!updatedIndices.contains(i)) {
            updatedIndices.add(i);
            stateChanged = true;
          }
        } else {
          if (updatedIndices.contains(i)) {
            updatedIndices.remove(i);
            stateChanged = true;
          }
        }
        _processedIndices.add(i);
      }
    }

    if (stateChanged) {
      setState(() {
        widget.image.selectedIndices = updatedIndices;

        // 通知选择状态变化
        widget.onSelectionChanged?.call(_isAllSelected);
      });
    }
  }

  /// 结束拖动
  void endPan() {
    setState(() {
      _isDragging = false;
      _panPath = [];
      _processedIndices = {};
    });
  }

  /// 切换全选/清除选择
  void _onViewText() {
    if (!widget.image.isProcessed) return;
    showDialog(
      context: context,
      builder: (context) => OcrTextEditDialog(
        image: widget.image,
        onCopy: (text) {},
      ),
    );
  }

  /// 切换全选/清除选择
  void _toggleSelectAll() {
    if (!widget.image.isProcessed) return;

    setState(() {
      if (_isAllSelected) {
        widget.image.clearSelection();
      } else {
        widget.image.selectAllBlocks();
      }

      // 通知选择状态变化
      widget.onSelectionChanged?.call(_isAllSelected);
    });
  }

  /// 复制选中的文本
  void _copySelectedText() {
    if (!widget.image.isProcessed) return;

    final text = widget.image.getSelectedText();
    Clipboard.setData(ClipboardData(text: text));

    // 调用复制回调
    widget.onTextCopied?.call(text);

    // 显示提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('anki.ocr.copied_to_clipboard'.tr),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 导出选中的文本
  Future<void> _exportSelectedText() async {
    if (!widget.image.isProcessed) return;

    final text = widget.image.getSelectedText();

    try {
      // 创建默认文件名和初始目录
      final now = DateTime.now()
          .toIso8601String()
          .replaceAll(':', '-')
          .substring(0, 19);
      final defaultFileName = 'ocr_text_$now.txt';

      String? outputPath;

      // 显示保存文件对话框
      if (Platform.isAndroid || Platform.isIOS) {
        // 移动平台处理 - 直接保存到下载目录
        outputPath = PathUtils.join([await PathUtils.downloadDir, defaultFileName]);
       
      } else {
        // 桌面平台处理
        final downloadsDir = await getDownloadsDirectory();
        final result = await FilePicker.platform.saveFile(
          dialogTitle: 'anki.ocr.save_ocr_text'.tr,
          fileName: defaultFileName,
          initialDirectory: downloadsDir?.path,
          type: FileType.custom,
          allowedExtensions: ['txt'],
        );
        outputPath = result;
      }

      // 如果用户取消了保存操作，直接返回
      if (outputPath == null) {
        return;
      }

      // 确保文件有.txt扩展名
      if (!outputPath.toLowerCase().endsWith('.txt')) {
        outputPath = '$outputPath.txt';
      }

      // 写入文件
      await File(outputPath).writeAsString(text);

      // 调用导出回调
      widget.onTextExported?.call(outputPath);

      // 显示提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('anki.ocr.exported_to'.trParams({'path': outputPath})),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('anki.ocr.export_failed'.trParams({'error': e.toString()})),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 检查点是否在文本块内
  bool _isPointInBlock(OcrBlock block, Offset point) {
    return block.getBoundingRect().contains(point);
  }

  /// 将屏幕坐标转换为图像坐标
  Offset _convertToImageCoordinates(Offset uiPosition) {
    if (widget.image.image == null || _widgetSize == Size.zero) {
      return Offset.zero;
    }

    final imageWidth = widget.image.image!.width.toDouble();
    final imageHeight = widget.image.image!.height.toDouble();
    final widgetWidth = _widgetSize.width;
    final widgetHeight = _widgetSize.height;

    final double baseScale =
        min(widgetWidth / imageWidth, widgetHeight / imageHeight);
    final Size renderedImageSize =
        Size(imageWidth * baseScale, imageHeight * baseScale);

    final double baseOffsetX = (widgetWidth - renderedImageSize.width) / 2;
    final double baseOffsetY = (widgetHeight - renderedImageSize.height) / 2;

    final Matrix4 transform = _transformationController.value;
    final viewerScale = transform.getMaxScaleOnAxis();
    final viewerOffsetX = transform.getTranslation().x;
    final viewerOffsetY = transform.getTranslation().y;

    final adjustedX = (uiPosition.dx - viewerOffsetX) / viewerScale;
    final adjustedY = (uiPosition.dy - viewerOffsetY) / viewerScale;

    return Offset((adjustedX - baseOffsetX) / baseScale,
        (adjustedY - baseOffsetY) / baseScale);
  }

  @override
  Widget build(BuildContext context) {
    final hasTextBlocks =
        widget.image.isProcessed && widget.image.textBlocks.isNotEmpty;

    // 创建内容组件
    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 工具栏（始终在顶部右侧）
        if (hasTextBlocks) _buildToolsRow(),

        // 图像预览区域
        Expanded(
          flex: 2,
          child: LayoutBuilder(
            builder: (context, constraints) {
              // 更新widget size
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (_widgetSize != constraints.biggest) {
                  setState(() {
                    _widgetSize = constraints.biggest;
                  });
                }
              });

              return Stack(
                alignment: Alignment.center,
                children: [
                  // 交互式查看器
                  InteractiveViewer(
                    minScale: 1.0,
                    maxScale: 4.0,
                    transformationController: _transformationController,
                    clipBehavior: Clip.none,
                    onInteractionEnd: (_) {
                      // 刷新状态
                      setState(() {});
                    },
                    child: Center(
                      child: Container(
                        alignment: Alignment.center,
                        width: constraints.maxWidth,
                        height: constraints.maxHeight,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            // 基础图片
                            ClipRRect(
                              borderRadius: BorderRadius.vertical(
                                top: Radius.circular(widget.useCard ? 12 : 0),
                              ),
                              child: LayoutBuilder(
                                builder: (context, imgConstraints) {
                                  return Image.memory(
                                    widget.image.imageData,
                                    fit: BoxFit.contain,
                                    width: imgConstraints.maxWidth,
                                    height: imgConstraints.maxHeight,
                                    alignment: Alignment.center,
                                  );
                                },
                              ),
                            ),

                            // OCR覆盖层
                            if (hasTextBlocks)
                              CustomPaint(
                                painter: OcrOverlayPainter(
                                  imageObj: widget.image,
                                  widgetSize: _widgetSize,
                                  isDragging: _isDragging,
                                  isSelectMode: _isSelectMode,
                                ),
                                size: _widgetSize,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // 手势检测层
                  if (hasTextBlocks)
                    Positioned.fill(
                      child: GestureDetector(
                        onTapUp: (details) => handleTap(details.localPosition),
                        onPanStart: (details) =>
                            startPan(details.localPosition),
                        onPanUpdate: (details) =>
                            updatePan(details.localPosition),
                        onPanEnd: (_) => endPan(),
                        behavior: _isDragging
                            ? HitTestBehavior.opaque
                            : HitTestBehavior.translucent,
                      ),
                    ),
                ],
              );
            },
          ),
        ),
      ],
    );

    // 根据useCard参数决定是否使用Card包装
    if (widget.useCard) {
      return Card(
        elevation: 4,
        margin: const EdgeInsets.all(8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: content,
      );
    } else {
      return content;
    }
  }

  /// 构建工具按钮行
  Widget _buildToolsRow() {
    final hasTextBlocks = widget.image.textBlocks.isNotEmpty;

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // 查看文字按钮
          ShadIconButtonCustom(
            icon: LucideIcons.text,
            size: 16,
            onPressed: hasTextBlocks ? _onViewText : null,
          ),

          const SizedBox(width: 8),

          // 复制按钮
          ShadIconButtonCustom(
            icon: LucideIcons.copy,
            size: 16,
            onPressed: hasTextBlocks ? _copySelectedText : null,
          ),

          const SizedBox(width: 8),

          // 全选/清除按钮
          ShadIconButtonCustom(
            icon: _isAllSelected ? LucideIcons.circleX : LucideIcons.checkCheck,
            size: 16,
            onPressed: hasTextBlocks ? _toggleSelectAll : null,
          ),

          // 导出按钮
          ShadIconButtonCustom(
            icon: LucideIcons.download,
            size: 16,
            onPressed: hasTextBlocks ? _exportSelectedText : null,
          ),

          // 额外的工具按钮
          if (widget.extraTools != null) ...widget.extraTools!,
        ],
      ),
    );
  }
}
