import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/ocr/components/ocr_image.dart';

/// OCR文本编辑对话框，用于显示和编辑OCR识别的文本
class OcrTextEditDialog extends StatefulWidget {
  /// OCR图像对象
  final OcrImage image;

  /// 复制文本的回调
  final Function(String)? onCopy;

  /// 对话框标题
  final String? title;

  /// 是否显示取消按钮
  final bool showCancel;

  const OcrTextEditDialog({
    super.key,
    required this.image,
    this.onCopy,
    this.title,
    this.showCancel = true,
  });

  @override
  State<OcrTextEditDialog> createState() => _OcrTextEditDialogState();
}

class _OcrTextEditDialogState extends State<OcrTextEditDialog> {
  late TextEditingController _textController;

  @override
  void initState() {
    super.initState();

    // 获取选中或全部的文本块
    final text = widget.image.getSelectedText();
    _textController = TextEditingController(text: text);
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dialogTitle = widget.title ??
        (widget.image.selectedIndices.isNotEmpty ? 'anki.ocr.selected_text'.tr : 'anki.ocr.all_text'.tr);

    return Dialog(
      // 最小边距，让对话框更宽
      insetPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 24),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8, // 尽可能宽
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                Text(
                  dialogTitle,
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                // 右上角关闭按钮
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  splashRadius: 20,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 文本编辑区
            Expanded(
              child: Material(
                color: Colors.transparent,
                child: TextField(
                  controller: _textController,
                  maxLines: null,
                  expands: true,
                  style: const TextStyle(fontSize: 14),
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.all(8),
                  ),
                ),
              ),
            ),

            // 底部按钮
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (widget.showCancel)
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text('anki.ocr.cancel'.tr),
                    ),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () {
                      Clipboard.setData(
                          ClipboardData(text: _textController.text));
                      if (widget.onCopy != null) {
                        widget.onCopy!(_textController.text);
                      }
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('anki.ocr.text_copied_to_clipboard'.tr),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    child: Text('anki.ocr.copy'.tr),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
