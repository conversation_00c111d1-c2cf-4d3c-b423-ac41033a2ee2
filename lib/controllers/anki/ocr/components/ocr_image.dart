import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:anki_guru/controllers/anki/ocr/ocr_parser.dart';

/// 表示OCR处理的图像及其识别结果
class OcrImage {
  /// 图像数据
  Uint8List imageData;

  /// 图像文件路径（如果有）
  String imagePath;

  /// OCR识别到的文本块
  List<OcrBlock> textBlocks;

  /// 用于获取准确的图像尺寸
  ui.Image? image;

  /// 选中的文本块索引
  List<int> selectedIndices;

  /// 是否已经进行OCR处理
  bool isProcessed;

  /// 是否将换行符转换为空格
  bool isConvertNewLine;

  OcrImage({
    required this.imageData,
    this.imagePath = "",
    this.textBlocks = const [],
    this.image,
    this.selectedIndices = const [],
    this.isProcessed = false,
    this.isConvertNewLine = false,
  });

  /// 添加处理OCR结果的方法
  void updateWithOcrResult(
      Map<String, dynamic> jsonResult, ui.Image loadedImage) {
    textBlocks = [];
    if (jsonResult.containsKey('blocks') && jsonResult['blocks'] is List) {
      for (var block in jsonResult['blocks']) {
        textBlocks.add(OcrBlock.fromJson(block));
      }
    }
    image = loadedImage;
    isProcessed = true;
    selectedIndices = [];
  }

  /// 获取所有选中文本块的文本
  String getSelectedText({bool? useConvertNewLine}) {
    List<OcrBlock> blocksToUse;

    // 如果传入了参数，则使用参数值，否则使用类属性
    final convertNewLine = useConvertNewLine ?? isConvertNewLine;

    // 如果没有选中任何文本块，则使用所有文本块
    if (selectedIndices.isEmpty) {
      blocksToUse = List.from(textBlocks);
    } else {
      // 使用选中的文本块
      blocksToUse = selectedIndices.map((i) => textBlocks[i]).toList();
    }

    try {
      // 使用OcrParser处理文本为自然段落
      return OcrParser.parseBlocks(blocksToUse,
          isConvertNewLine: convertNewLine);
    } catch (e) {
      // 处理失败时回退到简单连接
      return blocksToUse
          .map((block) => block.text)
          .join(convertNewLine ? ' ' : '\n');
    }
  }

  /// 选择所有文本块
  void selectAllBlocks() {
    selectedIndices = List.generate(textBlocks.length, (i) => i);
  }

  /// 清除所有选中
  void clearSelection() {
    selectedIndices = [];
  }

  /// 检查是否包含选中的文本块
  bool hasSelection() {
    return selectedIndices.isNotEmpty;
  }

  /// 获取文本块总数
  int get blockCount => textBlocks.length;

  /// 获取选中块的数量
  int get selectedCount => selectedIndices.length;

  /// 是否全部选中
  bool get isAllSelected => selectedCount == blockCount && blockCount > 0;
}
