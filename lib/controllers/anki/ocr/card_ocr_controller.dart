import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:dio/dio.dart';
import 'package:path/path.dart' as path;

class CardOcrParams extends GetxController {
  // 表单参数
  final cardIds = ''.obs;
  final text = ''.obs;
}

class CardOCRPageController extends GetxController {
  // 已有数据
  final roiModelList = <String>[].obs;
  final fieldList = <String>[].obs;
  final cardModel = "".obs;
  final noteList = <dynamic>[].obs;
  final modelFieldConfigs = <String, Map<String, Map<String, dynamic>>>{}.obs;

  // 表单参数
  final cardOcrParams = Get.put(CardOcrParams());

  // 控制器
  final progressController = Get.find<ProgressController>();
  final messageController = Get.find<MessageController>();
  final settingController = Get.find<SettingController>();
  final ankiConnectController = Get.find<AnkiConnectController>();

  @override
  void onInit() async {
    if (settingController.cardMode.value == "ankiconnect") {
      roiModelList.value = [];

      // Check if AnkiConnectController data is loaded, if not, load it
      if (ankiConnectController.modelList.isEmpty) {
        await ankiConnectController.resetAnkiConnectData();
      }

      if (ankiConnectController.modelList.isNotEmpty) {
        cardModel.value = ankiConnectController.modelList[0];
        updateFieldList(cardModel.value);
      }
    }
    super.onInit();
  }

  @override
  void onClose() {
    cardOcrParams.dispose();
    super.onClose();
  }

  Future<void> updateFieldList(String modelName) async {
    if (settingController.cardMode.value == "ankiconnect") {
      try {
        fieldList.value =
            await AnkiConnectController().updateFieldList(modelName);
      } catch (e) {
        logger.e("updateFieldList error: $e");
      }
    }
  }

  Future<Map<String, dynamic>> ocrRequest(String imagePath) async {
    // Verify image file exists
    final imageFile = File(imagePath);
    if (!imageFile.existsSync()) {
      logger.e("Image file does not exist: $imagePath");
      return {
        "status": "error",
        "message": "Image file does not exist: $imagePath",
      };
    }

    final data = {
      "file_paths": [imagePath],
      "provider": "paddle-ocr",
      "model_name": "",
      "api_key": "",
      "base_url": "",
      "system_prompt": "",
      "merge_output": false,
      "response_format": "json",
      "show_progress": false,
    };

    try {
      final resp = await messageController.request(data, "anki/ocr");
      return {
        "status": resp.status,
        "data": resp.data,
        "message": resp.message,
      };
    } catch (e) {
      logger.e("OCR request failed: $e");
      return {
        "status": "error",
        "message": e.toString(),
      };
    }
  }

  void submit(BuildContext context) async {
    logger.i("=== Card OCR Submit Started ===");

    if (Platform.isAndroid || Platform.isIOS) {
      showToastNotification(
          context, "anki.ocr.feature_not_supported_on_current_system".tr, "",
          type: "error");
      return;
    }

    // Fetch fresh note data before processing
    if (cardOcrParams.text.value.isEmpty) {
      showToastNotification(
          context, "anki.ocr.please_select_cards_first".tr, "",
          type: "error");
      return;
    }

    progressController.reset(
      showOutputHint: false,
      numberButtons: 0,
    );
    progressController.showProgressDialog(context);

    try {
      // Refresh note data to get the latest field values from Anki
      logger.i("Fetching latest note data from Anki...");
      noteList.value = await AnkiConnectController()
          .findNotes('nid:"${cardOcrParams.text.value.trim()}"');

      if (noteList.isEmpty) {
        progressController.updateProgress(
          status: "error",
          message: "anki.ocr.card_id_illegal".tr,
          current: 0.0,
          total: 100.0,
        );
        return;
      }

      logger.i(
          "Processing ${noteList.length} notes with configurations: $modelFieldConfigs");
    } catch (e) {
      logger.e("Failed to fetch note data: $e");
      progressController.updateProgress(
        status: "error",
        message: "Failed to fetch note data: $e",
        current: 0.0,
        total: 100.0,
      );
      return;
    }

    try {
      final resultNotes = <Map<String, dynamic>>[];

      for (var i = 0; i < noteList.length; i++) {
        final note = noteList[i];
        final progress = ((i + 1) / noteList.length) * 100;

        progressController.updateProgress(
          status: "running",
          message: "anki.ocr.processing_note"
              .trParams({'noteId': note['noteId'].toString()}),
          current: progress,
          total: 100.0,
        );

        final modelName = note['modelName'];
        final noteId = note['noteId'];
        final fields = note['fields'] as Map<String, dynamic>;

        logger.i("Processing note $noteId (${i + 1}/${noteList.length})");

        // 获取该模型的字段配置
        final fieldConfig = modelFieldConfigs[modelName] ?? {};

        if (fieldConfig.isEmpty) {
          logger.w(
              "No field configuration found for model '$modelName', skipping note");
          continue;
        }

        Map<String, String> resultFields = {};
        final tempFilesToCleanup = <String>[];

        // 遍历 fieldConfig 而不是所有字段
        for (final entry in fieldConfig.entries) {
          final config = entry.value;
          final originField = entry.key;
          final originFieldValue = fields[originField]?['value'] ?? '';
          final targetField = config['targetField'];
          String targetFieldValue = fields[targetField]?['value'] ?? '';

          if (config['replaceContent'] == true) {
            targetFieldValue = "";
          } else {
            if (targetFieldValue.isNotEmpty) {
              targetFieldValue = "$targetFieldValue<br><br>";
            }
          }
          if (config['performOcr'] == false) {
            continue;
          }

          // 从字段内容中提取图片
          var imageMatches = RegExp(r'''<img[^>]+src=['"]([^'"]+)['"][^>]*>''')
              .allMatches(originFieldValue);

          // 如果主要正则没有匹配，尝试更宽松的模式
          if (imageMatches.isEmpty) {
            imageMatches =
                RegExp(r'''<img[^>]*src\s*=\s*['"]([^'"]+)['"][^>]*>''')
                    .allMatches(originFieldValue);
          }

          // 如果还是没有匹配，尝试最宽松的模式
          if (imageMatches.isEmpty) {
            imageMatches = RegExp(r'''src\s*=\s*['"]([^'"]+)['"]''')
                .allMatches(originFieldValue);
          }

          if (imageMatches.isEmpty) {
            continue;
          }

          String combinedOcrText = '';

          for (final match in imageMatches) {
            final imageSrc = match.group(1);
            if (imageSrc == null) continue;

            String? imagePath;

            // 检查是否为网络图片
            if (imageSrc.startsWith('http://') ||
                imageSrc.startsWith('https://')) {
              logger.i("Processing network image: $imageSrc");
              // 下载网络图片到临时文件
              imagePath = await _downloadNetworkImage(imageSrc);
              if (imagePath == null) {
                logger.e("Failed to download network image: $imageSrc");
                continue;
              }
              // 记录临时文件以便后续清理
              tempFilesToCleanup.add(imagePath);
            } else {
              // 处理本地媒体文件
              final mediaDir = await AnkiConnectController().getMediaDir();
              imagePath = PathUtils.join([mediaDir, imageSrc]);

              final mediaFile = File(imagePath);
              if (!mediaFile.existsSync()) {
                logger.e("Media file does not exist: $imagePath");
                continue;
              }
            }

            // 此时 imagePath 已确保不为空

            // 执行OCR
            final ocrResult = await ocrRequest(imagePath);

            if (ocrResult['status'] == 'success') {
              try {
                final results = ocrResult['data'];

                // 处理 OCR 结果
                dynamic processedResults;

                if (results is List && results.isNotEmpty) {
                  processedResults = results;
                } else if (results is String) {
                  try {
                    final parsed = jsonDecode(results);
                    processedResults = parsed is List ? parsed : [parsed];
                  } catch (e) {
                    logger.e("Failed to parse OCR results: $e");
                    continue;
                  }
                } else {
                  processedResults = [results];
                }

                if (processedResults is List && processedResults.isNotEmpty) {
                  final firstResult = processedResults[0];

                  // 检查第一个结果是否是字符串（需要解析）还是已经是对象
                  dynamic resultJson;
                  if (firstResult is String) {
                    try {
                      resultJson = jsonDecode(firstResult);
                    } catch (e) {
                      logger.e("Failed to parse JSON: $e");
                      continue;
                    }
                  } else {
                    resultJson = firstResult;
                  }

                  // 解析OCR结果并提取文本
                  final ocrText = _extractTextFromOcrResult(resultJson);

                  if (ocrText.isNotEmpty) {
                    // 处理OCR文本，形成自然段落并转换为HTML格式
                    final processedText = _processOcrTextToHtml(ocrText);

                    if (combinedOcrText.isNotEmpty) {
                      // 多图片之间添加双换行分隔
                      combinedOcrText += '<br><br>$processedText';
                    } else {
                      combinedOcrText += processedText;
                    }
                  }
                }
              } catch (e) {
                logger.e("Parse OCR result failed: $e");
              }
            } else {
              logger.e("OCR request failed: ${ocrResult['message']}");
              progressController.updateProgress(
                status: "error",
                message: "anki.ocr.ocr_processing_failed"
                    .trParams({'error': ocrResult['message'].toString()}),
                current: 0.0,
                total: 100.0,
              );
              return;
            }
          }

          if (combinedOcrText.isNotEmpty) {
            final newFieldValue = "$targetFieldValue $combinedOcrText".trim();
            resultFields[targetField] = newFieldValue;
            logger.i("Updated field '$targetField' with OCR text");
          }
        }

        if (resultFields.isNotEmpty) {
          final noteUpdate = {
            'id': noteId,
            'fields': resultFields,
          };
          resultNotes.add(noteUpdate);

          final resp = await AnkiConnectController().updateNote(noteUpdate);
          logger.i("Updated note $noteId: $resp");
        }

        // 清理临时文件
        for (final tempFile in tempFilesToCleanup) {
          try {
            final file = File(tempFile);
            if (file.existsSync()) {
              await file.delete();
              logger.i("Cleaned up temp file: $tempFile");
            }
          } catch (e) {
            logger.w("Failed to cleanup temp file $tempFile: $e");
          }
        }
      }

      // 更新进度
      logger.i("=== Card OCR processing completed successfully ===");
      logger.i("Total notes processed: ${noteList.length}");
      logger.i("Total note updates sent: ${resultNotes.length}");

      progressController.updateProgress(
        status: "completed",
        message: "",
        current: 100.0,
        total: 100.0,
      );
    } catch (e) {
      logger.e("=== Card OCR processing failed ===");
      logger.e("Error: $e");

      progressController.updateProgress(
        status: "error",
        message: e.toString(),
        current: 0.0,
        total: 100.0,
      );
    }
  }

  Future<String?> _downloadNetworkImage(String imageUrl) async {
    try {
      logger.i("Downloading network image: $imageUrl");

      // 获取临时目录
      final tempDir = await PathUtils.tempDir;

      // 生成临时文件名
      final fileName = 'temp_image_${DateTime.now().millisecondsSinceEpoch}';
      final extension = path.extension(imageUrl).isNotEmpty
          ? path.extension(imageUrl)
          : '.png';
      final tempFilePath = PathUtils.join([tempDir, '$fileName$extension']);

      // 创建Dio实例
      final dio = Dio();

      // 设置超时时间
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 60);

      // 下载文件
      final response = await dio.download(
        imageUrl,
        tempFilePath,
        options: Options(
          responseType: ResponseType.bytes,
          followRedirects: true,
          validateStatus: (status) => status != null && status < 400,
        ),
      );

      if (response.statusCode == 200) {
        logger.i("Network image downloaded to: $tempFilePath");
        return tempFilePath;
      } else {
        logger.e("Failed to download image: HTTP ${response.statusCode}");
        return null;
      }
    } catch (e) {
      logger.e("Error downloading network image: $e");
      return null;
    }
  }

  String _extractTextFromOcrResult(dynamic resultJson) {
    try {
      if (resultJson is String) {
        final parsed = jsonDecode(resultJson);
        return _extractTextFromParsedResult(parsed);
      } else {
        return _extractTextFromParsedResult(resultJson);
      }
    } catch (e) {
      logger.e("Extract OCR text failed: $e");
      return '';
    }
  }

  String _extractTextFromParsedResult(Map<String, dynamic> parsed) {
    final textBlocks = <String>[];

    // 首先尝试 'blocks' 键（PaddleOCR格式）
    if (parsed.containsKey('blocks')) {
      final blocks = parsed['blocks'];
      if (blocks is List) {
        for (final block in blocks) {
          if (block is Map && block.containsKey('text')) {
            final text = block['text']?.toString().trim();
            if (text != null && text.isNotEmpty) {
              textBlocks.add(text);
            }
          }
        }
      }
    }
    // 然后尝试 'text_blocks' 键（其他OCR格式）
    else if (parsed.containsKey('text_blocks')) {
      final blocks = parsed['text_blocks'];
      if (blocks is List) {
        for (final block in blocks) {
          if (block is Map && block.containsKey('text')) {
            final text = block['text']?.toString().trim();
            if (text != null && text.isNotEmpty) {
              textBlocks.add(text);
            }
          }
        }
      }
    }
    // 尝试直接的 'text' 键
    else if (parsed.containsKey('text')) {
      final directText = parsed['text']?.toString().trim();
      if (directText != null && directText.isNotEmpty) {
        textBlocks.add(directText);
      }
    }

    return textBlocks.join(' ');
  }

  String _processOcrTextToHtml(String ocrText) {
    if (ocrText.trim().isEmpty) return '';

    // First, split by existing line breaks to get individual text blocks
    final lines = ocrText
        .split('\n')
        .map((line) => line.trim())
        .where((line) => line.isNotEmpty)
        .toList();
    if (lines.isEmpty) return '';

    final paragraphs = <String>[];
    var currentParagraph = <String>[];

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];

      // Add current line to paragraph
      currentParagraph.add(line);

      // Determine if this line should end the current paragraph
      bool shouldEndParagraph = false;

      // Rule 1: Line ends with strong sentence-ending punctuation
      if (line.endsWith('.') ||
          line.endsWith('。') ||
          line.endsWith('!') ||
          line.endsWith('！') ||
          line.endsWith('?') ||
          line.endsWith('？')) {
        // Check if next line suggests a new paragraph
        if (i + 1 < lines.length) {
          final nextLine = lines[i + 1];

          // New paragraph if next line starts with:
          // - Number (1., 2., etc.)
          // - Bullet point
          // - Capital letter/Chinese character (new sentence)
          if (RegExp(r'^\d+[\.、\s]').hasMatch(nextLine) ||
              RegExp(r'^[•·\-\*]').hasMatch(nextLine) ||
              RegExp(r'^[A-Z\u4e00-\u9fff]').hasMatch(nextLine)) {
            shouldEndParagraph = true;
          }
        } else {
          // Last line always ends paragraph
          shouldEndParagraph = true;
        }
      }

      // Rule 2: Current line is short (likely a title/header) and next line exists
      else if (i + 1 < lines.length && line.length < 30) {
        shouldEndParagraph = true;
      }

      // Rule 3: Current paragraph is getting very long (force break)
      else if (currentParagraph.length > 5) {
        shouldEndParagraph = true;
      }

      // Rule 4: Last line always ends paragraph
      else if (i == lines.length - 1) {
        shouldEndParagraph = true;
      }

      // Finalize paragraph if needed
      if (shouldEndParagraph && currentParagraph.isNotEmpty) {
        // Join lines intelligently (handle word breaks, punctuation)
        final paragraphText = _joinLinesIntelligently(currentParagraph);
        if (paragraphText.isNotEmpty) {
          paragraphs.add(paragraphText);
        }
        currentParagraph.clear();
      }
    }

    // Convert all newlines to HTML <br> tags
    return paragraphs.join('<br>');
  }

  String _joinLinesIntelligently(List<String> lines) {
    if (lines.isEmpty) return '';
    if (lines.length == 1) return lines[0];

    final result = StringBuffer();

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      result.write(line);

      // Add space between lines unless it's the last line
      if (i < lines.length - 1) {
        final nextLine = lines[i + 1];

        bool needsSpace = true;

        // Don't add space if:
        // 1. Current line ends with hyphen (word continuation)
        // 2. Next line starts with punctuation
        // 3. Current line ends with opening punctuation

        if (line.endsWith('-') || line.endsWith('—')) {
          needsSpace = false;
        } else if (RegExp(r'^[，。！？；：,\.!?;:]').hasMatch(nextLine)) {
          needsSpace = false;
        } else if (line.endsWith('(') ||
            line.endsWith('（') ||
            line.endsWith('[') ||
            line.endsWith('【')) {
          needsSpace = false;
        }

        if (needsSpace) {
          result.write(' ');
        }
      }
    }

    return result.toString();
  }

  // 更新字段配置的方法，增加模型参数
  void updateFieldConfig(String modelName, String fieldName,
      {bool? performOcr, bool? replaceContent, String? targetField}) {
    // 如果模型不存在，初始化模型配置
    if (!modelFieldConfigs.containsKey(modelName)) {
      modelFieldConfigs[modelName] = {};
    }

    // 如果字段不存在，初始化字段配置
    if (!modelFieldConfigs[modelName]!.containsKey(fieldName)) {
      modelFieldConfigs[modelName]![fieldName] = {
        'performOcr': false,
        'replaceContent': true,
        'targetField': fieldName,
      };
    }

    final config = modelFieldConfigs[modelName]![fieldName]!;
    if (performOcr != null) config['performOcr'] = performOcr;
    if (replaceContent != null) config['replaceContent'] = replaceContent;
    if (targetField != null) config['targetField'] = targetField;
    modelFieldConfigs.refresh();
  }
}
