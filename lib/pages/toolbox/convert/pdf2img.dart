import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/toolbox/convert.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';

class PDF2ImgPage extends StatefulWidget {
  const PDF2ImgPage({super.key});

  @override
  State<PDF2ImgPage> createState() => _PDF2ImgPageState();
}

class _PDF2ImgPageState extends State<PDF2ImgPage> {
  final controller = Get.put(PDFConvertPageController());

  @override
  void initState() {
    super.initState();
    controller.convertType.value = 'pdf2img';
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.convert.pdf2img.title'.tr,
            style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.convert.pdf2img.description'.tr,
                  style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.convert.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        // ShadRadioGroupCustom(
                        //   label: '输出格式',
                        //   items: controller.outputFormatList,
                        //   initialValue: controller.outputFormat.value,
                        //   onChanged: (v) {
                        //     controller.outputFormat.value = v;
                        //   },
                        // ),
                        ShadSwitchCustom(
                          label: 'toolbox.convert.pdf2img.grayscale'.tr,
                          initialValue: controller.isGray.value,
                          onChanged: (v) {
                            controller.isGray.value = v;
                          },
                        ),
                        ShadInputWithValidate(
                            label: 'toolbox.convert.pdf2img.resolution'.tr,
                            placeholder:
                                'toolbox.convert.pdf2img.resolutionPlaceholder'
                                    .tr,
                            initialValue: controller.dpi.value.toString(),
                            onChanged: (value) {
                              controller.dpi.value = int.parse(value);
                            },
                            onValidate: (value) async {
                              if (RegExp(r'^\d+$').hasMatch(value) &&
                                  int.parse(value) > 0) {
                                return "";
                              }
                              return 'toolbox.convert.pdf2img.resolutionError'
                                  .tr;
                            }),
                        ShadInputWithValidate(
                            label: 'toolbox.convert.pdf2img.pageRange'.tr,
                            placeholder:
                                'toolbox.convert.pdf2img.pageRangePlaceholder'
                                    .tr,
                            initialValue: controller.pageRange.value,
                            onChanged: (value) {
                              controller.pageRange.value = value;
                            },
                            onValidate: (value) async {
                              if (validatePageRange(value)) {
                                return "";
                              }
                              return 'toolbox.convert.pdf2img.pageRangeError'
                                  .tr;
                            }),

                        if (PathUtils.isDesktop)
                          ShadSelectCustom(
                            label: 'toolbox.convert.common.outputLocation'.tr,
                            placeholder:
                                'toolbox.convert.common.selectOutputLocation'
                                    .tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.convert.common.outputDirectory'.tr,
                            placeholder: Text(
                                'toolbox.convert.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.convert.common.inputFile'.tr,
                          placeholder: Text(
                              'toolbox.convert.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['pdf'],
                          isRequired: true,
                          allowMultiple: true,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
