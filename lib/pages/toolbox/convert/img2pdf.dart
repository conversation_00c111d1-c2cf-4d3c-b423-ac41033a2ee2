import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/toolbox/convert.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';

class IMG2PDFPage extends StatefulWidget {
  const IMG2PDFPage({super.key});

  @override
  State<IMG2PDFPage> createState() => _IMG2PDFPageState();
}

class _IMG2PDFPageState extends State<IMG2PDFPage> {
  final controller = Get.put(PDFConvertPageController());

  @override
  void initState() {
    super.initState();
    controller.convertType.value = 'img2pdf';
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title:
            Text('toolbox.convert.img2pdf.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.convert.img2pdf.description'.tr,
                  style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.convert.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        ShadSwitchCustom(
                          label: 'toolbox.convert.img2pdf.mergeFiles'.tr,
                          initialValue: controller.mergeToOne.value,
                          onChanged: (v) {
                            controller.mergeToOne.value = v;
                          },
                        ),
                        if (controller.mergeToOne.value) ...[
                          ShadSelectCustom(
                            label: 'toolbox.convert.img2pdf.paperSize'.tr,
                            placeholder:
                                'toolbox.convert.img2pdf.selectPaperSize'.tr,
                            options: controller.paperSizeList,
                            initialValue: [controller.paperSize.value],
                            onChanged: (v) {
                              controller.paperSize.value = v.single;
                            },
                          ),
                          if (controller.paperSize.value != 'sameAsImage')
                            ShadSelectCustom(
                                label: 'toolbox.convert.img2pdf.orientation'.tr,
                                placeholder:
                                    'toolbox.convert.img2pdf.selectOrientation'.tr,
                                options: controller.orientationList,
                                initialValue: [controller.orientation.value],
                                onChanged: (v) {
                                  controller.orientation.value = v.single;
                                }),
                          ShadRadioGroupCustom(
                            label: 'toolbox.convert.img2pdf.sortBy'.tr,
                            items: controller.sortByList,
                            initialValue: controller.sortBy.value,
                            onChanged: (v) {
                              controller.sortBy.value = v;
                            },
                          ),
                          ShadRadioGroupCustom(
                            label: 'toolbox.convert.img2pdf.sortDirection'.tr,
                            items: controller.directionList,
                            initialValue: controller.sortDirection.value,
                            onChanged: (v) {
                              controller.sortDirection.value = v;
                            },
                          ),
                        ],
                        if (PathUtils.isDesktop)
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.convert.common.outputDirectory'.tr,
                            placeholder:
                                Text('toolbox.convert.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.convert.common.inputFile'.tr,
                          placeholder: Text(
                              'toolbox.convert.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['jpg', 'png', 'jpeg'],
                          isRequired: true,
                          allowMultiple: true,
                          enableDragSort: true,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
