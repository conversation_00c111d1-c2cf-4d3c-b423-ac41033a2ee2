import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/toolbox/delete.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFDeletePage extends StatefulWidget {
  const PDFDeletePage({super.key});

  @override
  State<PDFDeletePage> createState() => _PDFDeletePageState();
}

class _PDFDeletePageState extends State<PDFDeletePage> {
  final controller = Get.put(PDFDeletePageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.delete.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('toolbox.common.functionDescription'.tr,
                style: defaultPageTitleStyle),
            Text('toolbox.delete.description'.tr, style: theme.textTheme.muted),
            const SizedBox(height: 16),
            const CustomPageForm()
            // Expanded(
            //   child: SingleChildScrollView(
            //     child: ShadTabs<String>(
            //       controller: controller.tabController,
            // scrollable: true,
            //       onChanged: (value) {
            //         print(controller.tabController.selected);
            //       },
            //       tabs: const [
            //         ShadTab(
            //           value: 'custom',
            //           content: CustomPageForm(),
            //           child: Text('指定页面'),
            //         ),
            //         ShadTab(
            //           value: 'blank',
            //           content: BlankPageForm(),
            //           child: Text('空白页面'),
            //         ),
            //       ],
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}

class CustomPageForm extends GetView<PDFDeletePageController> {
  const CustomPageForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Obx(() => Column(
              spacing: 6,
              children: [
                ShadInputWithValidate(
                    label: 'toolbox.common.pageRange'.tr,
                    placeholder: 'toolbox.common.pageRangePlaceholder'.tr,
                    initialValue: controller.pageRange.value,
                    onChanged: (value) {
                      controller.pageRange.value = value;
                    },
                    onValidate: (value) async {
                      if (validatePageRange(value)) {
                        return "";
                      }
                      return 'toolbox.common.enterPageRange'.tr;
                    }),
                if (PathUtils.isDesktop)
                  ShadSelectCustom(
                    label: 'toolbox.common.outputLocation'.tr,
                    placeholder: 'toolbox.common.selectOutputLocation'.tr,
                    initialValue: [controller.outputMode.value],
                    options: outputModeList,
                    onChanged: (value) {
                      controller.outputMode.value = value.single;
                    },
                  ),
                if (controller.outputMode.value == 'custom')
                  ShadInputWithFileSelect(
                    key: ValueKey("output-dir-${controller.outputDir.value}"),
                    title: 'toolbox.common.outputDirectory'.tr,
                    placeholder: Text('toolbox.common.outputDirectory'.tr),
                    initialValue: [controller.outputDir.value],
                    isRequired: true,
                    isFolder: true,
                    onFilesSelected: (value) {
                      controller.outputDir.value = value.single;
                    },
                    onValidate: (value, files) async {
                      return await validateOutputDir(value, files);
                    },
                    onValidateError: (error) {
                      controller.outputDirError.value = error;
                    },
                  ),
                ShadInputWithFileSelect(
                  key: const ValueKey("input-file"),
                  title: 'toolbox.common.inputFile'.tr,
                  placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                  allowedExtensions: const ['pdf'],
                  isRequired: true,
                  allowMultiple: true,
                  initialValue: controller.selectedFilePaths,
                  onFilesSelected: (files) {
                    controller.selectedFilePaths.value = files;
                  },
                  onValidate: (value, files) async {
                    return await validateFile(value, files);
                  },
                  onValidateError: (error) {},
                ),
              ],
            )),
      ),
    );
  }
}

class BlankPageForm extends GetView<PDFDeletePageController> {
  const BlankPageForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Obx(() => Column(
              spacing: 6,
              children: [
                if (PathUtils.isDesktop)
                  ShadSelectCustom(
                    label: 'toolbox.common.outputLocation'.tr,
                    placeholder: 'toolbox.common.selectOutputLocation'.tr,
                    initialValue: [controller.outputMode.value],
                    options: outputModeList,
                    onChanged: (value) {
                      controller.outputMode.value = value.single;
                    },
                  ),
                if (controller.outputMode.value == 'custom')
                  ShadInputWithFileSelect(
                    key: ValueKey("output-dir-${controller.outputDir.value}"),
                    title: 'toolbox.common.outputDirectory'.tr,
                    placeholder: Text('toolbox.common.outputDirectory'.tr),
                    initialValue: [controller.outputDir.value],
                    isRequired: true,
                    isFolder: true,
                    onFilesSelected: (value) {
                      controller.outputDir.value = value.single;
                    },
                    onValidate: (value, files) async {
                      return await validateOutputDir(value, files);
                    },
                    onValidateError: (error) {
                      controller.outputDirError.value = error;
                    },
                  ),
                ShadInputWithFileSelect(
                  key: const ValueKey("input-file"),
                  title: 'toolbox.common.inputFile'.tr,
                  placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                  allowedExtensions: const ['pdf'],
                  isRequired: true,
                  allowMultiple: true,
                  initialValue: controller.selectedFilePaths,
                  onFilesSelected: (files) {
                    controller.selectedFilePaths.value = files;
                  },
                  onValidate: (value, files) async {
                    return await validateFile(value, files);
                  },
                  onValidateError: (error) {},
                ),
              ],
            )),
      ),
    );
  }
}
