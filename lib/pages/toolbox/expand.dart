import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/toolbox/expand.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFExpandPage extends StatefulWidget {
  const PDFExpandPage({super.key});

  @override
  State<PDFExpandPage> createState() => _PDFExpandPageState();
}

class _PDFExpandPageState extends State<PDFExpandPage> {
  final controller = Get.put(PDFExpandController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.expand.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.expand.description'.tr, style: theme.textTheme.muted),
              const SizedBox(height: 16),
              const CustomPageForm()
            ],
          ),
        ),
      ),
    );
  }
}

class CustomPageForm extends GetView<PDFExpandController> {
  const CustomPageForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Obx(() => Column(
              spacing: 6,
              children: [
                ShadRadioGroupCustom(
                  label: 'toolbox.expand.expandType'.tr,
                  initialValue: controller.expandMode.value,
                  items: controller.expandModeList,
                  onChanged: (value) {
                    controller.expandMode.value = value;
                  },
                ),
                if (controller.expandMode.value == 'blank') ...[
                  ShadRadioGroupCustom(
                    label: 'toolbox.expand.blank.unit'.tr,
                    initialValue: controller.unit.value,
                    items: controller.unitList,
                    onChanged: (value) {
                      controller.unit.value = value;
                    },
                  ),
                  ShadInputWithValidate(
                      label: 'toolbox.expand.blank.margin.top'.tr,
                      placeholder: 'toolbox.expand.blank.margin.topPlaceholder'.tr,
                      initialValue: controller.top.value.toString(),
                      onChanged: (value) {
                        controller.top.value =
                            double.tryParse(value) ?? controller.top.value;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return 'toolbox.expand.blank.margin.topPlaceholder'.tr;
                        }
                        final regex = RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                        if (!regex.hasMatch(value)) {
                          return 'toolbox.expand.blank.margin.invalid'.tr;
                        }
                        return "";
                      }),
                  ShadInputWithValidate(
                      label: 'toolbox.expand.blank.margin.bottom'.tr,
                      placeholder:
                          'toolbox.expand.blank.margin.bottomPlaceholder'.tr,
                      initialValue: controller.bottom.value.toString(),
                      onChanged: (value) {
                        controller.bottom.value =
                            double.tryParse(value) ?? controller.bottom.value;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return 'toolbox.expand.blank.margin.bottomPlaceholder'.tr;
                        }
                        final regex = RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                        if (!regex.hasMatch(value)) {
                          return 'toolbox.expand.blank.margin.invalid'.tr;
                        }
                        return "";
                      }),
                  ShadInputWithValidate(
                      label: 'toolbox.expand.blank.margin.left'.tr,
                      placeholder:
                          'toolbox.expand.blank.margin.leftPlaceholder'.tr,
                      initialValue: controller.left.value.toString(),
                      onChanged: (value) {
                        controller.left.value =
                            double.tryParse(value) ?? controller.left.value;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return 'toolbox.expand.blank.margin.leftPlaceholder'.tr;
                        }
                        final regex = RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                        if (!regex.hasMatch(value)) {
                          return 'toolbox.expand.blank.margin.invalid'.tr;
                        }
                        return "";
                      }),
                  ShadInputWithValidate(
                      label: 'toolbox.expand.blank.margin.right'.tr,
                      placeholder:
                          'toolbox.expand.blank.margin.rightPlaceholder'.tr,
                      initialValue: controller.right.value.toString(),
                      onChanged: (value) {
                        controller.right.value =
                            double.tryParse(value) ?? controller.right.value;
                      },
                      onValidate: (value) async {
                        if (value.isEmpty) {
                          return 'toolbox.expand.blank.margin.rightPlaceholder'.tr;
                        }
                        final regex = RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                        if (!regex.hasMatch(value)) {
                          return 'toolbox.expand.blank.margin.invalid'.tr;
                        }
                        return "";
                      }),
                ],
                if (controller.expandMode.value == 'file') ...[
                  ShadRadioGroupCustom(
                    label: 'toolbox.expand.file.direction'.tr,
                    initialValue: controller.direction.value,
                    items: controller.directionList,
                    onChanged: (value) {
                      controller.direction.value = value;
                    },
                  ),
                  ShadInputWithFileSelect(
                    key: const ValueKey("bg-file"),
                    title: 'toolbox.expand.file.bgFile'.tr,
                    placeholder: Text('toolbox.expand.file.bgFilePlaceholder'.tr),
                    allowedExtensions: const ['pdf'],
                    isRequired: true,
                    allowMultiple: false,
                    initialValue: [controller.bgFilePath.value],
                    onFilesSelected: (files) {
                      controller.bgFilePath.value = files.single;
                    },
                    onValidate: (value, files) async {
                      return await validateFile(value, files);
                    },
                    onValidateError: (error) {},
                  ),
                ],
                if (PathUtils.isDesktop)
                  ShadSelectCustom(
                    label: 'toolbox.common.outputLocation'.tr,
                    placeholder: 'toolbox.common.selectOutputLocation'.tr,
                    initialValue: [controller.outputMode.value],
                    options: outputModeList,
                    onChanged: (value) {
                      controller.outputMode.value = value.single;
                    },
                  ),
                if (controller.outputMode.value == 'custom')
                  ShadInputWithFileSelect(
                    key: ValueKey("output-dir-${controller.outputDir.value}"),
                    title: 'toolbox.common.outputDirectory'.tr,
                    placeholder: Text('toolbox.common.outputDirectory'.tr),
                    initialValue: [controller.outputDir.value],
                    isRequired: true,
                    isFolder: true,
                    onFilesSelected: (value) {
                      controller.outputDir.value = value.single;
                    },
                    onValidate: (value, files) async {
                      return await validateOutputDir(value, files);
                    },
                    onValidateError: (error) {
                      controller.outputDirError.value = error;
                    },
                  ),
                ShadInputWithFileSelect(
                  key: const ValueKey("input-file"),
                  title: 'toolbox.common.inputFile'.tr,
                  placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                  allowedExtensions: const ['pdf'],
                  isRequired: true,
                  allowMultiple: true,
                  initialValue: controller.selectedFilePaths,
                  onFilesSelected: (files) {
                    controller.selectedFilePaths.value = files;
                  },
                  onValidate: (value, files) async {
                    return await validateFile(value, files);
                  },
                  onValidateError: (error) {},
                ),
              ],
            )),
      ),
    );
  }
}
