import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/toolbox/bookmark.dart';

class PDFBookmarkPage extends StatefulWidget {
  const PDFBookmarkPage({super.key});

  @override
  State<PDFBookmarkPage> createState() => _PDFBookmarkPageState();
}

class _PDFBookmarkPageState extends State<PDFBookmarkPage> {
  final controller = Get.put(PDFBookmarkPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.bookmark.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('toolbox.common.functionDescription'.tr,
                style: defaultPageTitleStyle),
            Text('toolbox.bookmark.description'.tr, style: theme.textTheme.muted),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 3;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        logger.i(controller.tabController.selected);
                      },
                      tabs: [
                        ShadTab(
                          value: 'import',
                          content: const WriteForm(),
                          width: tabWidth,
                          child: Text('toolbox.bookmark.importTab'.tr),
                        ),
                        ShadTab(
                          value: 'export',
                          content: const ExtractForm(),
                          width: tabWidth,
                          child: Text('toolbox.bookmark.exportTab'.tr),
                        ),
                        ShadTab(
                          value: 'delete',
                          content: const DeleteForm(),
                          width: tabWidth,
                          child: Text('toolbox.bookmark.deleteTab'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class WriteForm extends GetView<PDFBookmarkPageController> {
  const WriteForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Column(
          spacing: 6,
          children: [
            ShadInputWithFileSelect(
              key: const ValueKey("bookmark-file"),
              title: 'toolbox.bookmark.bookmarkFile'.tr,
              placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
              allowedExtensions: const ['txt', 'md'],
              isRequired: true,
              allowMultiple: false,
              initialValue: controller.selectedFilePaths,
              onFilesSelected: (files) {
                controller.bookmarkFile.value = files.single;
              },
              onValidate: (value, files) async {
                return await validateFile(value, files);
              },
              onValidateError: (error) {},
            ),
            if (PathUtils.isDesktop)
              ShadSelectCustom(
                label: 'toolbox.common.outputLocation'.tr,
                placeholder: 'toolbox.common.selectOutputLocation'.tr,
                initialValue: [controller.outputMode.value],
                options: outputModeList,
                onChanged: (value) {
                  controller.outputMode.value = value.single;
                },
              ),
            if (controller.outputMode.value == 'custom')
              ShadInputWithFileSelect(
                key: ValueKey("output-dir-${controller.outputDir.value}"),
                title: 'toolbox.common.outputDirectory'.tr,
                placeholder: Text('toolbox.common.outputDirectory'.tr),
                initialValue: [controller.outputDir.value],
                isRequired: true,
                isFolder: true,
                onFilesSelected: (value) {
                  controller.outputDir.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {
                  controller.outputDirError.value = error;
                },
              ),
            ShadInputWithFileSelect(
              key: const ValueKey("input-file"),
              title: 'toolbox.common.inputFile'.tr,
              placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
              allowedExtensions: const ['pdf'],
              isRequired: true,
              allowMultiple: true,
              initialValue: controller.selectedFilePaths,
              onFilesSelected: (files) {
                controller.selectedFilePaths.value = files;
              },
              onValidate: (value, files) async {
                return await validateFile(value, files);
              },
              onValidateError: (error) {},
            ),
          ],
        ),
      ),
    );
  }
}

class ExtractForm extends GetView<PDFBookmarkPageController> {
  const ExtractForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Column(
          spacing: 6,
          children: [
            ShadRadioGroupCustom(
              label: 'toolbox.bookmark.exportFormat'.tr,
              initialValue: controller.exportFormat.value,
              items: controller.exportFormatList.toList(),
              onChanged: (value) {
                controller.exportFormat.value = value;
              },
            ),
            if (PathUtils.isDesktop)
              ShadSelectCustom(
                label: 'toolbox.common.outputLocation'.tr,
                placeholder: 'toolbox.common.selectOutputLocation'.tr,
                initialValue: [controller.outputMode.value],
                options: outputModeList,
                onChanged: (value) {
                  controller.outputMode.value = value.single;
                },
              ),
            if (controller.outputMode.value == 'custom')
              ShadInputWithFileSelect(
                key: ValueKey("output-dir-${controller.outputDir.value}"),
                title: 'toolbox.common.outputDirectory'.tr,
                placeholder: Text('toolbox.common.outputDirectory'.tr),
                initialValue: [controller.outputDir.value],
                isRequired: true,
                isFolder: true,
                onFilesSelected: (value) {
                  controller.outputDir.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {
                  controller.outputDirError.value = error;
                },
              ),
            ShadInputWithFileSelect(
              key: const ValueKey("input-file"),
              title: 'toolbox.common.inputFile'.tr,
              placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
              allowedExtensions: const ['pdf'],
              isRequired: true,
              allowMultiple: true,
              initialValue: controller.selectedFilePaths,
              onFilesSelected: (files) {
                controller.selectedFilePaths.value = files;
              },
              onValidate: (value, files) async {
                return await validateFile(value, files);
              },
              onValidateError: (error) {},
            ),
          ],
        ),
      ),
    );
  }
}

class DeleteForm extends GetView<PDFBookmarkPageController> {
  const DeleteForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Column(
          spacing: 6,
          children: [
            if (PathUtils.isDesktop)
              ShadSelectCustom(
                label: 'toolbox.common.outputLocation'.tr,
                placeholder: 'toolbox.common.selectOutputLocation'.tr,
                initialValue: [controller.outputMode.value],
                options: outputModeList,
                onChanged: (value) {
                  controller.outputMode.value = value.single;
                },
              ),
            if (controller.outputMode.value == 'custom')
              ShadInputWithFileSelect(
                key: ValueKey("output-dir-${controller.outputDir.value}"),
                title: 'toolbox.common.outputDirectory'.tr,
                placeholder: Text('toolbox.common.outputDirectory'.tr),
                initialValue: [controller.outputDir.value],
                isRequired: true,
                isFolder: true,
                onFilesSelected: (value) {
                  controller.outputDir.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {
                  controller.outputDirError.value = error;
                },
              ),
            ShadInputWithFileSelect(
              key: const ValueKey("input-file"),
              title: 'toolbox.common.inputFile'.tr,
              placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
              allowedExtensions: const ['pdf'],
              isRequired: true,
              allowMultiple: true,
              initialValue: controller.selectedFilePaths,
              onFilesSelected: (files) {
                controller.selectedFilePaths.value = files;
              },
              onValidate: (value, files) async {
                return await validateFile(value, files);
              },
              onValidateError: (error) {},
            ),
          ],
        ),
      ),
    );
  }
}
