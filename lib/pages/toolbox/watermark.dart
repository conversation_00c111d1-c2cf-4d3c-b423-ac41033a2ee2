import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/toolbox/watermark.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFWatermarkPage extends StatefulWidget {
  const PDFWatermarkPage({super.key});

  @override
  State<PDFWatermarkPage> createState() => _PDFWatermarkPageState();
}

class _PDFWatermarkPageState extends State<PDFWatermarkPage> {
  final controller = Get.put(PDFWatermarkPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.watermark.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('toolbox.common.functionDescription'.tr,
                style: defaultPageTitleStyle),
            Text('toolbox.watermark.description'.tr,
                style: theme.textTheme.muted),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 2;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        logger.i(value);
                      },
                      tabs: [
                        ShadTab(
                          value: 'add',
                          content: const AddWatermarkForm(),
                          width: tabWidth,
                          child: Text('toolbox.watermark.addTab'.tr),
                        ),
                        ShadTab(
                          value: 'remove',
                          content: const RemoveWatermarkForm(),
                          width: tabWidth,
                          child: Text('toolbox.watermark.removeTab'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AddWatermarkForm extends GetView<PDFWatermarkPageController> {
  const AddWatermarkForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Obx(
        () => Column(
          spacing: 6,
          children: [
            ShadRadioGroupCustom(
              key: const ValueKey("watermark-type"),
              label: 'toolbox.watermark.type'.tr,
              initialValue: controller.watermarkType.value,
              items: controller.watermarkTypeList.toList(),
              onChanged: (value) {
                logger.i(value);
                controller.watermarkType.value = value;
              },
            ),
            ShadSwitchCustom(
              key: const ValueKey("advanced-mode"),
              label: 'toolbox.watermark.advancedOptions'.tr,
              initialValue: controller.advancedMode.value,
              onChanged: (v) {
                logger.i(v);
                controller.advancedMode.value = v;
              },
            ),
            if (controller.watermarkType.value == 'text') ...[
              ShadInputWithValidate(
                key: const ValueKey("watermark-text"),
                label: 'toolbox.watermark.text.content'.tr,
                placeholder: 'toolbox.watermark.text.content'.tr,
                initialValue: controller.watermarkText.value,
                onChanged: (value) {
                  controller.watermarkText.value = value;
                },
                onValidate: (value) async {
                  return "";
                },
              ),
              ShadSwitchCustom(
                key: const ValueKey("use-custom-font"),
                label: 'toolbox.watermark.text.customFont'.tr,
                initialValue: controller.useCustomFont.value,
                onChanged: (v) {
                  controller.useCustomFont.value = v;
                },
              ),
              if (controller.useCustomFont.value) ...[
                ShadInputWithFileSelect(
                  key: const ValueKey("custom-font-file"),
                  title: 'toolbox.watermark.text.fontFile'.tr,
                  placeholder: Text('toolbox.watermark.text.fontFile'.tr),
                  allowedExtensions: const ['ttf'],
                  isRequired: true,
                  allowMultiple: false,
                  initialValue: [controller.customFontFilePath.value],
                  onFilesSelected: (files) {
                    controller.customFontFilePath.value = files.single;
                  },
                  onValidate: (value, files) async {
                    return await validateFile(value, files);
                  },
                  onValidateError: (error) {},
                ),
              ] else ...[
                ShadSelectCustom(
                  key: const ValueKey("font-family"),
                  label: 'toolbox.watermark.text.fontFamily'.tr,
                  placeholder: 'toolbox.watermark.text.selectFontFamily'.tr,
                  isMultiple: false,
                  initialValue: [controller.fontName.value],
                  options: controller.fontFamilyList.toList(),
                  onChanged: (value) {
                    logger.i(value);
                    controller.fontName.value = value.single;
                  },
                ),
              ],
              ShadInputWithValidate(
                  key: const ValueKey("font-size"),
                  label: 'toolbox.watermark.text.fontSize'.tr,
                  placeholder: 'toolbox.watermark.text.fontSize'.tr,
                  initialValue: controller.fontSize.value.toString(),
                  onChanged: (value) {
                    controller.fontSize.value = double.tryParse(value) ?? 12;
                  },
                  onValidate: (value) async {
                    return "";
                  }),
              ShadColorPickerCustom(
                key: ValueKey(controller.fontColor.value.hashCode),
                label: 'toolbox.watermark.text.fontColor'.tr,
                initialValue: controller.fontColor.value,
                onChanged: (value) {
                  controller.fontColor.value = value;
                },
              ),
              if (controller.advancedMode.value) ...[
                ShadInputWithValidate(
                  key: const ValueKey("opacity"),
                  label: 'toolbox.watermark.text.opacity'.tr,
                  placeholder: 'toolbox.watermark.text.opacity'.tr,
                  initialValue: controller.opacity.value.toString(),
                  onChanged: (value) {
                    controller.opacity.value = double.tryParse(value) ?? 0.5;
                  },
                  onValidate: (value) async {
                    return "";
                  },
                ),
                ShadInputWithValidate(
                  key: const ValueKey("rotate-text"),
                  label: 'toolbox.watermark.text.rotation'.tr,
                  placeholder: 'toolbox.watermark.text.rotation'.tr,
                  initialValue: controller.rotate.value.toString(),
                  onChanged: (value) {
                    controller.rotate.value = double.tryParse(value) ?? 0;
                  },
                  onValidate: (value) async {
                    return "";
                  },
                ),
                ShadInputWithValidate(
                  key: const ValueKey("x-offset"),
                  label: "${'toolbox.watermark.text.position'.tr} X",
                  placeholder: "X",
                  initialValue: controller.xOffset.value.toString(),
                  onChanged: (value) {
                    controller.xOffset.value = double.tryParse(value) ?? 0;
                  },
                  onValidate: (value) async {
                    return "";
                  },
                ),
                ShadInputWithValidate(
                  key: const ValueKey("y-offset"),
                  label: "${'toolbox.watermark.text.position'.tr} Y",
                  placeholder: "Y",
                  initialValue: controller.yOffset.value.toString(),
                  onChanged: (value) {
                    controller.yOffset.value = double.tryParse(value) ?? 0;
                  },
                  onValidate: (value) async {
                    return "";
                  },
                ),
              ],
            ],
            if (controller.watermarkType.value == 'image') ...[
              ShadInputWithFileSelect(
                key: const ValueKey("image-file"),
                title: 'toolbox.watermark.image.file'.tr,
                placeholder: Text('toolbox.watermark.image.file'.tr),
                allowedExtensions: const ['png', 'jpg', 'jpeg', 'gif', 'bmp'],
                isRequired: true,
                allowMultiple: false,
                initialValue: [controller.watermarkImage.value],
                onFilesSelected: (files) {
                  controller.watermarkImage.value = files.single;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
              if (controller.advancedMode.value) ...[
                ShadInputWithValidate(
                  key: const ValueKey("opacity-image"),
                  label: 'toolbox.watermark.image.opacity'.tr,
                  placeholder: 'toolbox.watermark.image.opacity'.tr,
                  initialValue: controller.opacity.value.toString(),
                  onChanged: (value) {
                    controller.opacity.value = double.tryParse(value) ?? 0.5;
                  },
                  onValidate: (value) async {
                    return "";
                  },
                ),
                ShadInputWithValidate(
                  key: const ValueKey("rotate-image"),
                  label: 'toolbox.watermark.image.rotation'.tr,
                  placeholder: 'toolbox.watermark.image.rotation'.tr,
                  initialValue: controller.rotate.value.toString(),
                  onChanged: (value) {
                    controller.rotate.value = double.tryParse(value) ?? 0;
                  },
                  onValidate: (value) async {
                    return "";
                  },
                ),
                ShadInputWithValidate(
                  key: const ValueKey("scale"),
                  label: 'toolbox.watermark.image.scale'.tr,
                  placeholder: 'toolbox.watermark.image.scale'.tr,
                  initialValue: controller.scale.value.toString(),
                  onChanged: (value) {
                    controller.scale.value = double.tryParse(value) ?? 1;
                  },
                  onValidate: (value) async {
                    return "";
                  },
                ),
                ShadInputWithValidate(
                  key: const ValueKey("x-offset-image"),
                  label: "${'toolbox.watermark.image.position'.tr} X",
                  placeholder: "X",
                  initialValue: controller.xOffset.value.toString(),
                  onChanged: (value) {
                    controller.xOffset.value = double.tryParse(value) ?? 0;
                  },
                  onValidate: (value) async {
                    return "";
                  },
                ),
                ShadInputWithValidate(
                  key: const ValueKey("y-offset-image"),
                  label: "${'toolbox.watermark.image.position'.tr} Y",
                  placeholder: "Y",
                  initialValue: controller.yOffset.value.toString(),
                  onChanged: (value) {
                    controller.yOffset.value = double.tryParse(value) ?? 0;
                  },
                  onValidate: (value) async {
                    return "";
                  },
                ),
              ],
            ],
            if (PathUtils.isDesktop)
              ShadSelectCustom(
                key: const ValueKey("output-position"),
                label: 'toolbox.common.outputLocation'.tr,
                placeholder: 'toolbox.common.selectOutputLocation'.tr,
                initialValue: [controller.outputMode.value],
                options: outputModeList,
                onChanged: (value) {
                  controller.outputMode.value = value.single;
                },
              ),
            if (controller.outputMode.value == 'custom')
              ShadInputWithFileSelect(
                key: ValueKey("output-dir-${controller.outputDir.value}"),
                title: 'toolbox.common.outputDirectory'.tr,
                placeholder: Text('toolbox.common.outputDirectory'.tr),
                initialValue: [controller.outputDir.value],
                isRequired: true,
                isFolder: true,
                onFilesSelected: (value) {
                  controller.outputDir.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {
                  controller.outputDirError.value = error;
                },
              ),
            ShadInputWithFileSelect(
              key: const ValueKey("input-file"),
              title: 'toolbox.common.inputFile'.tr,
              placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
              allowedExtensions: const ['pdf'],
              isRequired: true,
              allowMultiple: true,
              initialValue: controller.selectedFilePaths,
              onFilesSelected: (files) {
                controller.selectedFilePaths.value = files;
              },
              onValidate: (value, files) async {
                return await validateFile(value, files);
              },
              onValidateError: (error) {},
            ),
            ShadInputWithValidate(
                key: const ValueKey("pageRange"),
                label: 'toolbox.common.pageRange'.tr,
                placeholder: 'toolbox.common.pageRangePlaceholder'.tr,
                initialValue: controller.pageRange.value,
                onChanged: (value) {
                  controller.pageRange.value = value;
                },
                onValidate: (value) async {
                  if (validatePageRange(value)) {
                    return "";
                  }
                  return 'toolbox.common.enterPageRange'.tr;
                }),
          ],
        ),
      ),
    );
  }
}

class RemoveWatermarkForm extends GetView<PDFWatermarkPageController> {
  const RemoveWatermarkForm({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16),
      footer: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('toolbox.common.submit'.tr),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
      child: Obx(
        () => Column(
          spacing: 6,
          children: [
            ShadRadioGroupCustom(
              key: const ValueKey("watermark-remove-type"),
              label: 'toolbox.watermark.type'.tr,
              initialValue: controller.watermarkRemoveType.value,
              items: controller.watermarkRemoveTypeList.toList(),
              onChanged: (value) {
                controller.watermarkRemoveType.value = value;
              },
            ),
            if (controller.watermarkRemoveType.value == "mask")
            ShadSwitchCustom(
              label: 'toolbox.crop.expandMode'.tr,
              initialValue: controller.expandMode.value,
              onChanged: (v) {
                controller.expandMode.value = v;
              },
            ),
            if (controller.watermarkRemoveType.value == "type" ||
                controller.watermarkRemoveType.value == "content" ||
                controller.watermarkRemoveType.value == "image" ||
                controller.watermarkRemoveType.value == "path") ...[
              ShadRadioGroupCustom(
                label: 'toolbox.watermark.step_option'.tr,
                initialValue: controller.step.value,
                items: controller.stepOptionList,
                onChanged: (value) {
                  controller.step.value = value;
                },
              ),
              if (controller.step.value == "step1")
                ShadInputWithValidate(
                    key: const ValueKey("wm_page_number"),
                    label: 'toolbox.watermark.wm_page_number'.tr,
                    placeholder:
                        'toolbox.watermark.wm_page_number_placeholder'.tr,
                    initialValue: controller.watermarkPage.value,
                    onChanged: (value) {
                      controller.watermarkPage.value = value;
                    },
                    onValidate: (value) async {
                      // 限制只能输入正整数
                      if (value.isEmpty) {
                        return 'toolbox.watermark.wm_page_number_required'.tr;
                      }
                      final reg = RegExp(r'^\d+$');
                      if (!reg.hasMatch(value)) {
                        return 'toolbox.watermark.wm_page_number_invalid'.tr;
                      }
                      return "";
                    }),
              if (controller.step.value == "step2")
                ShadInputWithValidate(
                    key: const ValueKey("wm_index"),
                    label: 'toolbox.watermark.wm_index'.tr,
                    placeholder: 'toolbox.watermark.wm_index_placeholder'.tr,
                    initialValue: controller.watermarkIndex.value,
                    onChanged: (value) {
                      controller.watermarkIndex.value = value;
                    },
                    onValidate: (value) async {
                      if (validatePageRange(value)) {
                        return "";
                      }
                      return 'toolbox.common.enterPageRange'.tr;
                    }),
            ],
            if (controller.watermarkRemoveType.value == "edit_text") ...[
              ShadInputWithValidate(
                  key: const ValueKey("wm_text_to_remove"),
                  label: 'toolbox.watermark.wm_text_to_remove'.tr,
                  placeholder:
                      'toolbox.watermark.wm_text_to_remove_placeholder'.tr,
                  initialValue: controller.watermarkTextToRemove.value,
                  onChanged: (value) {
                    controller.watermarkTextToRemove.value = value;
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return "toolbox.watermark.wm_text_cannot_empty".tr;
                    }
                    return '';
                  }),
            ],
            if (controller.watermarkRemoveType.value == "pixel") ...[
              ShadRadioGroupCustom(
                label: 'toolbox.watermark.step_option'.tr,
                initialValue: controller.step.value,
                items: controller.stepOptionList,
                onChanged: (value) {
                  controller.step.value = value;
                },
              ),
              if (controller.step.value == "step1") ...[
                ShadInputWithValidate(
                    key: const ValueKey("wm_page_number"),
                    label: 'toolbox.watermark.wm_page_number'.tr,
                    placeholder:
                        'toolbox.watermark.wm_page_number_placeholder'.tr,
                    initialValue: controller.watermarkPage.value,
                    onChanged: (value) {
                      controller.watermarkPage.value = value;
                    },
                    onValidate: (value) async {
                      if (validatePageRange(value)) {
                        return "";
                      }
                      return 'toolbox.common.enterPageRange'.tr;
                    }),
              ],
              if (controller.step.value == "step2") ...[
                ShadInputWithValidate(
                    key: const ValueKey("lower_bounds"),
                    label: 'toolbox.watermark.lower_bounds'.tr,
                    placeholder:
                        'toolbox.watermark.lower_bounds_placeholder'.tr,
                    initialValue: controller.lowerBounds.value,
                    onChanged: (value) {
                      controller.lowerBounds.value = value;
                    },
                    onValidate: (value) async {
                      // 验证是否为0～255之间的整数
                      if (value.isEmpty) {
                        return 'toolbox.watermark.lower_bounds_required'.tr;
                      }
                      final reg = RegExp(r'^\d+$');
                      if (!reg.hasMatch(value)) {
                        return 'toolbox.watermark.upper_bounds_invalid'.tr;
                      }
                      final num = int.parse(value);
                      if (num < 0 || num > 255) {
                        return 'toolbox.watermark.upper_bounds_range'.tr;
                      }
                      return "";
                    }),
                ShadInputWithValidate(
                    key: const ValueKey("upper_bounds"),
                    label: 'toolbox.watermark.upper_bounds'.tr,
                    placeholder:
                        'toolbox.watermark.upper_bounds_placeholder'.tr,
                    initialValue: controller.upperBounds.value,
                    onChanged: (value) {
                      controller.upperBounds.value = value;
                    },
                    onValidate: (value) async {
                      // 验证是否为0～255之间的整数
                      if (value.isEmpty) {
                        return 'toolbox.watermark.upper_bounds_required'.tr;
                      }
                      final reg = RegExp(r'^\d+$');
                      if (!reg.hasMatch(value)) {
                        return 'toolbox.watermark.upper_bounds_invalid'.tr;
                      }
                      final num = int.parse(value);
                      if (num < 0 || num > 255) {
                        return 'toolbox.watermark.upper_bounds_range'.tr;
                      }
                      return "";
                    }),
                ShadColorPickerCustom(
                  key: ValueKey(controller.targetColor.value.hashCode),
                  label: 'toolbox.watermark.text.target_color'.tr,
                  initialValue: controller.targetColor.value,
                  onChanged: (value) {
                    controller.targetColor.value = value;
                  },
                ),
                ShadSwitchCustom(
                  key: const ValueKey("limit_regin"),
                  label: 'toolbox.watermark.limit_regin'.tr,
                  initialValue: controller.advancedMode.value,
                  onChanged: (v) {
                    logger.i(v);
                    controller.advancedMode.value = v;
                  },
                ),
              ],
            ],
            if (PathUtils.isDesktop)
              ShadSelectCustom(
                key: const ValueKey("output-position-remove"),
                label: 'toolbox.common.outputLocation'.tr,
                placeholder: 'toolbox.common.selectOutputLocation'.tr,
                initialValue: [controller.outputMode.value],
                options: outputModeList,
                onChanged: (value) {
                  controller.outputMode.value = value.single;
                },
              ),
            if (controller.outputMode.value == 'custom')
              ShadInputWithFileSelect(
                key:
                    ValueKey("output-dir-${controller.outputDir.value}-remove"),
                title: 'toolbox.common.outputDirectory'.tr,
                placeholder: Text('toolbox.common.outputDirectory'.tr),
                initialValue: [controller.outputDir.value],
                isRequired: true,
                isFolder: true,
                onFilesSelected: (value) {
                  controller.outputDir.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {
                  controller.outputDirError.value = error;
                },
              ),
            if (!((controller.watermarkRemoveType.value == "type" ||
                    controller.watermarkRemoveType.value == "content" ||
                    controller.watermarkRemoveType.value == "image" ||
                    controller.watermarkRemoveType.value == "path" ||
                    controller.watermarkRemoveType.value == "pixel") &&
                controller.step == "step1"))
              ShadInputWithValidate(
                  key: const ValueKey("pageRange-remove"),
                  label: 'toolbox.common.pageRange'.tr,
                  placeholder: 'toolbox.common.pageRangePlaceholder'.tr,
                  initialValue: controller.pageRange.value,
                  onChanged: (value) {
                    controller.pageRange.value = value;
                  },
                  onValidate: (value) async {
                    if (validatePageRange(value)) {
                      return "";
                    }
                    return 'toolbox.common.enterPageRange'.tr;
                  }),
            ShadInputWithFileSelect(
              key: const ValueKey("input-file-remove"),
              title: 'toolbox.common.inputFile'.tr,
              placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
              allowedExtensions: const ['pdf'],
              isRequired: true,
              allowMultiple: true,
              initialValue: controller.selectedFilePaths,
              onFilesSelected: (files) {
                controller.selectedFilePaths.value = files;
              },
              onValidate: (value, files) async {
                return await validateFile(value, files);
              },
              onValidateError: (error) {},
            ),
          ],
        ),
      ),
    );
  }
}
