import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/toolbox/scale.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/common.dart';

class PDFScalePage extends StatefulWidget {
  const PDFScalePage({super.key});

  @override
  State<PDFScalePage> createState() => _PDFScalePageState();
}

class _PDFScalePageState extends State<PDFScalePage> {
  final controller = Get.put(PDFScalePageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('toolbox.scale.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('toolbox.common.functionDescription'.tr,
                  style: defaultPageTitleStyle),
              Text('toolbox.scale.description'.tr,
                  style: theme.textTheme.muted),
              const SizedBox(height: 16),
              ShadCard(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                footer: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ShadButton(
                        size: ShadButtonSize.lg,
                        onPressed: () {
                          controller.submit(context);
                        },
                        child: Text('toolbox.common.submit'.tr),
                      ),
                    )
                  ],
                ),
                child: Obx(() => Column(
                      spacing: 6,
                      children: [
                        const SizedBox(height: 4),
                        ShadRadioGroupCustom(
                          label: 'toolbox.scale.scaleType'.tr,
                          initialValue: controller.scaleMode.value,
                          items: controller.scaleModeList,
                          onChanged: (value) {
                            controller.scaleMode.value = value;
                          },
                        ),
                        if (controller.scaleMode.value == 'ratio') ...[
                          ShadInputWithValidate(
                              key: const ValueKey("scale-ratio"),
                              label: 'toolbox.scale.scaleRatio'.tr,
                              placeholder:
                                  'toolbox.scale.scaleRatioPlaceholder'.tr,
                              initialValue:
                                  controller.scaleRatio.value.toString(),
                              onChanged: (value) {
                                controller.scaleRatio.value =
                                    double.tryParse(value) ??
                                        controller.scaleRatio.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.scale.scaleRatioRequired'.tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return 'toolbox.scale.enterPositiveNumber'.tr;
                                }
                                return "";
                              }),
                        ],
                        if (controller.scaleMode.value == 'fixed') ...[
                          ShadSelectCustom(
                            label: 'toolbox.scale.paperSize'.tr,
                            placeholder: 'toolbox.scale.selectPaperSize'.tr,
                            initialValue: [controller.paperSize.value],
                            isMultiple: false,
                            options: paperSizeList,
                            onChanged: (value) {
                              logger.i(value);
                              controller.paperSize.value = value.single;
                            },
                          ),
                        ],
                        if (controller.scaleMode.value == 'custom') ...[
                          ShadInputWithValidate(
                              key: const ValueKey("scale-width"),
                              label: 'toolbox.scale.width'.tr,
                              placeholder: 'toolbox.scale.widthPlaceholder'.tr,
                              initialValue: controller.width.value.toString(),
                              onChanged: (value) {
                                controller.width.value =
                                    double.tryParse(value) ??
                                        controller.width.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.scale.widthRequired'.tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return 'toolbox.scale.enterPositiveNumber'.tr;
                                }
                                return "";
                              }),
                          ShadInputWithValidate(
                              key: const ValueKey("scale-height"),
                              label: 'toolbox.scale.height'.tr,
                              placeholder: 'toolbox.scale.heightPlaceholder'.tr,
                              initialValue: controller.height.value.toString(),
                              onChanged: (value) {
                                controller.height.value =
                                    double.tryParse(value) ??
                                        controller.height.value;
                              },
                              onValidate: (value) async {
                                if (value.isEmpty) {
                                  return 'toolbox.scale.heightRequired'.tr;
                                }
                                final regex =
                                    RegExp(r'^(?:0*\d+(?:\.\d+)?|0*\.\d+)$');
                                if (!regex.hasMatch(value)) {
                                  return 'toolbox.scale.enterPositiveNumber'.tr;
                                }
                                return "";
                              }),
                        ],
                        ShadInputWithValidate(
                            key: const ValueKey("page-range"),
                            label: 'toolbox.common.pageRange'.tr,
                            placeholder:
                                'toolbox.common.pageRangePlaceholder'.tr,
                            initialValue: controller.pageRange.value,
                            onChanged: (value) {
                              controller.pageRange.value = value;
                            },
                            onValidate: (value) async {
                              if (validatePageRange(value)) {
                                return "";
                              }
                              return 'toolbox.common.enterPageRange'.tr;
                            }),
                        if (PathUtils.isDesktop)
                          ShadSelectCustom(
                            key: const ValueKey("output-mode"),
                            label: 'toolbox.common.outputLocation'.tr,
                            placeholder:
                                'toolbox.common.selectOutputLocation'.tr,
                            initialValue: [controller.outputMode.value],
                            options: outputModeList,
                            onChanged: (value) {
                              controller.outputMode.value = value.single;
                            },
                          ),
                        if (controller.outputMode.value == 'custom')
                          ShadInputWithFileSelect(
                            key: ValueKey(
                                "output-dir-${controller.outputDir.value}"),
                            title: 'toolbox.common.outputDirectory'.tr,
                            placeholder:
                                Text('toolbox.common.outputDirectory'.tr),
                            initialValue: [controller.outputDir.value],
                            isRequired: true,
                            isFolder: true,
                            onFilesSelected: (value) {
                              controller.outputDir.value = value.single;
                            },
                            onValidate: (value, files) async {
                              return await validateOutputDir(value, files);
                            },
                            onValidateError: (error) {
                              controller.outputDirError.value = error;
                            },
                          ),
                        ShadInputWithFileSelect(
                          key: const ValueKey("input-file"),
                          title: 'toolbox.common.inputFile'.tr,
                          placeholder:
                              Text('toolbox.common.inputFilePlaceholder'.tr),
                          allowedExtensions: const ['pdf'],
                          isRequired: true,
                          allowMultiple: true,
                          initialValue: controller.selectedFilePaths,
                          onFilesSelected: (files) {
                            controller.selectedFilePaths.value = files;
                          },
                          onValidate: (value, files) async {
                            return await validateFile(value, files);
                          },
                          onValidateError: (error) {},
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
