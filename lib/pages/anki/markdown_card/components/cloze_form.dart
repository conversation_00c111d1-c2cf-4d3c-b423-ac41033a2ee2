import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/markdown_card.dart';

class <PERSON>lozeForm extends GetView<MarkdownCardPageController> {
  const ClozeForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
              onAddNew: (newDeckName) {
                // Add to the deck list if not already present
                if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                  ankiConnectController.parentDeckList.add(newDeckName);
                }

                // Set as selected deck
                controller.parentDeck.value = newDeckName;
              },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              // ShadSwitchCustom(
              //   label: 'anki.common.create_subdeck'.tr,
              //   initialValue: controller.isCreateSubDeck.value,
              //   onChanged: (v) {
              //     controller.isCreateSubDeck.value = v;
              //   },
              // ),
              ShadSelectCustom(
                key: ValueKey(controller.separator.value),
                label: 'anki.markdown_card.separator'.tr,
                placeholder: 'anki.markdown_card.separator'.tr,
                isMultiple: false,
                initialValue: [controller.separator.value],
                options: controller.separatorList.toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.separator.value = value.single;
                },
              ),

              ShadRadioGroupCustom(
                label: 'anki.common.card_mode'.tr,
                initialValue: controller.clozeMode.value,
                items: controller.clozeModeList,
                onChanged: (value) {
                  controller.clozeMode.value = value;
                },
              ),
              // ShadFieldMappingTable(
              //   key: ValueKey(
              //       "field_table_${controller.cardModel.value}_${ankiConnectController.fieldList.length}_cloze"),
              //   fieldList: ankiConnectController.fieldList,
              //   optionsList: {
              //     "Front": controller.separatorList.toList(),
              //     "Back": controller.qaAnswerRegexList.toList(),
              //   },
              //   defaultOptionsList: controller.qaAnswerRegexList,
              //   cardModel: controller.cardModel.value,
              //   onUpdateFieldMapping: (field, patternMatch) {
              //     controller.updateFieldMapping(field, patternMatch);
              //   },
              //   getFieldMappingValue: (field) {
              //     return controller.getFieldMappingValue(field);
              //   },
              // ),
              ShadCheckboxGroup(
                label: 'anki.markdown_card.cloze_grammar'.tr,
                initialValues: controller.clozeGrammar.toList(),
                items: controller.clozeGrammarList.toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.clozeGrammar.value = value;
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'anki.markdown_card.at_least_one_cloze_grammar'.tr;
                  }
                  return "";
                },
                onValidateError: (error) {},
              ),
              ShadSwitchCustom(
                label: 'anki.markdown_card.ignore_group'.tr,
                initialValue: controller.isIgnoreGroup.value,
                onChanged: (v) {
                  controller.isIgnoreGroup.value = v;
                },
              ),
              ShadSwitchCustom(
                label: 'anki.markdown_card.one_cloze_per_card'.tr,
                initialValue: controller.isPerClozePerCard.value,
                onChanged: (v) {
                  controller.isPerClozePerCard.value = v;
                },
              ),
              ShadSwitchCustom(
                label: 'anki.markdown_card.obsidian_syntax'.tr,
                initialValue: controller.isObsidian.value,
                onChanged: (v) {
                  controller.isObsidian.value = v;
                },
              ),
              ShadInputWithFileSelect(
                title: 'anki.markdown_card.media_file_directory'.tr,
                placeholder: Text('anki.markdown_card.media_folder_placeholder'.tr),
                initialValue: [controller.mediaFolder.value],
                isFolder: true,
                onFilesSelected: (value) {
                  controller.mediaFolder.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {},
              ),
              ShadSelectWithInput(
                key:
                    ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.markdown_card.select_tags'.tr,
                searchPlaceholder: 'anki.markdown_card.input_tags'.tr,
                isMultiple: true,
                initialValue: controller.tags,
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the global tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tags.contains(newTag)) {
                    controller.tags.add(newTag);
                  }
                },
              ),
              ShadInputWithFileSelect(
                key: const ValueKey('input-file'),
                title: 'toolbox.common.inputFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const ['txt', 'md'],
                isRequired: true,
                allowMultiple: true,
                onFilesSelected: (files) {
                  controller.selectedFilePaths.value = files;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
              const SizedBox(height: 8),
            ],
            // ],
          )),
    );
  }
}
