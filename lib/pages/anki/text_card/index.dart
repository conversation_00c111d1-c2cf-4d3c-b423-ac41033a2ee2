import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/cloze_form.dart';
import 'components/qa_form.dart';
import 'components/choice_form.dart';
import 'components/judge_form.dart';
import 'package:anki_guru/controllers/anki/text_card.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:logger/logger.dart';

class TextCard extends StatefulWidget {
  const TextCard({super.key});

  @override
  State<TextCard> createState() => _TextCardState();
}

class _TextCardState extends State<TextCard> {
  final controller = Get.put(TextCardFormController());
  final logger = Logger();

  void _handleHorizontalDrag(DragEndDetails details) {
    const sensitivity = 5.0;
    const values = ['cloze', 'qa', 'choice', 'judge'];
    final currentIndex = values.indexOf(controller.tabController.selected);

    if (details.primaryVelocity == null) return;

    if (details.primaryVelocity! < -sensitivity &&
        currentIndex < values.length - 1) {
      controller.tabController.select(values[currentIndex + 1]);
    } else if (details.primaryVelocity! > sensitivity && currentIndex > 0) {
      controller.tabController.select(values[currentIndex - 1]);
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.text_card.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_horiz),
            offset: const Offset(0, 40),
            onSelected: (value) {
              switch (value) {
                case 'save':
                  if (controller.tabController.selected == "cloze") {
                  } else if (controller.tabController.selected == "qa") {}
                  break;
                case 'restore':
                  if (controller.tabController.selected == "cloze") {
                  } else if (controller.tabController.selected == "qa") {}
                  break;
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              PopupMenuItem<String>(
                value: 'save',
                child: Row(
                  children: [
                    const Icon(Icons.save),
                    const SizedBox(width: 8),
                    Text('anki.text_card.save_params'.tr),
                  ],
                ),
              ),
              PopupMenuItem<String>(
                value: 'restore',
                child: Row(
                  children: [
                    const Icon(Icons.restore),
                    const SizedBox(width: 8),
                    Text('anki.text_card.restore_params'.tr),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: GestureDetector(
        onHorizontalDragEnd: _handleHorizontalDrag,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('anki.text_card.function_description'.tr, style: defaultPageTitleStyle),
                  Text('anki.text_card.feature_description'.tr,
                      style: theme.textTheme.muted),
                  const SizedBox(height: 16),
                ],
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      // 设置每个标签的最小宽度
                      const minTabWidth = 120.0;
                      // 计算平均分配的宽度
                      final avgTabWidth = constraints.maxWidth / 4;
                      // 使用较大的值，确保标签不会太窄
                      final tabWidth =
                          avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                      return ShadTabs<String>(
                        controller: controller.tabController,
                        scrollable: true,
                        onChanged: (value) async {
                          print(controller.tabController.selected);
                          if (controller.tabController.selected == 'choice' ||
                              controller.tabController.selected == 'judge') {
                            controller.cardModel.value = "Kevin Choice Card v2";
                            await controller
                                .updateFieldList(controller.cardModel.value);
                          } else if (controller.tabController.selected ==
                              'cloze') {
                            controller.cardModel.value = "Kevin Text Cloze v3";
                            await controller
                                .updateFieldList(controller.cardModel.value);
                          } else if (controller.tabController.selected ==
                              'qa') {
                            controller.cardModel.value =
                                "Kevin Text QA Card v2";
                            await controller
                                .updateFieldList(controller.cardModel.value);
                          }

                          // 确保更新整个控制器状态
                          controller.update();
                        },
                        tabs: [
                          ShadTab(
                            value: 'cloze',
                            content: const ClozeForm(),
                            width: tabWidth,
                            child: Text('anki.text_card.cloze_tab'.tr),
                          ),
                          ShadTab(
                            value: 'qa',
                            content: const QAForm(),
                            width: tabWidth,
                            child: Text('anki.text_card.qa_tab'.tr),
                          ),
                          ShadTab(
                            value: 'choice',
                            content: const ChoiceForm(),
                            width: tabWidth,
                            child: Text('anki.text_card.choice_tab'.tr),
                          ),
                          ShadTab(
                            value: 'judge',
                            content: const JudgeForm(),
                            width: tabWidth,
                            child: Text('anki.text_card.judge_tab'.tr),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
