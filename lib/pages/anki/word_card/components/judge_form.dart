import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/word_card.dart';

class JudgeForm extends GetView<WordCardFormController> {
  const JudgeForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () async {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
                onAddNew: (newDeckName) {
                  // Add to the deck list if not already present
                  if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                    ankiConnectController.parentDeckList.add(newDeckName);
                  }

                  // Set as selected deck
                  controller.parentDeck.value = newDeckName;
                },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              ShadSwitchCustom(
                key: ValueKey("subdeck-${controller.isCreateSubDeck.hashCode}"),
                label: 'anki.common.create_subdeck'.tr,
                initialValue: controller.isCreateSubDeck.value,
                onChanged: (v) {
                  controller.isCreateSubDeck.value = v;
                },
              ),
              if (controller.isCreateSubDeck.value)
                ShadSelectWithInput(
                  label: 'anki.word_card.subdeck_prefix'.tr,
                  placeholder: 'anki.word_card.select_subdeck_prefix'.tr,
                  searchPlaceholder: 'anki.word_card.input_subdeck_prefix'.tr,
                  isMultiple: false,
                  initialValue: [controller.subDeckPrefix.value],
                  options: controller.subDeckPrefixList,
                  onChanged: (value) {
                    logger.i(value);
                    controller.subDeckPrefix.value = value.single;
                  },
                  onAddNew: (newPrefix) {
                    // Add new sub deck prefix to the list if not already present
                    final newOption = {'value': newPrefix, 'label': newPrefix};
                    if (!controller.subDeckPrefixList.any((option) => option['value'] == newPrefix)) {
                      controller.subDeckPrefixList.add(newOption);
                    }

                    // Set as selected prefix
                    controller.subDeckPrefix.value = newPrefix;
                  },
                ),
              ShadRadioGroupCustom(
                label: 'anki.common.card_mode'.tr,
                initialValue: controller.judgeParams.mode.value,
                items: controller.qaModelList,
                onChanged: (value) {
                  controller.judgeParams.mode.value = value;
                },
              ),
              ShadSelectCustom(
                label: 'anki.word_card.card_template'.tr,
                placeholder: 'anki.word_card.select_card_template'.tr,
                initialValue: const ['Kevin Choice Card v2'],
                options: [
                  {
                    "value": "Kevin Choice Card v2",
                    "label": "Kevin Choice Card v2"
                  },
                ].toList(),
                onChanged: (value) {
                  controller.cardModel.value = value.single;
                  controller.updateFieldList(controller.cardModel.value);
                },
              ),
              ShadFieldMappingTable(
                key: ValueKey(
                    "field_table_${controller.cardModel.value}_${ankiConnectController.fieldList.length}_judge"),
                fieldList: ankiConnectController.fieldList,
                optionsList: {
                  "Question": controller.questionRegexList.toList(),
                  "Answers": controller.judgeAnswerList.toList(),
                  "Options": controller.optionRegexList.toList(),
                  "Remarks": controller.remarkRegexList.toList(),
                },
                defaultOptionsList: controller.commonRegexList,
                cardModel: controller.cardModel.value,
                onUpdateFieldMapping: (field, patternMatch) {
                  controller.updateFieldMapping(field, patternMatch);
                },
                getFieldMappingValue: (field) {
                  return controller.getFieldMappingValue(field);
                },
              ),
              ShadSelectWithInput(
                key:
                    ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.word_card.select_tags'.tr,
                searchPlaceholder: 'anki.word_card.input_tags'.tr,
                isMultiple: true,
                initialValue: controller.tags.toList(),
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the global tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tags.contains(newTag)) {
                    controller.tags.add(newTag);
                  }
                },
              ),
              if (controller.judgeParams.mode.value != 'cross_file')
                ShadInputWithFileSelect(
                  key: const ValueKey('same_file_input_judge'),
                  title: 'toolbox.common.inputFile'.tr,
                  placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                  allowedExtensions: const ['txt', 'md', 'docx'],
                  isRequired: true,
                  allowMultiple: true,
                  onFilesSelected: (files) {
                    controller.selectedFilePaths.value = files;
                  },
                  onValidate: (value, files) async {
                    return await validateFile(value, files);
                  },
                  onValidateError: (error) {},
                ),
              if (controller.judgeParams.mode.value == 'cross_file') ...[
                ShadInputWithFileSelect(
                  key: const ValueKey('qFile_judge'),
                  title: 'anki.common.q_file'.tr,
                  placeholder: Text('anki.placeholder.select_q_file'.tr),
                  allowedExtensions: const ['txt', 'md', 'docx'],
                  onFilesSelected: (files) {
                    controller.qFile.value = files.single;
                  },
                  onValidate: (value, files) async {
                    return await validateFile(value, files);
                  },
                  onValidateError: (error) {},
                ),
                ShadInputWithFileSelect(
                  key: const ValueKey('aFile_judge'),
                  title: 'anki.common.a_file'.tr,
                  placeholder: Text('anki.word_card.select_answer_file_placeholder'.tr),
                  allowedExtensions: const ['txt', 'md', 'docx'],
                  onFilesSelected: (files) {
                    controller.aFile.value = files.single;
                  },
                  onValidate: (value, files) async {
                    return await validateFile(value, files);
                  },
                  onValidateError: (error) {},
                ),
              ],
            ],
          )),
    );
  }
}
