import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/excel_card.dart';

class QAForm extends GetView<ExcelCardPageController> {
  const QAForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadInputWithFileSelect(
                key: ValueKey("excel_file-${controller.input.value}"),
                title: 'anki.excel_card.excel_file'.tr,
                allowedExtensions: const ["xlsx"],
                placeholder: Text('anki.excel_card.select_excel_file'.tr),
                initialValue: [controller.input.value],
                onFilesSelected: (value) {
                  controller.input.value = value.single;
                },
                onValidate: (value, files) async {
                  logger.i("value: $value, files: $files");
                  final result = await validateFile(value, files);
                  if (result.isEmpty) {
                    return await controller.validateInput(value);
                  }
                  return result;
                },
                onValidateError: (error) {
                  controller.inputError.value = error;
                },
              ),
              ShadSelectCustom(
                key: ValueKey("sheet-${controller.sheet.value}"),
                label: 'anki.excel_card.worksheet'.tr,
                placeholder: 'anki.excel_card.select_worksheet'.tr,
                options: controller.sheetList,
                isMultiple: false,
                initialValue: [controller.sheet.value],
                onChanged: (value) {
                  logger.i(value);
                  controller.updateSheetData(value.single);
                },
              ),
              ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
                onAddNew: (newDeckName) {
                  // Add to the deck list if not already present
                  if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                    ankiConnectController.parentDeckList.add(newDeckName);
                  }

                  // Set as selected deck
                  controller.parentDeck.value = newDeckName;
                },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              ShadSelectCustom(
                key: ValueKey("sub_deck_cols-${controller.subDeckCols.value}"),
                label: 'anki.excel_card.sub_deck_column'.tr,
                placeholder: 'anki.excel_card.select_sub_deck_column'.tr,
                options: controller.colList,
                isMultiple: true,
                initialValue: controller.subDeckCols,
                onChanged: (value) {
                  logger.i(value);
                  controller.subDeckCols.value = value;
                },
              ),
              ShadSelectCustom(
                key: ValueKey("tag_cols-${controller.tagCols.value}"),
                label: 'anki.excel_card.tag_column'.tr,
                placeholder: 'anki.excel_card.select_tag_column'.tr,
                options: controller.colList,
                isMultiple: true,
                initialValue: controller.tagCols,
                onChanged: (value) {
                  logger.i(value);
                  controller.tagCols.value = value;
                },
              ),
              ShadSelectCustom(
                key: ValueKey("guid_col-${controller.guidCol.value}"),
                label: 'anki.excel_card.id_column'.tr,
                placeholder: 'anki.excel_card.select_id_column_placeholder'.tr,
                options: controller.colList,
                isMultiple: false,
                allowDeselection: true,
                initialValue: [controller.guidCol.value],
                onChanged: (value) {
                  logger.i(value);
                  controller.guidCol.value = value.first;
                },
              ),
              ShadSelectWithSearch(
                key: ValueKey("card_model-${controller.cardModel.value}"),
                label: 'anki.excel_card.template'.tr,
                placeholder: 'anki.excel_card.select_template'.tr,
                searchPlaceholder: 'anki.excel_card.input_template'.tr,
                options: ankiConnectController.modelList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                isMultiple: false,
                initialValue: [controller.cardModel.value],
                onChanged: (value) {
                  logger.i(value);
                  controller.cardModel.value = value.single;
                  controller.updateFieldList(controller.cardModel.value);
                },
              ),
              if (ankiConnectController.fieldList.isNotEmpty) ...[
                ListTile(
                  contentPadding: EdgeInsets.zero,
                  minVerticalPadding: 0,
                  title: Text('anki.excel_card.field_mapping'.tr, style: defaultTitleStyle),
                  subtitle: Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxWidth: double.infinity,
                        maxHeight:
                            (1 + ankiConnectController.fieldList.length) * 48,
                      ),
                      child: ShadTable(
                        columnCount: 2,
                        rowCount: ankiConnectController.fieldList.length,
                        pinnedRowCount: 1,
                        header: (context, column) {
                          return ShadTableCell.header(
                            child: Text(
                              column == 0 ? 'anki.excel_card.template_field'.tr : 'anki.excel_card.table_column'.tr,
                            ),
                          );
                        },
                        columnSpanExtent: (index) {
                          return const FractionalTableSpanExtent(0.5);
                        },
                        rowSpanExtent: (index) =>
                            const FixedTableSpanExtent(48),
                        builder: (context, index) {
                          final field =
                              ankiConnectController.fieldList[index.row];
                          if (index.column == 0) {
                            return ShadTableCell(child: Text(field));
                          } else {
                            return ShadTableCell(
                              child: GetBuilder<ExcelCardPageController>(
                                  id: 'field_mappings',
                                  builder: (controller) {
                                    return SizedBox(
                                      width: double.infinity,
                                      child: ShadSelect(
                                        key: ValueKey('select_$field'),
                                        anchor: const ShadAnchorAuto(),
                                        placeholder: Text('anki.excel_card.select_column'.tr,
                                            style: ShadTheme.of(context)
                                                .inputTheme
                                                .placeholderStyle),
                                        allowDeselection: true,
                                        initialValue: controller
                                            .getFieldMappingValue(field)
                                            .firstOrNull,
                                        options: controller.colList
                                            .map((e) => ShadOption(
                                                value: e['value'],
                                                child: Text(e['label']!)))
                                            .toList(),
                                        selectedOptionBuilder:
                                            (context, value) {
                                          final selectedCol = controller.colList
                                              .firstWhere(
                                                  (e) => e['value'] == value,
                                                  orElse: () => {'label': ''});
                                          return Text(
                                              selectedCol['label'] ?? '');
                                        },
                                        onChanged: (value) {
                                          controller.updateFieldMapping(field,
                                              value != null ? [value] : []);
                                        },
                                      ),
                                    );
                                  }),
                            );
                          }
                        },
                      ),
                    ),
                  ),
                )
              ],
              ShadSelectWithInput(
                key:
                    ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.excel_card.select_tags'.tr,
                searchPlaceholder: 'anki.excel_card.input_tags'.tr,
                isMultiple: true,
                initialValue: controller.tags,
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the global tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tags.contains(newTag)) {
                    controller.tags.add(newTag);
                  }
                },
              ),
              const SizedBox(height: 8),
            ],
            // ],
          )),
    );
  }
}
