import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/deck_manager.dart';

class RemoveDeckForm extends GetView<DeckManagerController> {
  const RemoveDeckForm({super.key});

  @override
  Widget build(BuildContext context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadRadioGroupCustom(
                label: 'anki.deck_manager.remove_mode'.tr,
                initialValue: controller.removeMode.value,
                items: controller.removeModeList,
                onChanged: (value) {
                  controller.removeMode.value = value;
                },
              ),
              if (controller.removeMode.value == "manual") ...[
                ShadSelectWithSearch(
                  key: ValueKey(
                      "deck-${ankiConnectController.parentDeckList.value}"),
                  label: 'anki.deck_manager.remove_deck_label'.tr,
                  placeholder: 'anki.deck_manager.remove_deck_placeholder'.tr,
                  searchPlaceholder:
                      'anki.placeholder.target_deck_search_input'.tr,
                  isMultiple: true,
                  initialValue: controller.deckToRemove,
                  options: ankiConnectController.parentDeckList
                      .map((e) => {'value': e, 'label': e})
                      .toList(),
                  onChanged: (value) {
                    controller.deckToRemove.value = value;
                  },
                ),
              ],
              if (controller.removeMode.value == "empty") ...[
                ShadRadioGroupCustom(
                  label: 'anki.deck_manager.remove_range'.tr,
                  initialValue: controller.removeRange.value,
                  items: controller.removeRangeList,
                  onChanged: (value) {
                    controller.removeRange.value = value;
                  },
                ),
                if (controller.removeRange.value == "part") ...[
                  ShadSelectWithSearch(
                    key: ValueKey(
                        "deck-${ankiConnectController.parentDeckList.value}"),
                    label: 'anki.deck_manager.target_deck_label'.tr,
                    placeholder: 'anki.deck_manager.target_deck_select_placeholder'.tr,
                    searchPlaceholder: 'anki.deck_manager.target_deck_input_placeholder'.tr,
                    isMultiple: false,
                    initialValue: controller.deckToRemove.value,
                    options: ankiConnectController.parentDeckList
                        .map((e) => {'value': e, 'label': e})
                        .toList(),
                    onChanged: (value) {
                      controller.parentDeck.value = value.single;
                    },
                  ),
                ],
              ],
              const SizedBox(height: 4),
            ],
          )),
    );
  }
}
