import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/deck_manager.dart';

class ExportDeckForm extends GetView<DeckManagerController> {
  const ExportDeckForm({super.key});

  @override
  Widget build(BuildContext context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadSelectWithSearch(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.value}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
              ),
              ShadRadioGroupCustom(
                label: 'anki.deck_manager.export_format'.tr,
                initialValue: controller.exportFormat.value,
                items: controller.exportFormatList,
                onChanged: (value) {
                  controller.exportFormat.value = value;
                },
              ),
              if (controller.exportFormat.value == "html") ...[
                ShadRadioGroupCustom(
                  label: 'anki.deck_manager.export_mode'.tr,
                  initialValue: controller.exportMode.value,
                  items: controller.exportModeList,
                  onChanged: (value) {
                    controller.exportMode.value = value;
                  },
                ),
                ShadRadioGroupCustom(
                  label: 'anki.deck_manager.sort_method'.tr,
                  initialValue: controller.sortMethod.value,
                  items: controller.sortMethodList,
                  onChanged: (value) {
                    controller.sortMethod.value = value;
                  },
                ),
                ShadRadioGroupCustom(
                  label: 'anki.deck_manager.sort_direction'.tr,
                  initialValue: controller.sortDirection.value,
                  items: controller.sortDirectionList,
                  onChanged: (value) {
                    controller.sortDirection.value = value;
                  },
                ),
                ShadRadioGroupCustom(
                  label: 'anki.deck_manager.alignment'.tr,
                  initialValue: controller.align.value,
                  items: controller.alignList,
                  onChanged: (value) {
                    controller.align.value = value;
                  },
                ),
                ShadSwitchCustom(
                  label: 'anki.deck_manager.break_in_line'.tr,
                  initialValue: controller.breakInLine.value,
                  onChanged: (value) {
                    controller.breakInLine.value = value;
                  },
                ),
                ShadInputWithValidate(
                    key: const ValueKey("font-size"),
                    label: 'anki.deck_manager.custom_font_size'.tr,
                    placeholder: 'anki.deck_manager.font_size_placeholder'.tr,
                    initialValue: controller.fontSize.value.toString(),
                    onChanged: (value) {
                      controller.fontSize.value =
                          double.tryParse(value) ?? controller.fontSize.value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.deck_manager.font_size_required'.tr;
                      }
                      final reg = RegExp(r'^\d+(\.\d+)?$');
                      if (!reg.hasMatch(value)) {
                        return 'anki.deck_manager.font_size_invalid'.tr;
                      }
                      return "";
                    }),
                ShadInputWithValidate(
                    key: const ValueKey("cell-margin"),
                    label: 'anki.deck_manager.cell_margin'.tr,
                    placeholder: 'anki.deck_manager.cell_margin_placeholder'.tr,
                    initialValue: controller.cellMargin.value,
                    onChanged: (value) {
                      controller.cellMargin.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.deck_manager.cell_margin_required'.tr;
                      }
                      // 支持CSS样式的margin格式: 单值、两值、三值、四值
                      final reg =
                          RegExp(r'^(\d+(\.\d+)?(px|em|rem|%)?\s*){1,4}$');
                      if (!reg.hasMatch(value)) {
                        return 'anki.deck_manager.cell_margin_invalid'.tr;
                      }
                      return "";
                    }),
                ShadInputWithValidate(
                    key: const ValueKey("cell-padding"),
                    label: 'anki.deck_manager.cell_padding'.tr,
                    placeholder: 'anki.deck_manager.cell_padding_placeholder'.tr,
                    initialValue: controller.cellPadding.value,
                    onChanged: (value) {
                      controller.cellPadding.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.deck_manager.cell_padding_required'.tr;
                      }
                      // 支持CSS样式的padding格式: 单值、两值、三值、四值
                      final reg =
                          RegExp(r'^(\d+(\.\d+)?(px|em|rem|%)?\s*){1,4}$');
                      if (!reg.hasMatch(value)) {
                        return 'anki.deck_manager.cell_padding_invalid'.tr;
                      }
                      return "";
                    }),
                ShadInputWithValidate(
                    key: const ValueKey("cards-per-row"),
                    label: 'anki.deck_manager.cards_per_row'.tr,
                    placeholder: 'anki.deck_manager.cards_count_placeholder'.tr,
                    initialValue: controller.cardsPerRow.value.toString(),
                    onChanged: (value) {
                      controller.cardsPerRow.value =
                          int.tryParse(value) ?? controller.cardsPerRow.value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.deck_manager.cards_count_required'.tr;
                      }
                      final reg = RegExp(r'^\d+$');
                      if (!reg.hasMatch(value)) {
                        return 'anki.deck_manager.cards_count_invalid'.tr;
                      }
                      return "";
                    }),
                ShadInputWithValidate(
                    key: const ValueKey("cards-per-file"),
                    label: 'anki.deck_manager.cards_per_file'.tr,
                    placeholder: 'anki.deck_manager.cards_count_placeholder'.tr,
                    initialValue: controller.cardsPerFile.value.toString(),
                    onChanged: (value) {
                      controller.cardsPerFile.value =
                          int.tryParse(value) ?? controller.cardsPerFile.value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.deck_manager.cards_count_required'.tr;
                      }
                      final reg = RegExp(r'^\d+$');
                      if (!reg.hasMatch(value)) {
                        return 'anki.deck_manager.cards_count_invalid'.tr;
                      }
                      return "";
                    }),
              ]
              // else if (controller.exportFormat.value == "xlsx" ||
              //     controller.exportFormat.value == "csv" ||
              //     controller.exportFormat.value == "json") ...[
              //   ShadRadioGroupCustom(
              //     label: '排序方式',
              //     initialValue: controller.sortMethod.value,
              //     items: controller.sortMethodList,
              //     onChanged: (value) {
              //       controller.sortMethod.value = value;
              //     },
              //   ),
              //   ShadRadioGroupCustom(
              //     label: '排序方向',
              //     initialValue: controller.sortDirection.value,
              //     items: controller.sortDirectionList,
              //     onChanged: (value) {
              //       controller.sortDirection.value = value;
              //     },
              //   ),
              // ]
              else if (controller.exportFormat.value == "apkg") ...[
                ShadSwitchCustom(
                  label: 'anki.deck_manager.include_sched'.tr,
                  initialValue: controller.includeSched.value,
                  onChanged: (value) {
                    controller.includeSched.value = value;
                  },
                ),
              ],
              ShadInputWithFileSelect(
                key: ValueKey("output-dir-${controller.outputDir.value}"),
                title: 'anki.deck_manager.output_directory'.tr,
                placeholder: Text('anki.deck_manager.output_directory'.tr),
                initialValue: [controller.outputDir.value],
                isRequired: true,
                isFolder: true,
                onFilesSelected: (value) {
                  controller.outputDir.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
          )),
    );
  }
}
