import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/create_form.dart';
import 'components/import_form.dart';
import 'components/clone_form.dart';
import 'components/remove_form.dart';
import 'components/export_form.dart';
import 'package:anki_guru/controllers/anki/deck_manager.dart';
import 'package:anki_guru/pages/common.dart';

class DeckManagerPage extends StatefulWidget {
  const DeckManagerPage({super.key});

  @override
  State<DeckManagerPage> createState() => _DeckManagerPageState();
}

class _DeckManagerPageState extends State<DeckManagerPage> {
  final controller = Get.put(DeckManagerController());
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.deck_manager.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: Icon<PERSON><PERSON>on(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('anki.deck_manager.function_description'.tr, style: defaultPageTitleStyle),
                Text('anki.deck_manager.feature_description'.tr, style: theme.textTheme.muted),
                const SizedBox(height: 16),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 5;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        print(controller.tabController.selected);
                        controller.update();
                      },
                      tabs: [
                        ShadTab(
                          value: 'create',
                          content: const CreateDeckForm(),
                          width: tabWidth,
                          child: Text('anki.deck_manager.create_deck'.tr),
                        ),
                        ShadTab(
                          value: 'import',
                          content: const ImportDeckForm(),
                          width: tabWidth,
                          child: Text('anki.deck_manager.import_deck'.tr),
                        ),
                        ShadTab(
                          value: 'export',
                          content: const ExportDeckForm(),
                          width: tabWidth,
                          child: Text('anki.deck_manager.export_deck'.tr),
                        ),
                        ShadTab(
                          value: 'clone',
                          content: const CloneDeckForm(),
                          width: tabWidth,
                          child: Text('anki.deck_manager.clone_deck'.tr),
                        ),
                        ShadTab(
                          value: 'remove',
                          content: const RemoveDeckForm(),
                          width: tabWidth,
                          child: Text('anki.deck_manager.remove_deck'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
