import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/wereader_card.dart';

class CardForm extends GetView<WeReaderCardPageController> {
  const CardForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: 8,
        children: [
          Obx(() => ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.cardParams.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.cardParams.parentDeck.value = value.single;
                },
              onAddNew: (newDeckName) {
                // Add to the deck list if not already present
                if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                  ankiConnectController.parentDeckList.add(newDeckName);
                }

                // Set as selected deck
                controller.cardParams.parentDeck.value = newDeckName;
              },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              )),
          Obx(() => ShadSwitchCustom(
                key: ValueKey(
                    "subdeck-${controller.cardParams.isCreateSubDeck.hashCode}"),
                label: 'anki.common.create_subdeck'.tr,
                initialValue: controller.cardParams.isCreateSubDeck.value,
                onChanged: (v) {
                  controller.cardParams.isCreateSubDeck.value = v;
                },
              )),
          Obx(() => ShadInputWithValidate(
              key: ValueKey(controller.cookie.value),
              label: 'Cookie',
              placeholder: 'anki.wereader_card.cookie_placeholder'.tr,
              initialValue: controller.cookie.value,
              maxLines: 1,
              onChanged: (value) {
                // controller.cookie.value = value;
              },
              onValidate: (value) async {
                return await controller.validateCookie(value);
              })),
          Obx(() => ShadSelectWithSearch(
                label: 'anki.wereader_card.target_book'.tr,
                placeholder: 'anki.wereader_card.select_target_book'.tr,
                searchPlaceholder: 'anki.wereader_card.input_book'.tr,
                initialValue: controller.book.value.isNotEmpty
                    ? [controller.book.value]
                    : null,
                options: controller.bookList
                    .map((e) => {
                          'value': e['value'] as String,
                          'label': e['label'] as String
                        })
                    .toList(),
                onChanged: (value) {
                  controller.book.value = value.single;
                },
              )),
          Obx(() => ShadRadioGroupCustom(
                label: 'anki.wereader_card.note_source'.tr,
                initialValue: controller.cardParams.source.value,
                items: controller.sourceList,
                onChanged: (value) {
                  controller.cardParams.source.value = value;
                },
              )),
          Obx(() {
            if (controller.cardParams.source.value == "my") {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ShadCheckboxGroup(
                    label: 'anki.wereader_card.note_type'.tr,
                    initialValues: controller.cardParams.noteTypes.toList(),
                    items: controller.noteTypeList.toList(),
                    onChanged: (value) {
                      logger.i(value);
                      controller.cardParams.noteTypes.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.wereader_card.at_least_one_note_type'.tr;
                      }
                      return "";
                    },
                    onValidateError: (error) {},
                  ),
                  ShadSwitchCustom(
                    label: 'anki.wereader_card.limit_time_range'.tr,
                    initialValue: controller.isLimitRange.value,
                    onChanged: (v) {
                      controller.isLimitRange.value = v;
                    },
                  ),
                  if (controller.isLimitRange.value) ...[
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      minVerticalPadding: 0,
                      title: Text('anki.wereader_card.start_time'.tr, style: defaultTitleStyle),
                      subtitle: ShadDatePicker(
                        placeholder: Text('anki.wereader_card.select_start_time'.tr),
                        selected: controller.beginDate.value.isNotEmpty
                            ? DateTime.fromMillisecondsSinceEpoch(
                                int.parse(controller.beginDate.value))
                            : null,
                        onChanged: (v) {
                          controller.beginDate.value =
                              v?.millisecondsSinceEpoch.toString() ?? '';
                        },
                      ),
                    ),
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      minVerticalPadding: 0,
                      title: Text('anki.wereader_card.end_time'.tr, style: defaultTitleStyle),
                      subtitle: ShadDatePicker(
                        placeholder: Text('anki.wereader_card.select_end_time'.tr),
                        selected: controller.endDate.value.isNotEmpty
                            ? DateTime.fromMillisecondsSinceEpoch(
                                int.parse(controller.endDate.value))
                            : null,
                        onChanged: (v) {
                          controller.endDate.value =
                              v?.millisecondsSinceEpoch.toString() ?? '';
                        },
                      ),
                    ),
                  ],
                  ShadSwitchCustom(
                    label: 'anki.wereader_card.answer_cloze'.tr,
                    initialValue: controller.cardParams.isCloze.value,
                    onChanged: (v) {
                      controller.cardParams.isCloze.value = v;
                    },
                  ),
                  if (controller.cardParams.isCloze.value)
                    ShadSelectWithInput(
                      label: 'anki.wereader_card.cloze_grammar'.tr,
                      placeholder: 'anki.wereader_card.select_cloze_grammar'.tr,
                      searchPlaceholder: 'anki.wereader_card.input_cloze_grammar'.tr,
                      isMultiple: false,
                      initialValue: [controller.cardParams.clozeType.value],
                      options: controller.clozeGrammarList.toList(),
                      onChanged: (value) {
                        logger.i(value);
                        controller.cardParams.clozeType.value = value.single;
                      },
                      onAddNew: (newGrammar) {
                        // Add new cloze grammar to the list if not already present
                        final newOption = {'value': newGrammar, 'label': newGrammar};
                        if (!controller.clozeGrammarList.any((option) => option['value'] == newGrammar)) {
                          controller.clozeGrammarList.add(newOption);
                        }

                        // Set as selected grammar
                        controller.cardParams.clozeType.value = newGrammar;
                      },
                    ),
                  ShadSwitchCustom(
                    label: 'anki.wereader_card.enable_separator'.tr,
                    initialValue: controller.cardParams.isUseSep.value,
                    onChanged: (v) {
                      controller.cardParams.isUseSep.value = v;
                    },
                  ),
                  if (controller.cardParams.isUseSep.value)
                    ShadSelectWithInput(
                      label: 'anki.wereader_card.separator'.tr,
                      placeholder: 'anki.wereader_card.select_separator'.tr,
                      searchPlaceholder: 'anki.wereader_card.input_separator'.tr,
                      isMultiple: false,
                      initialValue: [controller.sepList.first['value']!],
                      options: controller.sepList.toList(),
                      onChanged: (value) {
                        logger.i(value);
                        controller.cardParams.sep.value = value.single;
                      },
                      onAddNew: (newSeparator) {
                        // Add new separator to the list if not already present
                        final newOption = {'value': newSeparator, 'label': newSeparator};
                        if (!controller.sepList.any((option) => option['value'] == newSeparator)) {
                          controller.sepList.add(newOption);
                        }

                        // Set as selected separator
                        controller.cardParams.sep.value = newSeparator;
                      },
                    ),
                ],
              );
            } else {
              return const SizedBox.shrink();
            }
          }),
          Obx(() => ShadSelectWithInput(
                key:
                    ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.wereader_card.select_tags'.tr,
                searchPlaceholder: 'anki.wereader_card.input_tags'.tr,
                isMultiple: true,
                initialValue: controller.cardParams.tags,
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.cardParams.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the global tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.cardParams.tags.contains(newTag)) {
                    controller.cardParams.tags.add(newTag);
                  }
                },
              )),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
