import 'package:anki_guru/pages/anki/image_card/components/qa_form.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ImageCardPage extends StatelessWidget {
  const ImageCardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('anki.image_card.title'.tr),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Get.toNamed('/image_card/config');
            },
          )
        ],
      ),
      body: const QAForm(),
    );
  }
}
