import 'dart:convert';
import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:ulid/ulid.dart';
import 'package:anki_guru/controllers/common.dart';

// --- Performance Optimization: Isolate for image decoding ---
Future<Size> _decodeImageSize(Uint8List imageBytes) async {
  try {
    final image = img.decodeImage(imageBytes);
    if (image != null) {
      return Size(image.width.toDouble(), image.height.toDouble());
    }
  } catch (e) {
    logger.e('Error decoding image in isolate: $e');
  }
  return Size.zero;
}

// LifecycleEventHandler remains the same
class LifecycleEventHandler extends WidgetsBindingObserver {
  final Function() onOrientationChange;

  LifecycleEventHandler({required this.onOrientationChange});

  @override
  void didChangeMetrics() {
    onOrientationChange();
  }
}

class ImageCloze extends StatefulWidget {
  final Function(List<Rect> annotations)? onAnnotationsChanged;

  const ImageCloze({
    super.key,
    this.onAnnotationsChanged,
  });

  @override
  State<ImageCloze> createState() => _ImageClozeState();
}

class _ImageClozeState extends State<ImageCloze> {
  // --- STATE VARIABLES ---
  List<Rect> annotations = [];
  List<List<double>> _relativeAnnotations = [];
  List<bool> _annotationsOpacity = [];
  List<int?> _maskGroups = []; // Track group assignments for each mask
  List<Rect> redoHistory = [];
  int? _selectedRectIndex;

  // Group mode state
  bool _isGroupMode = false;
  final Set<int> _groupModeSelectedIndices =
      {}; // Indices of masks selected in group mode
  int?
      _groupModeInitiatingMaskIndex; // The mask that initiated group mode (cannot be deselected)
  bool _isGroupModeDragging =
      false; // Track if user is dragging to select masks
  final Set<int> _groupModeDragProcessedMasks =
      {}; // Track masks already processed in current drag
  Offset? _panStartPoint;
  Rect? _currentDrawingRect;
  bool _isMoving = false;
  bool _isResizing = false;
  Offset? _lastPanPosition;
  double _scale = 1.0;
  Offset _offset = Offset.zero;
  bool _isScalingOrPanningImage = false;
  double _baseScale = 1.0;
  Offset _gestureStartOffset = Offset.zero;
  Offset _gestureStartFocalPoint = Offset.zero;
  int _pointerCount = 0;
  double _maxScale = 10.0;
  final FocusNode _focusNode = FocusNode();
  final GlobalKey _screenshotKey = GlobalKey();
  Rect? _imageDisplayRect;
  Size? _cachedImageSize;
  bool _isSaving = false;
  DateTime _lastKeyEventTime = DateTime.now();

  // --- LIFECYCLE & INITIALIZATION ---

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
      // Note: LayoutBuilder will handle initial image rect calculation with correct constraints
    });

    WidgetsBinding.instance.addObserver(
      LifecycleEventHandler(onOrientationChange: _onOrientationChange),
    );

    // Ensure focus is maintained when the widget is built
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        // Re-request focus if lost, but only if the widget is still mounted and visible
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && ModalRoute.of(context)?.isCurrent == true) {
            _focusNode.requestFocus();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    // 正确地移除监听器，需要传递同一个实例，但在这里简单移除即可
    // 为了简化，这里创建一个新的空实例来移除，虽然不是最佳实践，但能工作
    // 最佳实践是在initState中创建并持有LifecycleEventHandler实例
    WidgetsBinding.instance.removeObserver(
      LifecycleEventHandler(onOrientationChange: () {}),
    );
    _focusNode.dispose();
    super.dispose();
  }

  void _onOrientationChange() {
    if (mounted) {
      setState(() {
        _resetTransform();
        // Note: We'll let the LayoutBuilder trigger recalculation with correct constraints
        _imageDisplayRect = null;
        _cachedImageSize = null;
      });
    }
  }

  // --- ATOMIC STATE MODIFICATION HELPERS ---

  void _addAnnotation(Rect newRect) {
    if (_imageDisplayRect == null) return;
    final relativeCoords = _rectToRelative(newRect);

    if (relativeCoords[2] <= 0 || relativeCoords[3] <= 0) {
      logger.w("Attempted to add a zero-sized annotation. Ignoring.");
      return;
    }

    setState(() {
      annotations.add(newRect);
      _relativeAnnotations.add(relativeCoords);
      _annotationsOpacity.add(
          true); // Changed: Set initial state to opaque (true) instead of transparent (false)
      _maskGroups.add(null); // Initialize with no group assignment
      redoHistory.clear();
      _selectedRectIndex = annotations.length - 1;
    });

    widget.onAnnotationsChanged?.call(annotations);
    _checkStateConsistency();
  }

  void _removeAnnotationAtIndex(int index) {
    if (index < 0 || index >= annotations.length) return;

    setState(() {
      redoHistory.clear();
      annotations.removeAt(index);
      _relativeAnnotations.removeAt(index);
      _annotationsOpacity.removeAt(index);
      _maskGroups.removeAt(index);
      _selectedRectIndex = null;
    });

    widget.onAnnotationsChanged?.call(annotations);
    _checkStateConsistency();
  }

  void _updateAnnotationAtIndex(int index, Rect updatedRect) {
    if (index < 0 || index >= annotations.length || _imageDisplayRect == null) {
      return;
    }
    final newRelativeCoords = _rectToRelative(updatedRect);

    setState(() {
      annotations[index] = updatedRect;
      _relativeAnnotations[index] = newRelativeCoords;
    });
    _checkStateConsistency();
  }

  void _duplicateAnnotation(int index) {
    if (index < 0 || index >= annotations.length || _imageDisplayRect == null)
      return;

    final originalRect = annotations[index];
    const double offset = 15.0; // Offset for the duplicate

    // Create a new rect with a slight offset
    final newRect = Rect.fromLTWH(
      originalRect.left + offset,
      originalRect.top + offset,
      originalRect.width,
      originalRect.height,
    );

    // Ensure the new rect stays within the image bounds
    final clampedRect = Rect.fromLTWH(
      newRect.left.clamp(
          _imageDisplayRect!.left, _imageDisplayRect!.right - newRect.width),
      newRect.top.clamp(
          _imageDisplayRect!.top, _imageDisplayRect!.bottom - newRect.height),
      newRect.width,
      newRect.height,
    );

    _addAnnotation(clampedRect);
  }

  void _clearAllAnnotations() {
    setState(() {
      annotations.clear();
      _relativeAnnotations.clear();
      _annotationsOpacity.clear();
      _maskGroups.clear();
      redoHistory.clear();
      _selectedRectIndex = null;
    });
  }

  /// Show confirmation dialog for deleting all masks
  void _showDeleteAllMasksConfirmation() async {
    if (annotations.isEmpty) {
      // Show feedback that there are no masks to delete
      if (mounted) {
        showToastNotification(
            context, 'anki.image_card.no_masks_to_delete'.tr, '',
            type: "info");
      }
      return;
    }

    if (!mounted) return;

    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('anki.image_card.delete_all_masks_title'.tr),
          content: Text('anki.image_card.delete_all_masks_confirmation'.tr),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('anki.image_card.cancel'.tr),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: Text('anki.image_card.delete'.tr),
            ),
          ],
        );
      },
    );

    if (confirmed == true && mounted) {
      final int deletedCount = annotations.length;
      _clearAllAnnotations();
      widget.onAnnotationsChanged?.call(annotations);

      // Show success feedback
      showToastNotification(
          context,
          'anki.image_card.masks_deleted_success'.tr,
          'anki.image_card.masks_deleted_count'
              .trParams({'count': deletedCount.toString()}),
          type: "success");
    }
  }

  void _undoLast() {
    if (annotations.isEmpty) return;
    setState(() {
      redoHistory.add(annotations.last);
      annotations.removeLast();
      _relativeAnnotations.removeLast();
      _annotationsOpacity.removeLast();
      _maskGroups.removeLast();
      _selectedRectIndex = null;
    });
    widget.onAnnotationsChanged?.call(annotations);
    _checkStateConsistency();
  }

  void _redoLast() {
    if (redoHistory.isEmpty) return;
    final rectToRestore = redoHistory.removeLast();
    _addAnnotation(rectToRestore);
    _checkStateConsistency();
  }

  void _toggleOpacity(int index) {
    if (index < 0 || index >= _annotationsOpacity.length) return;
    setState(() {
      _annotationsOpacity[index] = !_annotationsOpacity[index];
    });
  }

  // --- COORDINATE, LAYOUT & TRANSFORM CALCULATION ---

  void _resetTransform() {
    setState(() {
      _scale = 1.0;
      _offset = Offset.zero;
      _isScalingOrPanningImage = false;
      _baseScale = 1.0;
      _gestureStartOffset = Offset.zero;
      _gestureStartFocalPoint = Offset.zero;
    });
  }

  Matrix4 _getCurrentTransform() {
    final transform = Matrix4.identity()
      ..translate(_offset.dx, _offset.dy)
      ..scale(_scale, _scale);

    // Debug logging for transform state (can be removed in production)
    // if (kDebugMode && (_offset != Offset.zero || _scale != 1.0)) {
    //   logger.d('Transform applied: offset=(${_offset.dx.toStringAsFixed(1)}, ${_offset.dy.toStringAsFixed(1)}), scale=${_scale.toStringAsFixed(2)}');
    // }

    return transform;
  }

  Offset _transformToLocalPoint(Offset globalPosition) {
    final Matrix4 transform = _getCurrentTransform();
    final Matrix4 inverseMatrix = Matrix4.inverted(transform);
    return MatrixUtils.transformPoint(inverseMatrix, globalPosition);
  }

  List<double> _rectToRelative(Rect rect) {
    final displayRect = _imageDisplayRect!;
    if (displayRect.width == 0 || displayRect.height == 0) {
      return [0.0, 0.0, 0.0, 0.0];
    }

    final relativeX = (rect.left - displayRect.left) / displayRect.width;
    final relativeY = (rect.top - displayRect.top) / displayRect.height;
    final relativeWidth = rect.width / displayRect.width;
    final relativeHeight = rect.height / displayRect.height;

    const int precision = 12;

    return [
      double.parse(relativeX.toStringAsFixed(precision)),
      double.parse(relativeY.toStringAsFixed(precision)),
      double.parse(relativeWidth.toStringAsFixed(precision)),
      double.parse(relativeHeight.toStringAsFixed(precision)),
    ];
  }

  Rect _relativeToRect(List<double> coords) {
    final displayRect = _imageDisplayRect!;
    return Rect.fromLTWH(
      displayRect.left + coords[0] * displayRect.width,
      displayRect.top + coords[1] * displayRect.height,
      coords[2] * displayRect.width,
      coords[3] * displayRect.height,
    );
  }

  void _recalculateImageRectAndLoadAnnotations([Size? availableSize]) async {
    await _recalculateImageRectAndAnnotations(availableSize);
    _loadExistingAnnotations();
  }

  Future<void> _recalculateImageRectAndAnnotations(
      [Size? availableSize]) async {
    final controller = Get.find<ImageCardController>();
    if (controller.byteArray.value == null || !context.mounted) return;

    final imageBytes = controller.byteArray.value!;
    final imageSize = await _getImageSize(imageBytes);
    if (imageSize == Size.zero || !mounted) return;

    Size containerSize;
    if (availableSize != null) {
      // Use the provided size from LayoutBuilder constraints (body area only)
      containerSize = availableSize;
    } else {
      // Fallback to render box constraints (may include AppBar)
      final renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox == null) return;
      final constraints = renderBox.constraints;
      containerSize = Size(constraints.maxWidth, constraints.maxHeight);
    }

    final newImageDisplayRect =
        _calculateImageDisplayRect(containerSize, imageSize);

    double requiredScale = 1.0;
    if (newImageDisplayRect.width > 0) {
      requiredScale = containerSize.width / newImageDisplayRect.width;
    }

    final newMaxScale = max(10.0, requiredScale);

    setState(() {
      _imageDisplayRect = newImageDisplayRect;
      _maxScale = newMaxScale;
      annotations = _relativeAnnotations.map(_relativeToRect).toList();
    });
  }

  Future<Size> _getImageSize(Uint8List imageBytes) async {
    if (_cachedImageSize != null) return _cachedImageSize!;

    final size = await compute(_decodeImageSize, imageBytes);

    if (mounted) {
      _cachedImageSize = size;
    }
    return size;
  }

  Rect _calculateImageDisplayRect(Size containerSize, Size imageSize) {
    // Handle edge cases
    if (imageSize.width <= 0 ||
        imageSize.height <= 0 ||
        containerSize.width <= 0 ||
        containerSize.height <= 0) {
      return Rect.zero;
    }

    final imageAspectRatio = imageSize.width / imageSize.height;
    final containerAspectRatio = containerSize.width / containerSize.height;

    Size displaySize;

    // Calculate display size to fit image within container while maintaining aspect ratio
    if (containerAspectRatio > imageAspectRatio) {
      // Container is wider than image - fit to height, center horizontally
      displaySize =
          Size(imageAspectRatio * containerSize.height, containerSize.height);
    } else {
      // Container is taller than image - fit to width, center vertically
      displaySize =
          Size(containerSize.width, containerSize.width / imageAspectRatio);
    }

    // Calculate centering offsets
    final xOffset = (containerSize.width - displaySize.width) / 2;
    final yOffset = (containerSize.height - displaySize.height) / 2;

    // Ensure offsets are not negative (shouldn't happen with correct logic, but safety check)
    final safeXOffset = xOffset.clamp(0.0, containerSize.width);
    final safeYOffset = yOffset.clamp(0.0, containerSize.height);

    final result = Rect.fromLTWH(
        safeXOffset, safeYOffset, displaySize.width, displaySize.height);

    // Debug logging to verify centering calculation (can be removed in production)
    if (kDebugMode) {
      logger.d(
          'Image positioning: container=${containerSize.width}x${containerSize.height}, '
          'image=${imageSize.width}x${imageSize.height}, '
          'display=${displaySize.width}x${displaySize.height}, '
          'offset=(${safeXOffset.toStringAsFixed(1)}, ${safeYOffset.toStringAsFixed(1)})');
    }

    return result;
  }

  // --- DATA PERSISTENCE (LOADING & SAVING) ---

  void _loadExistingAnnotations() {
    if (_imageDisplayRect == null) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) _loadExistingAnnotations();
      });
      return;
    }

    final controller = Get.find<ImageCardController>();
    final index = controller.currentPage.value;
    if (index >= controller.imageCards.length) return;

    final clozeText = controller.imageCards[index].clozeData;

    if (clozeText.isEmpty) {
      _clearAllAnnotations();
      return;
    }

    try {
      final groupText = controller.imageCards[index].groupData;

      if (groupText.isNotEmpty) {
        final editorStateData = jsonDecode(groupText);

        // Load from structured editor state data
        if (editorStateData is Map && editorStateData.containsKey('version')) {
          final masks = editorStateData['masks'] as List?;
          final opacity = editorStateData['opacity'] as List?;
          final groups = editorStateData['groups'] as List?;

          if (masks != null && opacity != null && groups != null) {
            final List<List<double>> newRelativeAnnotations = [];
            final List<bool> newAnnotationsOpacity = [];
            final List<int?> newMaskGroups = [];

            for (int i = 0; i < masks.length; i++) {
              if (i < opacity.length && i < groups.length) {
                final maskCoords = masks[i];
                if (maskCoords is List && maskCoords.length == 4) {
                  newRelativeAnnotations.add(List<double>.from(
                      maskCoords.map((e) => (e as num).toDouble())));
                  newAnnotationsOpacity.add(opacity[i] as bool? ?? true);

                  final groupValue = groups[i];
                  newMaskGroups.add(groupValue is int ? groupValue : null);
                }
              }
            }

            setState(() {
              _relativeAnnotations = newRelativeAnnotations;
              _annotationsOpacity = newAnnotationsOpacity;
              _maskGroups = newMaskGroups;
              annotations = _relativeAnnotations.map(_relativeToRect).toList();
            });
            _checkStateConsistency();

            if (kDebugMode) {
              logger.d(
                  'Loaded editor state: ${_maskGroups.length} masks with groups: $_maskGroups');
            }
          } else {
            _clearAllAnnotations();
          }
        } else {
          _clearAllAnnotations();
        }
      } else {
        _clearAllAnnotations();
      }
    } catch (e, stackTrace) {
      logger.e('Failed to load or parse annotations: $e\n$stackTrace');
      _clearAllAnnotations();
    }
  }

  Future<void> _saveCurrentState() async {
    final controller = Get.find<ImageCardController>();
    final index = controller.currentPage.value;
    if (index >= controller.imageCards.length) return;

    final List<List<List<double>>> coordinatesForAnki =
        _generateGroupedCoordinates();

    final jsonData = jsonEncode(coordinatesForAnki);
    controller.imageCards[index].clozeData = jsonData;

    // Save editor state data (individual masks with their group assignments)
    final editorStateData = {
      'masks': _relativeAnnotations,
      'opacity': _annotationsOpacity,
      'groups': _maskGroups,
      'version': 1, // For future compatibility
    };
    final groupData = jsonEncode(editorStateData);
    controller.imageCards[index].groupData = groupData;

    if (kDebugMode) {
      logger.d(
          'Saved editor state: ${_maskGroups.length} masks with groups: $_maskGroups');
    }

    await _captureScreenshot(controller, index);
  }

  // --- NAVIGATION ---

  // [修复] 使用 try...finally 块确保 _isSaving 状态总是被重置
  Future<void> _navigate({required bool toNext}) async {
    if (_isSaving) return;

    // Exit group mode if active
    if (_isGroupMode) {
      _exitGroupMode(applyChanges: false);
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final controller = Get.find<ImageCardController>();
      final currentIndex = controller.currentPage.value;
      final total = controller.imageCards.length;
      final newIndex = toNext ? currentIndex + 1 : currentIndex - 1;

      if (newIndex < 0 || newIndex >= total) {
        return; // 到达边界，直接返回，finally 块会重置 _isSaving
      }

      await _saveCurrentState();

      _resetTransform();
      controller.currentPage.value = newIndex;
      controller.editorTitle.value = "${newIndex + 1} / $total";
      controller.byteArray.value = controller.imageCards[newIndex].imageData;

      setState(() {
        _cachedImageSize = null;
        _imageDisplayRect = null;
      });

      _clearAllAnnotations();

      // 使用 addPostFrameCallback 确保在下一帧重绘后加载数据
      // Note: LayoutBuilder will trigger recalculation with correct constraints
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // Just trigger a rebuild, LayoutBuilder will handle the recalculation
          setState(() {});
        }
      });

      // 添加一个微小的延迟以允许UI更新
      await Future.delayed(const Duration(milliseconds: 50));
    } catch (e, s) {
      logger.e('Navigation failed: $e\n$s');
      // 可以在这里显示一个错误提示
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  // [修复] 使用 try...finally 块确保 _isSaving 状态总是被重置
  Future<void> _saveAndGoBack() async {
    if (_isSaving) return;

    // Exit group mode if active
    if (_isGroupMode) {
      _exitGroupMode(applyChanges: false);
    }

    setState(() {
      _isSaving = true;
    });

    try {
      await _saveCurrentState();
      if (mounted) Get.back();
    } catch (e, s) {
      logger.e('Save and go back failed: $e\n$s');
      // 可以在这里显示一个错误提示
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  // --- GESTURE HANDLING ---

  void _onScaleStart(ScaleStartDetails details) {
    if (kDebugMode) {
      logger
          .d('_onScaleStart called with position: ${details.localFocalPoint}');
    }

    _pointerCount = details.pointerCount;
    if (_pointerCount > 1) {
      if (kDebugMode)
        logger.d('Multi-pointer gesture detected, skipping action icon checks');
      if (_panStartPoint != null || _isMoving || _isResizing) return;
      setState(() {
        _isScalingOrPanningImage = true;
        _baseScale = _scale;
        _gestureStartOffset = _offset;
        _gestureStartFocalPoint = details.focalPoint;
      });
      return;
    }

    if (_imageDisplayRect == null) {
      if (kDebugMode) logger.d('_imageDisplayRect is null, returning early');
      return;
    }

    final position = _transformToLocalPoint(details.localFocalPoint);
    if (kDebugMode) {
      logger.d('Transformed position: $position');
      logger.d('Image display rect: $_imageDisplayRect');
    }

    if (!_imageDisplayRect!.contains(position)) {
      if (kDebugMode)
        logger.d('Position outside image display rect, returning early');
      _panStartPoint = null;
      return;
    }

    // In group mode, only allow mask selection operations
    if (_isGroupMode) {
      _handleGroupModeScaleStart(position);
      return;
    }

    setState(() {
      if (_selectedRectIndex != null) {
        if (kDebugMode) logger.d('Selected mask index: $_selectedRectIndex');
        final rect = annotations[_selectedRectIndex!];
        final iconSize = _calculateDynamicIconSize(rect);

        if (kDebugMode) {
          logger.d('Selected mask rect: $rect');
          logger.d('Icon size: $iconSize');
          logger.d('Current scale: $_scale');
        }

        // Position icons exactly at mask corners
        final deleteIconCenter = rect.topLeft;
        final copyIconCenter = rect.topRight;
        final resizeIconCenter = rect.bottomRight;
        final groupIconCenter = rect.bottomLeft;

        // Check for action icon hits
        final deleteIconRect = Rect.fromCenter(
            center: deleteIconCenter, width: iconSize, height: iconSize);
        final copyIconRect = Rect.fromCenter(
            center: copyIconCenter, width: iconSize, height: iconSize);
        final resizeIconRect = Rect.fromCenter(
            center: resizeIconCenter, width: iconSize, height: iconSize);
        final groupIconRect = Rect.fromCenter(
            center: groupIconCenter, width: iconSize, height: iconSize);

        if (kDebugMode) {
          logger.d('Action icon centers:');
          logger.d('  Delete: $deleteIconCenter');
          logger.d('  Copy: $copyIconCenter');
          logger.d('  Resize: $resizeIconCenter');
          logger.d('Action icon rects:');
          logger.d('  Delete rect: $deleteIconRect');
          logger.d('  Copy rect: $copyIconRect');
          logger.d('  Resize rect: $resizeIconRect');
          logger.d('Checking position $position against icon rects...');
        }

        if (deleteIconRect.contains(position)) {
          if (kDebugMode)
            logger.d('_onScaleStart: ✓ Delete icon HIT! Removing annotation.');
          _removeAnnotationAtIndex(_selectedRectIndex!);
          return;
        } else {
          if (kDebugMode) logger.d('_onScaleStart: ✗ Delete icon miss');
        }

        if (copyIconRect.contains(position)) {
          if (kDebugMode)
            logger.d('_onScaleStart: ✓ Copy icon HIT! Duplicating annotation.');
          _duplicateAnnotation(_selectedRectIndex!);
          return;
        } else {
          if (kDebugMode) logger.d('_onScaleStart: ✗ Copy icon miss');
        }

        if (resizeIconRect.contains(position)) {
          if (kDebugMode)
            logger.d('_onScaleStart: ✓ Resize icon HIT! Starting resize mode.');
          _isResizing = true;
          _lastPanPosition = position;
          return;
        } else {
          if (kDebugMode) logger.d('_onScaleStart: ✗ Resize icon miss');
        }

        if (groupIconRect.contains(position)) {
          if (kDebugMode)
            logger.d('_onScaleStart: ✓ Group icon HIT! Entering group mode.');
          _enterGroupMode(_selectedRectIndex!);
          return;
        } else {
          if (kDebugMode) logger.d('_onScaleStart: ✗ Group icon miss');
        }

        if (kDebugMode)
          logger
              .d('No action icon hit, proceeding with normal gesture handling');
      } else {
        if (kDebugMode)
          logger.d('No mask selected, skipping action icon checks');
      }

      final hitIndex = _getRectIndexAtPoint(position, inflate: false);

      if (hitIndex != null) {
        _isMoving = true;
        _selectedRectIndex = hitIndex;
        _lastPanPosition = position;
        return;
      }

      _isMoving = false;
      _isResizing = false;
      _selectedRectIndex = null;
      _panStartPoint = position;
      _currentDrawingRect = Rect.fromPoints(position, position);
    });
  }

  void _onScaleUpdate(ScaleUpdateDetails details) {
    // In group mode, handle drag selection
    if (_isGroupMode) {
      _handleGroupModeDragSelection(details);
      return;
    }

    if (_pointerCount > 1) {
      if (!_isScalingOrPanningImage ||
          !context.mounted ||
          _imageDisplayRect == null) return;
      if (!details.scale.isFinite || details.scale <= 0.0) return;

      setState(() {
        final newScale = (_baseScale * details.scale).clamp(1.0, _maxScale);

        final RenderBox renderBox = context.findRenderObject() as RenderBox;
        final containerSize = renderBox.size;
        final imageRect = _imageDisplayRect!;

        final focalPointInImage =
            (_gestureStartFocalPoint - _gestureStartOffset) / _baseScale;
        final newOffset = _gestureStartOffset +
            (details.focalPoint - _gestureStartFocalPoint) -
            (focalPointInImage * newScale - focalPointInImage * _baseScale);

        final scaledImageTopLeft = newOffset + imageRect.topLeft * newScale;

        double dx = scaledImageTopLeft.dx;
        double dy = scaledImageTopLeft.dy;

        final scaledWidth = imageRect.width * newScale;
        final scaledHeight = imageRect.height * newScale;
        const double epsilon = 0.001;

        if (scaledWidth < containerSize.width - epsilon) {
          dx = (containerSize.width - scaledWidth) / 2;
        } else {
          dx = dx.clamp(containerSize.width - scaledWidth, 0.0);
        }

        if (scaledHeight < containerSize.height - epsilon) {
          dy = (containerSize.height - scaledHeight) / 2;
        } else {
          dy = dy.clamp(containerSize.height - scaledHeight, 0.0);
        }

        _scale = newScale;
        _offset = Offset(dx, dy) - imageRect.topLeft * newScale;
      });
      return;
    }

    if (_imageDisplayRect == null ||
        (_isMoving == false && _isResizing == false && _panStartPoint == null))
      return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final containerSize = renderBox.size;
    final viewportRect =
        Rect.fromLTWH(0, 0, containerSize.width, containerSize.height);
    final inverseTransform = Matrix4.inverted(_getCurrentTransform());
    final visibleCanvasRect =
        MatrixUtils.transformRect(inverseTransform, viewportRect);
    final drawableArea = _imageDisplayRect!.intersect(visibleCanvasRect);

    final position = _transformToLocalPoint(details.localFocalPoint);
    final clampedPosition = Offset(
      position.dx.clamp(drawableArea.left, drawableArea.right),
      position.dy.clamp(drawableArea.top, drawableArea.bottom),
    );

    if (_isResizing && _selectedRectIndex != null) {
      final oldRect = annotations[_selectedRectIndex!];
      const minDisplayPixels = 2.0;
      const minWidth = minDisplayPixels;
      const minHeight = minDisplayPixels;
      final newRect = Rect.fromLTRB(
        oldRect.left,
        oldRect.top,
        clampedPosition.dx > oldRect.left + minWidth
            ? clampedPosition.dx
            : oldRect.left + minWidth,
        clampedPosition.dy > oldRect.top + minHeight
            ? clampedPosition.dy
            : oldRect.top + minHeight,
      );
      _updateAnnotationAtIndex(_selectedRectIndex!, newRect);
      _lastPanPosition = clampedPosition;
      return;
    }

    if (_isMoving && _selectedRectIndex != null && _lastPanPosition != null) {
      final delta = clampedPosition - _lastPanPosition!;
      Rect newRect =
          annotations[_selectedRectIndex!].translate(delta.dx, delta.dy);

      if (newRect.left < drawableArea.left)
        newRect = newRect.translate(drawableArea.left - newRect.left, 0);
      if (newRect.top < drawableArea.top)
        newRect = newRect.translate(0, drawableArea.top - newRect.top);
      if (newRect.right > drawableArea.right)
        newRect = newRect.translate(drawableArea.right - newRect.right, 0);
      if (newRect.bottom > drawableArea.bottom)
        newRect = newRect.translate(0, drawableArea.bottom - newRect.bottom);

      _updateAnnotationAtIndex(_selectedRectIndex!, newRect);
      _lastPanPosition = clampedPosition;
      return;
    }

    if (_panStartPoint != null) {
      setState(() {
        _currentDrawingRect = Rect.fromPoints(_panStartPoint!, clampedPosition);
      });
    }
  }

  void _onScaleEnd(ScaleEndDetails details) {
    // In group mode, end drag selection and reset state flags
    if (_isGroupMode) {
      setState(() {
        _isGroupModeDragging = false;
        _panStartPoint = null;
        _groupModeDragProcessedMasks
            .clear(); // Clear processed masks for next drag operation

        // Reset state flags to ensure tap selection works after drag operations
        _isResizing = false;
        _isMoving = false;
        _lastPanPosition = null;
        _isScalingOrPanningImage = false;
        _pointerCount = 0;
      });
      return;
    }

    if (_isResizing || _isMoving) {
      widget.onAnnotationsChanged?.call(annotations);
    } else if (_currentDrawingRect != null) {
      if (_isValidMaskSize(_currentDrawingRect!)) {
        _addAnnotation(_currentDrawingRect!);
      }
    }
    setState(() {
      _panStartPoint = null;
      _currentDrawingRect = null;
      _isResizing = false;
      _isMoving = false;
      _lastPanPosition = null;
      _isScalingOrPanningImage = false;
      _pointerCount = 0;
    });
  }

  // --- HELPERS & UTILITIES ---

  bool _isValidMaskSize(Rect rect) {
    if (_imageDisplayRect == null || _cachedImageSize == null) {
      return false;
    }
    final relativeCoords = _rectToRelative(rect);
    final relativeWidth = relativeCoords[2];
    final relativeHeight = relativeCoords[3];

    if (relativeWidth <= 0 || relativeHeight <= 0) {
      return false;
    }

    // Scale-aware minimum display size - smaller at higher zoom levels
    final scaleAdjustedMinPixels = 8.0 / _scale;
    final hasMinDisplaySize = rect.width >= scaleAdjustedMinPixels &&
        rect.height >= scaleAdjustedMinPixels;

    // More lenient relative area thresholds
    final imageSize = _cachedImageSize!;
    final imageAspectRatio = imageSize.width / imageSize.height;
    double minRelativeArea =
        0.0001; // Reduced from 0.001 to allow smaller masks

    // Even more lenient for extreme aspect ratios
    if (imageAspectRatio < 1 / 3 || imageAspectRatio > 3) {
      minRelativeArea = 0.00005; // Further reduced for extreme aspect ratios
    }

    final relativeArea = relativeWidth * relativeHeight;

    // Additional check: ensure mask is at least 1% of either dimension
    const minRelativeDimension = 0.01; // 1% of image dimension
    final hasMinRelativeDimension = relativeWidth >= minRelativeDimension ||
        relativeHeight >= minRelativeDimension;

    return relativeArea >= minRelativeArea &&
        hasMinDisplaySize &&
        hasMinRelativeDimension;
  }

  int? _getRectIndexAtPoint(Offset point, {bool inflate = true}) {
    for (int i = annotations.length - 1; i >= 0; i--) {
      Rect hitRect = annotations[i];
      if (inflate) {
        final inflationAmount = 4.0 / _scale;
        hitRect = hitRect.inflate(inflationAmount);
      }
      if (hitRect.contains(point)) {
        return i;
      }
    }
    return null;
  }

  void _handleKeyEvent(KeyEvent event) {
    // Debug logging to track keyboard events
    if (kDebugMode) {
      logger.d('_handleKeyEvent: ${event.runtimeType} - ${event.logicalKey}');
    }

    if (_isSaving ||
        _isMoving ||
        _isResizing ||
        _panStartPoint != null ||
        _isScalingOrPanningImage) {
      if (kDebugMode) {
        logger.d(
            '_handleKeyEvent: Blocked due to state - saving:$_isSaving, moving:$_isMoving, resizing:$_isResizing');
      }
      return;
    }

    if (event is! KeyDownEvent) {
      if (kDebugMode) {
        logger.d('_handleKeyEvent: Not a KeyDownEvent, ignoring');
      }
      return;
    }

    final now = DateTime.now();
    if (now.difference(_lastKeyEventTime).inMilliseconds < 200) {
      if (kDebugMode) {
        logger.d('_handleKeyEvent: Too soon since last event, ignoring');
      }
      return;
    }
    _lastKeyEventTime = now;

    if (kDebugMode) {
      logger.d('_handleKeyEvent: Processing key event - ${event.logicalKey}');
    }

    if (event.logicalKey == LogicalKeyboardKey.escape) {
      if (_isGroupMode) {
        _exitGroupMode(applyChanges: false);
      } else {
        _saveAndGoBack();
      }
    } else if (event.logicalKey == LogicalKeyboardKey.enter) {
      if (_isGroupMode) {
        // Enter key confirms grouping in group mode
        _exitGroupMode(applyChanges: true);
      }
      return;
    } else if (_isGroupMode) {
      // In group mode, disable other keyboard shortcuts
      if (kDebugMode) {
        logger.d('_handleKeyEvent: In group mode, ignoring other shortcuts');
      }
      return;
    } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
      if (kDebugMode) {
        logger.d('_handleKeyEvent: Right arrow pressed, navigating to next');
      }
      _navigate(toNext: true);
    } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
      if (kDebugMode) {
        logger.d('_handleKeyEvent: Left arrow pressed, navigating to previous');
      }
      _navigate(toNext: false);
    } else if (event.logicalKey == LogicalKeyboardKey.delete ||
        event.logicalKey == LogicalKeyboardKey.backspace) {
      if (_selectedRectIndex != null) {
        _removeAnnotationAtIndex(_selectedRectIndex!);
      }
    } else if (event.logicalKey == LogicalKeyboardKey.keyZ &&
        HardwareKeyboard.instance.isControlPressed) {
      // Ctrl+Z for undo
      if (annotations.isNotEmpty) {
        _undoLast();
      }
    } else if ((event.logicalKey == LogicalKeyboardKey.keyY &&
            HardwareKeyboard.instance.isControlPressed) ||
        (event.logicalKey == LogicalKeyboardKey.keyZ &&
            HardwareKeyboard.instance.isControlPressed &&
            HardwareKeyboard.instance.isShiftPressed)) {
      // Ctrl+Y or Ctrl+Shift+Z for redo
      if (redoHistory.isNotEmpty) {
        _redoLast();
      }
    }
  }

  Future<void> _captureScreenshot(
      ImageCardController controller, int index) async {
    try {
      RenderRepaintBoundary? boundary = _screenshotKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary != null) {
        final image = await boundary.toImage(
            pixelRatio: MediaQuery.of(context).devicePixelRatio);
        final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
        if (byteData != null) {
          controller.imageCards[index].coverData =
              byteData.buffer.asUint8List();
          controller.imageCards.refresh();
        }
      }
    } catch (e) {
      logger.e("Screenshot failed: $e");
    }
  }

  void _checkStateConsistency() {
    assert(
        annotations.length == _relativeAnnotations.length &&
            annotations.length == _annotationsOpacity.length &&
            annotations.length == _maskGroups.length,
        "CRITICAL: STATE INCONSISTENCY DETECTED!");
  }

  double _calculateDynamicIconSize(Rect maskRect) {
    final maskWidth = maskRect.width;
    final maskHeight = maskRect.height;
    final minMaskDimension = min(maskWidth, maskHeight);

    // Base icon size scaled by zoom, but constrained by mask size
    // Increased from 24.0 to 36.0 for better usability
    final baseIconSize = 36.0 / _scale;
    final maxIconSizeForMask = minMaskDimension *
        0.28; // Reduced from 35% to 28% of smallest mask dimension

    // Improved minimum size: icon radius should be at least 20% of mask's shortest dimension
    // This ensures better clickability for small masks
    final minIconSizeFromMask =
        minMaskDimension * 0.5; // Increased from 40% to 50% diameter
    final absoluteMinIconSize =
        16.0 / _scale; // Increased from 8.0 to 16.0 for better touch targets
    final minIconSize = max(absoluteMinIconSize, minIconSizeFromMask);

    return max(minIconSize, min(baseIconSize, maxIconSizeForMask));
  }

  void _handleGroupModeScaleStart(Offset position) {
    if (kDebugMode) logger.d('Group mode scale start at position: $position');

    // Store the start position but don't start dragging yet
    // We'll start dragging only when we detect actual movement in _handleGroupModeDragSelection
    setState(() {
      _panStartPoint = position;
      _isGroupModeDragging = false; // Don't start dragging immediately
      _groupModeDragProcessedMasks
          .clear(); // Reset processed masks for new drag operation

      // Ensure state flags are clean for proper tap detection
      _isMoving = false;
      _isResizing = false;
    });

    // Check if we hit a mask to toggle its selection (for tap selection)
    final hitIndex = _getRectIndexAtPoint(position, inflate: false);
    if (hitIndex != null) {
      setState(() {
        if (_groupModeSelectedIndices.contains(hitIndex)) {
          // Protect the initiating mask from deselection
          if (hitIndex != _groupModeInitiatingMaskIndex) {
            _groupModeSelectedIndices.remove(hitIndex);
            if (kDebugMode)
              logger.d('Group mode: Removed mask $hitIndex from selection');
          } else {
            if (kDebugMode)
              logger.d('Group mode: Cannot deselect initiating mask $hitIndex');
          }
        } else {
          _groupModeSelectedIndices.add(hitIndex);
          if (kDebugMode)
            logger.d('Group mode: Added mask $hitIndex to selection');
        }
      });
    }
  }

  void _handleGroupModeDragSelection(ScaleUpdateDetails details) {
    if (_panStartPoint == null) return;

    final currentPosition = _transformToLocalPoint(details.localFocalPoint);
    if (_imageDisplayRect == null ||
        !_imageDisplayRect!.contains(currentPosition)) return;

    // Detect if we've moved enough to start dragging (threshold to distinguish tap from drag)
    final dragDistance = (_panStartPoint! - currentPosition).distance;
    const dragThreshold = 10.0; // pixels

    if (!_isGroupModeDragging && dragDistance > dragThreshold) {
      // Start dragging mode
      setState(() {
        _isGroupModeDragging = true;
      });
      if (kDebugMode) logger.d('Group mode: Started drag selection');
    }

    // Only perform drag selection if we're actually in dragging mode
    if (!_isGroupModeDragging) return;

    // Find all masks that intersect with the drag path
    final dragRect = Rect.fromPoints(_panStartPoint!, currentPosition);

    // Track which masks are currently intersecting with the drag area
    final Set<int> currentlyIntersectingMasks = {};
    for (int i = 0; i < annotations.length; i++) {
      final maskRect = annotations[i];
      if (dragRect.overlaps(maskRect) || maskRect.overlaps(dragRect)) {
        currentlyIntersectingMasks.add(i);
      }
    }

    // Find masks that are newly intersecting (not processed yet in this drag)
    final Set<int> newlyIntersectingMasks =
        currentlyIntersectingMasks.difference(_groupModeDragProcessedMasks);

    // Find masks that are no longer intersecting (were processed but now outside drag area)
    final Set<int> noLongerIntersectingMasks =
        _groupModeDragProcessedMasks.difference(currentlyIntersectingMasks);

    if (newlyIntersectingMasks.isNotEmpty ||
        noLongerIntersectingMasks.isNotEmpty) {
      setState(() {
        // Process newly intersecting masks (toggle their selection)
        for (final i in newlyIntersectingMasks) {
          if (_groupModeSelectedIndices.contains(i)) {
            // Protect the initiating mask from deselection during drag
            if (i != _groupModeInitiatingMaskIndex) {
              _groupModeSelectedIndices.remove(i);
              if (kDebugMode)
                logger.d('Group mode drag: Removed mask $i from selection');
            } else {
              if (kDebugMode)
                logger.d('Group mode drag: Cannot deselect initiating mask $i');
            }
          } else {
            // Add unselected mask to selection
            _groupModeSelectedIndices.add(i);
            if (kDebugMode)
              logger.d('Group mode drag: Added mask $i to selection');
          }
        }

        // Update the processed masks set to reflect current state
        _groupModeDragProcessedMasks.addAll(newlyIntersectingMasks);
        _groupModeDragProcessedMasks.removeAll(noLongerIntersectingMasks);
      });
    }
  }

  List<List<List<double>>> _generateGroupedCoordinates() {
    // Create a map to group masks by their group numbers
    final Map<int?, List<List<double>>> groupMap = {};

    for (int i = 0; i < _relativeAnnotations.length; i++) {
      final groupNumber = _maskGroups[i];
      final coordinates = _relativeAnnotations[i];

      if (!groupMap.containsKey(groupNumber)) {
        groupMap[groupNumber] = [];
      }
      groupMap[groupNumber]!.add(coordinates);
    }

    // Convert the grouped map to the required nested list format
    final List<List<List<double>>> result = [];

    // Sort groups by group number (null groups come first, then by ascending number)
    final sortedGroups = groupMap.keys.toList()
      ..sort((a, b) {
        if (a == null && b == null) return 0;
        if (a == null) return -1;
        if (b == null) return 1;
        return a.compareTo(b);
      });

    for (final groupNumber in sortedGroups) {
      final coordinates = groupMap[groupNumber]!;
      result.add(coordinates);
    }

    return result;
  }

  void _enterGroupMode(int maskIndex) {
    if (maskIndex < 0 || maskIndex >= _maskGroups.length) return;

    final currentGroupId = _maskGroups[maskIndex];

    setState(() {
      _isGroupMode = true;
      _groupModeSelectedIndices.clear();
      _groupModeInitiatingMaskIndex = maskIndex; // Track the initiating mask

      // Select all masks that belong to the same group
      if (currentGroupId != null) {
        for (int i = 0; i < _maskGroups.length; i++) {
          if (_maskGroups[i] == currentGroupId) {
            _groupModeSelectedIndices.add(i);
          }
        }
      } else {
        // If no group, only select the current mask
        _groupModeSelectedIndices.add(maskIndex);
      }

      // Clear normal selection since we're in group mode
      _selectedRectIndex = null;
    });
  }

  void _exitGroupMode({bool applyChanges = false}) {
    if (!_isGroupMode) return;

    setState(() {
      if (applyChanges && _groupModeSelectedIndices.isNotEmpty) {
        // Generate new ULID-based group ID (using hash code for integer compatibility)
        final newGroupId = Ulid().toString().hashCode;

        // Apply the new group ID to all selected masks
        for (final index in _groupModeSelectedIndices) {
          if (index < _maskGroups.length) {
            _maskGroups[index] = newGroupId;
          }
        }
      }

      // Reset group mode state
      _isGroupMode = false;
      _groupModeSelectedIndices.clear();
      _groupModeInitiatingMaskIndex = null;
      _isGroupModeDragging = false;
      _groupModeDragProcessedMasks.clear();

      // Reset normal editing state to ensure mask selection works after group mode
      _selectedRectIndex = null;
      _panStartPoint = null;
      _currentDrawingRect = null;
      _isResizing = false;
      _isMoving = false;
    });
  }

  // --- BUILD METHOD ---
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ImageCardController>();

    // Ensure focus is requested after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_focusNode.hasFocus) {
        _focusNode.requestFocus();
        if (kDebugMode) {
          logger.d('Requesting focus for keyboard listener');
        }
      }
    });

    return Focus(
      focusNode: _focusNode,
      autofocus: true,
      onKeyEvent: (node, event) {
        if (kDebugMode) {
          logger.d(
              'Focus onKeyEvent: ${event.runtimeType} - ${event.logicalKey} - hasFocus: ${node.hasFocus}');
        }
        _handleKeyEvent(event);
        // Only handle arrow keys and other navigation keys, let other keys pass through
        if (event.logicalKey == LogicalKeyboardKey.arrowLeft ||
            event.logicalKey == LogicalKeyboardKey.arrowRight ||
            event.logicalKey == LogicalKeyboardKey.escape ||
            event.logicalKey == LogicalKeyboardKey.enter ||
            event.logicalKey == LogicalKeyboardKey.delete ||
            event.logicalKey == LogicalKeyboardKey.backspace ||
            (event.logicalKey == LogicalKeyboardKey.keyZ && HardwareKeyboard.instance.isControlPressed) ||
            (event.logicalKey == LogicalKeyboardKey.keyY && HardwareKeyboard.instance.isControlPressed)) {
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          automaticallyImplyLeading: !_isGroupMode,
          leading: _isGroupMode
              ? null
              : IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: _isSaving ? null : _saveAndGoBack),
          title: _isGroupMode
              ? Text('anki.image_card.group_mode_title'.tr)
              : Obx(() => Text(controller.editorTitle.value)),
          centerTitle: true,
          actions: _isGroupMode
              ? [
                  // Group mode actions
                  IconButton(
                    icon: const Icon(Icons.close),
                    tooltip: 'Cancel Group Mode',
                    onPressed: () => _exitGroupMode(applyChanges: false),
                  ),
                  IconButton(
                    icon: const Icon(Icons.check),
                    tooltip: 'Complete Group Mode',
                    onPressed: () => _exitGroupMode(applyChanges: true),
                  ),
                ]
              : [
                  // Normal mode actions
                  IconButton(
                    icon: const Icon(LucideIcons.chevronLeft),
                    tooltip: 'anki.image_card.previous_image'.tr,
                    onPressed:
                        _isSaving ? null : () => _navigate(toNext: false),
                  ),
                  IconButton(
                    icon: const Icon(LucideIcons.chevronRight),
                    tooltip: 'anki.image_card.next_image'.tr,
                    onPressed: _isSaving ? null : () => _navigate(toNext: true),
                  ),
                  IconButton(
                      icon: const Icon(Icons.delete),
                      tooltip: 'anki.image_card.delete_all_masks'.tr,
                      onPressed:
                          _isSaving ? null : _showDeleteAllMasksConfirmation),
                  IconButton(
                      icon: const Icon(Icons.undo),
                      tooltip: 'anki.image_card.undo'.tr,
                      onPressed:
                          _isSaving || annotations.isEmpty ? null : _undoLast),
                  IconButton(
                      icon: const Icon(Icons.redo),
                      tooltip: 'anki.image_card.redo'.tr,
                      onPressed:
                          _isSaving || redoHistory.isEmpty ? null : _redoLast),
                  IconButton(
                      icon: const Icon(Icons.check),
                      tooltip: 'anki.image_card.complete_and_return'.tr,
                      onPressed: _isSaving ? null : _saveAndGoBack),
                ],
        ),
        body: LayoutBuilder(
          builder: (context, constraints) {
            if (controller.byteArray.value == null) {
              return const Center(
                child: Text("No Image Data",
                    style: TextStyle(color: Colors.white)),
              );
            }

            // Ensure image display rect is calculated with correct body constraints
            final availableSize =
                Size(constraints.maxWidth, constraints.maxHeight);

            // Check if we need to recalculate due to size changes or initial load
            bool needsRecalculation =
                _imageDisplayRect == null || _cachedImageSize == null;

            // Also check if the available size has changed significantly
            if (_imageDisplayRect != null && _cachedImageSize != null) {
              final currentContainerSize = Size(
                  _imageDisplayRect!.width + (_imageDisplayRect!.left * 2),
                  _imageDisplayRect!.height + (_imageDisplayRect!.top * 2));
              final sizeDifference =
                  (availableSize.width - currentContainerSize.width).abs() +
                      (availableSize.height - currentContainerSize.height)
                          .abs();
              if (sizeDifference > 1.0) {
                // Threshold for size change
                needsRecalculation = true;
              }
            }

            if (needsRecalculation) {
              // Trigger recalculation with correct body size
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  _recalculateImageRectAndLoadAnnotations(availableSize);
                }
              });
            }

            // 在保存切换时显示加载指示器
            if (_imageDisplayRect == null ||
                _cachedImageSize == null ||
                _isSaving) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            final dpr = MediaQuery.of(context).devicePixelRatio;
            final imageSize = _cachedImageSize!;
            final imageAspectRatio = imageSize.width / imageSize.height;
            final cacheWidth = (constraints.maxWidth * dpr).round();
            final cacheHeight = (cacheWidth / imageAspectRatio).round();

            return Center(
              child: GestureDetector(
                onScaleStart: _onScaleStart,
                onScaleUpdate: _onScaleUpdate,
                onScaleEnd: _onScaleEnd,
                onTapUp: (details) {
                  // Ensure focus is maintained when user taps on the image
                  if (!_focusNode.hasFocus) {
                    _focusNode.requestFocus();
                    if (kDebugMode) {
                      logger.d('onTapUp: Requesting focus after tap');
                    }
                  }

                  if (kDebugMode) {
                    logger.d(
                        'onTapUp called with position: ${details.localPosition}');
                  }

                  if (_pointerCount != 0 ||
                      _isMoving ||
                      _isResizing ||
                      _isSaving) {
                    if (kDebugMode) {
                      logger.d('onTapUp: Early return due to state conditions');
                    }
                    return;
                  }

                  final position =
                      _transformToLocalPoint(details.localPosition);
                  final hitIndex = _getRectIndexAtPoint(position);

                  if (kDebugMode) {
                    logger.d('onTapUp: Transformed position: $position');
                    logger.d('onTapUp: Hit index: $hitIndex');
                    logger.d('onTapUp: Selected index: $_selectedRectIndex');
                  }

                  // Check if tap is on an action icon first (only for selected mask)
                  if (hitIndex != null && _selectedRectIndex == hitIndex) {
                    final rect = annotations[hitIndex];
                    final iconSize = _calculateDynamicIconSize(rect);

                    // Position icons exactly at mask corners
                    final deleteIconCenter = rect.topLeft;
                    final copyIconCenter = rect.topRight;
                    final resizeIconCenter = rect.bottomRight;
                    final groupIconCenter = rect.bottomLeft;

                    // Check for action icon hits
                    final deleteIconRect = Rect.fromCenter(
                        center: deleteIconCenter,
                        width: iconSize,
                        height: iconSize);
                    final copyIconRect = Rect.fromCenter(
                        center: copyIconCenter,
                        width: iconSize,
                        height: iconSize);
                    final resizeIconRect = Rect.fromCenter(
                        center: resizeIconCenter,
                        width: iconSize,
                        height: iconSize);
                    final groupIconRect = Rect.fromCenter(
                        center: groupIconCenter,
                        width: iconSize,
                        height: iconSize);

                    if (kDebugMode) {
                      logger.d(
                          'onTapUp: Checking action icons for selected mask');
                      logger.d('onTapUp: Delete rect: $deleteIconRect');
                      logger.d('onTapUp: Copy rect: $copyIconRect');
                      logger.d('onTapUp: Resize rect: $resizeIconRect');
                    }

                    if (deleteIconRect.contains(position)) {
                      if (kDebugMode)
                        logger.d(
                            'onTapUp: ✓ Delete icon HIT! Removing annotation.');
                      setState(() {
                        _removeAnnotationAtIndex(_selectedRectIndex!);
                      });
                      return;
                    }

                    if (copyIconRect.contains(position)) {
                      if (kDebugMode)
                        logger.d(
                            'onTapUp: ✓ Copy icon HIT! Duplicating annotation.');
                      setState(() {
                        _duplicateAnnotation(_selectedRectIndex!);
                      });
                      return;
                    }

                    if (resizeIconRect.contains(position)) {
                      if (kDebugMode)
                        logger.d(
                            'onTapUp: ✓ Resize icon HIT! But this should be handled by drag gestures.');
                      // Resize is handled by drag gestures, not taps
                      return;
                    }

                    if (groupIconRect.contains(position)) {
                      if (kDebugMode)
                        logger.d(
                            'onTapUp: ✓ Group icon HIT! Entering group mode.');
                      _enterGroupMode(_selectedRectIndex!);
                      return;
                    }

                    if (kDebugMode)
                      logger.d(
                          'onTapUp: No action icon hit, proceeding with normal tap handling');
                  }

                  setState(() {
                    if (_isGroupMode) {
                      // Group mode: toggle mask selection
                      if (hitIndex != null) {
                        if (_groupModeSelectedIndices.contains(hitIndex)) {
                          // Protect the initiating mask from deselection
                          if (hitIndex != _groupModeInitiatingMaskIndex) {
                            _groupModeSelectedIndices.remove(hitIndex);
                            if (kDebugMode)
                              logger.d(
                                  'onTapUp: Removed mask $hitIndex from group selection');
                          } else {
                            if (kDebugMode)
                              logger.d(
                                  'onTapUp: Cannot deselect initiating mask $hitIndex');
                          }
                        } else {
                          _groupModeSelectedIndices.add(hitIndex);
                          if (kDebugMode)
                            logger.d(
                                'onTapUp: Added mask $hitIndex to group selection');
                        }
                      }
                    } else {
                      // Normal mode: standard selection behavior
                      if (hitIndex != null) {
                        if (_selectedRectIndex == hitIndex) {
                          // Only toggle opacity if not clicking on action icons
                          if (kDebugMode)
                            logger.d(
                                'onTapUp: Toggling opacity for mask $hitIndex');
                          _toggleOpacity(hitIndex);
                        }
                        _selectedRectIndex = hitIndex;
                        if (kDebugMode)
                          logger.d('onTapUp: Selected mask $hitIndex');
                      } else {
                        if (kDebugMode) logger.d('onTapUp: Deselecting mask');
                        _selectedRectIndex = null;
                      }
                    }
                  });
                },
                child: Container(
                  color: Colors.black,
                  width: constraints.maxWidth,
                  height: constraints.maxHeight,
                  child: ClipRect(
                    child: Transform(
                      transform: _getCurrentTransform(),
                      child: Stack(
                        children: [
                          Positioned.fromRect(
                            rect: _imageDisplayRect!,
                            child: RepaintBoundary(
                              key: _screenshotKey,
                              child: Stack(
                                children: [
                                  SizedBox(
                                    width: _imageDisplayRect!.width,
                                    height: _imageDisplayRect!.height,
                                    child: Image.memory(
                                      controller.byteArray.value!,
                                      fit: BoxFit
                                          .fill, // Use BoxFit.fill within the properly sized container
                                      gaplessPlayback: true,
                                      cacheWidth: cacheWidth,
                                      cacheHeight: cacheHeight,
                                    ),
                                  ),
                                  CustomPaint(
                                    size: _imageDisplayRect!.size,
                                    painter: ImageAreaPainter(
                                      annotations: annotations
                                          .map((rect) => rect.translate(
                                              -_imageDisplayRect!.left,
                                              -_imageDisplayRect!.top))
                                          .toList(),
                                      opacityStates: _annotationsOpacity,
                                      primaryColor: controller.primaryColor
                                          .value, // Added: Pass primary color from controller
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          CustomPaint(
                            size: Size(
                                constraints.maxWidth, constraints.maxHeight),
                            painter: AnnotationPainter(
                              annotations: annotations,
                              currentRect: _currentDrawingRect,
                              selectedIndex: _selectedRectIndex,
                              scale: _scale,
                              primaryColor: controller.primaryColor.value,
                              isGroupMode: _isGroupMode,
                              groupModeSelectedIndices:
                                  _groupModeSelectedIndices,
                              groupModeInitiatingMaskIndex:
                                  _groupModeInitiatingMaskIndex,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class ImageAreaPainter extends CustomPainter {
  final List<Rect> annotations;
  final List<bool> opacityStates;
  final Color primaryColor; // Added: Primary color from controller

  ImageAreaPainter({
    required this.annotations,
    required this.opacityStates,
    required this.primaryColor, // Added: Primary color parameter
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (int i = 0; i < annotations.length; i++) {
      final rect = annotations[i];
      final isOpaque = i < opacityStates.length ? opacityStates[i] : false;

      final Paint fillPaint = Paint()
        ..color = isOpaque
            ? primaryColor
            : primaryColor.withAlpha(
                128) // Changed: Use primaryColor instead of Colors.red
        ..style = PaintingStyle.fill;

      canvas.drawRect(rect, fillPaint);
    }
  }

  @override
  bool shouldRepaint(covariant ImageAreaPainter oldDelegate) => true;
}

class AnnotationPainter extends CustomPainter {
  final List<Rect> annotations;
  final Rect? currentRect;
  final int? selectedIndex;
  final double scale;
  final Color primaryColor; // Added: Primary color from controller
  final bool isGroupMode;
  final Set<int> groupModeSelectedIndices;
  final int? groupModeInitiatingMaskIndex;

  AnnotationPainter({
    required this.annotations,
    this.currentRect,
    this.selectedIndex,
    required this.scale,
    required this.primaryColor, // Added: Primary color parameter
    this.isGroupMode = false,
    this.groupModeSelectedIndices = const {},
    this.groupModeInitiatingMaskIndex,
  });

  /// Calculates a contrasting border color based on the primary color
  /// Uses luminance-based contrast calculation to ensure accessibility
  Color _calculateContrastingBorderColor(Color primaryColor) {
    try {
      // Calculate relative luminance of the primary color
      final double luminance = _calculateRelativeLuminance(primaryColor);

      // Use WCAG contrast ratio guidelines
      // If primary color is light (luminance > 0.5), use dark border
      // If primary color is dark (luminance <= 0.5), use light border
      if (luminance > 0.5) {
        // Primary color is light, use dark border
        // For very light colors, use black for maximum contrast
        if (luminance > 0.8) {
          return Colors.black;
        } else {
          return Colors.grey.shade800;
        }
      } else {
        // Primary color is dark, use light border
        // For very dark colors, use white for maximum contrast
        if (luminance < 0.2) {
          return Colors.white;
        } else {
          return Colors.grey.shade300;
        }
      }
    } catch (e) {
      // Fallback to safe default if calculation fails
      return Colors.grey.shade800;
    }
  }

  /// Calculates the relative luminance of a color according to WCAG guidelines
  /// Returns a value between 0 (darkest) and 1 (lightest)
  double _calculateRelativeLuminance(Color color) {
    // Convert RGB values to linear RGB
    final double r = _linearizeColorComponent(color.r / 255.0);
    final double g = _linearizeColorComponent(color.g / 255.0);
    final double b = _linearizeColorComponent(color.b / 255.0);

    // Calculate relative luminance using WCAG formula
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearizes a color component for luminance calculation
  double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    final borderPaint = Paint()
      ..color = _calculateContrastingBorderColor(primaryColor)
      ..style = PaintingStyle.stroke;

    final iconBackgroundPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final iconBorderPaint = Paint()
      ..color = Colors.grey.shade400
      ..style = PaintingStyle.stroke
      ..strokeWidth =
          1.0 / scale; // Increased from 1.0 to 1.5 for better visibility

    final double strokeWidth = 1.0 / scale;

    for (int i = 0; i < annotations.length; i++) {
      final rect = annotations[i];

      // Determine border style based on mode and selection state
      bool shouldDrawBorder = false;

      if (isGroupMode) {
        // In group mode, use different styling for selected/unselected masks
        if (groupModeSelectedIndices.contains(i)) {
          shouldDrawBorder = true;
          if (i == groupModeInitiatingMaskIndex) {
            // Initiating mask - special styling (orange border, thicker stroke)
            borderPaint
              ..color = Colors.orange
              ..strokeWidth = strokeWidth * 3;
          } else {
            // Selected in group mode - bright border with thicker stroke
            borderPaint
              ..color = Colors.yellow
              ..strokeWidth = strokeWidth * 2;
          }
        } else {
          // Unselected in group mode - dimmed border
          shouldDrawBorder = true;
          borderPaint
            ..color = _calculateContrastingBorderColor(primaryColor)
                .withValues(alpha: 0.3)
            ..strokeWidth = strokeWidth;
        }
      } else {
        // Normal mode - only show border for selected mask
        if (i == selectedIndex) {
          shouldDrawBorder = true;
          borderPaint
            ..color = _calculateContrastingBorderColor(primaryColor)
            ..strokeWidth = strokeWidth;
        }
      }

      // Only draw border if it should be visible
      if (shouldDrawBorder) {
        canvas.drawRect(rect, borderPaint);
      }

      // Only show action icons in normal mode for selected mask
      if (!isGroupMode && i == selectedIndex) {
        // Calculate dynamic icon size based on mask dimensions
        final maskWidth = rect.width;
        final maskHeight = rect.height;
        final minMaskDimension = min(maskWidth, maskHeight);

        // Base icon size scaled by zoom, but constrained by mask size
        // Increased from 24.0 to 36.0 for better usability
        final baseIconSize = 36.0 / scale;
        final maxIconSizeForMask = minMaskDimension *
            0.28; // Reduced from 35% to 28% of smallest mask dimension
        final minIconSize =
            16.0 / scale; // Increased from 8.0 to 16.0 for better touch targets

        final dynamicIconSize =
            max(minIconSize, min(baseIconSize, maxIconSizeForMask));
        final dynamicIconRadius = dynamicIconSize / 2;
        final dynamicInnerIconSize =
            dynamicIconSize * 0.5; // 50% of icon size for inner content

        // Draw action icons exactly at mask corners
        final deleteIconCenter = rect.topLeft;
        final copyIconCenter = rect.topRight;
        final resizeIconCenter = rect.bottomRight;
        final groupIconCenter = rect.bottomLeft;

        _drawActionIcon(
            canvas,
            deleteIconCenter,
            dynamicIconRadius,
            iconBackgroundPaint,
            iconBorderPaint,
            dynamicInnerIconSize,
            'delete');
        _drawActionIcon(canvas, copyIconCenter, dynamicIconRadius,
            iconBackgroundPaint, iconBorderPaint, dynamicInnerIconSize, 'copy');
        _drawActionIcon(
            canvas,
            resizeIconCenter,
            dynamicIconRadius,
            iconBackgroundPaint,
            iconBorderPaint,
            dynamicInnerIconSize,
            'resize');
        _drawActionIcon(
            canvas,
            groupIconCenter,
            dynamicIconRadius,
            iconBackgroundPaint,
            iconBorderPaint,
            dynamicInnerIconSize,
            'group');
      }
    }

    if (currentRect != null) {
      final fillPaint = Paint()
        ..color = primaryColor
            .withAlpha(128); // Changed: Use primaryColor instead of Colors.red
      canvas.drawRect(currentRect!, fillPaint);
      borderPaint.strokeWidth = strokeWidth;
      canvas.drawRect(currentRect!, borderPaint);
    }
  }

  void _drawActionIcon(
      Canvas canvas,
      Offset center,
      double radius,
      Paint backgroundPaint,
      Paint borderPaint,
      double iconSize,
      String iconType) {
    // Draw background circle
    canvas.drawCircle(center, radius, backgroundPaint);
    canvas.drawCircle(center, radius, borderPaint);

    // Draw Flutter icon using TextPainter
    final IconData iconData = _getIconForType(iconType);
    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(iconData.codePoint),
        style: TextStyle(
          fontSize:
              iconSize * 1.0, // Increased from 0.6 to 0.8 for better visibility
          fontFamily: iconData.fontFamily,
          package: iconData.fontPackage,
          color:
              primaryColor, // Use consistent primary theme color for all icons
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // Center the icon within the circle
    final iconOffset = Offset(
      center.dx - textPainter.width / 2,
      center.dy - textPainter.height / 2,
    );

    textPainter.paint(canvas, iconOffset);
  }

  IconData _getIconForType(String iconType) {
    switch (iconType) {
      case 'delete':
        return Icons.close; // X icon for delete
      case 'copy':
        return LucideIcons.copy; // Copy icon
      case 'resize':
        return LucideIcons.moveDiagonal2; // Resize/expand icon
      case 'group':
        return LucideIcons.network; // Group icon
      default:
        return Icons.help; // Fallback icon
    }
  }

  @override
  bool shouldRepaint(covariant AnnotationPainter oldDelegate) {
    return oldDelegate.annotations != annotations ||
        oldDelegate.currentRect != currentRect ||
        oldDelegate.selectedIndex != selectedIndex ||
        oldDelegate.scale != scale ||
        oldDelegate.isGroupMode != isGroupMode ||
        oldDelegate.groupModeSelectedIndices != groupModeSelectedIndices ||
        oldDelegate.groupModeInitiatingMaskIndex !=
            groupModeInitiatingMaskIndex;
  }
}
