import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/tts/index_controller.dart';

class TextTTS extends GetView<CardTTSPageController> {
  const TextTTS({super.key});

  @override
  Widget build(context) {
    return ShadCard(
        padding:
            const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
        footer: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: ShadButton(
                size: ShadButtonSize.lg,
                onPressed: () {
                  controller.submit(context);
                },
                child: Text('toolbox.common.submit'.tr),
              ),
            )
          ],
        ),
        child: Obx(() => Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              spacing: 8,
              children: [
                ShadInputWithValidate(
                  label: 'anki.text'.tr,
                  placeholder: 'anki.tts.input_text'.tr,
                  maxLines: 8,
                  onChanged: (value) {
                    controller.textTtsParams.text.value = value;
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return 'anki.text_cannot_empty'.tr;
                    }
                    return "";
                  },
                  onValidateError: (error) {},
                ),
                ShadSwitchCustom(
                  label: 'anki.tts.is_use_sep'.tr,
                  initialValue: controller.textTtsParams.isUseSep.value,
                  onChanged: (v) {
                    controller.textTtsParams.isUseSep.value = v;
                  },
                ),
                if (controller.textTtsParams.isUseSep.value)
                  ShadSelectWithInput(
                    label: 'anki.sep'.tr,
                    placeholder: 'anki.tts.select_sep'.tr,
                    searchPlaceholder: 'anki.tts.input_sep'.tr,
                    isMultiple: false,
                    initialValue: [
                      controller.textTtsParams.sepList.first['value']!
                    ],
                    options: controller.textTtsParams.sepList.toList(),
                    onChanged: (value) {
                      logger.i(value);
                      controller.textTtsParams.sep.value = value.single;
                    },
                    onAddNew: (newSeparator) {
                      // Add new separator to the list if not already present
                      final newOption = {'value': newSeparator, 'label': newSeparator};
                      if (!controller.textTtsParams.sepList.any((option) => option['value'] == newSeparator)) {
                        controller.textTtsParams.sepList.add(newOption);
                      }

                      // Set as selected separator
                      controller.textTtsParams.sep.value = newSeparator;
                    },
                  ),
                ShadInputWithFileSelect(
                  key: ValueKey(controller.textTtsParams.outputDir.value),
                  title: 'toolbox.common.outputDir'.tr,
                  placeholder:  Text('toolbox.common.output.error'.tr),
                  initialValue: [controller.textTtsParams.outputDir.value],
                  isFolder: true,
                  onFilesSelected: (files) {
                    controller.textTtsParams.outputDir.value = files.single;
                  },
                  onValidate: (value, files) async {
                    return await validateOutputDir(value, files);
                  },
                  onValidateError: (error) {},
                ),
              ],
            )));
  }
}
