import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/tts/index_controller.dart';

class CardTTS extends GetView<CardTTSPageController> {
  const CardTTS({super.key});

  @override
  Widget build(context) {
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: SingleChildScrollView(
        child: Obx(() => Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              spacing: 8,
              children: [
                ShadInputWithValidate(
                  label: 'anki.tts.card_id'.tr,
                  placeholder:
                      'anki.tts.card_id_placeholder'.tr,
                  maxLines: 3,
                  onChanged: (value) {
                    controller.cardTtsParams.text.value = value;
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return 'anki.tts.card_id_cannot_empty'.tr;
                    }
                    try {
                      controller.noteList.value = await AnkiConnectController()
                          .findNotes('nid:"${value.trim()}"');
                      if (controller.noteList.isEmpty) {
                        return 'anki.tts.card_id_illegal'.tr;
                      }
                      logger.i(controller.noteList.value);
                      controller.roiModelList.value = controller.noteList
                          .map((e) => e['modelName'].toString())
                          .toSet()
                          .toList();
                      controller.cardModel.value = controller.roiModelList[0];
                      controller.fieldList.value = await AnkiConnectController()
                          .getModelFieldNames(controller.cardModel.value);
                    } catch (e) {
                      logger.e("validateCardIds error: $e");
                      return 'anki.tts.card_id_illegal'.tr;
                    }
                    return "";
                  },
                  onValidateError: (error) {},
                ),
                if (controller.roiModelList.isNotEmpty) ...[
                  ShadSelectWithSearch(
                    label: 'anki.tts.card_template'.tr,
                    placeholder: 'anki.tts.select_card_template'.tr,
                    searchPlaceholder:
                        'anki.placeholder.target_deck_search_input'.tr,
                    isMultiple: false,
                    initialValue: [controller.cardModel.value],
                    options: controller.roiModelList
                        .map((e) => {'value': e, 'label': e})
                        .toList(),
                    onChanged: (value) {
                      logger.i(value);
                      controller.cardModel.value = value.single;
                      controller.updateFieldList(controller.cardModel.value);
                    },
                  ),
                  if (controller.fieldList.isNotEmpty) ...[
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      minVerticalPadding: 0,
                      title: Text('anki.tts.voice_field_config'.tr, style: defaultTitleStyle),
                      subtitle: Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            maxWidth: double.infinity,
                            maxHeight: (1 + controller.fieldList.length) * 48,
                          ),
                          child: ShadTable.list(
                            pinnedRowCount: 1,
                            header: [
                              ShadTableCell.header(child: Text('anki.tts.original_field'.tr)),
                              ShadTableCell.header(child: Text('anki.tts.add_voice'.tr)),
                              ShadTableCell.header(child: Text('anki.tts.fill_field'.tr)),
                              ShadTableCell.header(child: Text('anki.tts.remove_voice'.tr)),
                            ],
                            columnSpanExtent: (index) {
                              // if (index == 0) {
                              //   return const FractionalTableSpanExtent(0.3);
                              // } else if (index == 1) {
                              //   return const FractionalTableSpanExtent(0.3);
                              // }
                              return const FractionalTableSpanExtent(0.25);
                            },
                            rowSpanExtent: (index) =>
                                const FixedTableSpanExtent(48),
                            children: [
                              for (var i = 0;
                                  i < controller.fieldList.length;
                                  i++)
                                [
                                  ShadTableCell(
                                      child: Text(controller.fieldList[i])),
                                  ShadTableCell(
                                    child: Obx(() => ShadSwitch(
                                          value: controller.modelFieldConfigs[
                                                          controller
                                                              .cardModel.value]
                                                      ?[controller.fieldList[i]]
                                                  ?['addVoice'] ??
                                              false,
                                          onChanged: (value) {
                                            controller.updateFieldConfig(
                                              controller.cardModel.value,
                                              controller.fieldList[i],
                                              addVoice: value,
                                              removeVoice: value ? false : null,
                                            );
                                          },
                                        )),
                                  ),
                                  ShadTableCell(
                                    child: ShadSelectCustom(
                                      label: 'anki.tts.select_field'.tr,
                                      showLabel: false,
                                      placeholder: 'anki.tts.select_field'.tr,
                                      options: controller.fieldList
                                          .map((e) => {'value': e, 'label': e})
                                          .toList(),
                                      isMultiple: false,
                                      // initialValue: [
                                      //   controller.modelFieldConfigs[controller
                                      //                   .cardModel.value]
                                      //               ?[controller.fieldList[i]]
                                      //           ?['targetField'] ??
                                      //       controller.fieldList[i]
                                      // ],
                                      onChanged: (value) {
                                        if (value.isNotEmpty) {
                                          controller.updateFieldConfig(
                                            controller.cardModel.value,
                                            controller.fieldList[i],
                                            targetField:
                                                value.single.toString(),
                                          );
                                        }
                                      },
                                    ),
                                  ),
                                  ShadTableCell(
                                    child: Obx(() => ShadSwitch(
                                          value: controller.modelFieldConfigs[
                                                          controller
                                                              .cardModel.value]
                                                      ?[controller.fieldList[i]]
                                                  ?['removeVoice'] ??
                                              false,
                                          onChanged: (value) {
                                            controller.updateFieldConfig(
                                              controller.cardModel.value,
                                              controller.fieldList[i],
                                              addVoice: value ? false : null,
                                              removeVoice: value,
                                            );
                                          },
                                        )),
                                  ),
                                ]
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ],
            )),
      ),
    );
  }
}
