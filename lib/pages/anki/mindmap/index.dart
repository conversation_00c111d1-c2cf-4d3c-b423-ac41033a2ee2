import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/index.dart';
import 'package:anki_guru/controllers/anki/mindmap_card.dart';
import 'package:anki_guru/pages/common.dart';

class MindmapCardPage extends StatefulWidget {
  const MindmapCardPage({super.key});

  @override
  State<MindmapCardPage> createState() => _MindmapCardPageState();
}

class _MindmapCardPageState extends State<MindmapCardPage> {
  final controller = Get.put(MindmapCardPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.mindmap.mindmap_card_title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('anki.llm_card.function_description'.tr, style: defaultPageTitleStyle),
                  Text('anki.mindmap.feature_description'.tr,
                      style: theme.textTheme.muted),
                  const SizedBox(height: 16),
                ],
              ),
              const MindmapForm(),
            ],
          ),
        ),
      ),
    );
  }
}
