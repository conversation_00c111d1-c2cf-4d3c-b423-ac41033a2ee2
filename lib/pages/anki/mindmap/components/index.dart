import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/anki/mindmap_card.dart';

class MindmapForm extends GetView<MindmapCardPageController> {
  const MindmapForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit();
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 8,
            children: [
              ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.first;
                },
                onAddNew: (newDeckName) {
                  // Add to the deck list if not already present
                  if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                    ankiConnectController.parentDeckList.add(newDeckName);
                  }

                  // Set as selected deck
                  controller.parentDeck.value = newDeckName;
                },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result && context.mounted) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              ShadRadioGroupCustom(
                label: 'anki.mindmap.mindmap_source'.tr,
                initialValue: controller.mindSource.value,
                items: controller.sourceList,
                onChanged: (value) {
                  controller.mindSource.value = value;
                  controller.maskTypes.value =
                      controller.initalMaskTypes[controller.mindSource.value] ??
                          [];
                },
              ),
              ShadSwitchCustom(
                label: 'anki.mindmap.cloze_mode'.tr,
                initialValue: controller.isClozeMode.value,
                onChanged: (v) {
                  controller.isClozeMode.value = v;
                },
              ),
              if (controller.isClozeMode.value) ...[
                ShadCheckboxGroup(
                  label: 'anki.mindmap.cloze_style'.tr,
                  initialValues: controller.maskTypes.toList(),
                  items: controller.maskTypesDict[controller.mindSource.value]
                          ?.toList() ??
                      [],
                  onChanged: (value) {
                    logger.i(value);
                    controller.maskTypes.value = value;
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return 'anki.mindmap.at_least_one_cloze_style'.tr;
                    }
                    return "";
                  },
                  onValidateError: (error) {},
                ),
                if (controller.maskTypes.contains("text_color"))
                  if (controller.mindSource.value == "mubu") ...[
                    ShadSelectCustom(
                      key: const ValueKey("text_color"),
                      label: 'anki.mindmap.text_color'.tr,
                      placeholder: 'anki.mindmap.select_color'.tr,
                      isMultiple: true,
                      initialValue: controller.textColorList.toList(),
                      options: controller.mubuColorList,
                      onChanged: (value) {
                        logger.i(value);
                        controller.textColorList.value = value;
                      },
                    ),
                  ] else ...[
                    ShadSelectWithInput(
                      key: const ValueKey("text_color"),
                      label: 'anki.mindmap.text_color'.tr,
                      placeholder: 'anki.mindmap.select_color'.tr,
                      searchPlaceholder: 'anki.mindmap.input_color_placeholder'.tr,
                      isMultiple: true,
                      initialValue: controller.textColorList.toList(),
                      options: controller.commonColorList,
                      onChanged: (value) {
                        logger.i(value);
                        controller.textColorList.value = value;
                      },
                      onAddNew: (newColor) {
                        // Add new color to the common color list if not already present
                        final newOption = {'value': newColor, 'label': newColor};
                        if (!controller.commonColorList.any((option) => option['value'] == newColor)) {
                          controller.commonColorList.add(newOption);
                        }

                        // Add to selected text colors if not already present
                        if (!controller.textColorList.contains(newColor)) {
                          controller.textColorList.add(newColor);
                        }
                      },
                    ),
                  ],
                if (controller.maskTypes.contains("text_highlight"))
                  if (controller.mindSource.value == "mubu") ...[
                    ShadSelectCustom(
                      key: const ValueKey("text_highlight"),
                      label: 'anki.mindmap.highlight_color'.tr,
                      placeholder: 'anki.mindmap.select_color'.tr,
                      isMultiple: true,
                      initialValue: controller.highlightColorList.toList(),
                      options: controller.mubuHighlightColorList,
                      onChanged: (value) {
                        logger.i(value);
                        controller.highlightColorList.value = value;
                      },
                    ),
                  ] else ...[
                    ShadSelectWithInput(
                      key: const ValueKey("text_highlight"),
                      label: 'anki.mindmap.highlight_color'.tr,
                      placeholder: 'anki.mindmap.select_color'.tr,
                      searchPlaceholder: 'anki.mindmap.input_color_placeholder'.tr,
                      isMultiple: true,
                      initialValue: controller.highlightColorList.toList(),
                      options: controller.commonColorList,
                      onChanged: (value) {
                        logger.i(value);
                        controller.highlightColorList.value = value;
                      },
                      onAddNew: (newColor) {
                        // Add new color to the common color list if not already present
                        final newOption = {'value': newColor, 'label': newColor};
                        if (!controller.commonColorList.any((option) => option['value'] == newColor)) {
                          controller.commonColorList.add(newOption);
                        }

                        // Add to selected highlight colors if not already present
                        if (!controller.highlightColorList.contains(newColor)) {
                          controller.highlightColorList.add(newColor);
                        }
                      },
                    ),
                  ]
              ],
              ShadSwitchCustom(
                label: 'anki.common.show_source'.tr,
                initialValue: controller.isShowSource.value,
                onChanged: (v) {
                  controller.isShowSource.value = v;
                },
              ),
              if (controller.mindSource.value == "markdown") ...[
                ShadSwitchCustom(
                  label: 'anki.mindmap.obsidian_syntax'.tr,
                  initialValue: controller.isObsidian.value,
                  onChanged: (v) {
                    controller.isObsidian.value = v;
                  },
                ),
                ShadInputWithFileSelect(
                  title: 'anki.mindmap.media_file_directory'.tr,
                  placeholder: Text('anki.mindmap.media_folder_placeholder'.tr),
                  initialValue: [controller.mediaFolder.value],
                  isFolder: true,
                  onFilesSelected: (value) {
                    controller.mediaFolder.value = value.single;
                  },
                  onValidate: (value, files) async {
                    return await validateOutputDir(value, files);
                  },
                  onValidateError: (error) {},
                ),
              ],
              ShadInputWithValidate(
                  label: 'MapID',
                  placeholder: 'anki.mindmap.map_id_placeholder'.tr,
                  onChanged: (value) {
                    controller.mapID.value = value;
                  },
                  initialValue: controller.mapID.value,
                  onValidate: (value) async {
                    return "";
                  }),
              ShadSelectWithInput(
                key:
                    ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.excel_card.select_tags'.tr,
                searchPlaceholder: 'anki.excel_card.input_tags'.tr,
                isMultiple: true,
                initialValue: controller.tags.toList(),
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the global tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tags.contains(newTag)) {
                    controller.tags.add(newTag);
                  }
                },
              ),
              ShadInputWithFileSelect(
                title: 'toolbox.common.inputFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const [
                  'xmind',
                  'zxm',
                  'md',
                  'mm',
                  'opml',
                  'docx'
                ],
                isRequired: true,
                allowMultiple: true,
                onFilesSelected: (files) {
                  controller.selectedFilePaths.value = files;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
          )),
    );
  }
}
