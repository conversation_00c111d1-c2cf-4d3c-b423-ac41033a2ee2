import 'package:anki_guru/pages/common.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/anki_sync.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class AnkiSyncConfig extends StatefulWidget {
  const AnkiSyncConfig({super.key});

  @override
  State<AnkiSyncConfig> createState() => _AnkiSyncConfigState();
}

class _AnkiSyncConfigState extends State<AnkiSyncConfig> {
  final controller = Get.find<AnkiSyncController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('anki.sync.config.title'.tr),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () async {
              await controller.saveSettings();
            },
          ),
        ],
      ),
      body: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          children: [
            ShadCard(
              padding: const EdgeInsets.only(
                  left: 16, right: 16, top: 16, bottom: 16),
              child: Column(
                children: [
                  ShadInputWithValidate(
                    label: "anki.sync.config.host".tr,
                    placeholder: 'anki.sync.config.hostPlaceholder'.tr,
                    initialValue: controller.host.value,
                    onChanged: (value) {
                      controller.host.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return "anki.sync.config.hostRequired".tr;
                      }
                      // 使用正则表达式验证IP地址格式
                      final ipRegex = RegExp(
                          r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$');

                      // 使用正则表达式验证域名格式
                      final domainRegex = RegExp(
                          r'^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])$');

                      if (!ipRegex.hasMatch(value) &&
                          !domainRegex.hasMatch(value)) {
                        return "anki.sync.config.hostInvalid".tr;
                      }
                      return "";
                    },
                    onValidateError: (error) {},
                  ),
                  ShadInputWithValidate(
                    label: "anki.sync.config.port".tr,
                    placeholder: 'anki.sync.config.portPlaceholder'.tr,
                    initialValue: controller.port.value,
                    onChanged: (value) {
                      controller.port.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return "anki.sync.config.portRequired".tr;
                      }
                      // 使用正则表达式验证端口号格式
                      final portRegex = RegExp(
                          r'^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$');

                      if (!portRegex.hasMatch(value)) {
                        return "anki.sync.config.portInvalid".tr;
                      }
                      return "";
                    },
                    onValidateError: (error) {},
                  ),
                  ShadInputWithFileSelect(
                    title: 'anki.sync.config.dataLocation'.tr,
                    placeholder:
                        Text('anki.sync.config.dataLocationPlaceholder'.tr),
                    allowedExtensions: const ['txt', 'md', 'docx'],
                    isRequired: true,
                    isFolder: true,
                    allowMultiple: false,
                    initialValue: [controller.dataDir.value],
                    onFilesSelected: (files) {
                      controller.dataDir.value = files.single;
                    },
                    onValidate: (value, files) async {
                      return validateOutputDir(value, files);
                    },
                    onValidateError: (error) {
                      // controller.dataDirError.value = error;
                    },
                  ),
                  ShadInputWithValidate(
                    label: "anki.sync.config.max_payload".tr,
                    placeholder: 'anki.sync.config.max_payload_placeholder'.tr,
                    initialValue: controller.maxPayload.value,
                    onChanged: (value) {
                      controller.maxPayload.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return "anki.sync.config.max_payloa_required".tr;
                      }
                      // 使用正则表达式验证是否为正整数
                      final reg = RegExp(r'^\d+$');
                      if (!reg.hasMatch(value)) {
                        return "anki.sync.config.max_payloa_invalid".tr;
                      }
                      return "";
                    },
                    onValidateError: (error) {},
                  ),
                ],
              ),
            ),
          ]),
    );
  }
}
