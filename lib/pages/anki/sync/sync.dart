import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/anki/sync/user_management_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class AnkiSyncPage extends StatefulWidget {
  const AnkiSyncPage({super.key});

  @override
  State<AnkiSyncPage> createState() => _AnkiSyncPageState();
}

class _AnkiSyncPageState extends State<AnkiSyncPage> {
  final settingController = Get.find<SettingController>();
  final messageController = Get.find<MessageController>();
  final progressController = Get.find<ProgressController>();
  final controller = Get.find<AnkiSyncController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.sync.title'.tr),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Get.toNamed('/toolbox/sync/config');
            },
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: ListView(
          padding: const EdgeInsets.all(8),
          children: [
            ShadCard(
              padding: const EdgeInsets.all(0),
              child: ListTile(
                leading: const Icon(LucideIcons.refreshCcw),
                title: Text('anki.sync.syncServer'.tr),
                trailing: SizedBox(
                  width: 96,
                  child: Obx(() => controller.isServerRunning.value
                      ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(LucideIcons.rotateCcw),
                              onPressed: () => controller.startServer(context),
                            ),
                            IconButton(
                              icon: const Icon(FontAwesomeIcons.stop),
                              onPressed: () => controller.stopServer(),
                            ),
                          ],
                        )
                      : Row(
                          children: [
                            IconButton(
                              icon: const Icon(LucideIcons.play),
                              onPressed: () => controller.startServer(context),
                            ),
                            const IconButton(
                              icon: Icon(FontAwesomeIcons.stop),
                              onPressed: null,
                            ),
                          ],
                        )),
                ),
                onTap: () {},
              ),
            ),
            const SizedBox(height: 16),
            ShadCard(
              title: Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('anki.sync.serverAddress'.tr,
                        style: const TextStyle(fontSize: 16)),
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: () => controller.fetchIpAddresses(),
                    ),
                  ],
                ),
              ),
              padding: const EdgeInsets.all(0),
              child: Obx(() => Column(
                    spacing: 0,
                    children: [
                      ...controller.ipAddresses.map((ip) => ListTile(
                            title: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    "http://$ip:${controller.port.value}",
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.copy),
                                  onPressed: () {
                                    Clipboard.setData(ClipboardData(
                                        text:
                                            'http://$ip:${controller.port.value}'));
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                          duration: const Duration(seconds: 1),
                                          content: Text('anki.sync.copied'
                                              .trParams({
                                            'address':
                                                'http://$ip:${controller.port.value}'
                                          }))),
                                    );
                                  },
                                ),
                              ],
                            ),
                            onTap: () {
                              Clipboard.setData(ClipboardData(
                                  text: "http://$ip:${controller.port.value}"));
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                    duration: const Duration(seconds: 1),
                                    content: Text('anki.sync.copied'.trParams({
                                      'address':
                                          'http://$ip:${controller.port.value}'
                                    }))),
                              );
                            },
                          )),
                    ],
                  )),
            ),
            const SizedBox(height: 16),
            _buildUserListCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserListCard() {
    return ShadCard(
      title: Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('anki.sync.user.title'.tr,
                style: const TextStyle(fontSize: 16)),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showAddUserDialog(),
            ),
          ],
        ),
      ),
      padding: const EdgeInsets.all(0),
      child: Obx(() {
        return Column(
          spacing: 0,
          children: [
            // Show empty state
            if (controller.userList.isEmpty)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: ShadCard(
                  padding: const EdgeInsets.all(16),
                  child: Center(
                    child: Text('anki.sync.no_user'.tr),
                  ),
                ),
              ),
            if (controller.userList.isNotEmpty)
              ...controller.userList.map((user) => _buildUserListItem(user)),
            const SizedBox(height: 8),
          ],
        );
      }),
    );
  }

  Widget _buildUserListItem(user) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.grey[400],
        child: const Icon(LucideIcons.user, size: 22
            // color: Colors.grey[600],
            ),
      ),
      title: Text(user.username),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _showEditUserDialog(user),
            tooltip: 'anki.sync.user.edit'.tr,
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _showDeleteUserDialog(user),
            tooltip: 'anki.sync.user.delete'.tr,
          ),
        ],
      ),
    );
  }

  void _showAddUserDialog() {
    showUserManagementDialog(context, controller);
  }

  void _showEditUserDialog(AnkiUser user) {
    showUserManagementDialog(context, controller, user: user);
  }

  Future<void> _deleteUser(AnkiUser user) async {
    await controller.deleteUser(user.id);
  }

  void _showDeleteUserDialog(user) {
    showDialog(
      context: context,
      builder: (context) => ShadDialog(
        title: Text('anki.sync.user.deleteTitle'.tr),
        description: Text('anki.sync.user.deleteConfirm'.trParams({
          'username': user.username,
        })),
        actions: [
          ShadButton.outline(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('anki.sync.user.cancel'.tr),
          ),
          ShadButton.destructive(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteUser(user);
            },
            child: Text('anki.sync.user.delete'.tr),
          ),
        ],
      ),
    );
  }
}
