import 'package:anki_guru/controllers/anki/anki_sync.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class UserManagementDialog extends StatefulWidget {
  final AnkiUser? user; // null for add, non-null for edit
  final AnkiSyncController controller;

  const UserManagementDialog({
    super.key,
    this.user,
    required this.controller,
  });

  @override
  State<UserManagementDialog> createState() => _UserManagementDialogState();
}

class _UserManagementDialogState extends State<UserManagementDialog> {
  late final TextEditingController _usernameController;
  late final TextEditingController _passwordController;

  String _usernameError = '';
  String _passwordError = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _usernameController =
        TextEditingController(text: widget.user?.username ?? '');
    _passwordController =
        TextEditingController(text: widget.user?.password ?? '');
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  bool get isEditing => widget.user != null;

  String get dialogTitle =>
      isEditing ? 'anki.sync.user.editTitle'.tr : 'anki.sync.user.addTitle'.tr;

  Future<void> _validateAndSave() async {
    setState(() {
      _usernameError = '';
      _passwordError = '';
      _isLoading = true;
    });

    // Validate username
    if (_usernameController.text.trim().isEmpty) {
      setState(() {
        _usernameError = 'anki.sync.config.usernameRequired'.tr;
        _isLoading = false;
      });
      return;
    }

    // Validate password
    if (_passwordController.text.isEmpty) {
      setState(() {
        _passwordError = 'anki.sync.config.passwordRequired'.tr;
        _isLoading = false;
      });
      return;
    }

    if (_passwordController.text.length < 6) {
      setState(() {
        _passwordError = 'anki.sync.config.passwordMinLength'.tr;
        _isLoading = false;
      });
      return;
    }

    // Save user
    bool success;
    if (isEditing) {
      success = await widget.controller.updateUser(
        widget.user!.id,
        _usernameController.text.trim(),
        _passwordController.text,
      );
    } else {
      success = await widget.controller.addUser(
        _usernameController.text.trim(),
        _passwordController.text,
      );
    }

    if (!mounted) return;

    setState(() {
      _isLoading = false;
    });

    if (success && mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ShadDialog(
      title: Text(dialogTitle),
      actions: [
        ShadButton.outline(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: Text('anki.sync.user.cancel'.tr),
        ),
        ShadButton(
          onPressed: _isLoading ? null : _validateAndSave,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing
                  ? 'anki.sync.user.update'.tr
                  : 'anki.sync.user.add'.tr),
        ),
      ],
      child: Material(
        child: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ShadInputWithValidate(
                label: "anki.sync.config.username".tr,
                placeholder: 'anki.sync.config.usernamePlaceholder'.tr,
                initialValue: _usernameController.text,
                onChanged: (value) {
                  _usernameController.text = value;
                  if (_usernameError.isNotEmpty) {
                    setState(() {
                      _usernameError = '';
                    });
                  }
                },
                onValidate: (value) async {
                  if (value.trim().isEmpty) {
                    return "anki.sync.config.usernameRequired".tr;
                  }
                  return "";
                },
                onValidateError: (error) {
                  setState(() {
                    _usernameError = error;
                  });
                },
              ),
              const SizedBox(height: 16),
              ShadInputPasswordCustom(
                label: "anki.sync.config.password".tr,
                placeholder: 'anki.sync.config.passwordPlaceholder'.tr,
                initialValue: _passwordController.text,
                onChanged: (value) {
                  _passwordController.text = value;
                  if (_passwordError.isNotEmpty) {
                    setState(() {
                      _passwordError = '';
                    });
                  }
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return "anki.sync.config.passwordRequired".tr;
                  }
                  if (value.length < 6) {
                    return "anki.sync.config.passwordMinLength".tr;
                  }
                  return "";
                },
                onValidateError: (error) {
                  setState(() {
                    _passwordError = error;
                  });
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Show user management dialog
Future<void> showUserManagementDialog(
  BuildContext context,
  AnkiSyncController controller, {
  AnkiUser? user,
}) {
  return showDialog(
    context: context,
    builder: (context) => UserManagementDialog(
      user: user,
      controller: controller,
    ),
  );
}
