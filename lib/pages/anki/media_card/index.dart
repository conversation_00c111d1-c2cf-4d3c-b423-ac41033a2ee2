import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/qa_form.dart';
import 'components/video_form.dart';
import 'package:anki_guru/controllers/anki/media_card.dart';
import 'package:anki_guru/pages/common.dart';

class MediaCardPage extends StatefulWidget {
  const MediaCardPage({super.key});

  @override
  State<MediaCardPage> createState() => _MediaCardPageState();
}

class _MediaCardPageState extends State<MediaCardPage> {
  final controller = Get.put(MediaCardPageController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.media_card.media_card_title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('anki.media_card.function_description'.tr, style: defaultPageTitleStyle),
                Text('anki.media_card.feature_description'.tr, style: theme.textTheme.muted),
                const SizedBox(height: 16),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 2;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        print(controller.tabController.selected);
                      },
                      tabs: [
                        ShadTab(
                          value: 'qa',
                          content: const QAForm(),
                          width: tabWidth,
                          child: Text('anki.media_card.qa_card_tab'.tr),
                        ),
                        ShadTab(
                          value: 'subtitle',
                          content: const VideoForm(),
                          width: tabWidth,
                          child: Text('anki.media_card.subtitle_card_tab'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
