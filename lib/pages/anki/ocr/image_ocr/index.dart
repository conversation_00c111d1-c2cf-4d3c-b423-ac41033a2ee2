import 'package:anki_guru/pages/anki/ocr/image_ocr/main_form.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/ocr/ocr_controller.dart';

class ImageOCRPage extends StatefulWidget {
  const ImageOCRPage({super.key});

  @override
  State<ImageOCRPage> createState() => _ImageOCRPageState();
}

class _ImageOCRPageState extends State<ImageOCRPage> {
  late OCRController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<OCRController>();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text("anki.ocr.imageOcr".tr),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          // IconButton(
          //   icon: const Icon(Icons.settings),
          //   onPressed: () {
          //     Get.toNamed('/image_card/config');
          //   },
          // )
        ],
      ),
      body: const MainForm(),
    );
  }
}
