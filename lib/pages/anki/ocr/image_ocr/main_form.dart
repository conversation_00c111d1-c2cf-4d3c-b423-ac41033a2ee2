import 'package:anki_guru/pages/common.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:desktop_drop/desktop_drop.dart';
import 'package:anki_guru/controllers/anki/ocr/ocr_controller.dart';
import 'package:anki_guru/controllers/anki/ocr/components/ocr_image_viewer.dart';
import 'package:anki_guru/controllers/anki/ocr/components/ocr_image.dart';

// 网格视图卡片组件
class GridViewCard extends StatelessWidget {
  final OcrImage card;
  final int index;
  final VoidCallback onTap;

  const GridViewCard({
    super.key,
    required this.card,
    required this.index,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        clipBehavior: Clip.antiAlias,
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image.memory(
              card.imageData,
              fit: BoxFit.contain,
            ),
            Positioned(
              left: 4,
              bottom: 4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${index + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// 网格视图组件
class ImagesGridView extends StatelessWidget {
  final OCRController controller;

  const ImagesGridView({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1,
        ),
        itemCount: controller.imageCards.length,
        itemBuilder: (context, index) {
          return GridViewCard(
            card: controller.imageCards[index],
            index: index,
            onTap: () => controller.switchToPageView(index),
          );
        },
      ),
    );
  }
}

// 单页视图组件
class ImagePageView extends StatelessWidget {
  final OCRController controller;

  const ImagePageView({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      physics:
          const ClampingScrollPhysics(), // 使用ClampingScrollPhysics替代NeverScrollableScrollPhysics
      controller: controller.pageController,
      itemCount: controller.imageCards.length,
      onPageChanged: (index) {
        controller.currentPage.value = index;
      },
      itemBuilder: (context, index) {
        return Card(
          color: Theme.of(context).cardColor,
          elevation: 4,
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 操作按钮 - 放在顶部
              Obx(() {
                final currentImage = controller.imageCards.isNotEmpty &&
                        index < controller.imageCards.length
                    ? controller.imageCards[index]
                    : null;

                if (currentImage == null || !currentImage.isProcessed) {
                  return const SizedBox.shrink();
                }
                return const SizedBox.shrink();
              }),

              // 图片预览区域
              Expanded(
                flex: 2,
                child: Obx(() {
                  if (controller.imageCards.isEmpty ||
                      index >= controller.imageCards.length) {
                    return Center(
                      child: Text('anki.ocr.imageDeleted'.tr),
                    );
                  }
                  final currentImage = controller.imageCards[index];
                  return OcrImageViewer(
                    image: currentImage,
                    useCard: false, // 避免Card嵌套
                    onTextCopied: (text) {},
                    onSelectionChanged: (isAllSelected) {},
                    onTextExported: (filePath) {},
                  );
                }),
              ),
            ],
          ),
        );
      },
    );
  }
}

class MainForm extends StatefulWidget {
  const MainForm({super.key});
  static const platform = MethodChannel('samples.flutter.dev/battery');

  @override
  State<MainForm> createState() => _MainFormState();
}

class _MainFormState extends State<MainForm> {
  final controller = Get.find<OCRController>();

  @override
  void initState() {
    // controller.loadSettings();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 顶部工具栏
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: LayoutBuilder(builder: (context, constraints) {
            // 当屏幕过窄时使用更紧凑的布局
            final bool isNarrow = constraints.maxWidth < 600;

            return Column(
              children: [
                // 在窄屏上垂直排列
                isNarrow
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // 文本左对齐
                          Obx(() => Text(
                                controller.imageCards.isEmpty
                                    ? 'anki.ocr.noImageSelected'.tr
                                    : 'anki.ocr.selectedImages'.trParams({'count': controller.imageCards.length.toString()}),
                              )),
                          const SizedBox(height: 8),
                          // 按钮行使用Wrap以适应空间
                          Wrap(
                            spacing: 8,
                            children: [
                              // 中间的按钮组
                              Obx(() => !controller.isGridView.value
                                  ? Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        // 中间的按钮组
                                        if (Platform.isWindows ||
                                            Platform.isLinux ||
                                            Platform.isMacOS ||
                                            controller
                                                .imageCards.isNotEmpty) ...[
                                          ShadIconButtonCustom(
                                            icon: LucideIcons.imagePlus,
                                            size: 22,
                                            onPressed: () =>
                                                controller.pickImageFile(),
                                          ),
                                          ShadIconButtonCustom(
                                            icon: LucideIcons.clipboardPaste,
                                            size: 22,
                                            onPressed: () async {
                                              await controller
                                                  .loadImageFromClipboard();
                                            },
                                          ),
                                          Obx(() =>
                                              controller.imageCards.isNotEmpty
                                                  ? ShadIconButtonCustom(
                                                      icon: LucideIcons
                                                          .slidersHorizontal,
                                                      size: 22,
                                                      onPressed: () async {
                                                        controller
                                                            .openImageForAnnotation(
                                                                controller
                                                                    .currentPage
                                                                    .value);
                                                      })
                                                  : const SizedBox.shrink()),
                                        ],
                                      ],
                                    )
                                  : const SizedBox()),
                              // 右侧按钮组
                              Obx(() => controller.imageCards.isNotEmpty
                                  ? Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        // 删除按钮组
                                        if (!controller.isGridView.value)
                                          ShadIconButtonCustom(
                                            icon: LucideIcons.trash2,
                                            size: 22,
                                            onPressed: () => controller
                                                .removeImage(controller
                                                    .currentPage.value),
                                            onLongPressStart: (details) {
                                              showShadDialog(
                                                context: context,
                                                builder:
                                                    (BuildContext context) =>
                                                        ShadDialog(
                                                  title: Text('anki.ocr.deleteImage'.tr),
                                                  actions: [
                                                    TextButton(
                                                      child: Text('anki.ocr.cancel'.tr),
                                                      onPressed: () =>
                                                          Navigator.of(context)
                                                              .pop(),
                                                    ),
                                                    TextButton(
                                                      child: Text(
                                                        'anki.ocr.delete'.tr,
                                                        style: const TextStyle(
                                                            color: Colors.red),
                                                      ),
                                                      onPressed: () {
                                                        controller
                                                            .removeAllImages();
                                                        Navigator.of(context)
                                                            .pop();
                                                      },
                                                    ),
                                                  ],
                                                  child:
                                                      Text('anki.ocr.deleteAllImages'.tr),
                                                ),
                                              );
                                            },
                                          ),
                                        // 宫格视图切换按钮
                                        ShadIconButtonCustom(
                                          icon: controller.isGridView.value
                                              ? LucideIcons.list
                                              : LucideIcons.layoutGrid,
                                          size: 22,
                                          onPressed: controller.toggleViewMode,
                                        ),
                                      ],
                                    )
                                  : const SizedBox.shrink()),
                            ],
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // 文本左对齐
                          Flexible(
                            child: Obx(() => Text(
                                  controller.imageCards.isEmpty
                                      ? 'anki.ocr.noImageSelected'.tr
                                      : 'anki.ocr.selectedImages'.trParams({'count': controller.imageCards.length.toString()}),
                                  overflow: TextOverflow.ellipsis,
                                )),
                          ),
                          // 中间的按钮组
                          Obx(() => !controller.isGridView.value
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    if (Platform.isWindows ||
                                        Platform.isLinux ||
                                        Platform.isMacOS ||
                                        controller.imageCards.isNotEmpty) ...[
                                      ShadIconButtonCustom(
                                        icon: LucideIcons.imagePlus,
                                        size: 22,
                                        onPressed: () =>
                                            controller.pickImageFile(),
                                      ),
                                      ShadIconButtonCustom(
                                        icon: LucideIcons.clipboardPaste,
                                        size: 22,
                                        onPressed: () async {
                                          await controller
                                              .loadImageFromClipboard();
                                        },
                                      ),
                                      Obx(() => controller.imageCards.isNotEmpty
                                          ? ShadIconButtonCustom(
                                              icon:
                                                  LucideIcons.slidersHorizontal,
                                              size: 22,
                                              onPressed: () async {
                                                controller
                                                    .openImageForAnnotation(
                                                        controller
                                                            .currentPage.value);
                                              })
                                          : const SizedBox.shrink()),
                                    ],
                                  ],
                                )
                              : const SizedBox()),

                          Obx(() => controller.imageCards.isNotEmpty
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // 删除按钮组
                                    if (!controller.isGridView.value)
                                      ShadIconButtonCustom(
                                        icon: LucideIcons.trash2,
                                        size: 22,
                                        onPressed: () => controller.removeImage(
                                            controller.currentPage.value),
                                        onLongPressStart: (details) {
                                          showShadDialog(
                                            context: context,
                                            builder: (BuildContext context) =>
                                                ShadDialog(
                                              title: Text('anki.ocr.deleteImage'.tr),
                                              actions: [
                                                TextButton(
                                                  child: Text('anki.ocr.cancel'.tr),
                                                  onPressed: () =>
                                                      Navigator.of(context)
                                                          .pop(),
                                                ),
                                                TextButton(
                                                  child: Text(
                                                    'anki.ocr.delete'.tr,
                                                    style: const TextStyle(
                                                        color: Colors.red),
                                                  ),
                                                  onPressed: () {
                                                    controller
                                                        .removeAllImages();
                                                    Navigator.of(context).pop();
                                                  },
                                                ),
                                              ],
                                              child: Text('anki.ocr.deleteAllImages'.tr),
                                            ),
                                          );
                                        },
                                      ),
                                    // 宫格视图切换按钮
                                    ShadIconButtonCustom(
                                      icon: controller.isGridView.value
                                          ? LucideIcons.list
                                          : LucideIcons.layoutGrid,
                                      size: 22,
                                      onPressed: controller.toggleViewMode,
                                    ),
                                  ],
                                )
                              : const SizedBox.shrink()),
                        ],
                      ),
              ],
            );
          }),
        ),
        // 主要内容区域
        Expanded(
          child: Obx(() => controller.imageCards.isEmpty
              ? GestureDetector(
                  onTap: () => controller.pickImageFile(),
                  child: DropTarget(
                    onDragEntered: (detail) {
                      print('onDragEntered');
                    },
                    onDragDone: (detail) async {
                      print('onDragDone');
                      final allowedFiles = detail.files.where((file) {
                        final extension =
                            file.path.split('.').last.toLowerCase();
                        return ["jpg", "jpeg", "png"].contains(extension);
                      }).toList();
                      if (allowedFiles.isEmpty) {
                        showToastNotification(
                            context, 'anki.ocr.pleaseSelectValidImageFormat'.tr, '',
                            type: "error");
                        return;
                      }

                      // 记录添加前的数量
                      final oldCount = controller.imageCards.length;

                      for (var file in allowedFiles) {
                        final imageBytes = await File(file.path).readAsBytes();
                        controller.imageCards.add(OcrImage(
                          imageData: imageBytes,
                        ));
                      }

                      // 如果成功添加了图片
                      if (controller.imageCards.length > oldCount) {
                        // 切换到新添加的第一张图片
                        final newIndex = oldCount;
                        controller.currentPage.value = newIndex;

                        // 如果当前是网格视图，切换到单页视图
                        if (controller.isGridView.value) {
                          controller.toggleViewMode();
                        }

                        // 使用延迟回调确保布局完成后再滚动和重置变换
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (controller.pageController.hasClients) {
                            controller.pageController.jumpToPage(newIndex);
                          }
                        });
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: ShadCard(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, top: 16, bottom: 16),
                        columnMainAxisAlignment: MainAxisAlignment.center,
                        columnCrossAxisAlignment: CrossAxisAlignment.center,
                        rowMainAxisAlignment: MainAxisAlignment.center,
                        rowCrossAxisAlignment: CrossAxisAlignment.center,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.add_photo_alternate,
                                  size: 64, color: Colors.grey[400]),
                              const SizedBox(height: 16),
                              if (Platform.isAndroid || Platform.isIOS)
                                Text('anki.ocr.clickToSelectImage'.tr,
                                    style: TextStyle(color: Colors.grey[600])),
                              if (Platform.isWindows ||
                                  Platform.isLinux ||
                                  Platform.isMacOS)
                                Text('anki.ocr.clickToSelectOrDragImage'.tr,
                                    style: TextStyle(color: Colors.grey[600])),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                )
              : controller.isGridView.value
                  ? ImagesGridView(controller: controller)
                  : ImagePageView(controller: controller)),
        ),
        // 页面指示器
        Obx(() =>
            controller.imageCards.isNotEmpty && !controller.isGridView.value
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 上一页按钮
                        Obx(
                          () => IconButton(
                              onPressed: () => controller.previousPage(),
                              icon: const Icon(Icons.arrow_back_ios),
                              // 第一页时禁用
                              color: controller.currentPage.value > 0
                                  ? ShadTheme.of(context).colorScheme.primary
                                  : Colors.grey),
                        ),
                        // 页面指示器
                        Obx(() => Text(
                              'anki.ocr.pageIndicator'.trParams({
                                'current': (controller.currentPage.value + 1).toString(),
                                'total': controller.imageCards.length.toString()
                              }),
                              style: const TextStyle(fontSize: 16),
                              textAlign: TextAlign.center,
                            )),
                        // 下一页按钮
                        Obx(
                          () => IconButton(
                              onPressed: () => controller.nextPage(),
                              icon: const Icon(Icons.arrow_forward_ios),
                              // 最后一页时禁用
                              color: controller.currentPage.value <
                                      controller.imageCards.length - 1
                                  ? ShadTheme.of(context).colorScheme.primary
                                  : Colors.grey),
                        ),
                      ],
                    ),
                  )
                : const SizedBox()),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            child: Obx(() => Row(
                  children: [
                    Expanded(
                      child: ShadButton(
                        onPressed: controller.imageCards.isEmpty
                            ? null
                            : () => controller.submitImageOCR(context),
                        child: Text(
                          'anki.ocr.submit'.tr,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ))),
      ],
    );
  }
}
