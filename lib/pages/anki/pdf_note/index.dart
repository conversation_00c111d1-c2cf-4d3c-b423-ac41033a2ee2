import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/index.dart';
import 'components/config.dart';
import 'package:anki_guru/controllers/anki/pdf_note.dart';
import 'package:anki_guru/pages/common.dart';

class PDFNotePage extends StatefulWidget {
  const PDFNotePage({super.key});

  @override
  State<PDFNotePage> createState() => _PDFNotePageState();
}

class _PDFNotePageState extends State<PDFNotePage> {
  final controller = Get.find<PDFNoteController>();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.pdf_note.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('anki.pdf_note.description.function_title'.tr, style: defaultPageTitleStyle),
                Text(
                    'anki.pdf_note.description.main'.tr,
                    style: theme.textTheme.muted),
                const SizedBox(height: 16),
                ShadAlert(
                  icon: Icon(Icons.info),
                  title: Padding(
                    padding: EdgeInsets.only(top: 4.0),
                    child: Text('anki.pdf_note.description.zotero_notice'.tr,
                        style: TextStyle(
                            fontSize: 14.5, fontWeight: FontWeight.normal)),
                  ),
                ),
                const SizedBox(height: 12),
              ],
            ),
            // const PDFNoteForm(),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 2;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        print(controller.tabController.selected);
                      },
                      tabs: [
                        ShadTab(
                          value: 'shortcuts',
                          content: const PDFNoteForm(),
                          width: tabWidth,
                          child: Text('anki.pdf_note.tabs.shortcuts'.tr),
                        ),
                        ShadTab(
                          value: 'config',
                          content: const PDFNoteConfig(),
                          width: tabWidth,
                          child: Text('anki.pdf_note.tabs.config'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
