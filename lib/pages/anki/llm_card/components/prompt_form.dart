import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/pages/anki/llm_card/components/prompt_editor.dart'
    as editor;
import 'package:intl/intl.dart';

class LL<PERSON>romptForm extends GetView<LLMCardPageController> {
  const LLMPromptForm({super.key});

  @override
  Widget build(context) {
    // 无需初始化 promptSortOption，它已在控制器中定义
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 16,
      children: [
        ShadCard(
          padding:
              const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
          footer: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ShadButton(
                  size: ShadButtonSize.lg,
                  onPressed: () {
                    controller.submit(context);
                  },
                  child: Text('anki.llm_card.generate_prompt'.tr),
                ),
              )
            ],
          ),
          child: Obx(() => Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                spacing: 4,
                children: [
                  ShadInputWithValidate(
                    key: ValueKey('prompt-name-input'),
                    label: "anki.llm_card.title".tr,
                    placeholder: "anki.llm_card.input_prompt_title_placeholder".tr,
                    initialValue: controller.promptNameInput.value,
                    onChanged: (value) {
                      controller.promptNameInput.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return "anki.llm_card.title_cannot_empty".tr;
                      }
                      return "";
                    },
                  ),
                  ShadCheckboxGroup(
                    key: ValueKey(
                        'prompt-card-types-${controller.cardTypes.value}'),
                    label: 'anki.llm_card.generate_card_type'.tr,
                    initialValues: controller.cardTypes.toList(),
                    items: controller.cardTypeList,
                    onChanged: (value) {
                      logger.i(value);
                      controller.cardTypes.clear();
                      controller.cardTypes.addAll(value);
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return "anki.llm_card.at_least_one_card_type".tr;
                      }
                      return "";
                    },
                    onValidateError: (error) {},
                  ),
                  if (controller.cardTypes.contains("qa"))
                    ShadSwitchCustom(
                      key: ValueKey(
                          'prompt-is-answer-cloze-${controller.isAnswerCloze.value}'),
                      label: 'anki.llm_card.answer_cloze'.tr,
                      initialValue: controller.isAnswerCloze.value,
                      onChanged: (value) {
                        controller.isAnswerCloze.value = value;
                      },
                    ),

                  ShadCheckboxGroup(
                    key: ValueKey(
                        'prompt-difficulties-${controller.difficulties.value}'),
                    label: 'anki.llm_card.question_difficulty'.tr,
                    initialValues: controller.difficulties.value,
                    items: controller.difficultyList,
                    onChanged: (value) {
                      controller.difficulties.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return "anki.llm_card.at_least_one_difficulty".tr;
                      }
                      return "";
                    },
                  ),
                  // ShadSwitchCustom(
                  //   key: ValueKey(
                  //       'prompt-is-auto-tag-${controller.isAutoTag.value}'),
                  //   label: '自动生成标签',
                  //   initialValue: controller.isAutoTag.value,
                  //   onChanged: (value) {
                  //     controller.isAutoTag.value = value;
                  //   },
                  // ),
                  const SizedBox(height: 8),
                ],
              )),
        ),
        _buildPromptList(),
      ],
    );
  }

  Widget _buildPromptList() {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题部分（始终显示）
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'anki.llm_card.my_prompts'.tr,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(Get.context!).colorScheme.onSurface,
                  ),
                ),
                if (controller.promptObjList.isNotEmpty)
                  ShadButton.link(
                    size: ShadButtonSize.sm,
                    onPressed: () {
                      // 切换排序选项
                      String currentOption = controller.promptSortOption.value;

                      // 依次切换: 按修改时间 -> 按创建时间 -> 按字数 -> 按名称 -> 按修改时间
                      switch (currentOption) {
                        case 'modifyTime':
                          controller.promptSortOption.value = 'createTime';
                          break;
                        case 'createTime':
                          controller.promptSortOption.value = 'wordCount';
                          break;
                        case 'wordCount':
                          controller.promptSortOption.value = 'name';
                          break;
                        case 'name':
                          controller.promptSortOption.value = 'modifyTime';
                          break;
                        default:
                          controller.promptSortOption.value = 'modifyTime';
                      }
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.sort,
                          size: 16,
                          color: Theme.of(Get.context!).colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getSortOptionText(),
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(Get.context!).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          // 提示词内容
          if (controller.promptObjList.isEmpty)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ShadCard(
                padding: const EdgeInsets.all(16),
                child: Center(
                  child: Text('anki.llm_card.no_prompts_message'.tr),
                ),
              ),
            )
          else
            _buildPromptGrid(),
        ],
      );
    });
  }

  // 获取当前排序选项的展示文本
  String _getSortOptionText() {
    switch (controller.promptSortOption.value) {
      case 'createTime':
        return 'anki.llm_card.sort_by_create_time'.tr;
      case 'modifyTime':
        return 'anki.llm_card.sort_by_modify_time'.tr;
      case 'wordCount':
        return 'anki.llm_card.sort_by_word_count'.tr;
      case 'name':
        return 'anki.llm_card.sort_by_name'.tr;
      default:
        return 'anki.llm_card.sort_by_modify_time'.tr;
    }
  }

  Widget _buildPromptGrid() {
    // 使用key来强制在排序方式改变时重建网格
    return Obx(() => LayoutBuilder(
          key: ValueKey('prompt-grid-${controller.promptSortOption.value}'),
          builder: (context, constraints) {
            // 根据屏幕宽度计算列数
            final double screenWidth = constraints.maxWidth;
            final int columnCount =
                screenWidth < 600 ? 1 : (screenWidth < 900 ? 2 : 3);

            // 对提示词按照选定的排序选项排序
            final sortedPrompts =
                List<Map<String, dynamic>>.from(controller.promptObjList);

            final sortOption = controller.promptSortOption.value;

            sortedPrompts.sort((a, b) {
              if (sortOption == 'wordCount') {
                // 按字数排序（从多到少）
                final int countA = a.containsKey('content')
                    ? a['content'].toString().length
                    : 0;
                final int countB = b.containsKey('content')
                    ? b['content'].toString().length
                    : 0;
                return countB.compareTo(countA);
              } else if (sortOption == 'createTime') {
                // 按创建时间排序（从新到旧）
                return b['createTime'].compareTo(a['createTime']);
              } else if (sortOption == 'name') {
                // 按名称排序（字母顺序）
                return a['name'].toString().compareTo(b['name'].toString());
              } else {
                // 默认按修改时间排序（从新到旧）
                // 如果没有modifyTime字段，则使用createTime
                String timeA = a.containsKey('modifyTime')
                    ? a['modifyTime']
                    : a['createTime'];
                String timeB = b.containsKey('modifyTime')
                    ? b['modifyTime']
                    : b['createTime'];
                return timeB.compareTo(timeA);
              }
            });

            // 单列模式使用ListView实现自适应高度
            if (columnCount == 1) {
              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: sortedPrompts.length,
                itemBuilder: (context, index) {
                  final prompt = sortedPrompts[index];

                  // 查找原始索引，用于编辑和删除操作
                  final originalIndex = controller.promptObjList.indexWhere(
                      (p) =>
                          p['name'] == prompt['name'] &&
                          p['createTime'] == prompt['createTime']);

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: _buildPromptCard(prompt, originalIndex,
                        isGridView: false),
                  );
                },
              );
            } else {
              // 多列模式使用GridView保持固定高宽比
              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: columnCount,
                  childAspectRatio: 1.3, // 调整卡片比例，减小底部空白
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: sortedPrompts.length,
                itemBuilder: (context, index) {
                  // 使用排序后的列表
                  final prompt = sortedPrompts[index];

                  // 查找原始索引，用于编辑和删除操作
                  final originalIndex = controller.promptObjList.indexWhere(
                      (p) =>
                          p['name'] == prompt['name'] &&
                          p['createTime'] == prompt['createTime']);

                  return _buildPromptCard(prompt, originalIndex,
                      isGridView: true);
                },
              );
            }
          },
        ));
  }

  Widget _buildPromptCard(Map<String, dynamic> prompt, int originalIndex,
      {bool isGridView = true}) {
    // 使用modifyTime（如果存在），否则使用createTime
    final displayTime = prompt.containsKey('modifyTime')
        ? DateTime.parse(prompt['modifyTime']).toLocal()
        : DateTime.parse(prompt['createTime']).toLocal();
    final formatter = DateFormat('yyyy-MM-dd HH:mm:ss');

    // 计算提示词内容字数
    final String content =
        prompt.containsKey('content') ? prompt['content'] : '';
    final int wordCount = content.length;

    // 文字颜色定义，用于统一字数统计区域的颜色
    final textColor = Theme.of(Get.context!).colorScheme.onSurfaceVariant;

    // 构建卡片内容
    Widget buildCardContent() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: isGridView ? MainAxisSize.max : MainAxisSize.min,
        children: [
          // 顶部区域：标题和操作按钮
          Container(
            padding: const EdgeInsets.all(12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题区
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        prompt['name'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        formatter.format(displayTime),
                        style: TextStyle(
                          fontSize: 12,
                          color: textColor,
                        ),
                      ),
                    ],
                  ),
                ),

                // 操作按钮
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.delete_outline,
                        size: 20,
                        color: Theme.of(Get.context!).colorScheme.onSurface,
                      ),
                      visualDensity: VisualDensity.compact,
                      splashRadius: 18,
                      onPressed: () {
                        showDialog(
                          context: Get.context!,
                          builder: (context) => AlertDialog(
                            title: Text('anki.llm_card.confirm_delete'.tr),
                            content: Text('anki.llm_card.delete_prompt_confirmation'.trParams({'name': prompt['name']})),
                            actions: [
                              ShadButton.outline(
                                onPressed: () => Navigator.pop(context),
                                child: Text('anki.llm_card.cancel'.tr),
                              ),
                              ShadButton.destructive(
                                onPressed: () {
                                  Navigator.pop(context);
                                  controller.deletePrompt(originalIndex);
                                  showToastNotification(null, 'anki.llm_card.delete_success'.tr, 'anki.llm_card.prompt_deleted'.tr,
                                      type: 'success');
                                },
                                child: Text('anki.llm_card.delete'.tr),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.content_copy_outlined,
                        size: 18,
                        color: Theme.of(Get.context!).colorScheme.primary,
                      ),
                      visualDensity: VisualDensity.compact,
                      splashRadius: 18,
                      onPressed: () {
                        // 复制提示词
                        controller.copyPrompt(originalIndex);
                        // 显示成功提示
                        showToastNotification(null, 'anki.llm_card.copy_success'.tr, 'anki.llm_card.prompt_copied'.tr,
                            type: 'success');
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.edit_outlined,
                        size: 20,
                        color: Theme.of(Get.context!).colorScheme.primary,
                      ),
                      visualDensity: VisualDensity.compact,
                      splashRadius: 18,
                      onPressed: () {
                        Get.to(
                            () => editor.PromptEditorPage(
                                  promptIndex: originalIndex,
                                  initialName: prompt['name'],
                                  initialContent: prompt['content'],
                                ),
                            transition: Transition.cupertino);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 字数统计
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              children: [
                Icon(
                  Icons.text_fields_outlined,
                  size: 14,
                  color: textColor,
                ),
                const SizedBox(width: 4),
                Text(
                  '$wordCount ${'anki.llm_card.character_unit'.tr}',
                  style: TextStyle(
                    fontSize: 12,
                    color: textColor,
                  ),
                ),
              ],
            ),
          ),

          // 预览内容 - 根据是否为网格视图使用不同的布局
          if (isGridView)
            Expanded(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
                child: Text(
                  prompt.containsKey('preview')
                      ? (prompt['preview'].length > 120
                          ? '${prompt['preview'].substring(0, 120)}...'
                          : prompt['preview'])
                      : (content.length > 120
                          ? '${content.substring(0, 120)}...'
                          : content),
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(Get.context!)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.8),
                    height: 1.4,
                  ),
                  maxLines: 6,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            )
          else
            // 非网格视图时使用自适应高度
            Padding(
              padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  // 根据内容长度设置不同的最大高度
                  maxHeight: _calculateMaxHeight(content),
                  minHeight: 40, // 最小高度
                ),
                child: Text(
                  prompt.containsKey('preview')
                      ? (prompt['preview'].length > 200
                          ? '${prompt['preview'].substring(0, 200)}...'
                          : prompt['preview'])
                      : (content.length > 200
                          ? '${content.substring(0, 200)}...'
                          : content),
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(Get.context!)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.8),
                    height: 1.4,
                  ),
                  overflow: TextOverflow.ellipsis,
                  // 最大行数根据内容长度动态设置
                  maxLines: _calculateMaxLines(content),
                ),
              ),
            ),
        ],
      );
    }

    return ShadCard(
      padding: EdgeInsets.zero,
      child: InkWell(
        onTap: () {
          Get.to(
              () => editor.PromptEditorPage(
                    promptIndex: originalIndex,
                    initialName: prompt['name'],
                    initialContent: prompt['content'],
                  ),
              transition: Transition.cupertino);
        },
        child: buildCardContent(),
      ),
    );
  }

  // 根据预览文本长度计算最大高度
  double _calculateMaxHeight(String text) {
    final length = text.length;
    // 预设一系列高度值，根据内容长度选择合适的高度
    if (length <= 30) {
      return 60; // 最小高度
    } else if (length <= 100) {
      return 80;
    } else if (length <= 200) {
      return 100;
    } else if (length <= 300) {
      return 120;
    } else {
      return 150; // 最大高度
    }
  }

  // 根据预览文本长度计算最大行数
  int _calculateMaxLines(String text) {
    final length = text.length;
    // 预设一系列行数值，根据内容长度选择合适的行数
    if (length <= 30) {
      return 2;
    } else if (length <= 100) {
      return 3;
    } else if (length <= 200) {
      return 5;
    } else if (length <= 300) {
      return 6;
    } else {
      return 8; // 最大行数
    }
  }
}
