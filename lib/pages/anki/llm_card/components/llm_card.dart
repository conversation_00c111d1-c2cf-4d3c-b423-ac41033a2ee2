import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class LLMCard extends GetView<LLMCardPageController> {
  const LLMCard({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();

    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 4,
            children: [
              ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
                onAddNew: (newDeckName) {
                  // Add to the deck list if not already present
                  if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                    ankiConnectController.parentDeckList.add(newDeckName);
                  }

                  // Set as selected deck
                  controller.parentDeck.value = newDeckName;
                },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              ShadSelectWithSearch(
                key: ValueKey("system-prompt-${controller.promptList.hashCode}"),
                label: 'anki.llm_card.system_prompt'.tr,
                placeholder: 'anki.llm_card.select_system_prompt'.tr,
                searchPlaceholder: 'anki.llm_card.select_system_prompt'.tr,
                isMultiple: false,
                initialValue: [controller.promptName.value],
                options: controller.promptList,
                onChanged: (value) {
                  logger.i(value);
                  controller.promptName.value = value.single;
                },
              ),
              // 模型提供商选择
              ShadSelectCustom(
                key: ValueKey(
                    "provider-${controller.selectedModelProvider.value}"),
                label: "anki.llm_card.model_provider".tr,
                placeholder: "anki.llm_card.select_model_provider".tr,
                initialValue: [controller.selectedModelProvider.value],
                options: controller.modelProviderList,
                onChanged: (values) {
                  controller.updateModelProvider(values.first);
                },
              ),
              ShadSelectCustom(
                key: ValueKey("protocol-${controller.selectedProtocol.value}"),
                label: "anki.llm_card.protocol_type".tr,
                placeholder: "anki.llm_card.select_protocol_type".tr,
                initialValue: [controller.selectedProtocol.value],
                options: controller.protocolList,
                onChanged: (values) {
                  controller.selectedProtocol.value = values.first;
                  if (controller.selectedModelProvider.value == "closeai") {
                    if (controller.selectedProtocol.value == "google") {
                      controller.apiEndpoint.value =
                          "https://api.openai-proxy.org/google/";
                    } else if (controller.selectedProtocol.value ==
                        "anthropic") {
                      controller.apiEndpoint.value =
                          "https://api.openai-proxy.org/anthropic/";
                    } else {
                      controller.apiEndpoint.value =
                          "https://api.openai-proxy.org/v1/";
                    }
                  }
                },
              ),

              // 自定义模型
              if (controller.selectedModelProvider.value == "custom") ...[
                ShadInputWithValidate(
                  key: ValueKey("custom-model-${controller.customModel.value}"),
                  label: "anki.llm_card.custom_model".tr,
                  placeholder: "anki.llm_card.input_custom_model_name".tr,
                  initialValue: controller.customModel.value,
                  onChanged: (value) {
                    controller.customModel.value = value;
                  },
                  onValidate: (value) async {
                    return "";
                  },
                ),
              ] else ...[
                // 模型选择
                ShadSelectWithInput(
                  key: ValueKey("model-${controller.selectedModel.value}"),
                  label: "anki.llm_card.select_model".tr,
                  placeholder: "anki.llm_card.select_model".tr,
                  searchPlaceholder: "anki.llm_card.input_model".tr,
                  initialValue: [controller.selectedModel.value],
                  options: (controller
                          .modelList[controller.selectedModelProvider.value] ??
                      []),
                  onChanged: (values) {
                    controller.updateModel(values.first);
                  },
                  onAddNew: (newModel) {
                    // Add new model to the current provider's model list
                    final currentProvider = controller.selectedModelProvider.value;
                    final newOption = {'value': newModel, 'label': newModel};

                    if (controller.modelList[currentProvider] != null) {
                      if (!controller.modelList[currentProvider]!.any((option) => option['value'] == newModel)) {
                        controller.modelList[currentProvider]!.add(newOption);
                      }
                    } else {
                      controller.modelList[currentProvider] = [newOption];
                    }

                    // Set as selected model
                    controller.updateModel(newModel);
                  },
                ),
              ],
              ShadSwitchCustom(
                key: ValueKey('isReasoning-${controller.isReasoning.value}'),
                label: 'anki.llm_card.reasoning_model'.tr,
                initialValue: controller.isReasoning.value,
                onChanged: (value) {
                  controller.isReasoning.value = value;
                },
              ),

              // API端点 (仅自定义提供商)
              ShadInputWithValidate(
                key: ValueKey("apiEndpoint-${controller.apiEndpoint.value}"),
                label: "anki.llm_card.api_address".tr,
                placeholder: "anki.llm_card.input_api_address_placeholder".tr,
                initialValue: controller.apiEndpoint.value,
                onChanged: (value) {
                  controller.apiEndpoint.value = value;
                },
                onValidate: (value) async {
                  return "";
                },
              ),
              ShadInputPasswordCustom(
                key: ValueKey("apiKey-${controller.apiKey.value}"),
                label: "anki.llm_card.api_key".tr,
                placeholder: 'anki.llm_card.input_api_key_placeholder'.tr,
                initialValue: controller.apiKey.value,
                onChanged: (value) {
                  controller.apiKey.value = value;
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return "anki.llm_card.api_key_cannot_empty".tr;
                  }
                  return "";
                },
                onValidateError: (error) {},
              ),
              ShadSelectWithInput(
                key: ValueKey("tag-${controller.tagsList.hashCode}"),
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.llm_card.select_tags'.tr,
                searchPlaceholder: 'anki.llm_card.input_tags'.tr,
                isMultiple: true,
                initialValue: controller.tags.toList(),
                options: controller.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the global tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tagsList.contains(newTag)) {
                    controller.tagsList.add(newTag);
                  }
                },
              ),
              ShadInputWithValidate(
                  key: ValueKey("page-range-${controller.pageRange.value}"),
                  label: 'anki.llm_card.page_range'.tr,
                  placeholder: 'anki.llm_card.input_page_range_placeholder'.tr,
                  initialValue: controller.pageRange.value,
                  onChanged: (value) {
                    controller.pageRange.value = value;
                  },
                  onValidate: (value) async {
                    if (validatePageRange(value)) {
                      return "";
                    }
                    return 'anki.placeholder.page_range_required'.tr;
                  }),
              ShadInputWithFileSelect(
                key: const ValueKey('input-file-static'),
                title: 'toolbox.common.inputFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const [
                  'txt',
                  'md',
                  'docx',
                  'pdf',
                  'png',
                  'jpg',
                  'jpeg',
                  'xls',
                  'xlsx',
                  'ppt',
                  'pptx'
                ],
                isRequired: true,
                allowMultiple: true,
                enableDragSort: true,
                initialValue: controller.selectedFilePaths,
                onFilesSelected: (files) {
                  logger.i(files);
                  controller.selectedFilePaths.value = files;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
          )),
    );
  }
}
