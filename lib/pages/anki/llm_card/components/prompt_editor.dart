import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:file_picker/file_picker.dart';
import 'package:markdown/markdown.dart' as md;
import 'package:markdown_quill/markdown_quill.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'dart:async';
import 'package:dart_quill_delta/dart_quill_delta.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:url_launcher/url_launcher.dart';

// 显示通知的辅助函数
void showToastNotification(BuildContext context, String title, String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text('$title: $message')),
  );
}

// 自定义 DeltaToMarkdown 转换器
class CustomDeltaToMarkdown extends DeltaToMarkdown {
  @override
  String convert(dynamic input) {
    try {
      // 调用父类方法获取基础Markdown
      final baseMarkdown = super.convert(input);
      if (baseMarkdown == null) {
        logger.w('DeltaToMarkdown返回了null');
        return ''; // 返回空字符串避免后续处理出错
      }

      // 处理下划线文本 - 查找可能已被转换为 __text__ 的下划线文本
      String processedMarkdown = baseMarkdown.replaceAllMapped(
          RegExp(r'__([^_]+)__'), (match) => '<u>${match.group(1)}</u>');

      // 处理水平线嵌入元素
      try {
        if (input != null && input is! String) {
          final ops = input is List ? input : (input.toJson() as List?);

          if (ops != null) {
            // 处理水平线
            for (final op in ops) {
              if (op is Map &&
                  op.containsKey('insert') &&
                  (op['insert'] is Map &&
                      (op['insert'] as Map).containsKey('divider'))) {
                // 找到水平线标记，将[divider]替换为Markdown水平线
                if (processedMarkdown.contains('[divider]')) {
                  processedMarkdown =
                      processedMarkdown.replaceAll('[divider]', '\n\n---\n\n');
                }
              }

              // 处理图片
              if (op is Map &&
                  op.containsKey('insert') &&
                  op['insert'] is Map &&
                  (op['insert'] as Map).containsKey('image')) {
                // 提取图片URL
                final imageUrl = (op['insert'] as Map)['image'];
                if (imageUrl != null && imageUrl is String) {
                  // 检查processedMarkdown中是否已包含这个图片
                  final normalImagePattern = '![]($imageUrl)';
                  final altImagePattern = '![image]($imageUrl)';

                  // 检查是否已包含非转义的图片标记
                  if (!processedMarkdown.contains(normalImagePattern) &&
                      !processedMarkdown.contains(altImagePattern)) {
                    // 检查是否包含转义版本的图片标记
                    final escapedUrl = imageUrl
                        .replaceAll('_', r'\_')
                        .replaceAll('-', r'\-')
                        .replaceAll(' ', r'\ ')
                        .replaceAll('(', r'\(')
                        .replaceAll(')', r'\)');

                    final escapedPattern1 = r'\!\[\]\(' + escapedUrl + r'\)';
                    final escapedPattern2 =
                        r'\!\[image\]\(' + escapedUrl + r'\)';

                    // 如果存在转义版本，替换为非转义版本
                    if (processedMarkdown.contains(escapedPattern1)) {
                      processedMarkdown = processedMarkdown.replaceAll(
                          escapedPattern1, '![]($imageUrl)');
                      logger.d('修复转义图片语法: $escapedPattern1 -> ![]($imageUrl)');
                    } else if (processedMarkdown.contains(escapedPattern2)) {
                      processedMarkdown = processedMarkdown.replaceAll(
                          escapedPattern2, '![image]($imageUrl)');
                      logger.d(
                          '修复转义图片语法: $escapedPattern2 -> ![image]($imageUrl)');
                    } else {
                      // 完全不存在，添加非转义版本
                      processedMarkdown += '\n\n![image]($imageUrl)\n\n';
                      logger.d('添加图片语法: ![image]($imageUrl)');
                    }
                  }
                }
              }
            }
          }
        }
      } catch (e) {
        logger.w('处理特殊元素时出错: $e');
      }

      // 最后的安全检查：如果还有任何转义的图片语法，尝试修复它们
      final escapedImageRegex = RegExp(r'\\!\\\[(.*?)\\\]\\\((.*?)\\\)');
      processedMarkdown =
          processedMarkdown.replaceAllMapped(escapedImageRegex, (match) {
        final alt = match.group(1)?.replaceAll(r'\', '') ?? '';
        final url = match.group(2)?.replaceAll(r'\', '') ?? '';
        logger.d('最终修复转义图片语法: ${match.group(0)} -> ![${alt}](${url})');
        return '![${alt}](${url})';
      });

      return processedMarkdown;
    } catch (e) {
      logger.e('Markdown转换出错: $e');
      // 转换失败时返回空字符串，避免崩溃
      return '';
    }
  }
}

class PromptEditorPage extends StatefulWidget {
  final int promptIndex;
  final String initialName;
  final String initialContent;

  const PromptEditorPage({
    super.key,
    required this.promptIndex,
    required this.initialName,
    required this.initialContent,
  });

  @override
  State<PromptEditorPage> createState() => _PromptEditorPageState();
}

class _PromptEditorPageState extends State<PromptEditorPage> {
  final TextEditingController _nameController = TextEditingController();
  late QuillController _controller;
  final LLMCardPageController _llmController =
      Get.find<LLMCardPageController>();
  bool _isLoading = true;
  final FocusNode _editorFocusNode = FocusNode();
  final FocusNode _keyboardListenerFocusNode = FocusNode();
  bool _isEditingTitle = false;
  final FocusNode _titleFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  final ValueNotifier<Style> _selectionStyle = ValueNotifier<Style>(Style());
  // 用于存储和管理需要释放的资源
  final List<Function()> _disposeCallbacks = [];

  // 添加思维导图相关变量
  final WebviewController _webviewController = Get.find<WebviewController>();
  final RxBool _isMindMapMode = false.obs;
  final RxBool _isMindMapLoading = false.obs;
  final RxString _mindMapData = ''.obs;
  InAppWebViewController? _mindMapWebViewController;
  // 添加防抖动变量
  bool _isToggling = false;
  DateTime _lastToggleTime = DateTime.now();

  // 添加Markdown实时渲染相关变量
  bool _isProcessingMarkdown = false;
  final RegExp _headerRegex = RegExp(r'^(#{1,6})\s+');
  final RegExp _listItemRegex = RegExp(r'^(\s*[-*+]\s+)');
  final RegExp _numberedListRegex = RegExp(r'^(\s*\d+\.\s+)');
  final RegExp _blockquoteRegex = RegExp(r'^(\s*>\s+)');
  final RegExp _codeBlockRegex = RegExp(r'^(\s*```\s*)');
  final RegExp _boldRegex = RegExp(r'\*\*(.*?)\*\*');
  final RegExp _italicRegex = RegExp(r'\*(.*?)\*');
  final RegExp _strikethroughRegex = RegExp(r'~~(.*?)~~');

  // 添加需要在dispose时执行的回调
  void addDisposeCallback(Function() callback) {
    _disposeCallbacks.add(callback);
  }

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.initialName;
    _initQuillController();

    // 添加编辑器焦点监听，用于处理链接点击
    _editorFocusNode.addListener(_handleEditorFocus);

    // 确保键盘监听器可以捕获键盘事件
    _keyboardListenerFocusNode.skipTraversal = true;
    _keyboardListenerFocusNode.canRequestFocus = true;

    // 编辑器焦点变化时保持键盘监听器的焦点
    _editorFocusNode.addListener(() {
      if (_editorFocusNode.hasFocus && !_keyboardListenerFocusNode.hasFocus) {
        _keyboardListenerFocusNode.requestFocus();
      }
    });
  }

  Future<void> _initQuillController() async {
    try {
      // 获取初始内容
      String markdownContent = widget.initialContent;
      Document document;

      // 如果内容为JSON格式（Quill Delta），尝试直接解析
      if (markdownContent.trim().startsWith('[') &&
          markdownContent.trim().endsWith(']')) {
        try {
          final json = jsonDecode(markdownContent);
          document = Document.fromJson(json);
        } catch (e) {
          // JSON解析失败，尝试作为Markdown处理
          document = await _convertMarkdownToDocument(markdownContent);
        }
      } else {
        // 将Markdown内容转换为Quill Document
        document = await _convertMarkdownToDocument(markdownContent);
      }

      // 预加载图片
      _preloadImages(document);

      // 创建控制器并监听变化
      _controller = QuillController(
        document: document,
        selection: const TextSelection.collapsed(offset: 0),
      );

      // 添加选择样式监听
      _controller.addListener(() {
        setState(() {});
      });

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      logger.e('初始化Quill控制器时出错: $e');
      // 回退到空白文档
      try {
        final document = Document();
        document.insert(0, "anki.llm_card.unable_to_load_content".tr);
        _controller = QuillController(
          document: document,
          selection: const TextSelection.collapsed(offset: 0),
        );
        _controller.addListener(() {
          setState(() {});
        });
      } catch (innerError) {
        logger.e('创建空白文档也失败: $innerError');
      }

      setState(() {
        _isLoading = false;
      });
    }
  }

  // 将Markdown转换为Document
  Future<Document> _convertMarkdownToDocument(String markdown) async {
    try {
      logger.d('开始转换Markdown为Document，Markdown长度: ${markdown.length}');
      logger.d(
          'Markdown内容前100字符: ${markdown.substring(0, math.min(markdown.length, 100))}');

      // 尝试直接查找图片标签 - 同时支持转义和非转义的版本
      final escapedImageRegex = RegExp(r'\\!\\\[(.*?)\\\]\\\((.*?)\\\)');
      final normalImageRegex = RegExp(r'!\[(.*?)\]\((.*?)\)');

      final escapedMatches = escapedImageRegex.allMatches(markdown);
      final normalMatches = normalImageRegex.allMatches(markdown);

      final List<Match> allMatches = [...normalMatches];
      // 处理转义版本的匹配
      for (final match in escapedMatches) {
        final alt = match.group(1)?.replaceAll(r'\', '') ?? '';
        final escapedUrl = match.group(2) ?? '';
        // 移除URL中的反斜杠转义
        final url = escapedUrl
            .replaceAll(r'\_', '_')
            .replaceAll(r'\-', '-')
            .replaceAll(r'\ ', ' ');

        // 在这里创建一个替换后的版本用于后续处理
        markdown =
            markdown.replaceAll(match.group(0) ?? '', '![${alt}](${url})');
      }

      // 重新获取匹配（包括替换后的）
      final updatedMatches = normalImageRegex.allMatches(markdown);
      logger.d('直接正则匹配到的图片数量: ${updatedMatches.length}');
      for (final match in updatedMatches) {
        final alt = match.group(1) ?? '';
        final url = match.group(2) ?? '';
        logger.d('发现图片: alt=${alt}, url=${url}');
      }

      // 配置Markdown解析器
      final mdDocument = md.Document(
        encodeHtml: false,
        extensionSet: md.ExtensionSet.gitHubFlavored,
      );

      // 创建转换器
      final converter = MarkdownToDelta(markdownDocument: mdDocument);

      // 转换Markdown为Delta
      final delta = converter.convert(markdown);

      // 检查基础Delta中是否已包含图片
      final operations = delta.toList();
      bool foundImageInDelta = false;
      for (final op in operations) {
        if (op.data is Map) {
          final data = op.data as Map<String, dynamic>;
          if (data.containsKey('image')) {
            logger.d('Delta中已包含图片: ${data['image']}');
            foundImageInDelta = true;
          }
        }
      }

      if (!foundImageInDelta && updatedMatches.isNotEmpty) {
        logger.d('Delta中未找到图片，但Markdown中有${updatedMatches.length}张图片，将进行处理');
      }

      // 特殊处理Markdown中的图片
      final processedDelta = _processImageTags(delta, markdown);
      logger.d(
          '已处理图片标签，处理前Delta长度: ${delta.length}，处理后长度: ${processedDelta.length}');

      // 创建Document并返回
      final document = Document.fromDelta(processedDelta);
      logger.d('已从处理后的Delta创建Document');

      return document;
    } catch (e) {
      logger.e('Markdown转换失败: $e');
      // 转换失败时，创建一个包含原始文本的文档
      final document = Document();
      document.insert(0, markdown);
      return document;
    }
  }

  // 处理Markdown中的图片标签，将其转换为适当的嵌入对象
  Delta _processImageTags(Delta delta, String originalMarkdown) {
    logger.d('开始处理图片标签');

    // 查找所有Markdown图片标记，包括转义版本
    final escapedImageRegex = RegExp(r'\\!\\\[(.*?)\\\]\\\((.*?)\\\)');
    final normalImageRegex = RegExp(r'!\[(.*?)\]\((.*?)\)');

    // 先处理转义版本
    String processedMarkdown = originalMarkdown;
    final escapedMatches = escapedImageRegex.allMatches(originalMarkdown);

    for (final match in escapedMatches) {
      final alt = match.group(1)?.replaceAll(r'\', '') ?? '';
      final escapedUrl = match.group(2) ?? '';
      // 移除URL中的反斜杠转义
      final url = escapedUrl
          .replaceAll(r'\_', '_')
          .replaceAll(r'\-', '-')
          .replaceAll(r'\ ', ' ');
      logger.d('处理转义图片: alt=${alt}, url=${url}');

      // 替换为非转义版本用于后续处理
      processedMarkdown = processedMarkdown.replaceAll(
          match.group(0) ?? '', '![${alt}](${url})');
    }

    // 然后处理所有标准格式图片
    final matches = normalImageRegex.allMatches(processedMarkdown);

    if (matches.isEmpty && escapedMatches.isEmpty) {
      logger.d('Markdown中未找到图片标记');
      return delta; // 没有图片标记，直接返回原始delta
    }

    logger.d(
        '在Markdown中找到${matches.length}个标准图片标记和${escapedMatches.length}个转义图片标记');

    // 检查Delta中是否已包含图片嵌入
    final deltaOperations = delta.toList();
    bool hasImageEmbeds = false;

    for (final op in deltaOperations) {
      if (op.data is Map) {
        final Map<String, dynamic> data = op.data as Map<String, dynamic>;
        if (data.containsKey('image')) {
          hasImageEmbeds = true;
          logger.d('Delta中已包含图片嵌入: ${data['image']}');
          break;
        }
      }
    }

    // 如果Delta中已包含图片嵌入，说明转换器已处理，直接返回
    if (hasImageEmbeds) {
      logger.d('Delta中已存在图片嵌入，无需额外处理');
      return delta;
    }

    logger.d('Delta中不包含图片嵌入，开始处理');

    // 创建一个新的delta用于构建结果
    final newDelta = Delta();
    final operations = delta.toList();
    bool processedImage = false;

    // 查找包含图片标记的文本并替换为图片嵌入
    for (int i = 0; i < operations.length; i++) {
      final op = operations[i];

      if (op.isInsert && op.data is String) {
        final text = op.data as String;
        final imgMatches = normalImageRegex.allMatches(text);

        if (imgMatches.isNotEmpty) {
          // 如果此文本包含图片标记
          processedImage = true;
          int lastIndex = 0;

          for (final match in imgMatches) {
            // 添加图片标记前的文本
            if (match.start > lastIndex) {
              newDelta.insert(text.substring(lastIndex, match.start));
            }

            // 提取图片URL
            final imageUrl = match.group(2) ?? '';
            if (imageUrl.isNotEmpty) {
              // 添加换行符（如果没有的话）
              if (newDelta.length > 0 &&
                  !newDelta.last.data.toString().endsWith('\n')) {
                newDelta.insert('\n');
              }

              // 插入图片嵌入
              newDelta.insert({'image': imageUrl});
              logger.d('成功处理Markdown图片: $imageUrl');

              // 确保图片后有换行符
              newDelta.insert('\n');
            }

            lastIndex = match.end;
          }

          // 添加图片标记后的文本
          if (lastIndex < text.length) {
            newDelta.insert(text.substring(lastIndex));
          }
        } else {
          // 没有图片标记的文本保持不变
          newDelta.insert(text);
        }
      } else {
        // 非文本操作保持不变
        newDelta.push(op);
      }
    }

    // 如果成功处理了图片，返回新构建的Delta
    if (processedImage) {
      logger.d('已处理图片，返回新构建的Delta，长度: ${newDelta.length}');
      return newDelta;
    }

    // 创建副本以避免修改原始Delta
    final resultDelta = Delta.from(delta);

    // 检查最后是否是换行符
    if (operations.isNotEmpty &&
        operations.last.data is String &&
        !(operations.last.data as String).endsWith('\n')) {
      resultDelta.insert('\n');
    }

    // 依次添加所有图片
    for (final match in matches) {
      final url = match.group(2) ?? '';
      if (url.isNotEmpty) {
        resultDelta.insert({'image': url});
        resultDelta.insert('\n');
      }
    }

    return resultDelta;
  }

  // 判断当前是否处于特定类型的列表中
  bool _isInList(String listType) {
    final attrs = _controller.getSelectionStyle().attributes;
    // 确保只有一种列表类型被激活
    return attrs.containsKey(listType) &&
        !(attrs.containsKey(Attribute.ul.key) &&
            attrs.containsKey(Attribute.ol.key));
  }

  @override
  void dispose() {
    _nameController.dispose();
    // 移除编辑器焦点监听
    _editorFocusNode.removeListener(_handleEditorFocus);
    _controller.dispose();
    _titleFocusNode.dispose();
    _keyboardListenerFocusNode.dispose();
    _scrollController.dispose();
    // 执行所有注册的dispose回调
    for (final callback in _disposeCallbacks) {
      callback();
    }
    super.dispose();
  }

  // 处理键盘事件 - 完全重写确保每次只扩展一个字符
  void _handleKeyEvent(KeyEvent event) {
    if (event is KeyDownEvent) {
      // 检查当前焦点是否在标题编辑框
      if (_titleFocusNode.hasFocus && _isEditingTitle) {
        // 标题编辑时不需要特殊处理方向键，使用默认行为
        return;
      }

      // 确保编辑器有焦点
      if (!_editorFocusNode.hasFocus) {
        return;
      }

      // 获取当前选择状态
      final selection = _controller.selection;
      final baseOffset = selection.baseOffset;
      final extentOffset = selection.extentOffset;
      final documentLength = _controller.document.length;
      final isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
      final isControlOrMetaPressed =
          HardwareKeyboard.instance.isControlPressed ||
              HardwareKeyboard.instance.isMetaPressed;

      // ===== 方向键处理 =====
      if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
        // 处理左方向键
        if (selection.isCollapsed) {
          // 光标未选中任何文本
          if (baseOffset > 0) {
            if (isShiftPressed) {
              // Shift+左方向，从当前光标开始选择一个字符
              _controller.updateSelection(
                TextSelection(
                  baseOffset: baseOffset,
                  extentOffset: baseOffset - 1,
                ),
                ChangeSource.local,
              );
            } else {
              // 普通左移，向左移动一个字符
              _controller.updateSelection(
                TextSelection.collapsed(offset: baseOffset - 1),
                ChangeSource.local,
              );
            }
          }
        } else {
          // 已有选中的文本
          if (isShiftPressed) {
            // 当前有选择且按住Shift
            // 判断光标实际活动的一端（extent）
            if (baseOffset == selection.start) {
              // 如果基准点在左侧，那么扩展右侧
              // 但这里是向左移动，所以实际是缩小选区
              final newExtent = extentOffset - 1;
              if (newExtent >= baseOffset) {
                // 缩小选区但仍保持选择
                _controller.updateSelection(
                  TextSelection(
                    baseOffset: baseOffset,
                    extentOffset: newExtent,
                  ),
                  ChangeSource.local,
                );
              } else {
                // 选区方向反转
                _controller.updateSelection(
                  TextSelection(
                    baseOffset: baseOffset,
                    extentOffset: baseOffset - 1,
                  ),
                  ChangeSource.local,
                );
              }
            } else {
              // 如果基准点在右侧，那么扩展左侧
              final newExtent = extentOffset - 1;
              if (newExtent >= 0) {
                _controller.updateSelection(
                  TextSelection(
                    baseOffset: baseOffset,
                    extentOffset: newExtent,
                  ),
                  ChangeSource.local,
                );
              }
            }
          } else {
            // 不按Shift，光标移动到选择区域起始位置
            _controller.updateSelection(
              TextSelection.collapsed(offset: selection.start),
              ChangeSource.local,
            );
          }
        }
      } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
        // 处理右方向键
        if (selection.isCollapsed) {
          // 光标未选中任何文本
          if (baseOffset < documentLength) {
            if (isShiftPressed) {
              // Shift+右方向，从当前光标开始选择一个字符
              _controller.updateSelection(
                TextSelection(
                  baseOffset: baseOffset,
                  extentOffset: baseOffset + 1,
                ),
                ChangeSource.local,
              );
            } else {
              // 普通右移，向右移动一个字符
              _controller.updateSelection(
                TextSelection.collapsed(offset: baseOffset + 1),
                ChangeSource.local,
              );
            }
          }
        } else {
          // 已有选中的文本
          if (isShiftPressed) {
            // 当前有选择且按住Shift
            if (baseOffset == selection.start) {
              // 基准点在起始位置(左侧)，向右扩展
              if (extentOffset < documentLength) {
                _controller.updateSelection(
                  TextSelection(
                    baseOffset: baseOffset,
                    extentOffset: extentOffset + 1,
                  ),
                  ChangeSource.local,
                );
              }
            } else {
              // 基准点在结束位置(右侧)，向左缩小
              if (extentOffset > 0 && extentOffset > baseOffset - 1) {
                _controller.updateSelection(
                  TextSelection(
                    baseOffset: baseOffset,
                    extentOffset: extentOffset - 1,
                  ),
                  ChangeSource.local,
                );
              } else if (extentOffset == baseOffset - 1) {
                // 如果选区即将反转方向
                _controller.updateSelection(
                  TextSelection(
                    baseOffset: baseOffset,
                    extentOffset: baseOffset + 1,
                  ),
                  ChangeSource.local,
                );
              }
            }
          } else {
            // 不按Shift，光标移动到选择区域结束位置
            _controller.updateSelection(
              TextSelection.collapsed(offset: selection.end),
              ChangeSource.local,
            );
          }
        }
      } else if (event.logicalKey == LogicalKeyboardKey.home) {
        // 找到当前行的开始位置
        final text =
            _controller.document.getPlainText(0, selection.extentOffset);
        final lastNewlineIndex = text.lastIndexOf('\n');
        final lineStart = lastNewlineIndex >= 0 ? lastNewlineIndex + 1 : 0;

        if (isShiftPressed) {
          // Shift+Home，选择到行首
          _controller.updateSelection(
            TextSelection(
              baseOffset: selection.isCollapsed
                  ? selection.baseOffset
                  : selection.baseOffset,
              extentOffset: lineStart,
            ),
            ChangeSource.local,
          );
        } else {
          // 普通Home，移动到行首
          _controller.updateSelection(
            TextSelection.collapsed(offset: lineStart),
            ChangeSource.local,
          );
        }
      } else if (event.logicalKey == LogicalKeyboardKey.end) {
        // 找到当前行的结束位置
        final offset = selection.extentOffset;
        final text =
            _controller.document.getPlainText(offset, documentLength - offset);
        final newlineIndex = text.indexOf('\n');
        final lineEnd =
            newlineIndex >= 0 ? offset + newlineIndex : documentLength;

        if (isShiftPressed) {
          // Shift+End，选择到行尾
          _controller.updateSelection(
            TextSelection(
              baseOffset: selection.isCollapsed
                  ? selection.baseOffset
                  : selection.baseOffset,
              extentOffset: lineEnd,
            ),
            ChangeSource.local,
          );
        } else {
          // 普通End，移动到行尾
          _controller.updateSelection(
            TextSelection.collapsed(offset: lineEnd),
            ChangeSource.local,
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _handleBackPressed,
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: _isEditingTitle
              ? SizedBox(
                  width: MediaQuery.of(context).size.width * 0.6,
                  child: FocusScope(
                    child: KeyboardListener(
                      focusNode: FocusNode(),
                      onKeyEvent: (KeyEvent event) {
                        if (event is KeyDownEvent) {
                          if (event.logicalKey == LogicalKeyboardKey.escape) {
                            setState(() {
                              _isEditingTitle = false;
                            });
                          } else if (event.logicalKey ==
                                  LogicalKeyboardKey.enter ||
                              event.logicalKey ==
                                  LogicalKeyboardKey.numpadEnter) {
                            setState(() {
                              _isEditingTitle = false;
                            });
                            FocusScope.of(context).unfocus();
                          }
                          // 其他按键保持默认行为
                        }
                      },
                      child: TextField(
                        controller: _nameController,
                        focusNode: _titleFocusNode,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.0)),
                            borderSide: BorderSide(color: Colors.transparent),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.0)),
                            borderSide: BorderSide(color: Colors.transparent),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.0)),
                            borderSide: BorderSide(
                                color:
                                    ShadTheme.of(context).colorScheme.primary,
                                width: 1.0),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 8.0, vertical: 0),
                          isDense: true,
                          filled: true,
                          fillColor: ShadTheme.of(context).colorScheme.muted,
                        ),
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: ShadTheme.of(context).colorScheme.foreground,
                        ),
                        textAlign: TextAlign.center,
                        onSubmitted: (_) {
                          setState(() {
                            _isEditingTitle = false;
                          });
                        },
                      ),
                    ),
                  ),
                )
              : GestureDetector(
                  onDoubleTap: () {
                    setState(() {
                      _isEditingTitle = true;
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _titleFocusNode.requestFocus();
                        // 选择全部文本，便于直接覆盖输入
                        _nameController.selection = TextSelection(
                            baseOffset: 0,
                            extentOffset: _nameController.text.length);
                      });
                    });
                  },
                  child: Text(
                    _nameController.text,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
          actions: [
            TextButton(
              onPressed: _savePrompt,
              child: Text('anki.llm_card.save'.tr),
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SafeArea(
                child: Column(
                  children: [
                    const Divider(height: 1),
                    // 使用QuillSimpleToolbar实现工具栏
                    Container(
                      height: 55,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            // 标题
                            QuillToolbarSelectHeaderStyleDropdownButton(
                              controller: _controller,
                              options:
                                  QuillToolbarSelectHeaderStyleDropdownButtonOptions(
                                // 包含从h1到h6的所有标题级别
                                attributes: [
                                  Attribute.header,
                                  Attribute.h1,
                                  Attribute.h2,
                                  Attribute.h3,
                                  Attribute.h4,
                                  Attribute.h5,
                                  Attribute.h6,
                                ],
                              ),
                            ),
                            // 加粗 - 自定义样式
                            IconButton(
                              icon: Icon(
                                Icons.format_bold,
                                color: _controller
                                        .getSelectionStyle()
                                        .attributes
                                        .containsKey(Attribute.bold.key)
                                    ? ShadTheme.of(context).colorScheme.primary
                                    : ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground,
                              ),
                              onPressed: () {
                                // 检查当前选中文本是否已应用加粗样式
                                final hasAttribute = _controller
                                    .getSelectionStyle()
                                    .attributes
                                    .containsKey(Attribute.bold.key);
                                if (hasAttribute) {
                                  // 如果已有加粗样式，则移除
                                  _controller.formatSelection(
                                      Attribute.clone(Attribute.bold, null));
                                } else {
                                  // 如果没有加粗样式，则添加
                                  _controller.formatSelection(Attribute.bold);
                                }

                                // 确保编辑器在操作后重新获得焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.bold'.tr,
                            ),
                            // 斜体 - 自定义样式
                            IconButton(
                              icon: Icon(
                                Icons.format_italic,
                                color: _controller
                                        .getSelectionStyle()
                                        .attributes
                                        .containsKey(Attribute.italic.key)
                                    ? ShadTheme.of(context).colorScheme.primary
                                    : ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground,
                              ),
                              onPressed: () {
                                // 检查当前选中文本是否已应用斜体样式
                                final hasAttribute = _controller
                                    .getSelectionStyle()
                                    .attributes
                                    .containsKey(Attribute.italic.key);
                                if (hasAttribute) {
                                  // 如果已有斜体样式，则移除
                                  _controller.formatSelection(
                                      Attribute.clone(Attribute.italic, null));
                                } else {
                                  // 如果没有斜体样式，则添加
                                  _controller.formatSelection(Attribute.italic);
                                }

                                // 确保编辑器在操作后重新获得焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.italic'.tr,
                            ),
                            // 下划线 - 自定义样式
                            IconButton(
                              icon: Icon(
                                Icons.format_underline,
                                color: _controller
                                        .getSelectionStyle()
                                        .attributes
                                        .containsKey(Attribute.underline.key)
                                    ? ShadTheme.of(context).colorScheme.primary
                                    : ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground,
                              ),
                              onPressed: () {
                                // 检查当前选中文本是否已应用下划线样式
                                final hasAttribute = _controller
                                    .getSelectionStyle()
                                    .attributes
                                    .containsKey(Attribute.underline.key);
                                if (hasAttribute) {
                                  // 如果已有下划线样式，则移除
                                  _controller.formatSelection(Attribute.clone(
                                      Attribute.underline, null));
                                } else {
                                  // 如果没有下划线样式，则添加
                                  _controller
                                      .formatSelection(Attribute.underline);
                                }

                                // 确保编辑器在操作后重新获得焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.underline'.tr,
                            ),
                            // 删除线 - 自定义样式
                            IconButton(
                              icon: Icon(
                                Icons.format_strikethrough,
                                color: _controller
                                        .getSelectionStyle()
                                        .attributes
                                        .containsKey(
                                            Attribute.strikeThrough.key)
                                    ? ShadTheme.of(context).colorScheme.primary
                                    : ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground,
                              ),
                              onPressed: () {
                                // 检查当前选中文本是否已应用删除线样式
                                final hasAttribute = _controller
                                    .getSelectionStyle()
                                    .attributes
                                    .containsKey(Attribute.strikeThrough.key);
                                if (hasAttribute) {
                                  // 如果已有删除线样式，则移除
                                  _controller.formatSelection(Attribute.clone(
                                      Attribute.strikeThrough, null));
                                } else {
                                  // 如果没有删除线样式，则添加
                                  _controller
                                      .formatSelection(Attribute.strikeThrough);
                                }

                                // 确保编辑器在操作后重新获得焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.strikethrough'.tr,
                            ),
                            VerticalDivider(
                              indent: 12,
                              endIndent: 12,
                              color: ShadTheme.of(context).colorScheme.border,
                            ),
                            // 有序列表 - 自定义样式
                            IconButton(
                              icon: Icon(
                                Icons.format_list_numbered,
                                color: _isInList(Attribute.ol.key)
                                    ? ShadTheme.of(context).colorScheme.primary
                                    : ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground,
                              ),
                              onPressed: () {
                                // 检查当前选中文本是否已应用有序列表
                                final hasOl = _controller
                                    .getSelectionStyle()
                                    .attributes
                                    .containsKey(Attribute.ol.key);
                                final hasUl = _controller
                                    .getSelectionStyle()
                                    .attributes
                                    .containsKey(Attribute.ul.key);

                                if (hasOl) {
                                  // 如果已有有序列表样式，则移除
                                  _controller.formatSelection(
                                      Attribute.clone(Attribute.ol, null));
                                } else {
                                  // 如果已有无序列表样式，先移除
                                  if (hasUl) {
                                    _controller.formatSelection(
                                        Attribute.clone(Attribute.ul, null));
                                  }
                                  // 添加有序列表样式
                                  _controller.formatSelection(Attribute.ol);
                                }

                                // 确保编辑器在操作后重新获得焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.ordered_list'.tr,
                            ),
                            // 无序列表 - 自定义样式
                            IconButton(
                              icon: Icon(
                                Icons.format_list_bulleted,
                                color: _isInList(Attribute.ul.key)
                                    ? ShadTheme.of(context).colorScheme.primary
                                    : ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground,
                              ),
                              onPressed: () {
                                // 检查当前选中文本是否已应用无序列表
                                final hasUl = _controller
                                    .getSelectionStyle()
                                    .attributes
                                    .containsKey(Attribute.ul.key);
                                final hasOl = _controller
                                    .getSelectionStyle()
                                    .attributes
                                    .containsKey(Attribute.ol.key);

                                if (hasUl) {
                                  // 如果已有无序列表样式，则移除
                                  _controller.formatSelection(
                                      Attribute.clone(Attribute.ul, null));
                                } else {
                                  // 如果已有有序列表样式，先移除
                                  if (hasOl) {
                                    _controller.formatSelection(
                                        Attribute.clone(Attribute.ol, null));
                                  }
                                  // 添加无序列表样式
                                  _controller.formatSelection(Attribute.ul);
                                }

                                // 确保编辑器在操作后重新获得焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.unordered_list'.tr,
                            ),
                            VerticalDivider(
                              indent: 12,
                              endIndent: 12,
                              color: ShadTheme.of(context).colorScheme.border,
                            ),
                            // 代码块 - 自定义样式
                            IconButton(
                              icon: Icon(
                                Icons.code,
                                color: _controller
                                        .getSelectionStyle()
                                        .attributes
                                        .containsKey(Attribute.codeBlock.key)
                                    ? ShadTheme.of(context).colorScheme.primary
                                    : ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground,
                              ),
                              onPressed: () {
                                // 检查当前选中文本是否已应用代码块
                                final hasAttribute = _controller
                                    .getSelectionStyle()
                                    .attributes
                                    .containsKey(Attribute.codeBlock.key);
                                if (hasAttribute) {
                                  // 如果已有代码块样式，则移除
                                  _controller.formatSelection(Attribute.clone(
                                      Attribute.codeBlock, null));
                                } else {
                                  // 如果已有引用样式，先移除
                                  if (_controller
                                      .getSelectionStyle()
                                      .attributes
                                      .containsKey(Attribute.blockQuote.key)) {
                                    _controller.formatSelection(Attribute.clone(
                                        Attribute.blockQuote, null));
                                  }
                                  // 添加代码块样式
                                  _controller
                                      .formatSelection(Attribute.codeBlock);
                                }

                                // 确保编辑器在操作后重新获得焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.code_block'.tr,
                            ),
                            // 引用 - 自定义样式
                            IconButton(
                              icon: Icon(
                                Icons.format_quote,
                                color: _controller
                                        .getSelectionStyle()
                                        .attributes
                                        .containsKey(Attribute.blockQuote.key)
                                    ? ShadTheme.of(context).colorScheme.primary
                                    : ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground,
                              ),
                              onPressed: () {
                                // 检查当前选中文本是否已应用引用
                                final hasAttribute = _controller
                                    .getSelectionStyle()
                                    .attributes
                                    .containsKey(Attribute.blockQuote.key);
                                if (hasAttribute) {
                                  // 如果已有引用样式，则移除
                                  _controller.formatSelection(Attribute.clone(
                                      Attribute.blockQuote, null));
                                } else {
                                  // 如果已有代码块样式，先移除
                                  if (_controller
                                      .getSelectionStyle()
                                      .attributes
                                      .containsKey(Attribute.codeBlock.key)) {
                                    _controller.formatSelection(Attribute.clone(
                                        Attribute.codeBlock, null));
                                  }
                                  // 添加引用样式
                                  _controller
                                      .formatSelection(Attribute.blockQuote);
                                }

                                // 确保编辑器在操作后重新获得焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.quote'.tr,
                            ),
                            // 链接
                            IconButton(
                              icon: Icon(
                                Icons.link,
                                color: _controller
                                        .getSelectionStyle()
                                        .attributes
                                        .containsKey(Attribute.link.key)
                                    ? ShadTheme.of(context).colorScheme.primary
                                    : ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground,
                              ),
                              onPressed: () {
                                _showLinkDialog(context);
                                // 对话框关闭后恢复焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.hyperlink'.tr,
                            ),
                            // 图片
                            IconButton(
                              icon: Icon(
                                Icons.image,
                                color: ShadTheme.of(context)
                                    .colorScheme
                                    .mutedForeground,
                              ),
                              onPressed: () {
                                _showImageDialog(context);
                                // 对话框关闭后恢复焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.add_image'.tr,
                            ),
                            // 分隔线
                            IconButton(
                              icon: Icon(
                                Icons.horizontal_rule,
                                color: ShadTheme.of(context)
                                    .colorScheme
                                    .mutedForeground,
                              ),
                              onPressed: () {
                                _insertHorizontalRule();
                                // 操作后恢复焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                              tooltip: 'anki.llm_card.add_divider'.tr,
                            ),
                            VerticalDivider(
                              indent: 12,
                              endIndent: 12,
                              color: ShadTheme.of(context).colorScheme.border,
                            ),
                            // 撤销
                            IconButton(
                              icon: Icon(
                                Icons.undo,
                                color: _controller.document.hasUndo
                                    ? ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground
                                    : ShadTheme.of(context).colorScheme.muted,
                              ),
                              onPressed: _controller.document.hasUndo
                                  ? () {
                                      _controller.undo();
                                      // 操作后恢复焦点
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) {
                                        _editorFocusNode.requestFocus();
                                      });
                                    }
                                  : null,
                              tooltip: '撤销',
                            ),
                            // 恢复
                            IconButton(
                              icon: Icon(
                                Icons.redo,
                                color: _controller.document.hasRedo
                                    ? ShadTheme.of(context)
                                        .colorScheme
                                        .mutedForeground
                                    : ShadTheme.of(context).colorScheme.muted,
                              ),
                              onPressed: _controller.document.hasRedo
                                  ? () {
                                      _controller.redo();
                                      // 操作后恢复焦点
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) {
                                        _editorFocusNode.requestFocus();
                                      });
                                    }
                                  : null,
                              tooltip: '恢复',
                            ),
                            VerticalDivider(
                              indent: 12,
                              endIndent: 12,
                              color: ShadTheme.of(context).colorScheme.border,
                            ),
                            // 导入按钮
                            IconButton(
                              icon: const Icon(LucideIcons.clipboardCopy),
                              tooltip: '导入',
                              onPressed: () {
                                _importMd();
                                // 导入操作后恢复焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                            ),
                            // 导出按钮
                            IconButton(
                              icon: const Icon(LucideIcons.clipboardPaste),
                              tooltip: '导出',
                              onPressed: () {
                                _exportAsMd();
                                // 导出操作后恢复焦点
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  _editorFocusNode.requestFocus();
                                });
                              },
                            ),
                            // 添加思维导图按钮 - 移到导出按钮前面
                            Obx(() => IconButton(
                                  icon: Icon(
                                    Icons.account_tree,
                                    color: _isMindMapMode.value
                                        ? ShadTheme.of(context)
                                            .colorScheme
                                            .primary
                                        : ShadTheme.of(context)
                                            .colorScheme
                                            .mutedForeground,
                                  ),
                                  tooltip: '思维导图',
                                  onPressed: () {
                                    _toggleMindMapMode();
                                  },
                                )),
                          ],
                        ),
                      ),
                    ),
                    const Divider(height: 1),
                    // 编辑器区域
                    Expanded(
                      child: Obx(() {
                        // 更新思维导图按钮颜色状态
                        return _isMindMapMode.value
                            ? _buildMindMapView()
                            : _buildEditorView();
                      }),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  // 构建编辑器视图
  Widget _buildEditorView() {
    return Container(
      decoration: BoxDecoration(
        color: ShadTheme.of(context).colorScheme.background,
        border: Border(
          left: BorderSide(color: ShadTheme.of(context).colorScheme.border),
          right: BorderSide(color: ShadTheme.of(context).colorScheme.border),
          bottom: BorderSide(color: ShadTheme.of(context).colorScheme.border),
        ),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      padding: const EdgeInsets.all(16),
      child: Theme(
        // 设置文本选择颜色
        data: Theme.of(context).copyWith(
          textSelectionTheme: TextSelectionThemeData(
            selectionColor:
                ShadTheme.of(context).colorScheme.primary.withAlpha(76),
            cursorColor: ShadTheme.of(context).colorScheme.primary,
            selectionHandleColor: ShadTheme.of(context).colorScheme.primary,
          ),
        ),
        // 使用 KeyboardListener 替代废弃的 RawKeyboardListener
        child: KeyboardListener(
          focusNode: _keyboardListenerFocusNode,
          onKeyEvent: _handleKeyEvent,
          autofocus: true,
          child: QuillEditor(
            controller: _controller,
            focusNode: _editorFocusNode,
            scrollController: _scrollController,
            config: QuillEditorConfig(
              placeholder:
                  '在此编辑提示词...\n\n提示：\n1. 使用三个反引号```创建代码块\n2. 在反引号后添加语言名称，例如：```python\n3. 每行前可添加行号，例如：1. print("hello")',
              padding: EdgeInsets.zero,
              autoFocus: false,
              expands: true,
              keyboardAppearance: Brightness.light,
              enableInteractiveSelection: true,
              showCursor: true,
              scrollable: true,
              embedBuilders: [
                // 添加图片嵌入构建器
                ImageEmbedBuilder(),
                // 添加水平线嵌入构建器
                HorizontalRuleEmbedBuilder(),
              ],
              // 添加自定义样式，特别处理中文字体问题
              customStyles: DefaultStyles(
                // 设置段落文本样式
                paragraph: DefaultTextBlockStyle(
                  TextStyle(
                    fontSize: 16,
                    height: 1.5,
                    color: ShadTheme.of(context).colorScheme.foreground,
                    // backgroundColor:
                    //     ShadTheme.of(context).colorScheme.background,
                    fontFamily: 'SourceHanSansSC', // 使用自定义字体
                  ),
                  const HorizontalSpacing(0, 0),
                  const VerticalSpacing(8, 0),
                  const VerticalSpacing(0, 0),
                  null,
                ),
                // 设置加粗文本样式，解决中文加粗镂空问题
                bold: TextStyle(
                  fontWeight: FontWeight.w600, // 使用w600而不是bold，减少镂空
                  fontFamily: 'SourceHanSansSC',
                  color: ShadTheme.of(context).colorScheme.foreground,
                  backgroundColor: ShadTheme.of(context).colorScheme.background,
                ),
                // 设置链接文本样式，使用鲜明的颜色和下划线
                link: TextStyle(
                  color: ShadTheme.of(context).colorScheme.primary, // 使用主题主色
                  backgroundColor: ShadTheme.of(context).colorScheme.background,
                  fontSize: 16,
                  decoration: TextDecoration.underline,
                  decorationColor: ShadTheme.of(context).colorScheme.primary,
                  decorationThickness: 1.5, // 增加下划线粗细
                ),
                // 设置内联代码样式
                inlineCode: InlineCodeStyle(
                    style: TextStyle(
                  color: ShadTheme.of(context).colorScheme.primary, // 使用主题主色
                  backgroundColor: ShadTheme.of(context).colorScheme.background,
                  fontSize: 16,    
                ),
                backgroundColor: ShadTheme.of(context).colorScheme.muted,
                ),
                // 设置代码段样式
                code: DefaultTextBlockStyle(
                  TextStyle(
                    fontSize: 14,
                    height: 1.3,
                    fontFamily: 'Menlo',
                    fontFamilyFallback: ['Monaco', 'Consolas', 'monospace'],
                    color: ShadTheme.of(context).colorScheme.primary,
                  ),
                  const HorizontalSpacing(8, 8),
                  const VerticalSpacing(8, 0),
                  const VerticalSpacing(0, 0),
                  BoxDecoration(
                    color: ShadTheme.of(context).colorScheme.muted,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // 标题样式
                h1: DefaultTextBlockStyle(
                  TextStyle(
                    fontSize: 28,
                    height: 1.3,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'SourceHanSansSC',
                    color: ShadTheme.of(context).colorScheme.foreground,
                  ),
                  const HorizontalSpacing(0, 0),
                  const VerticalSpacing(16, 0),
                  const VerticalSpacing(0, 0),
                  null,
                ),
                h2: DefaultTextBlockStyle(
                  TextStyle(
                    fontSize: 24,
                    height: 1.3,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'SourceHanSansSC',
                    color: ShadTheme.of(context).colorScheme.foreground,
                  ),
                  const HorizontalSpacing(0, 0),
                  const VerticalSpacing(12, 0),
                  const VerticalSpacing(0, 0),
                  null,
                ),
                h3: DefaultTextBlockStyle(
                  TextStyle(
                    fontSize: 20,
                    height: 1.3,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'SourceHanSansSC',
                    color: ShadTheme.of(context).colorScheme.foreground,
                  ),
                  const HorizontalSpacing(0, 0),
                  const VerticalSpacing(8, 0),
                  const VerticalSpacing(0, 0),
                  null,
                ),
                // 添加h4标题样式
                h4: DefaultTextBlockStyle(
                  TextStyle(
                    fontSize: 18,
                    height: 1.3,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'SourceHanSansSC',
                    color: ShadTheme.of(context).colorScheme.foreground,
                  ),
                  const HorizontalSpacing(0, 0),
                  const VerticalSpacing(8, 0),
                  const VerticalSpacing(0, 0),
                  null,
                ),
                // 添加h5标题样式
                h5: DefaultTextBlockStyle(
                  TextStyle(
                    fontSize: 16,
                    height: 1.3,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'SourceHanSansSC',
                    color: ShadTheme.of(context).colorScheme.foreground,
                  ),
                  const HorizontalSpacing(0, 0),
                  const VerticalSpacing(6, 0),
                  const VerticalSpacing(0, 0),
                  null,
                ),
                // 添加h6标题样式
                h6: DefaultTextBlockStyle(
                  TextStyle(
                    fontSize: 14,
                    height: 1.3,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'SourceHanSansSC',
                    color: ShadTheme.of(context).colorScheme.mutedForeground,
                  ),
                  const HorizontalSpacing(0, 0),
                  const VerticalSpacing(6, 0),
                  const VerticalSpacing(0, 0),
                  null,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 构建思维导图视图
  Widget _buildMindMapView() {
    return Obx(
      () => AnimatedSwitcher(
        duration: Duration(milliseconds: 300),
        child: _isMindMapLoading.value
            ? Center(
                key: ValueKey('loading'),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('正在生成思维导图...', style: TextStyle(fontSize: 16)),
                  ],
                ))
            : _mindMapData.value.isEmpty
                ? Center(
                    key: ValueKey('empty'),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.error_outline,
                            size: 64,
                            color: ShadTheme.of(context)
                                .colorScheme
                                .mutedForeground),
                        SizedBox(height: 16),
                        Text('无法生成思维导图，请检查Markdown格式',
                            style: TextStyle(fontSize: 16)),
                        SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () => _toggleMindMapMode(),
                          child: Text('返回编辑器'),
                        ),
                      ],
                    ),
                  )
                : Container(
                    key: ValueKey('mindmap'),
                    decoration: BoxDecoration(
                      color: ShadTheme.of(context).colorScheme.background,
                      border: Border(
                        left: BorderSide(
                            color: ShadTheme.of(context).colorScheme.border),
                        right: BorderSide(
                            color: ShadTheme.of(context).colorScheme.border),
                        bottom: BorderSide(
                            color: ShadTheme.of(context).colorScheme.border),
                      ),
                    ),
                    margin:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                    child: InAppWebView(
                      initialUrlRequest: URLRequest(url: WebUri("about:blank")),
                      initialSettings: InAppWebViewSettings(
                        javaScriptEnabled: true,
                        useHybridComposition: true,
                      ),
                      onWebViewCreated: (controller) async {
                        _mindMapWebViewController = controller;
                        logger.i('思维导图WebView已创建');
                        _renderMindMap();
                      },
                      onConsoleMessage: (controller, consoleMessage) {
                        logger.i('WebView控制台: ${consoleMessage.message}');
                      },
                      onLoadError: (controller, url, code, message) {
                        logger.e('WebView加载错误: $message ($code)');
                      },
                    ),
                  ),
      ),
    );
  }

  // 渲染思维导图
  Future<void> _renderMindMap() async {
    // 检查WebView控制器是否有效
    if (_mindMapWebViewController == null || _mindMapData.value.isEmpty) {
      return;
    }

    try {
      // 解析JSON数据以获取Markdown内容
      final jsonData = jsonDecode(_mindMapData.value);
      final markdownContent = jsonData['markdown'] as String? ?? '';

      // 构建完整的HTML，包含所有必要的JavaScript库
      final htmlContent = '''
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
          <title>思维导图</title>
          <style>
            html, body { 
              margin: 0; 
              padding: 0; 
              width: 100%; 
              height: 100%; 
              overflow: hidden; 
              background: ${ShadTheme.of(context).colorScheme.background.value.toRadixString(16).padLeft(8, '0').substring(2)};
            }
            #container {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            }
            #markmap {
              width: 100%;
              height: 100%;
              display: block;
              overflow: visible;
            }
            .error-message {
              color: red;
              padding: 20px;
              font-family: Arial, sans-serif;
              text-align: center;
            }
          </style>
          <!-- 内联D3.js库 -->
          <script src="https://cdn.jsdelivr.net/npm/d3@6.7.0"></script>
          <!-- 内联Markmap库 -->
          <script src="https://cdn.jsdelivr.net/npm/markmap-view@0.14.4"></script>
          <script src="https://cdn.jsdelivr.net/npm/markmap-lib@0.14.4"></script>
        </head>
        <body>
          <div id="container">
            <svg id="markmap"></svg>
          </div>
          
          <script>
            // 等待页面加载完成
            window.onload = function() {
              try {
                console.log('页面加载完成，准备渲染思维导图');
                
                // 获取Markdown内容
                const markdownContent = `${markdownContent.replaceAll("`", "\\`").replaceAll("\$", "\\\$")}`;
                console.log('Markdown内容长度:', markdownContent.length);
                
                // 检查markmap库是否加载
                if (typeof markmap === 'undefined' || typeof markmap.Transformer === 'undefined') {
                  throw new Error('markmap库未加载');
                }
                
                // 创建转换器
                const transformer = new markmap.Transformer();
                console.log('转换器创建成功');
                
                // 转换Markdown为思维导图数据
                const { root, features } = transformer.transform(markdownContent);
                console.log('Markdown转换成功，开始渲染');
                
                // 创建思维导图
                const { Markmap } = markmap;
                const svg = document.getElementById('markmap');
                
                if (!svg) {
                  throw new Error('未找到SVG元素');
                }
                
                // 初始缩放比例
                let scale = 1;
                
                // 添加拖动功能
                window.isDragging = false;
                let startX, startY;
                let translateX = 0, translateY = 0;
                
                // 鼠标按下事件
                svg.addEventListener('mousedown', (e) => {
                  window.isDragging = true;
                  startX = e.clientX;
                  startY = e.clientY;
                  svg.style.cursor = 'grabbing';
                  console.log('Mouse down: isDragging =', window.isDragging);
                });
                
                // 鼠标移动事件
                document.addEventListener('mousemove', (e) => {
                  if (!window.isDragging) return;
                  
                  const dx = e.clientX - startX;
                  const dy = e.clientY - startY;
                  
                  translateX += dx;
                  translateY += dy;
                  
                  startX = e.clientX;
                  startY = e.clientY;
                  
                  const g = svg.querySelector('g');
                  if (g) {
                    g.style.transform = `translate(\${translateX}px, \${translateY}px) scale(\${scale})`;
                  }
                });
                
                // 鼠标释放事件
                document.addEventListener('mouseup', () => {
                  if (window.isDragging) {
                    window.isDragging = false;
                    svg.style.cursor = 'grab';
                    console.log('Mouse up: isDragging =', window.isDragging);
                  }
                });
                
                // 鼠标离开事件
                svg.addEventListener('mouseleave', () => {
                  if (window.isDragging) {
                    window.isDragging = false;
                    svg.style.cursor = 'grab';
                    console.log('Mouse leave: isDragging =', window.isDragging);
                  }
                });
                
                // 触摸事件支持
                svg.addEventListener('touchstart', (e) => {
                  if (e.touches.length === 1) {
                    window.isDragging = true;
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                  }
                });
                
                document.addEventListener('touchmove', (e) => {
                  if (!window.isDragging || e.touches.length !== 1) return;
                  
                  const dx = e.touches[0].clientX - startX;
                  const dy = e.touches[0].clientY - startY;
                  
                  translateX += dx;
                  translateY += dy;
                  
                  startX = e.touches[0].clientX;
                  startY = e.touches[0].clientY;
                  
                  const g = svg.querySelector('g');
                  if (g) {
                    g.style.transform = `translate(\${translateX}px, \${translateY}px) scale(\${scale})`;
                  }
                  
                  e.preventDefault(); // 防止页面滚动
                }, { passive: false });
                
                document.addEventListener('touchend', () => {
                  window.isDragging = false;
                });
                
                // 初始化鼠标样式
                svg.style.cursor = 'grab';
                
                // 修改应用缩放函数，保持平移
                function applyTransform() {
                  const g = svg.querySelector('g');
                  if (g) {
                    g.style.transform = `translate(\${translateX}px, \${translateY}px) scale(\${scale})`;
                  }
                }
                
                // 渲染思维导图
                const mm = Markmap.create(svg, {
                  maxWidth: 300,
                  initialExpandLevel: 2,
                  duration: 0,
                  autoFit: true,
                  color: (node) => {
                    const colors = ['${ShadTheme.of(context).colorScheme.primary.value.toRadixString(16).padLeft(8, '0').substring(2)}', '${ShadTheme.of(context).colorScheme.secondary.value.toRadixString(16).padLeft(8, '0').substring(2)}', '${ShadTheme.of(context).colorScheme.accent.value.toRadixString(16).padLeft(8, '0').substring(2)}', '${ShadTheme.of(context).colorScheme.destructive.value.toRadixString(16).padLeft(8, '0').substring(2)}', '${ShadTheme.of(context).colorScheme.mutedForeground.value.toRadixString(16).padLeft(8, '0').substring(2)}'];
                    return colors[node.depth % colors.length];
                  },
                }, root);
                
                console.log('思维导图渲染完成');
                
                // 自动调整大小
                setTimeout(() => {
                  try {
                    mm.fit();
                    console.log('自动调整大小完成');
                  } catch (e) {
                    console.error('自动调整大小出错:', e);
                  }
                }, 500);
                
                // 添加全局函数，用于从外部重置拖拽状态
                window.resetDragState = function() {
                  if (window.isDragging) {
                    window.isDragging = false;
                    svg.style.cursor = 'grab';
                    console.log('拖拽状态已重置');
                  }
                };
                
                // 监听全局鼠标释放事件，以防止拖拽状态被卡住
                window.addEventListener('mouseup', function(e) {
                  if (window.isDragging) {
                    window.isDragging = false;
                    svg.style.cursor = 'grab';
                    console.log('Window mouse up: isDragging =', window.isDragging);
                  }
                });
                
              } catch (error) {
                console.error('渲染思维导图时出错:', error);
                document.body.innerHTML = '<div class="error-message">渲染思维导图时出错: ' + error.message + '</div>';
              }
            };
          </script>
        </body>
        </html>
      ''';

      // 加载HTML内容
      await _mindMapWebViewController!.loadData(data: htmlContent);

      // 页面加载完成后，添加一个定时器定期检查并重置拖拽状态
      await Future.delayed(Duration(seconds: 1));
      _startDragStateMonitor();
    } catch (e) {
      logger.e('构建HTML时出错: $e');

      try {
        // 尝试加载错误页面
        await _mindMapWebViewController?.loadData(data: '''
          <!DOCTYPE html>
          <html>
          <body>
            <div style="color: red; padding: 20px; font-family: Arial, sans-serif; text-align: center;">
              构建思维导图时出错: ${e.toString()}
            </div>
          </body>
          </html>
        ''');
      } catch (innerError) {
        logger.e('加载错误页面失败: $innerError');
      }
    }
  }

  // 启动拖拽状态监控
  void _startDragStateMonitor() {
    // 创建一个定时器，每300毫秒检查一次拖拽状态
    Timer.periodic(Duration(milliseconds: 300), (timer) {
      if (_mindMapWebViewController == null || !_isMindMapMode.value) {
        // 如果WebView已被销毁或不在思维导图模式，取消定时器
        timer.cancel();
        return;
      }

      // 执行JavaScript检查拖拽状态
      _mindMapWebViewController!.evaluateJavascript(source: '''
        try {
          // 检查鼠标是否被按下
          const mousePressed = (function() {
            try {
              return (window.event && window.event.buttons !== undefined) 
                ? (window.event.buttons !== 0) 
                : false;
            } catch (e) {
              return false;
            }
          })();
          
          // 如果鼠标未按下但拖拽状态为true，则重置拖拽状态
          if (!mousePressed && window.isDragging) {
            window.resetDragState();
            console.log('Timer reset drag state');
          }
        } catch (e) {
          console.error('监控脚本错误:', e);
        }
      ''');
    });

    // 将定时器的取消函数添加到dispose回调中
    addDisposeCallback(() {
      // 在这里不需要做什么，因为定时器会自行检查并取消
    });
  }

  // 切换思维导图模式
  Future<void> _toggleMindMapMode() async {
    // 防抖动：如果正在切换中或者距离上次切换不到500毫秒，则不执行
    final now = DateTime.now();
    if (_isToggling || now.difference(_lastToggleTime).inMilliseconds < 500) {
      return;
    }

    // 设置切换状态和时间
    _isToggling = true;
    _lastToggleTime = now;

    try {
      if (!_isMindMapMode.value) {
        // 先设置加载状态，显示加载动画
        _isMindMapLoading.value = true;
        // 切换到思维导图模式
        _isMindMapMode.value = true;

        try {
          // 将编辑器内容转换为Markdown
          final converter = CustomDeltaToMarkdown();
          final markdownContent =
              converter.convert(_controller.document.toDelta());

          if (markdownContent.isEmpty) {
            logger.w('Markdown内容为空');
            _mindMapData.value = '';
            _isMindMapLoading.value = false;
            _isToggling = false;
            return;
          }

          logger.i(
              'Markdown内容: ${markdownContent.substring(0, math.min(100, markdownContent.length))}...');

          // 构造JSON结构，包含markdown字段
          final mindMapData = {
            'root': {
              'v': '思维导图',
              'c': [
                {
                  'v': '内容将在WebView中解析',
                }
              ]
            },
            'features': {},
            'markdown': markdownContent
                .replaceAll('\\', '\\\\')
                .replaceAll("'", "\\'")
                .replaceAll('\n', '\\n')
          };

          final jsonData = jsonEncode(mindMapData);
          logger.i('生成的JSON数据长度: ${jsonData.length}');

          // 使用短延迟确保UI有时间显示加载状态
          await Future.delayed(Duration(milliseconds: 300));

          _mindMapData.value = jsonData;

          // 如果WebView已经创建，则直接渲染思维导图
          if (_mindMapWebViewController != null) {
            try {
              await _renderMindMap();
            } catch (e) {
              logger.e('渲染思维导图失败: $e');
              // WebView可能已被释放，忽略错误
            }
          }
        } catch (e) {
          logger.e('生成思维导图时出错: $e');

          // 创建一个错误提示思维导图
          final errorMap = {
            'root': {
              'v': '思维导图生成失败',
              'c': [
                {
                  'v':
                      '错误信息: ${e.toString().substring(0, math.min(100, e.toString().length))}'
                }
              ]
            },
            'features': {}
          };
          _mindMapData.value = jsonEncode(errorMap);
        } finally {
          // 完成数据准备后，关闭加载状态
          _isMindMapLoading.value = false;
        }
      } else {
        // 切换回编辑器模式
        _isMindMapMode.value = false;
        _mindMapData.value = '';

        // 恢复编辑器焦点
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _editorFocusNode.requestFocus();
        });
      }
    } finally {
      // 确保切换状态被重置
      _isToggling = false;
    }
  }

  void _savePrompt() {
    final name = _nameController.text.trim();
    if (name.isEmpty) {
      showToastNotification(context, '错误', '提示词名称不能为空');
      return;
    }

    try {
      // 使用自定义转换器将内容保存为Markdown格式
      final converter = CustomDeltaToMarkdown();
      final markdownContent = converter.convert(_controller.document.toDelta());

      // 更新控制器中的提示词
      _llmController.updatePrompt(
        widget.promptIndex,
        name: name,
        content: markdownContent,
      );

      // 显示成功消息并返回
      Get.back();
      showToastNotification(context, '成功', '提示词已保存');
    } catch (e) {
      logger.e('Error saving prompt: $e');
      showToastNotification(context, '错误', '保存失败: ${e.toString()}');
    }
  }

  // 将当前内容导出为Markdown文件
  Future<void> _exportAsMd() async {
    try {
      // 使用自定义转换器将内容导出为Markdown
      final converter = CustomDeltaToMarkdown();
      final delta = _controller.document.toDelta();
      if (delta == null) {
        showToastNotification(context, '导出失败', '文档内容为空');
        return;
      }

      final content = converter.convert(delta);
      if (content == null || content.isEmpty) {
        showToastNotification(context, '导出失败', '转换结果为空');
        return;
      }

      // 添加标题
      final title = _nameController.text.trim().isEmpty
          ? "Prompt"
          : _nameController.text.trim();
      final String fullContent = "# $title\n\n$content";

      String filename = '${title.toLowerCase().replaceAll(' ', '_')}.md';

      try {
        String? outputPath = await FilePicker.platform.saveFile(
          dialogTitle: '保存Markdown文件',
          fileName: filename,
          allowedExtensions: ['md'],
          type: FileType.custom,
        );

        if (outputPath != null) {
          if (!outputPath.toLowerCase().endsWith('.md')) {
            outputPath += '.md';
          }

          final File file = File(outputPath);
          await file.writeAsString(fullContent);

          showToastNotification(context, '导出成功', '文件已保存至: $outputPath');
        }
      } catch (e) {
        logger.e('保存文件时出错: $e');
        showToastNotification(context, '导出失败', '无法保存文件: ${e.toString()}');
      }
    } catch (e) {
      logger.e('导出Markdown时出错: $e');
      showToastNotification(context, '导出失败', '无法导出文件: ${e.toString()}');
    }
  }

  // 导入Markdown文件替换当前内容
  Future<void> _importMd() async {
    try {
      // 使用FilePicker选择Markdown文件
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['md', 'markdown', 'txt'],
        dialogTitle: '选择Markdown文件',
      );

      if (result != null && result.files.isNotEmpty) {
        final path = result.files.first.path;
        if (path != null) {
          // 读取文件内容
          final File file = File(path);
          final String content = await file.readAsString();

          // 确认是否替换当前内容
          bool shouldReplace = await showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: const Text('确认导入'),
                    content: const Text('导入将替换当前编辑器中的所有内容，确定继续吗？'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('取消'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('确定'),
                      ),
                    ],
                  );
                },
              ) ??
              false;

          if (shouldReplace) {
            // 不再提取标题，整个文件内容都作为编辑器内容
            final document = await _convertMarkdownToDocument(content);
            // 预加载图片
            _preloadImages(document);
            // 更新编辑器内容
            _controller.document = document;
            // 通知用户导入成功
            showToastNotification(
                context, '导入成功', '成功导入文件: \\${result.files.first.name}');
          }
        }
      }
    } catch (e) {
      logger.e('导入Markdown时出错: $e');
      showToastNotification(context, '导入失败', '无法导入文件: ${e.toString()}');
    }
  }

  // 插入水平分隔线
  void _insertHorizontalRule() {
    final selection = _controller.selection;
    final index = selection.start;
    final length = selection.end - selection.start;

    final horizontalRuleEmbed = BlockEmbed('divider', '---');

    // Delta to delete selection, then insert newline, embed, newline
    final delta = Delta()
      ..retain(index)
      ..delete(length)
      ..insert('\n') // Preceding newline
      ..insert(horizontalRuleEmbed.toJson()) // The embed
      ..insert('\n'); // Trailing newline

    // Apply the change
    _controller.compose(
      delta,
      // Set selection to be after the embed and the preceding newline,
      // effectively at the start of the trailing newline.
      TextSelection.collapsed(
          offset: index + 1 + 1), // index + '\n'(1) + embed(1)
      ChangeSource.local,
    );

    // 插入水平线后恢复焦点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _editorFocusNode.requestFocus();
    });
  }

  // 添加链接对话框
  void _showLinkDialog(BuildContext context) {
    final TextEditingController urlController = TextEditingController();

    // 插入链接的函数
    void insertLink() {
      if (urlController.text.isNotEmpty) {
        // 使用正确的API来设置链接
        final linkAttr = LinkAttribute(urlController.text);
        _controller.formatSelection(linkAttr);
      }
      Navigator.of(context).pop();
      // 确保对话框关闭后恢复焦点
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _editorFocusNode.requestFocus();
      });
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('anki.llm_card.insert_link'.tr),
          content: TextField(
            controller: urlController,
            decoration: const InputDecoration(
              labelText: 'URL',
              hintText: 'https://example.com',
            ),
            onSubmitted: (_) => insertLink(), // 添加回车确认
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 确保对话框关闭后恢复焦点
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _editorFocusNode.requestFocus();
                });
              },
              child: Text('anki.llm_card.cancel'.tr),
            ),
            TextButton(
              onPressed: insertLink, // 使用同一个函数处理确认
              child: Text('anki.llm_card.confirm'.tr),
            ),
          ],
        );
      },
    );
  }

  // 添加图片对话框
  void _showImageDialog(BuildContext context) {
    final TextEditingController urlController = TextEditingController();

    // 函数：插入图片
    void insertImage(String imageUrl) {
      if (imageUrl.isNotEmpty) {
        // 在当前位置插入图片
        final index = _controller.selection.baseOffset;
        final length = _controller.selection.extentOffset - index;

        // 使用BlockEmbed创建图片嵌入
        final imageEmbed = BlockEmbed.image(imageUrl);

        // 先在当前位置创建新行
        _controller.replaceText(index, length, '\n', null);

        // 在新行插入图片
        _controller.document.insert(index + 1, imageEmbed);

        // 在图片后添加一个换行符
        _controller.updateSelection(
          TextSelection.collapsed(offset: index + 2),
          ChangeSource.local,
        );
        _controller.replaceText(index + 2, 0, '\n', null);

        // 插入图片后恢复焦点
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _editorFocusNode.requestFocus();
        });
      }
    }

    // 选择本地图片函数
    Future<void> pickLocalImage() async {
      try {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.image,
          allowMultiple: false,
        );

        if (result != null && result.files.isNotEmpty) {
          final path = result.files.first.path;
          if (path != null) {
            // 直接插入图片并关闭对话框
            insertImage(path);
            Navigator.of(context).pop();
            // 确保对话框关闭后恢复焦点
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _editorFocusNode.requestFocus();
            });
          }
        } else {
          // 用户取消选择后恢复焦点
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _editorFocusNode.requestFocus();
          });
        }
      } catch (e) {
        logger.e('选择图片时出错: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('错误: 无法选择图片: ${e.toString()}')),
        );
        // 出错后恢复焦点
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _editorFocusNode.requestFocus();
        });
      }
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('anki.llm_card.insert_image'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: urlController,
                decoration: InputDecoration(
                  labelText: 'anki.llm_card.image_url'.tr,
                  hintText: 'https://example.com/image.jpg',
                ),
                onSubmitted: (_) {
                  insertImage(urlController.text);
                  Navigator.of(context).pop();
                },
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.upload_file),
                    label: Text('anki.llm_card.select_local_image'.tr),
                    onPressed: () => pickLocalImage(),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 确保对话框关闭后恢复焦点
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _editorFocusNode.requestFocus();
                });
              },
              child: Text('anki.llm_card.cancel'.tr),
            ),
            TextButton(
              onPressed: () {
                insertImage(urlController.text);
                Navigator.of(context).pop();
                // 确保对话框关闭后恢复焦点
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _editorFocusNode.requestFocus();
                });
              },
              child: Text('anki.llm_card.confirm'.tr),
            ),
          ],
        );
      },
    );
  }

  // 检查内容是否为空
  bool _isContentEmpty() {
    // 检查文档是否为空
    if (_controller.document.length <= 1) {
      return true;
    }

    // 获取纯文本内容，检查是否只包含空白字符
    final text =
        _controller.document.getPlainText(0, _controller.document.length);
    return text.trim().isEmpty;
  }

  // 处理Markdown语法的实时渲染
  void _processMarkdownSyntax(DocChange change) {
    try {
      _isProcessingMarkdown = true;

      // 只处理本地用户输入导致的变化
      if (change.source != ChangeSource.local) {
        _isProcessingMarkdown = false;
        return;
      }

      // 获取当前行的内容
      final baseOffset = _controller.selection.baseOffset;
      if (baseOffset <= 0) {
        _isProcessingMarkdown = false;
        return;
      }

      // 获取文档总长度以进行边界检查
      final documentLength = _controller.document.length;
      if (baseOffset > documentLength) {
        _isProcessingMarkdown = false;
        return;
      }

      // 查找当前行的开始位置
      final text = _controller.document.getPlainText(0, baseOffset);
      final lastNewlineIndex = text.lastIndexOf('\n');
      final lineStart = lastNewlineIndex >= 0 ? lastNewlineIndex + 1 : 0;

      // 检查lineStart是否有效
      if (lineStart >= baseOffset) {
        _isProcessingMarkdown = false;
        return;
      }

      final currentLineText = text.substring(lineStart, baseOffset);

      // 检查是否是空格键输入（可能是Markdown语法完成的时刻）
      final deltaOperations = change.change.toList();
      bool isSpaceInput = false;
      if (deltaOperations.isNotEmpty &&
          deltaOperations.last.isInsert &&
          deltaOperations.last.data == ' ') {
        isSpaceInput = true;
      }

      // 如果是空格输入，检查各种Markdown语法
      if (isSpaceInput) {
        // 标题匹配 (# ## ### #### ##### ###### 后面跟空格)
        final headerMatch = _headerRegex.matchAsPrefix(currentLineText);
        if (headerMatch != null) {
          // 删除Markdown语法
          final headerText = headerMatch.group(1)!;
          final headerLevel = headerText.length; // #的数量决定标题级别
          final syntaxLength = headerText.length + 1; // +1 是为了包含空格

          // 边界检查：确保要删除的语法长度不超过当前行长度
          if (syntaxLength > currentLineText.length ||
              lineStart + syntaxLength > documentLength) {
            _isProcessingMarkdown = false;
            return;
          }

          // 删除Markdown标记和空格
          _controller.replaceText(
            lineStart,
            syntaxLength,
            '',
            null,
          );

          // 应用标题样式
          switch (headerLevel) {
            case 1:
              _controller.formatText(lineStart, 0, Attribute.h1);
              break;
            case 2:
              _controller.formatText(lineStart, 0, Attribute.h2);
              break;
            case 3:
              _controller.formatText(lineStart, 0, Attribute.h3);
              break;
            case 4:
              _controller.formatText(lineStart, 0, Attribute.h4);
              break;
            case 5:
              _controller.formatText(lineStart, 0, Attribute.h5);
              break;
            case 6:
              _controller.formatText(lineStart, 0, Attribute.h6);
              break;
          }

          // 将光标移动到删除Markdown语法后的位置
          _controller.updateSelection(
            TextSelection.collapsed(offset: lineStart),
            ChangeSource.local,
          );

          _isProcessingMarkdown = false;
          return;
        }

        // 无序列表匹配 (- * + 后面跟空格)
        final listMatch = _listItemRegex.matchAsPrefix(currentLineText);
        if (listMatch != null) {
          // 删除Markdown语法
          final listMarker = listMatch.group(1)!;

          // 边界检查
          if (listMarker.length > currentLineText.length ||
              lineStart + listMarker.length > documentLength) {
            _isProcessingMarkdown = false;
            return;
          }

          _controller.replaceText(
            lineStart,
            listMarker.length,
            '',
            null,
          );

          // 应用无序列表样式
          _controller.formatText(lineStart, 0, Attribute.ul);

          // 将光标移动到删除Markdown语法后的位置
          _controller.updateSelection(
            TextSelection.collapsed(offset: lineStart),
            ChangeSource.local,
          );

          _isProcessingMarkdown = false;
          return;
        }

        // 有序列表匹配 (数字. 后面跟空格)
        final numberedListMatch =
            _numberedListRegex.matchAsPrefix(currentLineText);
        if (numberedListMatch != null) {
          // 删除Markdown语法
          final listMarker = numberedListMatch.group(1)!;

          // 边界检查
          if (listMarker.length > currentLineText.length ||
              lineStart + listMarker.length > documentLength) {
            _isProcessingMarkdown = false;
            return;
          }

          _controller.replaceText(
            lineStart,
            listMarker.length,
            '',
            null,
          );

          // 应用有序列表样式
          _controller.formatText(lineStart, 0, Attribute.ol);

          // 将光标移动到删除Markdown语法后的位置
          _controller.updateSelection(
            TextSelection.collapsed(offset: lineStart),
            ChangeSource.local,
          );

          _isProcessingMarkdown = false;
          return;
        }

        // 引用匹配 (> 后面跟空格)
        final blockquoteMatch = _blockquoteRegex.matchAsPrefix(currentLineText);
        if (blockquoteMatch != null) {
          // 删除Markdown语法
          final quoteMarker = blockquoteMatch.group(1)!;

          // 边界检查
          if (quoteMarker.length > currentLineText.length ||
              lineStart + quoteMarker.length > documentLength) {
            _isProcessingMarkdown = false;
            return;
          }

          _controller.replaceText(
            lineStart,
            quoteMarker.length,
            '',
            null,
          );

          // 应用引用样式
          _controller.formatText(lineStart, 0, Attribute.blockQuote);

          // 将光标移动到删除Markdown语法后的位置
          _controller.updateSelection(
            TextSelection.collapsed(offset: lineStart),
            ChangeSource.local,
          );

          _isProcessingMarkdown = false;
          return;
        }

        // 代码块匹配 (``` 后面跟空格)
        final codeMatch = _codeBlockRegex.matchAsPrefix(currentLineText);
        if (codeMatch != null) {
          // 删除Markdown语法
          final codeMarker = codeMatch.group(1)!;

          // 边界检查
          if (codeMarker.length > currentLineText.length ||
              lineStart + codeMarker.length > documentLength) {
            _isProcessingMarkdown = false;
            return;
          }

          _controller.replaceText(
            lineStart,
            codeMarker.length,
            '',
            null,
          );

          // 应用代码块样式
          _controller.formatText(lineStart, 0, Attribute.codeBlock);

          // 将光标移动到删除Markdown语法后的位置
          _controller.updateSelection(
            TextSelection.collapsed(offset: lineStart),
            ChangeSource.local,
          );

          _isProcessingMarkdown = false;
          return;
        }
      }

      // 检查行内样式，如加粗、斜体、删除线
      // 这些需要在用户完成输入后处理（即输入完**bold**后）

      // 注意：行内样式处理较复杂，实现过程中会替换文本并保持光标位置，
      // 这里仅实现了行首样式的自动转换，行内样式可在后续版本中扩展
    } catch (e) {
      logger.e('处理Markdown语法时出错: $e');
    } finally {
      _isProcessingMarkdown = false;
    }
  }

  // 处理返回按钮点击事件
  Future<bool> _handleBackPressed() async {
    // 如果是思维导图模式，先切回编辑器模式
    if (_isMindMapMode.value) {
      _isMindMapMode.value = false;
      _mindMapData.value = '';
      return false; // 阻止返回
    }

    // 检查内容是否为空
    final isEmpty = _isContentEmpty();

    if (!isEmpty) {
      // 内容不为空，直接保存
      final name = _nameController.text.trim();

      // 如果标题为空，自动设置为"未命名提示词"
      final promptName = name.isEmpty ? "anki.llm_card.unnamed_prompt".tr : name;

      try {
        // 使用自定义转换器将内容保存为Markdown格式
        final converter = CustomDeltaToMarkdown();
        final markdownContent =
            converter.convert(_controller.document.toDelta());

        // 更新控制器中的提示词
        _llmController.updatePrompt(
          widget.promptIndex,
          name: promptName,
          content: markdownContent,
        );

        // 保存成功但不显示提示，静默保存提高用户体验
      } catch (e) {
        // 保存失败时才显示错误，但仍允许退出
        logger.e('保存提示词时出错: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('anki.llm_card.auto_save_failed'.trParams({'error': e.toString()}))),
        );
      }
    } else {
      // 内容为空，删除提示词
      logger.i('提示词内容为空，删除提示词');

      try {
        // 删除空提示词
        _llmController.deletePrompt(widget.promptIndex);
      } catch (e) {
        logger.e('删除空提示词时出错: $e');
        // 即使删除失败也允许返回
      }
    }

    // 允许返回
    return true;
  }

  // 链接点击处理
  Future<void> _launchUrl(String url) async {
    logger.d('尝试打开链接: $url');
    try {
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        // 添加https前缀
        url = 'https://$url';
        logger.d('已添加https前缀: $url');
      }

      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        logger.d('成功打开链接: $url');
      } else {
        logger.e('无法打开链接: $url');
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('anki.llm_card.cannot_open_link'.trParams({'url': url}))),
        );
      }
    } catch (e) {
      logger.e('打开链接时出错: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('anki.llm_card.open_link_error'.trParams({'error': e.toString()}))),
      );
    }
  }

  // 处理编辑器焦点变化
  void _handleEditorFocus() {
    // 当编辑器获得焦点时，设置为编辑模式
    // 当编辑器失去焦点时，设置为只读模式（这样链接才能点击）
    _controller.readOnly = !_editorFocusNode.hasFocus;
  }

  // 预加载文档中的图片
  void _preloadImages(Document document) {
    try {
      // 获取文档中所有的Delta操作
      final delta = document.toDelta();
      final operations = delta.toList();

      // 遍历所有操作找图片
      for (final op in operations) {
        if (op.data is Map) {
          final Map<String, dynamic> data = op.data as Map<String, dynamic>;
          if (data.containsKey('image')) {
            final String imageUrl = data['image'] as String;
            if (imageUrl.isNotEmpty) {
              // 使用缓存图片提前加载
              if (imageUrl.startsWith('http')) {
                precacheImage(NetworkImage(imageUrl), context);
                logger.d('预加载网络图片: $imageUrl');
              } else if (imageUrl.startsWith('/')) {
                try {
                  precacheImage(FileImage(File(imageUrl)), context);
                  logger.d('预加载本地图片: $imageUrl');
                } catch (e) {
                  logger.w('无法预加载本地图片: $e');
                }
              }
            }
          }
        }
      }
      logger.d('图片预加载完成');
    } catch (e) {
      logger.w('预加载图片时出错: $e');
    }
  }
}

class ImageEmbedBuilder extends EmbedBuilder {
  @override
  String get key => BlockEmbed.imageType;

  @override
  Widget build(BuildContext context, EmbedContext embedContext) {
    final imageUrl = embedContext.node.value.data;
    logger.d('构建图片嵌入: $imageUrl');

    if (imageUrl == null || imageUrl.isEmpty) {
      logger.w('图片URL为空');
      return const SizedBox.shrink();
    }

    // 处理图片URL，移除可能的转义字符
    String processedUrl = imageUrl;
    try {
      // 处理转义字符
      processedUrl = imageUrl
          .replaceAll(r'\_', '_')
          .replaceAll(r'\-', '-')
          .replaceAll(r'\ ', ' ')
          .replaceAll(r'\(', '(')
          .replaceAll(r'\)', ')');

      if (processedUrl != imageUrl) {
        logger.d('图片URL被处理: $imageUrl -> $processedUrl');
      }
    } catch (e) {
      logger.e('处理图片URL时出错: $e');
    }

    try {
      // 根据URL类型确定图片来源
      Widget imageWidget;
      if (processedUrl.startsWith('http') || processedUrl.startsWith('https')) {
        // 网络图片
        logger.d('处理网络图片: $processedUrl');
        imageWidget = Image.network(
          processedUrl,
          fit: BoxFit.contain,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) {
              logger.d('网络图片加载完成: $processedUrl');
              return child;
            }
            return Center(
              child: SizedBox(
                width: 200,
                height: 150,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
                    const SizedBox(height: 8),
                    const Text('正在加载图片...')
                  ],
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            logger.e('网络图片加载失败: $error\n$stackTrace');
            return Center(
              child: Container(
                padding: const EdgeInsets.all(8.0),
                color: ShadTheme.of(context).colorScheme.muted,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.broken_image,
                        color: ShadTheme.of(context).colorScheme.destructive),
                    const SizedBox(height: 4),
                    Text(
                      '图片加载失败: ${processedUrl.length > 20 ? processedUrl.substring(0, 20) + '...' : processedUrl}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      } else if (processedUrl.startsWith('/')) {
        // 本地文件
        logger.d('处理本地图片: $processedUrl');
        try {
          // 为Mac路径处理特殊情况
          if (processedUrl.contains('Macintosh HD')) {
            processedUrl = processedUrl.replaceAll('/Volumes/Macintosh HD', '');
            logger.d('处理Mac路径: $processedUrl');
          }

          final file = File(processedUrl);
          if (file.existsSync()) {
            logger.d('文件存在: $processedUrl');
          } else {
            logger.w('文件不存在: $processedUrl');
          }

          imageWidget = Image.file(
            file,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              logger.e('本地图片加载失败: $error\n$stackTrace');
              return Center(
                child: Container(
                  padding: const EdgeInsets.all(8.0),
                  color: ShadTheme.of(context).colorScheme.muted,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.broken_image,
                          color: ShadTheme.of(context).colorScheme.destructive),
                      const SizedBox(height: 4),
                      Text(
                        '本地图片加载失败: ${processedUrl.length > 20 ? processedUrl.substring(0, 20) + '...' : processedUrl}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        } catch (e) {
          logger.e('创建本地图片控件失败: $e');
          imageWidget = Center(
            child: Container(
              padding: const EdgeInsets.all(8.0),
              color: ShadTheme.of(context).colorScheme.muted,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.error_outline,
                      color: ShadTheme.of(context).colorScheme.destructive),
                  const SizedBox(height: 4),
                  Text(
                    '无法加载图片: $e\n路径: $processedUrl',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          );
        }
      } else {
        // 其他类型图片 - 尝试作为网络URL处理
        logger.d('尝试作为网络图片处理未知格式: $processedUrl');
        imageWidget = Image.network(
          processedUrl,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            logger.e('未知格式图片加载失败: $error');
            return Center(
              child: Container(
                padding: const EdgeInsets.all(8.0),
                color: ShadTheme.of(context).colorScheme.muted,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.help_outline,
                        color: ShadTheme.of(context).colorScheme.secondary),
                    const SizedBox(height: 4),
                    Text(
                      '不支持的图片格式: $processedUrl',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }

      // 获取屏幕宽度
      final screenWidth = MediaQuery.of(context).size.width;
      final maxWidth = screenWidth * 0.85; // 调整最大宽度为屏幕宽度的85%
      final maxHeight = 300.0; // 调整最大高度为300px

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Center(
          child: InkWell(
            onTap: () {
              // 点击图片时显示大图
              showDialog(
                context: context,
                builder: (context) => Dialog(
                  backgroundColor: Colors.transparent,
                  insetPadding: const EdgeInsets.all(16),
                  child: GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: InteractiveViewer(
                      minScale: 0.5,
                      maxScale: 4.0,
                      child: Container(
                        constraints: BoxConstraints(
                          maxWidth: MediaQuery.of(context).size.width,
                          maxHeight: MediaQuery.of(context).size.height * 0.8,
                        ),
                        child: imageWidget,
                      ),
                    ),
                  ),
                ),
              );
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.0),
                boxShadow: [
                  BoxShadow(
                    color: ShadTheme.of(context)
                        .colorScheme
                        .border
                        .withOpacity(0.3),
                    spreadRadius: 0.1,
                    blurRadius: 1,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4.0),
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: maxWidth,
                    maxHeight: maxHeight,
                  ),
                  child: imageWidget,
                ),
              ),
            ),
          ),
        ),
      );
    } catch (e) {
      logger.e('构建图片时出现异常: $e');
      return Center(
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          padding: const EdgeInsets.all(8.0),
          color: ShadTheme.of(context).colorScheme.destructive.withOpacity(0.1),
          child: Text(
            '图片渲染错误: $e',
            style:
                TextStyle(color: ShadTheme.of(context).colorScheme.destructive),
          ),
        ),
      );
    }
  }

  @override
  bool get expanded => false;

  @override
  WidgetSpan buildWidgetSpan(Widget widget) {
    return WidgetSpan(
      alignment: PlaceholderAlignment.middle,
      child: widget,
    );
  }

  @override
  String toPlainText(Embed node) {
    return '[图片]';
  }
}

// 水平线嵌入构建器
class HorizontalRuleEmbedBuilder extends EmbedBuilder {
  @override
  String get key => 'divider';

  @override
  Widget build(BuildContext context, EmbedContext embedContext) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16.0),
      height: 1.0,
      decoration: BoxDecoration(
        color: ShadTheme.of(context).colorScheme.border,
        boxShadow: [
          BoxShadow(
            color: ShadTheme.of(context).colorScheme.muted,
            blurRadius: 1.0,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }

  @override
  bool get expanded => false;

  @override
  WidgetSpan buildWidgetSpan(Widget widget) {
    return WidgetSpan(
      alignment: PlaceholderAlignment.middle,
      child: widget,
    );
  }

  @override
  String toPlainText(Embed node) {
    return '\n---\n';
  }
}
