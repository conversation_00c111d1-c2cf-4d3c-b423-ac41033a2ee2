import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/image_compression_form.dart';
import 'components/upload_images_form.dart';
import 'components/scan_images_form.dart';
import 'package:anki_guru/controllers/anki/card_media_controller.dart';
import 'package:anki_guru/pages/common.dart';

class CardMediaManagerPage extends StatefulWidget {
  const CardMediaManagerPage({super.key});

  @override
  State<CardMediaManagerPage> createState() => _CardMediaManagerPageState();
}

class _CardMediaManagerPageState extends State<CardMediaManagerPage> {
  final controller = Get.put(CardMediaManagerController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('anki.card_media_manager.title'.tr),
        centerTitle: true,
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('anki.card_media_manager.function_description'.tr,
                    style: defaultPageTitleStyle),
                Text('anki.card_media_manager.description'.tr,
                    style: theme.textTheme.muted),
                const SizedBox(height: 16),
              ],
            ),
            // 标签页内容
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 3;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        // Tab changed
                      },
                      tabs: [
                        ShadTab(
                          value: 'compression',
                          content: const ImageCompressionForm(),
                          width: tabWidth,
                          child: Text(
                              'anki.card_media_manager.compression_tab'.tr),
                        ),
                        ShadTab(
                          value: 'upload',
                          content: const UploadImagesForm(),
                          width: tabWidth,
                          child: Text('anki.card_media_manager.upload_tab'.tr),
                        ),
                        ShadTab(
                          value: 'scan',
                          content: const ScanImagesForm(),
                          width: tabWidth,
                          child: Text('anki.card_media_manager.scan_tab'.tr),
                        )
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
