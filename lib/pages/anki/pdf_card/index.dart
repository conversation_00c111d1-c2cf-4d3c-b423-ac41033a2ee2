import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'components/cloze_form.dart';
import 'components/qa_form.dart';
import 'package:anki_guru/controllers/anki/pdf_card.dart';
import 'package:anki_guru/pages/common.dart';

class PdfCard extends StatefulWidget {
  const PdfCard({super.key});

  @override
  State<PdfCard> createState() => _PdfCardState();
}

class _PdfCardState extends State<PdfCard> {
  final controller = Get.put(PDFCardFormController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('anki.pdf_card.title'.tr, style: theme.textTheme.large),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        // actions: [
        //   PopupMenuButton<String>(
        //     icon: const Icon(Icons.more_horiz),
        //     offset: const Offset(0, 40),
        //     onSelected: (value) {
        //       switch (value) {
        //         case 'save':
        //           if (controller.tabController.selected == "cloze") {
        //             controller.clozeParams.saveFormToJson();
        //           } else if (controller.tabController.selected == "qa") {
        //             controller.qaParams.saveFormToJson();
        //           }
        //           break;
        //         case 'restore':
        //           if (controller.tabController.selected == "cloze") {
        //             controller.clozeParams.loadFormFromJson();
        //           } else if (controller.tabController.selected == "qa") {
        //             controller.qaParams.loadFormFromJson();
        //           }
        //           break;
        //       }
        //     },
        //     itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        //       const PopupMenuItem<String>(
        //         value: 'save',
        //         child: Row(
        //           children: [
        //             Icon(Icons.save),
        //             SizedBox(width: 8),
        //             Text('保存参数'),
        //           ],
        //         ),
        //       ),
        //       const PopupMenuItem<String>(
        //         value: 'restore',
        //         child: Row(
        //           children: [
        //             Icon(Icons.restore),
        //             SizedBox(width: 8),
        //             Text('恢复参数'),
        //           ],
        //         ),
        //       ),
        //     ],
        //   ),
        // ],
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('anki.pdf_card.function_description'.tr, style: defaultPageTitleStyle),
                Text('anki.pdf_card.feature_description'.tr, style: theme.textTheme.muted),
                const SizedBox(height: 16),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 设置每个标签的最小宽度
                    const minTabWidth = 120.0;
                    // 计算平均分配的宽度
                    final avgTabWidth = constraints.maxWidth / 2;
                    // 使用较大的值，确保标签不会太窄
                    final tabWidth =
                        avgTabWidth > minTabWidth ? avgTabWidth : minTabWidth;

                    return ShadTabs<String>(
                      controller: controller.tabController,
                      scrollable: true,
                      onChanged: (value) {
                        print(controller.tabController.selected);
                      },
                      tabs: [
                        ShadTab(
                          value: 'cloze',
                          content: const ClozeForm(),
                          width: tabWidth,
                          child: Text('anki.pdf_card.cloze_tab'.tr),
                        ),
                        ShadTab(
                          value: 'qa',
                          content: const QAForm(),
                          width: tabWidth,
                          child: Text('anki.pdf_card.qa_tab'.tr),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
