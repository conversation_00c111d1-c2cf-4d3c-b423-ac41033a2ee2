import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/pdf_card.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'dart:io';

class QAForm extends GetView<PDFCardFormController> {
  const QAForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: 4,
            children: [
              ShadSelectWithInput(
                key: ValueKey(
                    "deck-${ankiConnectController.parentDeckList.length}"),
                label: 'anki.common.target_deck'.tr,
                placeholder: 'anki.placeholder.target_deck_search_input'.tr,
                searchPlaceholder:
                    'anki.placeholder.target_deck_search_input'.tr,
                isMultiple: false,
                initialValue: [controller.parentDeck.value],
                options: ankiConnectController.parentDeckList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.parentDeck.value = value.single;
                },
                onAddNew: (newDeckName) {
                  // Add to the deck list if not already present
                  if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                    ankiConnectController.parentDeckList.add(newDeckName);
                  }

                  // Set as selected deck
                  controller.parentDeck.value = newDeckName;
                },
                hasSuffix: true,
                onRefresh: () async {
                  logger.i("refresh");
                  final result =
                      await ankiConnectController.resetAnkiConnectData();
                  if (result) {
                    showToastNotification(
                        context, 'anki.common.refresh_success'.tr, "");
                  }
                },
              ),
              ShadSwitchCustom(
                key: ValueKey("subdeck-${controller.isCreateSubDeck.hashCode}"),
                label: 'anki.common.create_subdeck'.tr,
                initialValue: controller.isCreateSubDeck.value,
                onChanged: (v) {
                  controller.isCreateSubDeck.value = v;
                },
              ),
              if (!controller.isZotero.value)
                ShadRadioGroupCustom(
                  label: 'anki.common.card_mode'.tr,
                  initialValue: controller.qaParams.mode.value,
                  items: controller.qaModeList,
                  onChanged: (value) {
                    controller.qaParams.mode.value = value;
                  },
                ),
              if (controller.isZotero.value)
                ShadRadioGroupCustom(
                  label: 'anki.common.card_mode'.tr,
                  initialValue: controller.qaParams.mode.value,
                  items: controller.qaModeListforZotero,
                  onChanged: (value) {
                    controller.qaParams.mode.value = value;
                  },
                ),
              ListTile(
                contentPadding: EdgeInsets.zero,
                minVerticalPadding: 0,
                title: Text('anki.common.advanced_options'.tr,
                    style: defaultTitleStyle),
                subtitle: Wrap(
                  spacing: 16,
                  runSpacing: 8,
                  children: [
                    ShadCheckbox(
                      value: controller.isShowSource.value,
                      label: Text('anki.common.show_source'.tr),
                      padding: EdgeInsets.zero,
                      onChanged: (v) {
                        controller.isShowSource.value = v;
                      },
                    ),
                    ShadCheckbox(
                      value: controller.isAnswerCloze.value,
                      label: Text('anki.common.is_answer_cloze'.tr),
                      padding: EdgeInsets.zero,
                      onChanged: (v) {
                        controller.isAnswerCloze.value = v;
                      },
                    ),
                    ShadCheckbox(
                      value: controller.isMixCloze.value,
                      label: Text('anki.pdf_card.mix_card'.tr),
                      padding: EdgeInsets.zero,
                      onChanged: (v) {
                        controller.isMixCloze.value = v;
                      },
                    ),
                    if (Platform.isWindows ||
                        Platform.isMacOS ||
                        Platform.isLinux)
                      ShadCheckbox(
                        value: controller.isZotero.value,
                        label: Text('anki.pdf_card.zotero_card'.tr),
                        padding: EdgeInsets.zero,
                        onChanged: (v) {
                          controller.isZotero.value = v;
                          if (v) {
                            controller.qaParams.mode.value = 'note';
                          } else {
                            controller.qaParams.mode.value = 'single_file';
                          }
                        },
                      )
                  ],
                ),
              ),
              if (controller.qaParams.mode.value == 'note') ...[
                ShadCheckboxGroup(
                  label: 'anki.pdf_card.annotation_types'.tr,
                  initialValues: controller.qaParams.annotTypes.toList(),
                  items: controller.qaParams.annotTypeList.toList(),
                  onChanged: (value) {
                    logger.i(value);
                    controller.qaParams.annotTypes.value = value;
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return 'anki.placeholder.atLeastOneAnnotationType'.tr;
                    }
                    return "";
                  },
                  onValidateError: (error) {},
                ),
                if (controller.isAnswerCloze.value) ...[
                  ShadCheckboxGroup(
                    label: 'anki.common.answer_cloze_grammar'.tr,
                    initialValues: controller.qaParams.zoteroClozeGrammarList,
                    items: controller.clozeGrammarList,
                    onChanged: (value) {
                      controller.qaParams.zoteroClozeGrammarList.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.placeholder.atLeastOneAnswerClozeGrammar'
                            .tr;
                      }
                      return "";
                    },
                    onValidateError: (error) {},
                  ),
                ]
              ],
              if (controller.qaParams.mode.value == 'single_file' ||
                  controller.qaParams.mode.value == "dual_file") ...[
                if (controller.isAnswerCloze.value)
                  ShadCheckboxGroup(
                    label: 'anki.pdf_card.mask_type'.tr,
                    initialValues: controller.maskTypes.toList(),
                    items: controller.clozeParams.maskTypeList.toList(),
                    onChanged: (value) {
                      logger.i(value);
                      controller.maskTypes.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.placeholder.atLeastOneMaskType'.tr;
                      }
                      return "";
                    },
                    onValidateError: (error) {},
                  ),
              ],
              ShadInputWithValidate(
                  label: 'anki.pdf_card.pdf_columns'.tr,
                  placeholder: 'anki.placeholder.pdf_columns'.tr,
                  initialValue: controller.nCols.value.toString(),
                  onChanged: (value) {
                    controller.nCols.value = int.parse(value);
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return 'anki.placeholder.pdf_columns_required'.tr;
                    }
                    final reg = RegExp(r'^\d+$');
                    if (!reg.hasMatch(value)) {
                      return "anki.placeholder.mustBeInteger".tr;
                    }
                    final size = int.parse(value);
                    if (size <= 0) {
                      return "anki.placeholder.mustBeGreaterThan0".tr;
                    }
                    return "";
                  }),
              ShadInputWithValidate(
                  label: 'anki.pdf_card.extra_info'.tr,
                  placeholder: 'anki.placeholder.extra_info'.tr,
                  initialValue: controller.extraInfo.value,
                  onChanged: (value) {
                    controller.extraInfo.value = value;
                  },
                  onValidate: (value) async {
                    return "";
                  }),
              ShadSelectWithInput(
                key:
                    ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
                label: 'anki.common.tags'.tr,
                placeholder: 'anki.placeholder.tags'.tr,
                searchPlaceholder: 'anki.placeholder.tags'.tr,
                isMultiple: true,
                initialValue: controller.tags.toList(),
                options: ankiConnectController.tagsList
                    .map((e) => {'value': e, 'label': e})
                    .toList(),
                onChanged: (value) {
                  logger.i(value);
                  controller.tags.value = value;
                },
                onAddNew: (newTag) {
                  // Add new tag to the tags list if not already present
                  if (!ankiConnectController.tagsList.contains(newTag)) {
                    ankiConnectController.tagsList.add(newTag);
                  }

                  // Add to selected tags if not already present
                  if (!controller.tags.contains(newTag)) {
                    controller.tags.add(newTag);
                  }
                },
              ),
              if (controller.qaParams.mode.value != 'dual_file') ...[
                ShadInputWithValidate(
                    label: 'anki.common.page_range'.tr,
                    placeholder: 'anki.placeholder.page_range'.tr,
                    initialValue: controller.pageRange.value,
                    onChanged: (value) {
                      controller.pageRange.value = value;
                    },
                    onValidate: (value) async {
                      if (validatePageRange(value)) {
                        return "";
                      }
                      return "anki.placeholder.page_range_required".tr;
                    }),
              ],
              if (!controller.isZotero.value) ...[
                if (controller.qaParams.mode.value != 'dual_file')
                  ShadInputWithFileSelect(
                    key: const ValueKey("input"),
                    title: 'toolbox.common.inputFile'.tr,
                    placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                    allowedExtensions: const ['pdf'],
                    isRequired: true,
                    allowMultiple: true,
                    onFilesSelected: (files) {
                      controller.selectedFilePaths.value = files;
                    },
                    onValidate: (value, files) async {
                      return await validateFile(value, files);
                    },
                    onValidateError: (error) {},
                  ),
                if (controller.qaParams.mode.value == 'dual_file') ...[
                  ShadInputWithFileSelect(
                    key: const ValueKey("q-file"),
                    title: 'anki.common.q_file'.tr,
                    placeholder: Text('anki.placeholder.select_q_file'.tr),
                    allowedExtensions: const ['pdf'],
                    onFilesSelected: (files) {
                      controller.qFile.value = files.single;
                    },
                    onValidate: (value, files) async {
                      return await validateFile(value, files);
                    },
                    onValidateError: (error) {},
                  ),
                  ShadInputWithValidate(
                      label: 'anki.pdf_card.q_page_range'.tr,
                      placeholder: 'anki.placeholder.q_page_range'.tr,
                      initialValue: controller.aPageRange.value,
                      onChanged: (value) {
                        controller.pageRange.value = value;
                      },
                      onValidate: (value) async {
                        if (validatePageRange(value)) {
                          return "";
                        }
                        return 'anki.placeholder.page_range_required'.tr;
                      }),
                  ShadInputWithFileSelect(
                    key: const ValueKey("a-file"),
                    title: 'anki.common.a_file'.tr,
                    placeholder: Text('anki.placeholder.select_a_file'.tr),
                    allowedExtensions: const ['pdf'],
                    onFilesSelected: (files) {
                      controller.aFile.value = files.single;
                    },
                    onValidate: (value, files) async {
                      return await validateFile(value, files);
                    },
                    onValidateError: (error) {},
                  ),
                  ShadInputWithValidate(
                      label: 'anki.pdf_card.a_page_range'.tr,
                      placeholder: 'anki.placeholder.a_page_range'.tr,
                      initialValue: controller.aPageRange.value,
                      onChanged: (value) {
                        controller.aPageRange.value = value;
                      },
                      onValidate: (value) async {
                        if (validatePageRange(value)) {
                          return "";
                        }
                        return 'anki.placeholder.page_range_required'.tr;
                      }),
                ]
              ],
              if (controller.isZotero.value) ...[
                if (controller.qaParams.mode.value != 'dual_file') ...[
                  ShadInputWithValidate(
                    label: 'anki.pdf_card.zotero_item_id'.tr,
                    placeholder: 'anki.placeholder.zotero_item_id'.tr,
                    initialValue: controller.qItemId.value,
                    onChanged: (value) {
                      controller.qItemId.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.placeholder.zotero_item_id_required'.tr;
                      }
                      return "";
                    },
                  ),
                ],
                if (controller.qaParams.mode.value == 'dual_file') ...[
                  ShadInputWithValidate(
                    label: 'anki.pdf_card.q_item_id'.tr,
                    placeholder: 'anki.placeholder.q_item_id'.tr,
                    initialValue: controller.qItemId.value,
                    onChanged: (value) {
                      controller.qItemId.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.placeholder.q_item_id_required'.tr;
                      }
                      return "";
                    },
                  ),
                  ShadInputWithValidate(
                    label: 'anki.pdf_card.a_item_id'.tr,
                    placeholder: 'anki.placeholder.a_item_id'.tr,
                    initialValue: controller.aItemId.value,
                    onChanged: (value) {
                      controller.aItemId.value = value;
                    },
                    onValidate: (value) async {
                      if (value.isEmpty) {
                        return 'anki.placeholder.a_item_id_required'.tr;
                      }
                      return "";
                    },
                  ),
                ]
              ]
            ],
          )),
    );
  }
}
