import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gap/gap.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:io';

class AboutPage extends StatefulWidget {
  const AboutPage({super.key});

  @override
  State<AboutPage> createState() => _AboutPageState();
}

class _AboutPageState extends State<AboutPage> {
  @override
  Widget build(BuildContext context) {
    final settingController = Get.find<SettingController>();
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('about.title'.tr),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16),
            child: ShadCard(
              padding: const EdgeInsets.only(top: 16, bottom: 16),
              rowMainAxisAlignment: MainAxisAlignment.center,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                spacing: 4,
                children: [
                  Image.asset(
                    'assets/images/logo.png', // 确保添加你的应用logo
                    width: 100,
                    height: 100,
                  ),
                  Text(
                    'PDF Guru Anki', // 你的应用名称
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                  Text(
                    '${'about.version'.tr} ${settingController.version} (${settingController.buildNumber})', // 应用版本号
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Text(
                    'about.copyright'.tr, // 你的应用版权信息本号
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ShadButton.link(
                        onPressed: () => launchUrl(Uri.parse(
                            'https://guru.kevin2li.com/privacy.html')),
                        child: Text('about.privacyPolicy'.tr),
                      ),
                      ShadButton.link(
                        onPressed: () => launchUrl(
                            Uri.parse('https://guru.kevin2li.com/terms.html')),
                        child: Text('about.termsOfService'.tr),
                      ),
                    ],
                  ),
                  ShadButton(
                    onPressed: () => checkUpdate(context),
                    child: Text('about.checkUpdate'.tr),
                  )
                ],
              ),
            ),
          ),
          const Gap(24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'about.friendlyLinks'.tr,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              spacing: 4,
              children: [
                if (!Platform.isIOS)
                  ShadCard(
                    padding: const EdgeInsets.all(0),
                    child: ListTile(
                      leading: const Icon(Icons.language),
                      title: Text('about.officialWebsite'.tr),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () =>
                          launchUrl(Uri.parse('https://guru.kevin2li.top/')),
                    ),
                  ),
                ShadCard(
                  padding: const EdgeInsets.all(0),
                  child: ListTile(
                    leading: const Icon(LucideIcons.github),
                    title: Text('about.github'.tr),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => launchUrl(
                        Uri.parse('https://github.com/kevin2li/PDF-Guru')),
                  ),
                ),
                ShadCard(
                  padding: const EdgeInsets.all(0),
                  child: ListTile(
                    leading: const FaIcon(FontAwesomeIcons.bilibili),
                    title: Text('about.bilibili'.tr),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => launchUrl(
                        Uri.parse('https://space.bilibili.com/369356107')),
                  ),
                ),
              ],
            ),
          ),
          const Gap(24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'about.communication'.tr,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              spacing: 4,
              children: [
                ShadCard(
                  padding: const EdgeInsets.all(0),
                  child: ListTile(
                    leading: const FaIcon(FontAwesomeIcons.qq),
                    title: Text('about.qqChannel'.tr),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      showShadDialog(
                        context: context,
                        builder: (context) => Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: ShadDialog(
                            title: Text('about.qqChannel'.tr),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text('${'about.scanQrCode'.tr}：'),
                                const SizedBox(height: 16),
                                Image.asset(
                                  AssetStore.qqChannel,
                                  width: 200,
                                  // height: 200,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                ShadCard(
                  padding: const EdgeInsets.all(0),
                  child: ListTile(
                    leading: const FaIcon(FontAwesomeIcons.qq),
                    title: Text('about.qqGroup'.tr),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      showShadDialog(
                        context: context,
                        builder: (context) => Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: ShadDialog(
                            title: Text('about.qqGroup'.tr),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (!Platform.isIOS)
                                  Text('about.paidUsersOnly'.tr),
                                const SizedBox(height: 8),
                                Text('${'about.scanQrCode'.tr}：'),
                                const SizedBox(height: 16),
                                Image.asset(
                                  AssetStore.qqGroup,
                                  width: 200,
                                  // height: 200,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                ShadCard(
                  padding: const EdgeInsets.all(0),
                  child: ListTile(
                    leading: const FaIcon(FontAwesomeIcons.weixin),
                    title: Text('about.wechatGroup'.tr),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      showShadDialog(
                        context: context,
                        builder: (context) => Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: ShadDialog(
                            title: Text('about.wechatGroup'.tr),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (!Platform.isIOS)
                                  Text('about.paidUsersOnly'.tr),
                                const SizedBox(height: 8),
                                Text('${'about.scanQrCode'.tr}：'),
                                const SizedBox(height: 16),
                                Image.asset(
                                  AssetStore.wechatGroup,
                                  width: 200,
                                  // height: 200,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const Gap(16),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
