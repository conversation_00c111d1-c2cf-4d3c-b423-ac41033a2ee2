import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'components/home_page.dart';
import 'components/settings_page.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';
import 'package:anki_guru/controllers/anki/pdf_note.dart';

const double kWindowCaptionHeight = 32;

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  bool isAlwaysOnTop = false;
  final settingController = Get.find<SettingController>();
  final imageCardController = Get.find<ImageCardController>();
  final videoNoteController = Get.find<VideoNoteController>();
  final pdfNoteController = Get.find<PDFNoteController>();
  final ankiConnectController = Get.find<AnkiConnectController>();
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // await pdfNoteController.initHotKey();
      // await videoNoteController.initHotKey();
      // await imageCardController.initHotKey();
      await ankiConnectController.resetAnkiConnectData();
      // logger.i(ankiConnectController.parentDeckList);
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final settingController = Get.find<SettingController>();
    final pageController = PageController();
    final screenWidth = MediaQuery.of(context).size.width;

    // 只保留一个宽度阈值，用于切换显示模式
    const minDrawerWidth = 600.0;
    final useDrawer = PathUtils.isDesktop && screenWidth >= minDrawerWidth;

    final navigationItems = [
      (
        icon: const Icon(Icons.home),
        label: Text('navigation.home'.tr, style: ShadTheme.of(context).textTheme.h4),
        labelText: 'navigation.home'.tr,
        onTap: () {
          settingController.currentIndex.value = 0;
          pageController.jumpToPage(0);
        },
      ),
      (
        icon: const Icon(Icons.settings),
        label: Text('navigation.settings'.tr, style: ShadTheme.of(context).textTheme.h4),
        labelText: 'navigation.settings'.tr,
        onTap: () {
          settingController.currentIndex.value = 1;
          pageController.jumpToPage(1);
        },
      ),
    ];

    return Obx(
      () => Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: useDrawer
            ? null
            : AppBar(
                title: Text(
                  settingController.currentIndex.value == 0 ? 'navigation.home'.tr : 'navigation.settings'.tr,
                  style:
                      TextStyle(color: Theme.of(context).colorScheme.onSurface),
                ),
                backgroundColor: Theme.of(context).colorScheme.surface,
              ),
        // appBar: AppBar(
        //   title: const PreferredSize(
        //     preferredSize: Size.fromHeight(kWindowCaptionHeight),
        //     child: WindowCaption(),
        //   ),
        // ),
        body: Row(
          children: [
            if (useDrawer)
              NavigationRail(
                selectedIndex: settingController.currentIndex.value,
                // backgroundColor: ShadTheme.of(context).tabsTheme.tabHoverBackgroundColor,
                onDestinationSelected: (index) {
                  settingController.currentIndex.value = index;
                  pageController.jumpToPage(index);
                },
                extended: false, // 始终不显示标签
                labelType: NavigationRailLabelType.none,
                destinations: navigationItems
                    .map(
                      (item) => NavigationRailDestination(
                        icon: item.icon,
                        label: Text(item.labelText),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    )
                    .toList(),
                minWidth: 72, // 固定宽度
                // groupAlignment: -0.85,
              ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (useDrawer) ...[
                    Padding(
                      padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                      child: Text(
                        settingController.currentIndex.value == 0 ? 'navigation.home'.tr : 'navigation.settings'.tr,
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ],
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: PageView(
                        controller: pageController,
                        allowImplicitScrolling: true,
                        onPageChanged: (value) =>
                            settingController.currentIndex.value = value,
                        children: const [
                          HomePage(),
                          SettingsPage(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        bottomNavigationBar: useDrawer
            ? null
            : BottomNavigationBar(
                currentIndex: settingController.currentIndex.value,
                items: navigationItems
                    .map((item) => BottomNavigationBarItem(
                          icon: item.icon,
                          label: item.labelText,
                        ))
                    .toList(),
                onTap: (index) {
                  settingController.currentIndex.value = index;
                  pageController.jumpToPage(index);
                },
              ),
      ),
    );
  }
}
