import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as p;
import 'package:desktop_drop/desktop_drop.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/anki/text_card.dart';
import 'package:flex_color_picker/flex_color_picker.dart';
import 'dart:async';
import 'dart:math';

List<Map<String, String>> get outputModeList => [
      {
        'value': 'same',
        'label': 'common.fileSelect.sameDirectory'.tr,
      },
      {
        'value': 'custom',
        'label': 'common.fileSelect.customDirectory'.tr,
      },
      {
        'value': 'overwrite',
        'label': 'common.fileSelect.overwriteOriginal'.tr,
      }
    ];

TextStyle get defaultTitleStyle =>
    const TextStyle(fontSize: 14, fontWeight: FontWeight.bold);

TextStyle get defaultCardTitleStyle =>
    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

TextStyle get defaultPageTitleStyle =>
    const TextStyle(fontSize: 14.5, fontWeight: FontWeight.bold);

Future<String> validateOutputDir(String value, List<String> files) async {
  if (files.isEmpty && value.isEmpty) {
    return 'common.fileSelect.directoryCannotBeEmpty'.tr;
  }
  final path = PathUtils(value);
  if (value.isNotEmpty) {
    if (!path.exists()) {
      return 'common.fileSelect.directoryNotExist'.tr;
    }
    if (path.isFile) {
      return 'common.fileSelect.selectDirectoryNotFile'.tr;
    }
  }
  return "";
}

Future<String> validateFile(String value, List<String> files) async {
  if (value.isEmpty && files.isEmpty) {
    return 'common.fileSelect.fileCannotBeEmpty'.tr;
  }
  final path = PathUtils(value);
  if (value.isNotEmpty) {
    if (!path.exists()) {
      return 'common.fileSelect.fileNotExist'.tr;
    }
    if (path.isDir) {
      return 'common.fileSelect.selectFileNotDirectory'.tr;
    }
  }
  return "";
}

class SectionTitle extends StatelessWidget {
  final String title;

  const SectionTitle({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: Theme.of(context)
            .textTheme
            .titleMedium
            ?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }
}

class CustomErrorWidget extends StatelessWidget {
  final String? error;

  const CustomErrorWidget({
    super.key,
    required this.error,
  });

  @override
  Widget build(BuildContext context) {
    if (error == null || error!.isEmpty) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        error!,
        style: const TextStyle(color: Colors.red, fontSize: 13),
      ),
    );
  }
}

class ShadIconButtonCustom extends StatelessWidget {
  final IconData icon;
  final double? size;
  final Function()? onPressed;
  final Function(LongPressStartDetails)? onLongPressStart;
  const ShadIconButtonCustom({
    super.key,
    required this.onPressed,
    this.onLongPressStart,
    required this.icon,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return ShadIconButton(
      icon: Icon(icon, size: size),
      backgroundColor: Colors.transparent,
      foregroundColor: ShadTheme.of(context).colorScheme.primary,
      hoverForegroundColor: ShadTheme.of(context).colorScheme.primary,
      hoverBackgroundColor: ShadTheme.of(context).colorScheme.secondary,
      onPressed: onPressed,
      onLongPressStart: onLongPressStart,
    );
  }
}

// 文件选择组件
class ShadInputWithFileSelect extends StatefulWidget {
  final String title;
  final Function(List<String>)? onFilesSelected;
  final Widget? placeholder;
  final List<String>? initialValue;
  final List<String>? allowedExtensions;
  final bool? allowMultiple;
  final bool? isRequired;
  final bool isFolder;
  final bool enableDragSort;
  final Future<String> Function(String, List<String>)? onValidate;
  final Function(String)? onValidateError;

  const ShadInputWithFileSelect({
    super.key,
    required this.title,
    required this.onFilesSelected,
    this.placeholder,
    this.initialValue,
    this.allowedExtensions,
    this.allowMultiple = false,
    this.onValidate,
    this.isRequired = false,
    this.isFolder = false,
    this.enableDragSort = false,
    this.onValidateError,
  });

  @override
  State<ShadInputWithFileSelect> createState() =>
      _ShadInputWithFileSelectState();
}

class _ShadInputWithFileSelectState extends State<ShadInputWithFileSelect> {
  final controller = TextEditingController();
  final settingController = Get.find<SettingController>();
  List<String> selectedFilePaths = [];
  String? errorText;
  Timer? _debounceTimer;
  bool _isSelecting = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialValue != null && widget.initialValue!.isNotEmpty) {
      selectedFilePaths = List.from(widget.initialValue!);
      if (selectedFilePaths.length == 1) {
        controller.text = selectedFilePaths.first;
      }
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _removeFile(int index) {
    setState(() {
      selectedFilePaths.removeAt(index);
      widget.onFilesSelected?.call(selectedFilePaths);
      if (selectedFilePaths.isEmpty) {
        controller.text = '';
      } else if (selectedFilePaths.length == 1) {
        controller.text = selectedFilePaths.first;
      }
    });
  }

  void _clearFiles() {
    setState(() {
      selectedFilePaths.clear();
      widget.onFilesSelected?.call(selectedFilePaths);
    });
  }

  void _reorderFiles(int oldIndex, int newIndex) {
    if (!widget.enableDragSort) return;

    setState(() {
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final String item = selectedFilePaths.removeAt(oldIndex);
      selectedFilePaths.insert(newIndex, item);
      widget.onFilesSelected?.call(selectedFilePaths);
    });
  }

  Future<bool> validate(String value, List<String> files) async {
    if (!mounted) return false;

    if (widget.onValidate != null) {
      final res = await widget.onValidate?.call(value, files);
      if (res != null && res.isNotEmpty) {
        if (!mounted) return false;
        setState(() {
          errorText = res;
        });
        widget.onValidateError?.call(res);
        return false;
      } else {
        if (!mounted) return false;
        setState(() {
          errorText = "";
        });
        widget.onValidateError?.call("");
        return true;
      }
    }
    return true;
  }

  Future<void> selectFileOrFolder() async {
    if (!mounted || _isSelecting) return;

    // 防止短时间内多次点击触发
    if (_debounceTimer?.isActive ?? false) return;

    _isSelecting = true;
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isSelecting = false;
        });
      }
    });

    if (widget.isFolder) {
      final result = await FilePicker.platform.getDirectoryPath(
        dialogTitle: widget.title,
        initialDirectory: controller.text,
      );
      if (result != null && mounted) {
        widget.onFilesSelected?.call([result]);
        controller.text = result;
      }
    } else {
      FilePickerResult? result;
      if (Platform.isAndroid || widget.allowedExtensions == null) {
        result = await FilePicker.platform.pickFiles(
            dialogTitle: widget.title,
            type: FileType.any,
            allowMultiple: widget.allowMultiple ?? false,
            compressionQuality: 0);
      } else {
        result = await FilePicker.platform.pickFiles(
            dialogTitle: widget.title,
            type: FileType.custom,
            allowedExtensions: widget.allowedExtensions,
            allowMultiple: widget.allowMultiple ?? false,
            compressionQuality: 0);
      }
      if (result != null && mounted) {
        if (widget.allowMultiple ?? false) {
          setState(() {
            selectedFilePaths
                .addAll(result!.files.map((xfile) => xfile.path!).toList());
          });
          widget.onFilesSelected?.call(selectedFilePaths);
          if (selectedFilePaths.length > 1) {
            controller.text = "";
          } else {
            controller.text = selectedFilePaths.first;
          }
        } else {
          setState(() {
            selectedFilePaths = [result!.files.first.path!];
          });
          widget.onFilesSelected?.call(selectedFilePaths);
          controller.text = selectedFilePaths.first;
        }
        if (mounted) {
          await validate(controller.text, selectedFilePaths);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DropTarget(
      onDragEntered: (detail) {
        print('onDragEntered');
      },
      onDragDone: (detail) async {
        print('onDragDone');
        // 防止短时间内多次拖放操作触发
        if (_isSelecting || (_debounceTimer?.isActive ?? false)) return;

        _isSelecting = true;
        _debounceTimer = Timer(const Duration(milliseconds: 1000), () {
          if (mounted) {
            setState(() {
              _isSelecting = false;
            });
          }
        });

        if (widget.isFolder) {
          final allowedFolders = detail.files;
          if (widget.onFilesSelected != null) {
            if (widget.allowMultiple ?? true) {
              widget.onFilesSelected
                  ?.call(allowedFolders.map((xfile) => xfile.path).toList());
              controller.text = "";
            } else {
              final folderPath =
                  FileSystemEntity.isFileSync(allowedFolders.single.path)
                      ? Directory(allowedFolders.single.path).parent.path
                      : allowedFolders.single.path;
              widget.onFilesSelected?.call([folderPath]);
              controller.text = folderPath;
            }
            await validate(controller.text, selectedFilePaths);
          }
        } else {
          final allowedFiles = detail.files.where((file) {
            final extension = file.path.split('.').last.toLowerCase();
            return widget.allowedExtensions?.contains(extension) ?? true;
          }).toList();

          if (allowedFiles.isEmpty) {
            String formatString = widget.allowedExtensions?.join(',') ?? '';
            showToastNotification(
                context,
                'common.fileSelect.pleaseSelectFiles'
                    .trParams({'format': formatString}),
                '',
                type: "error");
            return;
          }
          if (widget.onFilesSelected != null) {
            final files = allowedFiles.map((xfile) => xfile.path).toList();
            if (widget.allowMultiple ?? false) {
              // 多选
              setState(() {
                selectedFilePaths.addAll(files);
              });
              widget.onFilesSelected?.call(selectedFilePaths);
              if (selectedFilePaths.length > 1) {
                controller.text = "";
              } else {
                controller.text = selectedFilePaths.first;
              }
            } else {
              // 单选
              setState(() {
                selectedFilePaths = [files.first];
              });
              widget.onFilesSelected?.call(selectedFilePaths);
              controller.text = selectedFilePaths.first;
            }
            await validate(controller.text, selectedFilePaths);
          }
        }
      },
      child: ListTile(
        contentPadding: EdgeInsets.zero,
        title: Text(widget.title, style: defaultTitleStyle),
        subtitle: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ShadInput(
                controller: controller,
                placeholder: widget.placeholder,
                onPressed: () async {
                  if (Platform.isAndroid || Platform.isIOS) {
                    // 防止短时间内多次点击触发
                    if (_isSelecting || (_debounceTimer?.isActive ?? false)) {
                      return;
                    }
                    await selectFileOrFolder();
                  }
                },
                readOnly: Platform.isAndroid || Platform.isIOS,
                onChanged: (value) async {
                  final res = await validate(value, selectedFilePaths);
                  if (res) {
                    selectedFilePaths = [value];
                    widget.onFilesSelected?.call([value]);
                  }
                },
                trailing: GestureDetector(
                  onTap: () {
                    // 防止短时间内多次点击触发
                    if (_isSelecting || (_debounceTimer?.isActive ?? false))
                      return;
                    selectFileOrFolder();
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: Icon(
                      LucideIcons.folder,
                      size: 16,
                      color:
                          ShadTheme.of(context).colorScheme.popoverForeground,
                    ),
                  ),
                ),
              ),
              if (errorText != null && errorText!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(left: 8, right: 8, top: 4),
                  child: Text(errorText!,
                      style: const TextStyle(color: Colors.red, fontSize: 13)),
                ),
              if (selectedFilePaths.isNotEmpty &&
                  selectedFilePaths.length > 1) ...[
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('common.fileSelect.selectedFiles'
                        .trParams({'count': '${selectedFilePaths.length}'})),
                    TextButton.icon(
                      onPressed: _clearFiles,
                      icon: const Icon(Icons.clear_all),
                      label: Text('common.fileSelect.clearAll'.tr),
                    ),
                  ],
                ),

                // 设置最大显示文件数限制
                Builder(builder: (context) {
                  final int maxDisplayedFiles = 5;
                  final bool shouldScroll =
                      selectedFilePaths.length > maxDisplayedFiles;
                  // 计算单个列表项的估计高度，用于限制最大高度
                  final double estimatedItemHeight = 56.0; // 一个ListTile的大致高度
                  final double maxListHeight =
                      maxDisplayedFiles * estimatedItemHeight;

                  return widget.enableDragSort
                      ? Container(
                          constraints: BoxConstraints(
                            maxHeight:
                                shouldScroll ? maxListHeight : double.infinity,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: ShadTheme.of(context)
                                  .colorScheme
                                  .border
                                  .withOpacity(0.5),
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: ReorderableListView.builder(
                            shrinkWrap: true,
                            padding: const EdgeInsets.all(4),
                            physics: shouldScroll
                                ? const AlwaysScrollableScrollPhysics()
                                : const NeverScrollableScrollPhysics(),
                            itemCount: selectedFilePaths.length,
                            onReorder: _reorderFiles,
                            itemBuilder: (context, index) {
                              final path = selectedFilePaths[index];
                              return ListTile(
                                contentPadding: EdgeInsets.zero,
                                visualDensity: VisualDensity.compact,
                                key: ValueKey('${path}_$index'),
                                leading: IconButton(
                                  icon: const Icon(LucideIcons.trash, size: 18),
                                  onPressed: () => _removeFile(index),
                                ),
                                title: Text(PathUtils(path).name),
                                subtitle: Text(path,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        color: ShadTheme.of(context)
                                            .colorScheme
                                            .mutedForeground)),
                              );
                            },
                          ),
                        )
                      : Container(
                          constraints: BoxConstraints(
                            maxHeight:
                                shouldScroll ? maxListHeight : double.infinity,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: ShadTheme.of(context)
                                  .colorScheme
                                  .border
                                  .withOpacity(0.5),
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: ListView.builder(
                            shrinkWrap: true,
                            padding: const EdgeInsets.all(4),
                            physics: shouldScroll
                                ? const AlwaysScrollableScrollPhysics()
                                : const NeverScrollableScrollPhysics(),
                            itemCount: selectedFilePaths.length,
                            itemBuilder: (context, index) {
                              final path = selectedFilePaths[index];
                              return ListTile(
                                contentPadding: EdgeInsets.zero,
                                visualDensity: VisualDensity.compact,
                                key: ValueKey('${path}_$index'),
                                leading: const Icon(LucideIcons.file, size: 18),
                                title: Text(p.basename(path)),
                                subtitle: Text(path,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        color: ShadTheme.of(context)
                                            .colorScheme
                                            .mutedForeground)),
                                trailing: IconButton(
                                  icon: const Icon(LucideIcons.trash, size: 18),
                                  onPressed: () => _removeFile(index),
                                ),
                              );
                            },
                          ),
                        );
                }),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class ShadSelectCustom extends StatefulWidget {
  final String label;
  final String placeholder;
  final bool isMultiple;
  final bool showLabel;
  final bool allowDeselection;
  final List<String>? initialValue;
  final List<Map<String, String>> options;
  final Function(List<String>)? onChanged;
  final Future<String> Function(List<String?>)? onValidate;
  final Function(String)? onValidateError;

  const ShadSelectCustom({
    super.key,
    required this.label,
    required this.placeholder,
    required this.options,
    this.isMultiple = false,
    this.showLabel = true,
    this.initialValue,
    this.allowDeselection = false,
    this.onChanged,
    this.onValidate,
    this.onValidateError,
  });

  @override
  State<ShadSelectCustom> createState() => _ShadSelectCustomState();
}

class _ShadSelectCustomState extends State<ShadSelectCustom> {
  String errorText = "";
  late List<Map<String, String>> options;

  @override
  void initState() {
    super.initState();
    options = List.from(widget.options);
  }

  @override
  void didUpdateWidget(ShadSelectCustom oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.options != widget.options) {
      options = List.from(widget.options);
    }
  }

  Widget _buildSelectOptions() {
    if (options.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Text('common.ui.emptyList'.tr),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...options.map(
          (option) => ShadOption(
            value: option['value']!,
            child: Text(option['label']!),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final selectWidget = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: double.infinity,
          child: widget.isMultiple
              ? ShadSelect<String>.multiple(
                  closeOnSelect: false,
                  anchor: const ShadAnchorAuto(),
                  initialValues: widget.initialValue ?? [],
                  placeholder: Text(widget.placeholder,
                      style: ShadTheme.of(context).inputTheme.placeholderStyle),
                  options: [_buildSelectOptions()],
                  selectedOptionsBuilder: (context, values) {
                    return Text(values
                        .map((value) =>
                            widget.options.firstWhereOrNull(
                                (e) => e['value'] == value)?['label'] ??
                            value)
                        .join(', '));
                  },
                  onChanged: (values) async {
                    if (widget.onValidate != null) {
                      final res = await widget.onValidate?.call(values);
                      if (res != null) {
                        setState(() {
                          errorText = res;
                        });
                        widget.onValidateError?.call(res);
                      } else {
                        setState(() {
                          errorText = "";
                        });
                      }
                    }
                    widget.onChanged?.call(values);
                  },
                )
              : ShadSelect<String>(
                  anchor: const ShadAnchorAuto(),
                  allowDeselection: widget.allowDeselection,
                  initialValue: widget.options.firstWhereOrNull((e) =>
                      e['value'] == widget.initialValue?.first)?['label'],
                  placeholder: Text(widget.placeholder,
                      style: ShadTheme.of(context).inputTheme.placeholderStyle),
                  options: [_buildSelectOptions()],
                  selectedOptionBuilder: (context, value) {
                    final result = widget.options
                        .firstWhereOrNull((e) => e['value'] == value)?['label'];
                    return Text(result ?? value);
                  },
                  onChanged: (value) async {
                    if (widget.onValidate != null) {
                      final res = await widget.onValidate?.call([value]);
                      if (res != null) {
                        setState(() {
                          errorText = res;
                        });
                        widget.onValidateError?.call(res);
                      } else {
                        setState(() {
                          errorText = "";
                        });
                      }
                    }
                    widget.onChanged?.call([value!]);
                  },
                ),
        ),
        if (errorText.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(left: 8, right: 8, top: 4),
            child: Text(errorText,
                style: const TextStyle(color: Colors.red, fontSize: 13)),
          ),
      ],
    );
    if (!widget.showLabel) {
      return selectWidget;
    }
    return ListTile(
        contentPadding: EdgeInsets.zero,
        minVerticalPadding: 0,
        title: Text(widget.label, style: defaultTitleStyle),
        subtitle: selectWidget);
  }
}

class ShadSelectWithInput extends StatefulWidget {
  final String label;
  final String placeholder;
  final String searchPlaceholder;
  final bool isMultiple;
  final bool showLabel;
  final bool hasSuffix;
  final List<String>? initialValue;
  final List<Map<String, String>> options;
  final Function(List<String>)? onChanged;
  final Function()? onRefresh;
  final Function(String)? onAddNew; // Callback for adding new items

  const ShadSelectWithInput({
    super.key,
    required this.label,
    required this.placeholder,
    required this.searchPlaceholder,
    required this.options,
    this.showLabel = true,
    this.isMultiple = false,
    this.hasSuffix = false,
    this.initialValue,
    this.onChanged,
    this.onRefresh,
    this.onAddNew,
  });

  @override
  State<ShadSelectWithInput> createState() => _ShadSelectWithInputState();
}

class _ShadSelectWithInputState extends State<ShadSelectWithInput> {
  late final TextEditingController controller = TextEditingController();

  // GetX reactive variables for state management
  final RxString searchValue = ''.obs;
  final RxList<Map<String, String>> filteredOptions = <Map<String, String>>[].obs;
  final RxList<String> selectedValues = <String>[].obs;
  final RxBool showAddOption = false.obs;

  @override
  void initState() {
    super.initState();
    filteredOptions.value = List.from(widget.options);
    selectedValues.value = List.from(widget.initialValue ?? []);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  void _handleSearchChanged(String value) {
    searchValue.value = value;

    // Filter existing options
    filteredOptions.value = widget.options
        .where((option) =>
            option['label']!.toLowerCase().contains(value.toLowerCase()))
        .toList();

    // Check if we should show "add new" option
    if (value.isNotEmpty) {
      final exactMatch = widget.options.any((option) =>
          option['label']!.toLowerCase() == value.toLowerCase() ||
          option['value']!.toLowerCase() == value.toLowerCase());
      showAddOption.value = !exactMatch;
    } else {
      showAddOption.value = false;
    }
  }

  void _handleAddNew(String value) {
    if (value.isNotEmpty && widget.onAddNew != null) {
      widget.onAddNew!(value);

      // Add to selected values if not multiple selection
      if (!widget.isMultiple) {
        selectedValues.value = [value];
      } else {
        if (!selectedValues.contains(value)) {
          selectedValues.add(value);
        }
      }

      // Notify parent of change
      widget.onChanged?.call(selectedValues.toList());

      // Clear search
      controller.clear();
      searchValue.value = '';
      showAddOption.value = false;
      filteredOptions.value = List.from(widget.options);
    }
  }

  Widget _buildSelectOptions() {
    final options = <Widget>[];

    // Add "Add new" option if applicable
    if (showAddOption.value && searchValue.value.isNotEmpty) {
      options.add(
        ShadOption(
          value: '__ADD_NEW__${searchValue.value}',
          child: Row(
            children: [
              const Icon(Icons.add, size: 16),
              const SizedBox(width: 8),
              Text('Add "${searchValue.value}"'),
            ],
          ),
        ),
      );
    }

    // Add filtered existing options
    for (final option in filteredOptions) {
      options.add(
        ShadOption(
          value: option['value']!,
          child: Text(option['label']!),
        ),
      );
    }

    return Column(children: options);
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      title: widget.showLabel
          ? Text(widget.label, style: defaultTitleStyle)
          : null,
      subtitle: Obx(() => Row(
        children: [
          Expanded(
            child: widget.isMultiple
                ? ShadSelect<String>.multipleWithSearch(
                    closeOnSelect: false,
                    anchor: const ShadAnchorAuto(),
                    search: ShadInput(
                      controller: controller,
                      placeholder: Text(
                        widget.searchPlaceholder,
                        style: ShadTheme.of(context).inputTheme.placeholderStyle,
                      ),
                      onChanged: _handleSearchChanged,
                    ),
                    initialValues: selectedValues.toList(),
                    placeholder: Text(widget.placeholder,
                        style: ShadTheme.of(context).inputTheme.placeholderStyle),
                    options: [_buildSelectOptions()],
                    selectedOptionsBuilder: (context, values) {
                      return Text(values
                          .map((value) {
                            if (value.startsWith('__ADD_NEW__')) {
                              return value.substring(11); // Remove __ADD_NEW__ prefix
                            }
                            return widget.options.firstWhereOrNull(
                                    (e) => e['value'] == value)?['label'] ??
                                value;
                          })
                          .join(', '));
                    },
                    onSearchChanged: _handleSearchChanged,
                    onChanged: (values) {
                      // Handle add new items
                      final newValues = <String>[];
                      for (final value in values) {
                        if (value.startsWith('__ADD_NEW__')) {
                          final newItem = value.substring(11);
                          _handleAddNew(newItem);
                          newValues.add(newItem);
                        } else {
                          newValues.add(value);
                        }
                      }

                      selectedValues.value = newValues;
                      widget.onChanged?.call(newValues);

                      // Clear search after selection
                      controller.clear();
                      searchValue.value = '';
                      showAddOption.value = false;
                      filteredOptions.value = List.from(widget.options);
                    },
                  )
                : ShadSelect<String>.withSearch(
                    anchor: const ShadAnchorAuto(),
                    search: ShadInput(
                      controller: controller,
                      placeholder: Text(
                        widget.searchPlaceholder,
                        style: ShadTheme.of(context).inputTheme.placeholderStyle,
                      ),
                      onChanged: _handleSearchChanged,
                    ),
                    initialValue: selectedValues.isNotEmpty ? selectedValues.first : null,
                    placeholder: Text(widget.placeholder,
                        style: ShadTheme.of(context).inputTheme.placeholderStyle),
                    options: [_buildSelectOptions()],
                    selectedOptionBuilder: (context, value) {
                      if (value.startsWith('__ADD_NEW__')) {
                        return Text(value.substring(11)); // Remove __ADD_NEW__ prefix
                      }
                      return Text(widget.options.firstWhereOrNull(
                              (e) => e['value'] == value)?['label'] ??
                          value);
                    },
                    onSearchChanged: _handleSearchChanged,
                    onChanged: (value) {
                      if (value != null) {
                        if (value.startsWith('__ADD_NEW__')) {
                          final newItem = value.substring(11);
                          _handleAddNew(newItem);
                        } else {
                          selectedValues.value = [value];
                          widget.onChanged?.call([value]);
                        }
                      }

                      // Clear search after selection
                      controller.clear();
                      searchValue.value = '';
                      showAddOption.value = false;
                      filteredOptions.value = List.from(widget.options);
                    },
                  ),
          ),
          if (widget.hasSuffix && widget.onRefresh != null)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: widget.onRefresh,
            ),
        ],
      )),
    );
  }
}

class ShadSelectWithSearch extends StatefulWidget {
  final String label;
  final String placeholder;
  final String searchPlaceholder;
  final bool isMultiple;
  final List<String>? initialValue;
  final List<Map<String, String>> options;
  final Function(List<String>)? onChanged;
  final bool showLabel;

  const ShadSelectWithSearch({
    super.key,
    required this.label,
    required this.placeholder,
    required this.searchPlaceholder,
    required this.options,
    this.isMultiple = false,
    this.initialValue,
    this.onChanged,
    this.showLabel = true,
  });

  @override
  State<ShadSelectWithSearch> createState() => _ShadSelectWithSearchState();
}

class _ShadSelectWithSearchState extends State<ShadSelectWithSearch> {
  late final TextEditingController controller = TextEditingController();
  String searchValue = '';
  List<Map<String, String>> filteredOptions = [];

  @override
  void initState() {
    super.initState();
    filteredOptions = List.from(widget.options);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  void _handleSearchChanged(String value) {
    setState(() {
      searchValue = value;
      filteredOptions = widget.options
          .where((option) =>
              option['label']!.toLowerCase().contains(value.toLowerCase()))
          .toList();
    });
  }

  Widget _buildSelectOptions() {
    if (filteredOptions.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 24),
        child: Text('common.ui.emptyList'.tr),
      );
    }

    return Column(
      children: [
        ...filteredOptions.map(
          (option) => ShadOption(
            value: option['value']!,
            child: Text(option['label']!),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      title: widget.showLabel
          ? Text(widget.label, style: defaultTitleStyle)
          : null,
      subtitle: widget.isMultiple
          ? ShadSelect<String>.multipleWithSearch(
              closeOnSelect: false,
              anchor: const ShadAnchorAuto(),
              search: ShadInput(
                controller: controller,
                placeholder: Text(
                  widget.searchPlaceholder,
                  style: ShadTheme.of(context).inputTheme.placeholderStyle,
                ),
                onChanged: _handleSearchChanged,
              ),
              initialValues: widget.initialValue ?? [],
              placeholder: Text(widget.placeholder,
                  style: ShadTheme.of(context).inputTheme.placeholderStyle),
              options: [_buildSelectOptions()],
              selectedOptionsBuilder: (context, values) {
                return Text(values
                    .map((value) =>
                        widget.options.firstWhereOrNull(
                            (e) => e['value'] == value)?['label'] ??
                        value)
                    .join(', '));
              },
              onSearchChanged: _handleSearchChanged,
              onChanged: (values) {
                widget.onChanged?.call(values);
                setState(() {
                  searchValue = '';
                  controller.text = '';
                  // 清空后重置过滤列表
                  filteredOptions = List.from(widget.options);
                });
              },
            )
          : ShadSelect<String>.withSearch(
              anchor: const ShadAnchorAuto(),
              search: ShadInput(
                controller: controller,
                placeholder: Text(
                  widget.searchPlaceholder,
                  style: ShadTheme.of(context).inputTheme.placeholderStyle,
                ),
                onChanged: _handleSearchChanged,
              ),
              initialValue: widget.initialValue?.isNotEmpty ?? false
                  ? (widget.options
                          .any((e) => e['value'] == widget.initialValue?.first)
                      ? widget.initialValue?.first
                      : null)
                  : null,
              placeholder: Text(widget.placeholder,
                  style: ShadTheme.of(context).inputTheme.placeholderStyle),
              options: [_buildSelectOptions()],
              selectedOptionBuilder: (context, value) {
                return Text(widget.options.firstWhereOrNull(
                        (e) => e['value'] == value)?['label'] ??
                    value);
              },
              onSearchChanged: _handleSearchChanged,
              onChanged: (value) {
                if (value != null) {
                  widget.onChanged?.call([value]);
                }
              },
            ),
    );
  }
}

class ShadInputWithValidate extends StatefulWidget {
  final String label;
  final String placeholder;
  final String? initialValue;
  final bool isRequired;
  final int? maxLines;
  final Function(String)? onChanged;
  final Future<String?> Function(String) onValidate;
  final Function(String)? onValidateError;
  const ShadInputWithValidate({
    super.key,
    required this.label,
    required this.placeholder,
    this.initialValue,
    this.isRequired = false,
    this.maxLines,
    this.onChanged,
    required this.onValidate,
    this.onValidateError,
  });

  @override
  State<ShadInputWithValidate> createState() => _ShadInputWithValidateState();
}

class _ShadInputWithValidateState extends State<ShadInputWithValidate> {
  late final TextEditingController _controller = TextEditingController();
  String errorText = "";

  @override
  void initState() {
    super.initState();
    // 在 initState 中初始化 controller
    if (widget.initialValue != null) {
      _controller.text = widget.initialValue!;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      visualDensity: VisualDensity.compact,
      title: Text(widget.label, style: defaultTitleStyle),
      subtitle: Builder(builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShadInput(
              placeholder: Text(widget.placeholder),
              controller: _controller,
              maxLines: widget.maxLines,
              onPressedOutside: (event) async {
                logger.w("onPressedOutside");
                widget.onChanged?.call(_controller.text);
              },
              onEditingComplete: () async {
                // logger.w("onEditingComplete");
              },
              onChanged: (value) async {
                // logger.w("onChanged: $value");
                final error = await widget.onValidate.call(_controller.text);
                if (!mounted) return;

                if (error != null && error.isNotEmpty) {
                  setState(() {
                    errorText = error;
                  });
                  widget.onValidateError?.call(error);
                } else {
                  setState(() {
                    errorText = "";
                  });
                }
              },
            ),
            if (errorText.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(left: 8, right: 8, top: 4),
                child: Text(errorText,
                    style: const TextStyle(color: Colors.red, fontSize: 13)),
              ),
          ],
        );
      }),
    );
  }
}

class ShadInputPasswordCustom extends StatefulWidget {
  final String label;
  final String placeholder;
  final String? initialValue;
  final bool isRequired;
  final Function(String)? onChanged;
  final Future<String?> Function(String) onValidate;
  final Function(String)? onValidateError;

  const ShadInputPasswordCustom({
    super.key,
    required this.label,
    required this.placeholder,
    this.initialValue,
    this.isRequired = false,
    this.onChanged,
    required this.onValidate,
    this.onValidateError,
  });

  @override
  State<ShadInputPasswordCustom> createState() =>
      _ShadInputPasswordCustomState();
}

class _ShadInputPasswordCustomState extends State<ShadInputPasswordCustom> {
  late final TextEditingController _controller = TextEditingController();
  String errorText = "";
  bool showPassword = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialValue != null) {
      _controller.text = widget.initialValue!;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      visualDensity: VisualDensity.compact,
      title: Text(widget.label, style: defaultTitleStyle),
      subtitle: Builder(builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShadInput(
              placeholder: Text(widget.placeholder),
              controller: _controller,
              obscureText: !showPassword,
              trailing: GestureDetector(
                onTapDown: (_) {
                  setState(() {
                    showPassword = true;
                  });
                },
                onTapUp: (_) {
                  setState(() {
                    showPassword = false;
                  });
                },
                onTapCancel: () {
                  setState(() {
                    showPassword = false;
                  });
                },
                child: Icon(
                    showPassword ? Icons.visibility_off : Icons.visibility),
              ),
              onPressedOutside: (event) async {
                widget.onChanged?.call(_controller.text);
              },
              onChanged: (value) async {
                final error = await widget.onValidate.call(value);
                if (error != null && error.isNotEmpty) {
                  setState(() {
                    errorText = error;
                  });
                  widget.onValidateError?.call(error);
                } else {
                  setState(() {
                    errorText = "";
                  });
                }
              },
            ),
            if (errorText.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(left: 8, right: 8, top: 4),
                child: Text(errorText,
                    style: const TextStyle(color: Colors.red, fontSize: 13)),
              ),
          ],
        );
      }),
    );
  }
}

class ShadCheckboxGroup extends StatefulWidget {
  final String label;
  final List<Map<String, String>> items;
  final List<String> initialValues;
  final Function(List<String>) onChanged;
  final Future<String?> Function(List<String>) onValidate;
  final Function(String)? onValidateError;

  const ShadCheckboxGroup({
    super.key,
    required this.label,
    required this.items,
    this.initialValues = const [],
    required this.onChanged,
    required this.onValidate,
    this.onValidateError,
  });

  @override
  State<ShadCheckboxGroup> createState() => _ShadCheckboxGroupState();
}

class _ShadCheckboxGroupState extends State<ShadCheckboxGroup> {
  String errorText = "";

  @override
  Widget build(BuildContext context) {
    List<String> selectedValues = widget.initialValues;
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      visualDensity: VisualDensity.compact,
      title: Text(widget.label, style: defaultTitleStyle),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Wrap(
              spacing: 16,
              runSpacing: 8,
              children: widget.items
                  .map((item) => ShadCheckbox(
                        value: widget.initialValues.contains(item['value']),
                        label: Text(item['label']!),
                        onChanged: (value) async {
                          logger.i(
                              "label: ${item['label']}, value: ${item['value']}");

                          selectedValues = value
                              ? {...selectedValues, item['value']!}.toList()
                              : selectedValues
                                  .where((v) => v != item['value'])
                                  .toList();
                          final error =
                              await widget.onValidate.call(selectedValues);
                          if (error != null && error.isNotEmpty) {
                            setState(() {
                              errorText = error;
                            });
                            widget.onValidateError?.call(error);
                          } else {
                            setState(() {
                              errorText = "";
                            });
                          }
                          widget.onChanged(selectedValues);
                        },
                      ))
                  .toList(),
            ),
          ),
          if (errorText.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(left: 8, right: 8, top: 4),
              child: Text(errorText,
                  style: const TextStyle(color: Colors.red, fontSize: 13)),
            ),
        ],
      ),
    );
  }
}

class ShadRadioGroupCustom extends StatefulWidget {
  final String label;
  final List<Map<String, String>> items;
  final String? initialValue;
  final Function(String) onChanged;
  final bool isHorizontal;
  const ShadRadioGroupCustom({
    super.key,
    required this.label,
    required this.items,
    this.initialValue,
    required this.onChanged,
    this.isHorizontal = true,
  });

  @override
  State<ShadRadioGroupCustom> createState() => _ShadRadioGroupCustomState();
}

class _ShadRadioGroupCustomState extends State<ShadRadioGroupCustom> {
  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.only(top: 4.0),
      minVerticalPadding: 0,
      dense: true,
      title: Text(widget.label, style: defaultTitleStyle),
      subtitle: Padding(
        padding: const EdgeInsets.only(top: 4.0),
        child: ShadRadioGroup(
          axis: widget.isHorizontal ? Axis.horizontal : Axis.vertical,
          initialValue: widget.initialValue,
          items: widget.items
              .map(
                  (e) => ShadRadio(value: e['value'], label: Text(e['label']!)))
              .toList(),
          onChanged: (value) {
            widget.onChanged(value!);
          },
        ),
      ),
    );
  }
}

class ShadSwitchCustom extends StatefulWidget {
  final String label;
  final bool? initialValue;
  final MainAxisAlignment? mainAxisAlignment;
  final Function(bool) onChanged;

  const ShadSwitchCustom({
    super.key,
    required this.label,
    this.initialValue = false,
    this.mainAxisAlignment = MainAxisAlignment.start,
    required this.onChanged,
  });

  @override
  State<ShadSwitchCustom> createState() => _ShadSwitchCustomState();
}

class _ShadSwitchCustomState extends State<ShadSwitchCustom> {
  @override
  Widget build(BuildContext context) {
    bool _value = widget.initialValue ?? false;
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      dense: true,
      onTap: () {
        setState(() {
          _value = !_value;
          widget.onChanged(_value);
        });
      },
      title: Row(
        mainAxisAlignment: widget.mainAxisAlignment ?? MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              widget.label,
              style: defaultTitleStyle,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 16),
          ShadSwitch(
            value: _value,
            onChanged: (v) {
              setState(() {
                _value = v;
                widget.onChanged(v);
              });
            },
          ),
        ],
      ),
    );
  }
}

class ShadColorPickerCustom extends StatefulWidget {
  final String label;
  final Color? initialValue;
  final MainAxisAlignment? mainAxisAlignment;
  final Function(Color) onChanged;

  const ShadColorPickerCustom({
    super.key,
    required this.label,
    this.initialValue,
    this.mainAxisAlignment = MainAxisAlignment.start,
    required this.onChanged,
  });

  @override
  State<ShadColorPickerCustom> createState() => _ShadColorPickerCustomState();
}

class _ShadColorPickerCustomState extends State<ShadColorPickerCustom> {
  @override
  Widget build(BuildContext context) {
    Color _value = widget.initialValue ?? Colors.transparent;
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      dense: true,
      onTap: () {
        widget.onChanged(_value);
      },
      title: Row(
        mainAxisAlignment: widget.mainAxisAlignment ?? MainAxisAlignment.start,
        spacing: 16,
        children: [
          Text(widget.label, style: defaultTitleStyle),
          TextButton(
            onPressed: () {
              showShadDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      content: SingleChildScrollView(
                        child: SizedBox(
                          width: 300,
                          child: ColorPicker(
                            color: _value,
                            onColorChanged: (Color color) {
                              _value = color;
                            },
                            pickersEnabled: const {
                              ColorPickerType.primary: false,
                              ColorPickerType.accent: false,
                              ColorPickerType.wheel: true,
                              ColorPickerType.custom: true,
                              ColorPickerType.customSecondary: true
                            },
                            // enableOpacity: true,
                            showColorCode: true,
                            showEditIconButton: true,
                            copyPasteBehavior:
                                const ColorPickerCopyPasteBehavior(
                                    copyFormat:
                                        ColorPickerCopyFormat.numHexRRGGBB),
                            heading: Text(
                              'common.colorPicker.selectColor'.tr,
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            subheading: Text(
                              'common.colorPicker.selectColor'.tr,
                              style: Theme.of(context).textTheme.titleSmall,
                            ),
                          ),
                        ),
                      ),
                      actions: [
                        ShadButton.outline(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Text('common.colorPicker.cancel'.tr),
                        ),
                        ShadButton(
                          onPressed: () {
                            widget.onChanged(_value);
                            Navigator.of(context).pop();
                          },
                          child: Text('common.colorPicker.confirm'.tr),
                        ),
                      ],
                    );
                  });
            },
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: _value,
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ShadColorPickerDualColorsCustom extends StatefulWidget {
  final String label;
  final String label1;
  final String label2;
  final Color? initialValue1;
  final Color? initialValue2;
  final MainAxisAlignment? mainAxisAlignment;
  final Function(Color, Color) onChanged;

  const ShadColorPickerDualColorsCustom({
    super.key,
    required this.label,
    required this.label1,
    required this.label2,
    this.initialValue1,
    this.initialValue2,
    this.mainAxisAlignment = MainAxisAlignment.start,
    required this.onChanged,
  });

  @override
  State<ShadColorPickerDualColorsCustom> createState() =>
      _ShadColorPickerDualColorsCustomState();
}

class _ShadColorPickerDualColorsCustomState
    extends State<ShadColorPickerDualColorsCustom> {
  @override
  Widget build(BuildContext context) {
    Color _value1 = widget.initialValue1 ?? Colors.transparent;
    Color _value2 = widget.initialValue2 ?? Colors.transparent;
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      dense: true,
      onTap: () {
        widget.onChanged(_value1, _value2);
      },
      title: Text(widget.label, style: defaultTitleStyle),
      subtitle: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: 16,
        children: [
          Row(
            children: [
              Text(widget.label1, style: defaultTitleStyle),
              TextButton(
                onPressed: () {
                  showShadDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          content: SingleChildScrollView(
                            child: SizedBox(
                              width: 300,
                              child: ColorPicker(
                                color: _value1,
                                onColorChanged: (Color color) {
                                  _value1 = color;
                                },
                                pickersEnabled: const {
                                  ColorPickerType.primary: false,
                                  ColorPickerType.accent: false,
                                  ColorPickerType.wheel: true,
                                  ColorPickerType.custom: true,
                                  ColorPickerType.customSecondary: true
                                },
                                // enableOpacity: true,
                                showColorCode: true,
                                showEditIconButton: true,
                                copyPasteBehavior:
                                    const ColorPickerCopyPasteBehavior(
                                        copyFormat:
                                            ColorPickerCopyFormat.numHexRRGGBB),
                                heading: Text(
                                  'common.colorPicker.selectColor'.tr,
                                  style:
                                      Theme.of(context).textTheme.headlineSmall,
                                ),
                                subheading: Text(
                                  'common.colorPicker.selectColor'.tr,
                                  style: Theme.of(context).textTheme.titleSmall,
                                ),
                              ),
                            ),
                          ),
                          actions: [
                            ShadButton.outline(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: Text('common.colorPicker.cancel'.tr),
                            ),
                            ShadButton(
                              onPressed: () {
                                widget.onChanged(_value1, _value2);
                                Navigator.of(context).pop();
                              },
                              child: Text('common.colorPicker.confirm'.tr),
                            ),
                          ],
                        );
                      });
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: _value1,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
          Row(
            children: [
              Text(widget.label2, style: defaultTitleStyle),
              TextButton(
                onPressed: () {
                  showShadDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          content: SingleChildScrollView(
                            child: SizedBox(
                              width: 300,
                              child: ColorPicker(
                                color: _value2,
                                onColorChanged: (Color color) {
                                  _value2 = color;
                                },
                                pickersEnabled: const {
                                  ColorPickerType.primary: false,
                                  ColorPickerType.accent: false,
                                  ColorPickerType.wheel: true,
                                  ColorPickerType.custom: true,
                                  ColorPickerType.customSecondary: true
                                },
                                // enableOpacity: true,
                                showColorCode: true,
                                showEditIconButton: true,
                                copyPasteBehavior:
                                    const ColorPickerCopyPasteBehavior(
                                        copyFormat:
                                            ColorPickerCopyFormat.numHexRRGGBB),
                                heading: Text(
                                  'common.colorPicker.selectColor'.tr,
                                  style:
                                      Theme.of(context).textTheme.headlineSmall,
                                ),
                                subheading: Text(
                                  'common.colorPicker.selectColor'.tr,
                                  style: Theme.of(context).textTheme.titleSmall,
                                ),
                              ),
                            ),
                          ),
                          actions: [
                            ShadButton.outline(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: Text('common.colorPicker.cancel'.tr),
                            ),
                            ShadButton(
                              onPressed: () {
                                widget.onChanged(_value1, _value2);
                                Navigator.of(context).pop();
                              },
                              child: Text('common.colorPicker.confirm'.tr),
                            ),
                          ],
                        );
                      });
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: _value2,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class ShadSliderCustom extends StatefulWidget {
  final String label;
  final double? initialValue;
  final double? max;
  final double? min;
  final int? divisions;
  final String? labelSuffix;
  final Function(double) onChanged;
  const ShadSliderCustom({
    super.key,
    required this.label,
    this.initialValue = 0,
    this.max = 100,
    this.min = 0,
    this.divisions = 10,
    this.labelSuffix = "%",
    required this.onChanged,
  });

  @override
  State<ShadSliderCustom> createState() => _ShadSliderCustomState();
}

class _ShadSliderCustomState extends State<ShadSliderCustom> {
  @override
  Widget build(BuildContext context) {
    double _value = widget.initialValue!;
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      onTap: () {},
      title: Text(widget.label, style: defaultTitleStyle),
      subtitle: Padding(
        padding: const EdgeInsets.only(top: 8.0),
        child: ShadSlider(
          initialValue: widget.initialValue,
          min: widget.min ?? 0,
          max: widget.max ?? 100,
          divisions: widget.divisions,
          label: '${_value.toStringAsFixed(1)}${widget.labelSuffix}',
          onChanged: (value) {
            setState(() {
              _value = value;
              widget.onChanged(value);
            });
          },
        ),
      ),
    );
  }
}

class ShadFieldMappingTable extends StatefulWidget {
  final List<String> fieldList;
  final Map<String, List<Map<String, String>>> optionsList;
  final List<Map<String, String>>? defaultOptionsList;
  final String cardModel;
  final Function(String field, PatternMatch patternMatch) onUpdateFieldMapping;
  final PatternMatch? Function(String field) getFieldMappingValue;

  const ShadFieldMappingTable({
    super.key,
    required this.fieldList,
    required this.optionsList,
    this.defaultOptionsList,
    required this.cardModel,
    required this.onUpdateFieldMapping,
    required this.getFieldMappingValue,
  });

  @override
  State<ShadFieldMappingTable> createState() => _ShadFieldMappingTableState();
}

class _ShadFieldMappingTableState extends State<ShadFieldMappingTable> {
  // Map to store the current state of each switch
  final Map<String, bool> _switchStates = {};

  @override
  void initState() {
    super.initState();
    // Initialize switch states
    for (final field in widget.fieldList) {
      final patternMatch = widget.getFieldMappingValue(field);
      _switchStates[field] = patternMatch?.keepPrefix ?? true;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Calculate a safe width that ensures min <= max
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    // Set appropriate minimum width based on device size
    final minWidth = isMobile ? screenWidth * 0.9 : 400.0;

    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      title: Text('common.ui.fieldMapping'.tr, style: defaultTitleStyle),
      subtitle: Padding(
        padding: const EdgeInsets.only(top: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              constraints: BoxConstraints(
                maxHeight: (1 + widget.fieldList.length) * 48.0,
              ),
              width: double.infinity,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: SizedBox(
                  width: max(minWidth, screenWidth * 0.9),
                  child: ShadTable(
                    columnCount: 3,
                    rowCount: widget.fieldList.length,
                    pinnedRowCount: 1,
                    header: (context, column) {
                      if (column == 0) {
                        return ShadTableCell.header(
                          child: Text('common.ui.templateField'.tr),
                        );
                      } else if (column == 1) {
                        return ShadTableCell.header(
                          child: Text('common.ui.matchMode'.tr),
                        );
                      } else {
                        return ShadTableCell.header(
                          child: Text('common.ui.keepPrefix'.tr),
                        );
                      }
                    },
                    columnSpanExtent: (index) {
                      // Adjust column widths based on device type
                      if (isMobile) {
                        // Mobile-specific column widths
                        final splitMap = {
                          0: 0.25, // Field name column
                          1: 0.40, // Match mode column - reduced further
                          2: 0.35, // Keep prefix column - increased further
                        };
                        return FractionalTableSpanExtent(
                            splitMap[index] ?? 0.25);
                      } else if (Platform.isWindows ||
                          Platform.isLinux ||
                          Platform.isMacOS) {
                        final splitMap = {
                          0: 0.3,
                          1: 0.4, // Reduced further
                          2: 0.3, // Increased further
                        };
                        return FractionalTableSpanExtent(
                            splitMap[index] ?? 0.2);
                      } else {
                        if (index == 1) {
                          return const FractionalTableSpanExtent(
                              0.4); // Reduced further
                        }
                        return FractionalTableSpanExtent(index == 0
                            ? 0.3
                            : 0.3); // Equal distribution for first and last column
                      }
                    },
                    rowSpanExtent: (index) => const FixedTableSpanExtent(48),
                    builder: (context, index) {
                      final field = widget.fieldList[index.row];
                      if (index.column == 0) {
                        return ShadTableCell(child: Text(field));
                      } else if (index.column == 1) {
                        return ShadTableCell(
                          child: Builder(
                            builder: (context) {
                              // 获取字段专属选项或默认选项
                              final fieldSpecificOptions =
                                  widget.optionsList[field] ??
                                      widget.defaultOptionsList ??
                                      [];

                              PatternMatch? patternMatch =
                                  widget.getFieldMappingValue(field);

                              return SizedBox(
                                width: double.infinity,
                                child: ShadSelectWithInput(
                                  key: ValueKey(
                                      'field_mapping_${field}_${widget.cardModel}'),
                                  label: 'common.ui.matchMode'.tr,
                                  placeholder: 'common.ui.selectOrEnter'.tr,
                                  searchPlaceholder:
                                      'common.ui.searchPlaceholder'.tr,
                                  isMultiple: false,
                                  showLabel: false,
                                  initialValue: patternMatch?.regex != null
                                      ? [patternMatch!.regex]
                                      : [],
                                  options: fieldSpecificOptions,
                                  onChanged: (value) {
                                    widget.onUpdateFieldMapping(
                                      field,
                                      PatternMatch(
                                        field,
                                        value.single,
                                        null,
                                        _switchStates[field] ?? true,
                                      ),
                                    );
                                  },
                                  onAddNew: (newPattern) {
                                    // Add new regex pattern to field-specific options
                                    final newOption = {
                                      'value': newPattern,
                                      'label': newPattern,
                                    };

                                    // Check if pattern already exists
                                    final exists = fieldSpecificOptions.any((option) =>
                                        option['value'] == newPattern);

                                    if (!exists) {
                                      fieldSpecificOptions.add(newOption);
                                    }

                                    // Apply the new pattern
                                    widget.onUpdateFieldMapping(
                                      field,
                                      PatternMatch(
                                        field,
                                        newPattern,
                                        null,
                                        _switchStates[field] ?? true,
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                          ),
                        );
                      } else {
                        return ShadTableCell(
                          child: Builder(
                            builder: (context) {
                              PatternMatch? patternMatch =
                                  widget.getFieldMappingValue(field);
                              // Use the cached state or get from pattern match
                              bool currentValue = _switchStates[field] ??
                                  (patternMatch?.keepPrefix ?? true);

                              // Wrap the switch in a Container with padding to prevent overflow
                              return Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                alignment: Alignment.centerLeft,
                                child: SizedBox(
                                  width:
                                      80, // Further increased width to prevent overflow
                                  child: ShadSwitch(
                                    value: currentValue,
                                    onChanged: (v) {
                                      // Update local state immediately
                                      setState(() {
                                        _switchStates[field] = v;
                                      });

                                      // Then update the parent
                                      widget.onUpdateFieldMapping(
                                        field,
                                        PatternMatch(
                                          field,
                                          patternMatch?.regex ?? "",
                                          null,
                                          v,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      }
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
