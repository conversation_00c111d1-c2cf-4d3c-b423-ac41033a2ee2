import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
// Provides [Player], [Media], [Playlist] etc.
import 'package:flutter_slidable/flutter_slidable.dart';

class PlaylistView extends StatelessWidget {
  final videoNoteController = Get.find<VideoNoteController>();
  final settingController = Get.find<SettingController>();

  PlaylistView({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 如果高度极小，只显示按钮行
        if (constraints.maxHeight < 100) {
          // 根据实际需要调整这个值
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Tooltip(
                  message: 'videoNotes.tooltip.openLocal'.tr,
                  child: IconButton.outlined(
                    onPressed: () => videoNoteController.openLocalVideo(),
                    icon: const Icon(Icons.folder_open),
                    iconSize: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Tooltip(
                  message: 'videoNotes.tooltip.openNetwork'.tr,
                  child: IconButton.outlined(
                    onPressed: () => videoNoteController.openNetworkVideo(),
                    icon: const Icon(Icons.link),
                    iconSize: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Tooltip(
                  message: 'videoNotes.tooltip.openPlaylist'.tr,
                  child: IconButton.outlined(
                    onPressed: () => videoNoteController.openPlaylist(),
                    icon: const Icon(Icons.playlist_play),
                    iconSize: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Tooltip(
                  message: 'videoNotes.tooltip.savePlaylist'.tr,
                  child: IconButton.outlined(
                    onPressed: () => videoNoteController.saveCurrentPlaylist(),
                    icon: const Icon(Icons.save),
                    iconSize: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Tooltip(
                  message: 'videoNotes.tooltip.clearPlaylist'.tr,
                  child: IconButton.outlined(
                    onPressed: () => videoNoteController.clearPlaylist(),
                    icon: const Icon(Icons.delete_forever),
                    iconSize: 24,
                  ),
                ),
              ],
            ),
          );
        }

        // 正常高度时显示完整布局
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Tooltip(
                    message: 'videoNotes.tooltip.openLocal'.tr,
                    child: IconButton.outlined(
                      onPressed: () => videoNoteController.openLocalVideo(),
                      icon: const Icon(Icons.folder_open),
                      iconSize: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Tooltip(
                    message: 'videoNotes.tooltip.openNetwork'.tr,
                    child: IconButton.outlined(
                      onPressed: () => videoNoteController.openNetworkVideo(),
                      icon: const Icon(Icons.link),
                      iconSize: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Tooltip(
                    message: 'videoNotes.tooltip.openPlaylist'.tr,
                    child: IconButton.outlined(
                      onPressed: () => videoNoteController.openPlaylist(),
                      icon: const Icon(Icons.playlist_play),
                      iconSize: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Tooltip(
                    message: 'videoNotes.tooltip.savePlaylist'.tr,
                    child: IconButton.outlined(
                      onPressed: () =>
                          videoNoteController.saveCurrentPlaylist(),
                      icon: const Icon(Icons.save),
                      iconSize: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Tooltip(
                    message: 'videoNotes.tooltip.clearPlaylist'.tr,
                    child: IconButton.outlined(
                      onPressed: () => videoNoteController.clearPlaylist(),
                      icon: const Icon(Icons.delete_forever),
                      iconSize: 24,
                    ),
                  ),
                ],
              ),
            ),
            // 列表部分
            Expanded(
              child: Obx(() {
                if (videoNoteController.playlist.isEmpty) {
                  return const EmptyWidget();
                }
                return ListView.builder(
                  itemCount: videoNoteController.playlist.length,
                  itemBuilder: (context, index) {
                    final item = videoNoteController.playlist[index];
                    final isPlaying =
                        videoNoteController.getCurrentVideoUrl() == item['url'];
                    return Slidable(
                      key: ValueKey(item['url']),
                      endActionPane: ActionPane(
                        motion: const ScrollMotion(),
                        children: [
                          SlidableAction(
                            onPressed: (context) {
                              videoNoteController.removeFromPlaylist(index);
                            },
                            backgroundColor:
                                Theme.of(context).colorScheme.error,
                            foregroundColor:
                                Theme.of(context).colorScheme.onError,
                            icon: Icons.delete,
                            label: 'videoNotes.action.delete'.tr,
                            autoClose: true,
                          ),
                        ],
                      ),
                      child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 0.0, vertical: 0.0),
                          child: Container(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 8.0, vertical: 4.0),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              border: Border.all(
                                color: Theme.of(context)
                                    .colorScheme
                                    .outline
                                    .withAlpha(75),
                              ),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: ListTile(
                              leading: ShadIconButtonCustom(
                                icon: isPlaying
                                    ? Icons.play_circle
                                    : Icons.play_circle_outline,
                                size: 22,
                                onPressed: () {},
                              ),
                              title: Text(
                                item['title'] ?? '',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: isPlaying
                                      ? Theme.of(context).primaryColor
                                      : Theme.of(context).colorScheme.onSurface,
                                  fontWeight: isPlaying
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                              subtitle: Text(
                                item['url'] ?? '',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onSurfaceVariant,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              onTap: () {
                                videoNoteController.playVideo(index);
                              },
                            ),
                          )),
                    );
                  },
                );
              }),
            ),
          ],
        );
      },
    );
  }
}

class EmptyWidget extends StatelessWidget {
  const EmptyWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "videoNotes.empty.playlistEmpty".tr,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            "videoNotes.empty.addVideoPrompt".tr,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }
}
