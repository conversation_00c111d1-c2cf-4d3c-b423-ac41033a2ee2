import 'package:anki_guru/controllers/video_notes/index.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// Provides [Player], [Media], [Playlist] etc.
import 'package:media_kit_video/media_kit_video.dart'; // Provides [VideoController] & [Video] etc.
import 'controls.dart';
import 'playlist_view.dart';
import 'subtitle_view.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:io';
import 'webview.dart';
import 'package:flutter/services.dart';


class TooltipShape extends ShapeBorder {
  const TooltipShape();

  final double arrowWidth = 20.0;
  final double arrowHeight = 10.0;

  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.only(top: arrowHeight);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) => Path();

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    rect = Rect.fromPoints(
        rect.topLeft + Offset(0, arrowHeight), rect.bottomRight);
    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(rect, const Radius.circular(8)))
      ..moveTo(rect.topRight.dx - arrowWidth * 1.5, rect.top)
      ..relativeLineTo(arrowWidth / 2, -arrowHeight)
      ..relativeLineTo(arrowWidth / 2, arrowHeight)
      ..close();
    return path;
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {}

  @override
  ShapeBorder scale(double t) => this;
}

class VideoNotePage extends StatefulWidget {
  const VideoNotePage({super.key});

  @override
  State<VideoNotePage> createState() => _VideoNotePageState();
}

class _VideoNotePageState extends State<VideoNotePage> {
  final videoNoteController = Get.find<VideoNoteController>();
  final clipboardController = Get.find<ClipboardController>();
  final settingController = Get.find<SettingController>();
  final _storage = StorageManager();
  // 添加FocusNode用于处理键盘事件
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    Get.put(BilibiliService());
    videoNoteController.loadSettings();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final rootDir = _storage.read(
          StorageBox.videoNotes, VideoNotesStorageKeys.rootDir, "");
      logger.d('rootDir: $rootDir');
      if (Platform.isAndroid && rootDir.toString().isEmpty) {
        Get.dialog(
          AlertDialog(
            title: Text('videoNotes.dialog.selectStorageLocation'.tr),
            content: Text('videoNotes.dialog.selectStorageLocationContent'.tr),
            actions: [
              TextButton(
                onPressed: () async {
                  await videoNoteController.selectRootDir();
                  Get.back();
                },
                child: Text('videoNotes.action.select'.tr),
              ),
            ],
          ),
        );
      }
      // 确保页面加载后自动获取焦点
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    // 暂停视频播放（如果有正在播放的视频）
    if (videoNoteController.getCurrentVideoUrl().isNotEmpty) {
      try {
        videoNoteController.player.value.pause();
      } catch (e) {
        debugPrint('Error pausing video: $e');
      }
    }
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => PopScope(
          canPop: !videoNoteController.isFullScreen.value,
          onPopInvokedWithResult: (bool didPop, dynamic result) {
            // If in fullscreen mode, exit fullscreen instead of navigating back
            if (!didPop && videoNoteController.isFullScreen.value) {
              videoNoteController.toggleFullscreen();
            }
          },
          child: KeyboardListener(
            focusNode: _focusNode,
            onKeyEvent: (keyEvent) {
              // 检测空格键按下事件
              if (keyEvent is KeyDownEvent &&
                  keyEvent.logicalKey == LogicalKeyboardKey.space) {
                videoNoteController.playOrPause();
              }
              // 检测ESC键退出全屏
              if (keyEvent is KeyDownEvent &&
                  keyEvent.logicalKey == LogicalKeyboardKey.escape &&
                  videoNoteController.isFullScreen.value) {
                videoNoteController.toggleFullscreen();
              }
            },
            child: Scaffold(
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              appBar: videoNoteController.isFullScreen.value
                  ? null // Hide app bar in fullscreen mode
                  : AppBar(
                      title: Text("videoNotes.title.main".tr),
                      centerTitle: true,
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      leading: IconButton(
                        icon: const Icon(Icons.arrow_back),
                        onPressed: () => Get.back(),
                      ),
                      actions: [
                        PopupMenuButton<String>(
                          icon: const Icon(Icons.more_horiz),
                          offset: const Offset(0, 40),
                          shape: const TooltipShape(),
                          onSelected: (value) {
                            switch (value) {
                              case 'webpage':
                                Get.to(() => VideoWebView());
                                break;
                              case 'bilibili':
                                videoNoteController.openBiliBiliVideo();
                                break;
                              case 'local':
                                videoNoteController.openLocalVideo();
                                break;
                              case 'network':
                                videoNoteController.openNetworkVideo();
                                break;
                              case 'playlist':
                                videoNoteController.openPlaylist();
                                break;
                              case 'settings':
                                Get.toNamed("/video-settings");
                                break;
                            }
                          },
                          itemBuilder: (BuildContext context) =>
                              <PopupMenuEntry<String>>[
                            PopupMenuItem<String>(
                              value: 'local',
                              child: Row(
                                children: [
                                  const Icon(Icons.folder_open),
                                  const SizedBox(width: 8),
                                  Text('videoNotes.menu.openLocal'.tr),
                                ],
                              ),
                            ),
                            PopupMenuItem<String>(
                              value: 'network',
                              child: Row(
                                children: [
                                  const Icon(Icons.link),
                                  const SizedBox(width: 8),
                                  Text('videoNotes.menu.openNetwork'.tr),
                                ],
                              ),
                            ),
                            PopupMenuItem<String>(
                              value: 'playlist',
                              child: Row(
                                children: [
                                  const Icon(Icons.playlist_play),
                                  const SizedBox(width: 8),
                                  Text('videoNotes.menu.openPlaylist'.tr),
                                ],
                              ),
                            ),
                            PopupMenuItem<String>(
                              value: 'settings',
                              child: Row(
                                children: [
                                  const Icon(Icons.settings),
                                  const SizedBox(width: 8),
                                  Text('videoNotes.menu.preferences'.tr),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
              body: videoNoteController.isFullScreen.value
                  ? // Fullscreen mode - video takes entire screen
                  SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height,
                      child: Video(
                        controller: videoNoteController.videoController,
                        controls: CustomMaterialVideoControls,
                      ),
                    )
                  : // Normal mode with the original layout
                  Column(
                      children: [
                        Expanded(
                          child: SizedBox(
                            width: MediaQuery.of(context).size.width,
                            child: Video(
                              controller: videoNoteController.videoController,
                              controls: CustomMaterialVideoControls,
                            ),
                          ),
                        ),
                        if (MediaQuery.of(context).orientation ==
                            Orientation.portrait)
                          const Expanded(
                            child: ExtraWidget(),
                          ),
                      ],
                    ),
            ),
          ),
        ));
  }
}

class ExtraWidget extends StatefulWidget {
  const ExtraWidget({super.key});
  @override
  State<ExtraWidget> createState() => _ExtraWidgetState();
}

class _ExtraWidgetState extends State<ExtraWidget>
    with TickerProviderStateMixin {
  final videoNoteController = Get.find<VideoNoteController>();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      TabBar(
        tabs: [
          Tab(text: 'videoNotes.navigation.playlist'.tr),
          Tab(text: 'videoNotes.navigation.subtitle'.tr),
        ],
        controller: _tabController,
        labelColor: Theme.of(context).colorScheme.onSurface,
        unselectedLabelColor:
            Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
        indicatorColor: Theme.of(context).colorScheme.primary,
      ),
      Expanded(
        child: TabBarView(
          controller: _tabController,
          children: [
            PlaylistView(),
            MySubtitleView(),
          ],
        ),
      ),
    ]);
  }
}

