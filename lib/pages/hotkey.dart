import 'package:flutter/material.dart';
import 'package:hotkey_manager/hotkey_manager.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';

class HotKeyInputDialog extends StatefulWidget {
  final HotKey? initialHotKey;

  const HotKeyInputDialog({
    super.key,
    this.initialHotKey,
  });

  static Future<HotKey?> show(BuildContext context, {HotKey? initialHotKey}) {
    return showDialog<HotKey>(
      context: context,
      builder: (context) => HotKeyInputDialog(initialHotKey: initialHotKey),
    );
  }

  @override
  State<HotKeyInputDialog> createState() => _HotKeyInputDialogState();
}

class _HotKeyInputDialogState extends State<HotKeyInputDialog> {
  final hotKey = Rx<HotKey?>(null);

  @override
  void initState() {
    super.initState();
    hotKey.value = widget.initialHotKey;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('hotkey.dialog.title'.tr),
      content: SizedBox(
          height: 150,
          child: Center(
              child: HotKeyRecorder(
            initalHotKey: hotKey.value,
            onHotKeyRecorded: (value) {
              hotKey.value = value;
            },
          ))),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('hotkey.action.cancel'.tr),
        ),
        TextButton(
          onPressed: () {
            if (hotKey.value == null) {
              showToastNotification(context, 'hotkey.error.title'.tr, 'hotkey.error.enterShortcut'.tr, type: "error");
              return;
            }

            // 检查是否包含修饰键
            final hasModifier = (hotKey.value!.modifiers ?? []).isNotEmpty;
            if (!hasModifier) {
              showToastNotification(
                  context, 'hotkey.error.title'.tr, 'hotkey.error.requireModifier'.tr,
                  type: "error");
              return;
            }
            Navigator.pop(context, hotKey.value);
          },
          child: Text('hotkey.action.confirm'.tr),
        ),
      ],
    );
  }
}
