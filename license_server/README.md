
# 套餐激活码生成

```bash

# 套餐一：10天，1台，不可注销
go run main.go code -m create -b 100 -n 1 -u -d 240 -f 1 > a.txt

# 套餐二：90天，1台，不可注销
go run main.go code -m create -b 100 -n 1 -u -d 2160 -f 1 > a.txt

# 套餐三：365天，1台，不可注销
go run main.go code -m create -b 100 -n 1 -u -d 8760 -f 1 > a.txt

# 套餐四：永久，1台，不可注销
go run main.go code -m create -b 100 -n 1 -u -d 87600 -f 1 > a.txt

# 套餐五：永久，3台，不可注销
go run main.go code -m create -b 100 -n 3 -u -d 87600 -f 1 > a.txt

# 套餐六：永久，1台，可注销
go run main.go code -m create -b 100 -n 1 -u -r -d 87600 -f 1 > a.txt

# 套餐七：永久，2台，可注销
go run main.go code -m create -b 100 -n 2 -u -r -d 87600 -f 1 > a.txt

# 套餐八：永久，3台，可注销
go run main.go code -m create -b 100 -n 3 -u -r -d 87600 -f 1 > a.txt
```