package main

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/ed25519"
	"crypto/md5"
	"crypto/rand"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type CodeState struct {
	ID             string  `json:"id"`
	DeviceCount    int     `json:"device_count"`
	Duration       float32 `json:"duration"`
	FunctionMode   int     `json:"function_mode"`
	ProductID      string  `json:"productid"`
	ProductVersion string  `json:"product_version"`
	CanUpgrade     bool    `json:"can_upgrade"`
	CanUnregister  bool    `json:"can_unregister"`
	Note           string  `json:"note"`
	Sign           string  `json:"sign"`
}

type LicenseState struct {
	Code           string  `json:"code"`
	ProductID      string  `json:"productid"`
	ProductVersion string  `json:"product_version"`
	CanUpgrade     bool    `json:"can_upgrade"`
	CanUnregister  bool    `json:"can_unregister"`
	MachineID      string  `json:"machineid"`
	IssueTime      string  `json:"issue_time"`
	DeviceCount    int     `json:"device_count"`
	Duration       float32 `json:"duration"`
	FunctionMode   int     `json:"function_mode"`
	Note           string  `json:"note"`
	Platform       string  `json:"platform"`
	InvitationCode string  `json:"invitation_code"`
	Sign           string  `json:"sign"`
}

type LicenseItem struct {
	Code           string  `json:"code"`
	ProductID      string  `json:"productid"`
	ProductVersion string  `json:"product_version"`
	CanUpgrade     bool    `json:"can_upgrade"`
	CanUnregister  bool    `json:"can_unregister"`
	MachineID      string  `json:"machineid"`
	IssueTime      string  `json:"issue_time"`
	DeviceCount    int     `json:"device_count"`
	Duration       float32 `json:"duration"`
	FunctionMode   int     `json:"function_mode"`
	Note           string  `json:"note"`
	Platform       string  `json:"platform"`
	InvitationCode string  `json:"invitation_code"`
	Sign           string  `json:"sign"`
	Valid          bool    `json:"valid"`
}

type BlacklistItem struct {
	Code    string `json:"sign"`
	AddTime string `json:"add_time"`
	Valid   bool   `json:"valid"`
}

type RegisterRequest struct {
	Code      string `json:"code"`
	MachineID string `json:"machineid"`
}

type TrialRequest struct {
	Platform  string `json:"platform"`
	MachineID string `json:"machineid"`
}

type BlacklistRequest struct {
	Code string `json:"code"`
}

type VersionInfo struct {
	Version string `json:"version"`
	Message string `json:"message"`
	Url     string `json:"url"`
}

var (
	// MongoDBURI is the URI of MongoDB
	MongoDBURI     string
	Version        string = "v1.1.25"
	MobileVersion  string = "1.0.0"
	VersionMessage string = "飞书链接：https://kevin2li.feishu.cn/docx/Xu3NdUub9ojwA7xrI5NcqdApnQb?from=from_copylink   密码：4C%5$9t;"
	VersionUrl     string = "https://kevin2li.feishu.cn/docx/Xu3NdUub9ojwA7xrI5NcqdApnQb?from=from_copylink"
	AESKey         string = "PDFGURUANKI_Kevin2li_0123456789#"
)

// PKCS7 填充
func padPKCS7(data []byte, blockSize int) []byte {
	padding := blockSize - (len(data) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

// 去除 PKCS7 填充
func unpadPKCS7(data []byte) []byte {
	length := len(data)
	unpadding := int(data[length-1])
	if unpadding > length {
		return nil
	}
	return data[:(length - unpadding)]
}

func AESEncrypt(plainText []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// 生成随机的初始化向量（IV）
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}

	// 使用 CBC 模式进行加密
	mode := cipher.NewCBCEncrypter(block, iv)

	// 填充明文数据
	paddedPlainText := padPKCS7(plainText, block.BlockSize())

	// 加密数据
	cipherText := make([]byte, len(paddedPlainText))
	mode.CryptBlocks(cipherText, paddedPlainText)

	// 将 IV 与密文组合
	encrypted := append(iv, cipherText...)
	return encrypted, nil
}

func AESDecrypt(encrypted []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// 提取 IV
	iv := encrypted[:aes.BlockSize]
	encrypted = encrypted[aes.BlockSize:]

	// 使用 CBC 模式进行解密
	mode := cipher.NewCBCDecrypter(block, iv)

	// 解密数据
	plainText := make([]byte, len(encrypted))
	mode.CryptBlocks(plainText, encrypted)

	// 去除填充
	unpaddedPlainText := unpadPKCS7(plainText)
	return unpaddedPlainText, nil
}

func GenerateKeyPair() error {
	pubkey, prikey, err := ed25519.GenerateKey(nil)
	if err != nil {
		return err
	}
	log.Printf("pubkey: %x\n", pubkey)
	log.Printf("prikey: %x\n", prikey)
	// save key pair
	err = os.WriteFile("pubkey", []byte(fmt.Sprintf("%x", pubkey)), 0644)
	if err != nil {
		return err
	}
	err = os.WriteFile("prikey", []byte(fmt.Sprintf("%x", prikey)), 0644)
	if err != nil {
		return err
	}
	return nil
}

func PrivateKeyToPEM(privKey ed25519.PrivateKey) ([]byte, error) {
	keybytes, _ := x509.MarshalPKCS8PrivateKey(privKey)

	keyBuf := &bytes.Buffer{}
	err := pem.Encode(keyBuf, &pem.Block{Type: "PRIVATE KEY", Bytes: keybytes})

	return keyBuf.Bytes(), err
}

func CreateCode(
	device_count int,
	duration float32,
	function_mode int,
	productid string,
	can_upgrade bool,
	can_unregister bool,
	note string,
	key ed25519.PrivateKey,
) (string, error) {
	var code CodeState
	code.DeviceCount = device_count
	code.Duration = duration
	code.FunctionMode = function_mode
	code.ProductID = productid
	code.ID = uuid.New().String()
	code.ProductVersion = Version
	code.CanUpgrade = can_upgrade
	code.CanUnregister = can_unregister
	code.Note = note
	bytes, err := json.Marshal(code)
	if err != nil {
		return "", err
	}
	// aes encrypt
	encrypted, err := AESEncrypt(bytes, []byte(AESKey))
	if err != nil {
		return "", err
	}

	// encrypted := bytes
	sign := ed25519.Sign(key, encrypted)
	result := base64.StdEncoding.EncodeToString(encrypted) + "." + base64.StdEncoding.EncodeToString(sign)
	return result, nil
}

func CreateCodeBatch(
	count int,
	device_count int,
	duration float32,
	function_mode int,
	productid string,
	can_upgrade bool,
	can_unregister bool,
	note string,
	key ed25519.PrivateKey,
) ([]string, error) {
	var codeList []string
	for i := 0; i < count; i++ {
		code, err := CreateCode(device_count, duration, function_mode, productid, can_upgrade, can_unregister, note, key)
		if err != nil {
			return codeList, err
		}
		codeList = append(codeList, code)
	}
	return codeList, nil
}

func VerifyCode(code string) error {
	parts := strings.Split(code, ".")
	if len(parts) != 2 {
		return errors.New("invalid code")
	}
	payload, err := base64.StdEncoding.DecodeString(parts[0])
	if err != nil {
		return errors.New("invalid code")
	}
	sign, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		return errors.New("invalid code")
	}

	pubkey_b, err := os.ReadFile("pubkey")
	if err != nil {
		err = errors.Wrap(err, "invalid public key")
		return err
	}
	pubkey_b, err = hex.DecodeString(string(pubkey_b))
	if err != nil {
		err = errors.Wrap(err, "invalid public key")
		return err
	}
	pubkey := ed25519.PublicKey(pubkey_b)
	if !ed25519.Verify(pubkey, payload, sign) {
		return errors.New("sign mismatch")
	}
	return nil
}

func AddCodeToBlacklist(code string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(MongoDBURI))
	if err != nil {
		return err
	}
	defer client.Disconnect(ctx)
	collection := client.Database("license").Collection("blacklist")
	blacklistItem := BlacklistItem{Code: code, AddTime: time.Now().Format(time.RFC3339), Valid: true}
	_, err = collection.InsertOne(ctx, blacklistItem)
	if err != nil {
		return err
	}
	return nil
}

func FindCodeInBlacklist(code string) (BlacklistItem, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(MongoDBURI))
	if err != nil {
		return BlacklistItem{}, err
	}
	defer client.Disconnect(ctx)
	collection := client.Database("license").Collection("blacklist")
	filter := bson.M{"code": code, "valid": true}
	var blacklistItem BlacklistItem
	err = collection.FindOne(ctx, filter).Decode(&blacklistItem)
	if err != nil {
		return BlacklistItem{}, err
	}
	return blacklistItem, nil
}

func DeleteCodeFromBlacklist(code string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(MongoDBURI))
	if err != nil {
		return err
	}
	defer client.Disconnect(ctx)
	collection := client.Database("license").Collection("blacklist")
	filter := bson.M{"code": code}
	update := bson.M{"$set": bson.M{"valid": false}}
	_, err = collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func CreateLicense(code string, machineid string, key ed25519.PrivateKey) (LicenseState, error) {
	var license LicenseState
	err := VerifyCode(code)
	if err != nil {
		return license, err
	}
	parts := strings.Split(code, ".")
	bytes, err := base64.StdEncoding.DecodeString(parts[0])
	if err != nil {
		return license, err
	}
	// 解密
	var decrypted []byte
	// 兼容老版本，判断code是否以eyJpZC开头
	if strings.HasPrefix(parts[0], "eyJpZC") {
		decrypted = bytes
	} else {
		decrypted, err = AESDecrypt(bytes, []byte(AESKey))
		if err != nil {
			return license, err
		}
	}
	fmt.Printf("code: %s", decrypted)

	// 检查是否位于黑名单
	blacklistItem, err := FindCodeInBlacklist(code)
	if err == nil {
		fmt.Printf("found code in blacklist: %+v", blacklistItem)
		return license, errors.New("code is not allowed!")
	}
	var code_state CodeState
	err = json.Unmarshal(decrypted, &code_state)
	if err != nil {
		err = errors.Wrap(err, "code unmarshal error")
		return license, err
	}
	fmt.Printf("code_state: %+v", code_state)

	// 检查数量限制
	lics, err := GetLicenses(code, "")
	fmt.Printf("len lics: %d", len(lics))
	if err != nil {
		return license, err
	}
	valid_count := 0
	for _, lic := range lics {
		if lic.Valid {
			valid_count++
		}
		if lic.MachineID == machineid && lic.Code == code {
			// 检查是否已注销
			if !lic.Valid {
				fmt.Printf("device is already unregistered!")
				return license, errors.New("device is already unregistered!")
			}
			// 设备已经注册过，检查是否有效
			fmt.Printf("device is already registered! %+v", lic)
			result := LicenseState{
				Code:           lic.Code,
				ProductID:      lic.ProductID,
				ProductVersion: lic.ProductVersion,
				CanUpgrade:     lic.CanUpgrade,
				CanUnregister:  lic.CanUnregister,
				MachineID:      lic.MachineID,
				IssueTime:      lic.IssueTime,
				DeviceCount:    lic.DeviceCount,
				Duration:       lic.Duration,
				FunctionMode:   lic.FunctionMode,
				Note:           lic.Note,
				Platform:       lic.Platform,
				InvitationCode: lic.InvitationCode,
				Sign:           lic.Sign,
			}
			pubkey_b, err := os.ReadFile("pubkey")
			if err != nil {
				log.Printf("Error reading public key: %v", err)
			}
			pubkey_b, err = hex.DecodeString(string(pubkey_b))
			if err != nil {
				log.Printf("Error decoding public key: %v", err)
			}
			pubkey := ed25519.PublicKey(pubkey_b)
			_, err = VerifyLicenseRemote(result, pubkey)
			if err != nil {
				return license, errors.New("code was expired!")
			}
			return result, nil
		}
	}

	if valid_count >= code_state.DeviceCount {
		return license, errors.New("device count exceeded")
	}
	// 生成license
	issue_time := time.Now().Format(time.RFC3339)
	salt := "kevin2li-anki"
	msg := fmt.Sprintf("%s.%s.%s.%t.%s.%s.%d.%f.%d.%s", code, code_state.ProductID, code_state.ProductVersion, code_state.CanUpgrade, machineid, issue_time, code_state.DeviceCount, code_state.Duration, code_state.FunctionMode, salt)
	h2 := md5.New()
	io.WriteString(h2, msg)
	license_hash := h2.Sum(nil)
	sign := ed25519.Sign(key, license_hash)

	license.Code = code
	license.MachineID = machineid
	license.ProductID = code_state.ProductID
	license.ProductVersion = code_state.ProductVersion
	license.CanUpgrade = code_state.CanUpgrade
	license.CanUnregister = code_state.CanUnregister
	license.IssueTime = issue_time
	license.DeviceCount = code_state.DeviceCount
	license.Duration = code_state.Duration
	license.FunctionMode = code_state.FunctionMode
	license.Sign = hex.EncodeToString(sign)
	err = InsertLicense(&license, "license")
	if err != nil {
		return license, err
	}
	return license, nil
}

func UnregisterLicense(code string, machineid string) error {
	lics, err := GetLicenses(code, machineid)
	if err != nil {
		return err
	}
	if len(lics) == 0 {
		return errors.New("no license found")
	}
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(MongoDBURI))
	if err != nil {
		return err
	}
	defer client.Disconnect(ctx)
	collection := client.Database("license").Collection("license")
	// 更新license表中的valid字段为false
	_, err = collection.UpdateMany(
		ctx,
		bson.M{"code": code, "machineid": machineid},
		bson.M{"$set": bson.M{"valid": false}},
	)
	if err != nil {
		fmt.Printf("update license error: %v\n", err)
		return err
	}
	fmt.Printf("machineid %s unregistered\n", machineid)
	return nil
}

func VerifyLicenseRemote(license LicenseState, pubkey ed25519.PublicKey) (LicenseState, error) {
	// 验证证书是否被篡改
	salt := "kevin2li-anki"
	msg := fmt.Sprintf("%s.%s.%s.%t.%s.%s.%d.%f.%d.%s", license.Code, license.ProductID, license.ProductVersion, license.CanUpgrade, license.MachineID, license.IssueTime, license.DeviceCount, license.Duration, license.FunctionMode, salt)
	h := md5.New()
	io.WriteString(h, msg)
	hash := h.Sum(nil)
	lic_sign, err := hex.DecodeString(license.Sign)
	if err != nil {
		return license, errors.New("invalid license")
	}
	if !ed25519.Verify(pubkey, hash, lic_sign) {
		return license, errors.New("invalid license")
	}

	// 验证激活码是否有效
	code := license.Code
	if code != "trial" {
		parts := strings.Split(code, ".")
		if len(parts) != 2 {
			return license, errors.New("invalid code")
		}
		payload, err := base64.StdEncoding.DecodeString(parts[0])
		if err != nil {
			return license, errors.New("invalid code")
		}
		sign, err := base64.StdEncoding.DecodeString(parts[1])
		if err != nil {
			return license, errors.New("invalid code")
		}
		if !ed25519.Verify(pubkey, payload, sign) {
			return license, errors.New("invalid code")
		}
	}
	// 检查是否位于黑名单
	blacklistItem, err := FindCodeInBlacklist(code)
	if err == nil {
		fmt.Printf("found code in blacklist: %+v", blacklistItem)
		return license, errors.New("The code is no longer valid!")
	}

	// 检查是否过期
	issue_time, err := time.Parse(time.RFC3339, license.IssueTime)
	if err != nil {
		return license, errors.New("invalid issue time")
	}
	expiry_time := issue_time.Add(time.Duration(license.Duration) * time.Hour)
	if time.Now().After(expiry_time) {
		return license, errors.New("license expired")
	}

	// 检查设备数量
	lics, err := GetLicenses(code, "")
	fmt.Printf("len lics: %d", len(lics))
	if err != nil {
		return license, err
	}
	valid_count := 0
	for _, lic := range lics {
		if lic.Valid {
			valid_count++
		}
		// 检查是否已注销
		if !lic.Valid && lic.MachineID == license.MachineID && lic.Code == license.Code {
			fmt.Printf("machineid %s has been unregistered\n", license.MachineID)
			return license, errors.New("device has been already unregistered")
		}
	}
	if valid_count > license.DeviceCount {
		return license, errors.New("device count exceed!")
	}

	return license, nil
}

func InsertLicense(lic *LicenseState, collectionName string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(MongoDBURI))
	if err != nil {
		return err
	}
	defer client.Disconnect(ctx)
	collection := client.Database("license").Collection(collectionName)
	item := LicenseItem{
		Code:           lic.Code,
		ProductID:      lic.ProductID,
		ProductVersion: lic.ProductVersion,
		CanUpgrade:     lic.CanUpgrade,
		CanUnregister:  lic.CanUnregister,
		MachineID:      lic.MachineID,
		IssueTime:      lic.IssueTime,
		DeviceCount:    lic.DeviceCount,
		Duration:       lic.Duration,
		FunctionMode:   lic.FunctionMode,
		Note:           lic.Note,
		Platform:       lic.Platform,
		InvitationCode: lic.InvitationCode,
		Sign:           lic.Sign,
		Valid:          true,
	}
	_, err = collection.InsertOne(ctx, item)
	if err != nil {
		return err
	}
	return nil
}

func GetLicenses(code string, machineid string) ([]LicenseItem, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(MongoDBURI))
	if err != nil {
		return nil, err
	}
	defer client.Disconnect(ctx)
	collection := client.Database("license").Collection("license")
	filter := bson.M{}
	if code != "" && machineid != "" {
		filter = bson.M{"code": code, "machineid": machineid}
	} else if code != "" {
		filter = bson.M{"code": code}
	} else if machineid != "" {
		filter = bson.M{"machineid": machineid}
	}
	var licenses []LicenseItem
	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var license LicenseItem
		err := cursor.Decode(&license)
		if err != nil {
			return nil, err
		}
		licenses = append(licenses, license)
	}
	return licenses, nil
}

func GetTrialLicenses(machineid string) ([]LicenseState, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(MongoDBURI))
	if err != nil {
		return nil, err
	}
	defer client.Disconnect(ctx)
	collection := client.Database("license").Collection("trial")
	filter := bson.M{"machineid": machineid}
	var licenses []LicenseState
	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var license LicenseState
		err := cursor.Decode(&license)
		if err != nil {
			return nil, err
		}
		licenses = append(licenses, license)
	}
	return licenses, nil
}

func GetTrial(machineid string, platform string, key ed25519.PrivateKey) (LicenseState, error) {
	// 检查是否已经位于trial表中
	lics, err := GetTrialLicenses(machineid)
	if err != nil {
		return LicenseState{}, err
	}
	log.Printf("%+v\n", lics)
	for _, lic := range lics {
		if lic.MachineID == machineid {
			// 判断是否超时
			issue_time, err := time.Parse(time.RFC3339, lic.IssueTime)
			if err != nil {
				return LicenseState{}, errors.New("invalid issue time")
			}
			expiry_time := issue_time.Add(time.Duration(lic.Duration) * time.Hour)
			if time.Now().After(expiry_time) {
				return LicenseState{}, errors.New("you have used up your trial period")
			}
			return lic, nil
		}
	}
	// 创建证书
	lic := LicenseState{
		Code:           "trial",
		ProductID:      "pdf-guru-anki",
		ProductVersion: Version,
		CanUpgrade:     true,
		MachineID:      machineid,
		IssueTime:      time.Now().Format(time.RFC3339),
		DeviceCount:    1,
		Duration:       24 * 3,
		FunctionMode:   0,
		Platform:       platform,
		Sign:           "",
	}
	salt := "kevin2li-anki"
	msg := fmt.Sprintf("%s.%s.%s.%t.%s.%s.%d.%f.%d.%s", lic.Code, lic.ProductID, lic.ProductVersion, lic.CanUpgrade, lic.MachineID, lic.IssueTime, lic.DeviceCount, lic.Duration, lic.FunctionMode, salt)
	h2 := md5.New()
	io.WriteString(h2, msg)
	license_hash := h2.Sum(nil)
	log.Printf("hash: %x\n", license_hash)
	sign := ed25519.Sign(key, license_hash)
	lic.Sign = hex.EncodeToString(sign)
	err = InsertLicense(&lic, "trial")
	if err != nil {
		return lic, err
	}
	return lic, nil
}

func RunServer() {
	// 设置日志格式
	gin.DisableConsoleColor()

	// 创建日志文件
	logFile, err := os.OpenFile("server.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Println(err)
		log.Fatal("Failed to create log file: ", err)
	}

	// 同时将日志写入文件和控制台
	gin.DefaultWriter = io.MultiWriter(logFile, os.Stdout)
	log.SetOutput(io.MultiWriter(logFile, os.Stdout)) // 设置标准日志输出
	log.Println("Server started...")
	err = godotenv.Load(".env")
	if err != nil {
		log.Println(err)
		log.Fatal("Error loading .env file")
	}

	MongoDBURI = fmt.Sprintf("mongodb://%s:%s@%s:%s", os.Getenv("MONGO_USER"), os.Getenv("MONGO_PASSWORD"), os.Getenv("MONGO_HOST"), os.Getenv("MONGO_PORT"))

	// 设置为发布模式
	gin.SetMode(gin.ReleaseMode)
	r := gin.New() // 使用 New() 替代 Default() 以自定义中间件

	// 添加自定义日志中间件
	r.Use(gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] | %s | %s | %s | %d | %s | %s | %s\n",
			param.TimeStamp.Format(time.RFC3339),
			param.ClientIP,
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	}))

	// 添加恢复中间件
	r.Use(gin.Recovery())

	r.GET("/ping", func(c *gin.Context) {
		log.Printf("Received ping request from %s", c.ClientIP())
		c.JSON(http.StatusOK, gin.H{
			"message": "pong",
		})
	})

	r.POST("/register_code", func(c *gin.Context) {
		var req RegisterRequest
		err := c.ShouldBindJSON(&req)
		if err != nil {
			log.Printf("Error binding JSON: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}

		log.Printf("Processing registration request - Code: %s, MachineID: %s", req.Code, req.MachineID)

		err = VerifyCode(req.Code)
		if err != nil {
			log.Printf("Code verification failed: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}

		// 创建证书
		prikey_b, err := os.ReadFile("prikey")
		if err != nil {
			log.Printf("Error reading private key: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		prikey_b, err = hex.DecodeString(string(prikey_b))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		prikey := ed25519.PrivateKey(prikey_b)
		lic, err := CreateLicense(req.Code, req.MachineID, prikey)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		data, err := json.Marshal(lic)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		c.JSON(http.StatusOK, gin.H{"status": "success", "message": nil, "data": string(data)})
	})

	r.POST("/unregister_code", func(c *gin.Context) {
		var req RegisterRequest
		err := c.ShouldBindJSON(&req)
		if err != nil {
			log.Printf("Error binding JSON: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}

		log.Printf("Processing unregistration request - Code: %s, MachineID: %s", req.Code, req.MachineID)

		err = UnregisterLicense(req.Code, req.MachineID)
		if err != nil {
			log.Printf("Error unregistering license: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		log.Printf("Successfully unregistered license for MachineID: %s", req.MachineID)
		c.JSON(http.StatusOK, gin.H{"status": "success", "message": nil, "data": nil})
	})

	r.POST("/get_trial", func(c *gin.Context) {
		var req TrialRequest
		err := c.ShouldBindJSON(&req)
		if err != nil {
			log.Printf("Error binding JSON: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		log.Printf("Processing trial request - Platform: %s, MachineID: %s", req.Platform, req.MachineID)

		prikey_b, err := os.ReadFile("prikey")
		if err != nil {
			log.Printf("Error reading private key: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		prikey_b, err = hex.DecodeString(string(prikey_b))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		prikey := ed25519.PrivateKey(prikey_b)
		lic, err := GetTrial(req.MachineID, req.Platform, prikey)
		if err != nil {
			log.Printf("Error creating trial license: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		log.Printf("Successfully created trial license for MachineID: %s", req.MachineID)
		data, err := json.Marshal(lic)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		c.JSON(http.StatusOK, gin.H{"status": "success", "message": nil, "data": string(data)})
	})

	r.POST("/verify_license", func(c *gin.Context) {
		var lic LicenseState
		err := c.ShouldBindJSON(&lic)
		if err != nil {
			log.Printf("Error binding JSON: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		log.Printf("Processing license verification - MachineID: %s, Code: %s", lic.MachineID, lic.Code)

		pubkey_b, err := os.ReadFile("pubkey")
		if err != nil {
			log.Printf("Error reading public key: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		pubkey_b, err = hex.DecodeString(string(pubkey_b))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		pubkey := ed25519.PublicKey(pubkey_b)
		_, err = VerifyLicenseRemote(lic, pubkey)
		if err != nil {
			log.Printf("License verification failed: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		log.Printf("Successfully verified license for MachineID: %s", lic.MachineID)
		c.JSON(http.StatusOK, gin.H{"status": "success", "message": nil, "data": nil})
	})

	r.GET("/check_update", func(c *gin.Context) {
		log.Printf("Processing update check request from %s", c.ClientIP())
		versionInfo := VersionInfo{Version: Version, Message: VersionMessage, Url: VersionUrl}
		data, err := json.Marshal(versionInfo)
		if err != nil {
			log.Printf("Error marshaling version info: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		log.Printf("Returning version info: %s", Version)
		c.JSON(http.StatusOK, gin.H{"status": "success", "message": nil, "data": string(data)})
	})

	r.GET("/check_update/mobile", func(c *gin.Context) {
		log.Printf("Processing mobile update check request from %s", c.ClientIP())
		versionInfo := VersionInfo{Version: MobileVersion, Message: VersionMessage, Url: VersionUrl}
		data, err := json.Marshal(versionInfo)
		if err != nil {
			log.Printf("Error marshaling mobile version info: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"status": "error", "message": err.Error(), "data": nil})
			return
		}
		log.Printf("Returning mobile version info: %s", MobileVersion)
		c.JSON(http.StatusOK, gin.H{"status": "success", "message": nil, "data": string(data)})
	})

	serverPort := os.Getenv("SERVER_PORT")
	log.Printf("Server starting on port %s", serverPort)
	if err := r.Run(fmt.Sprintf(":%s", serverPort)); err != nil {
		log.Fatal("Server failed to start: ", err)
	}
}

// 命令行接口
var (
	productid       string
	product_version string
	device_count    int
	batch_size      int
	duration        float32
	function_mode   int
	can_upgrade     bool
	can_unregister  bool
	code            string
	machine_id      string
	pubkey          string
	prikey          string
	method          string
	input           string
	output          string
)

var rootCmd = &cobra.Command{
	Use:   "licensectl",
	Short: "A license_server command-line application",
	Long:  "A license_server command-line application, used to generate license and verify license",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("Hello, CLI!")
	},
}

var codeCmd = &cobra.Command{
	Use:   "code",
	Short: "Generate activation code",
	Long:  "Generate activation code",
	Run: func(cmd *cobra.Command, args []string) {
		if method == "create" {
			prikey_b, err := os.ReadFile(prikey)
			if err != nil {
				log.Fatal(err)
			}
			prikey_b, err = hex.DecodeString(string(prikey_b))
			if err != nil {
				log.Fatal(err)
			}
			prikey := ed25519.PrivateKey(prikey_b)
			codeList, err := CreateCodeBatch(batch_size, device_count, duration, function_mode, productid, can_upgrade, can_unregister, "", prikey)
			if err != nil {
				log.Fatal(err)
			}
			for _, code := range codeList {
				fmt.Println(code)
			}
			bytes, err := json.Marshal(codeList)
			if err != nil {
				log.Fatal(err)
			}
			err = os.WriteFile(output, bytes, 0644)
			if err != nil {
				log.Fatal(err)
			}
		}
		if method == "verify" {
			err := VerifyCode(code)
			if err != nil {
				log.Fatal(err)
			}
			log.Println("OK")
		}
	},
}

var licenseCmd = &cobra.Command{
	Use:   "license",
	Short: "Generate and Verify License",
	Long:  "",
	Run: func(cmd *cobra.Command, args []string) {
		if method == "verify" {
			data, err := os.ReadFile(input)
			if err != nil {
				log.Fatal(err)
			}
			var lic LicenseState
			err = json.Unmarshal(data, &lic)
			if err != nil {
				log.Fatal(err)
			}
			log.Printf("%+v\n", lic)
			pubkey_b, err := os.ReadFile("pubkey")
			if err != nil {
				log.Fatal(err)
			}
			pubkey_b, err = hex.DecodeString(string(pubkey_b))
			if err != nil {
				log.Fatal(err)
			}
			pubkey := ed25519.PublicKey(pubkey_b)
			_, err = VerifyLicenseRemote(lic, pubkey)
			if err != nil {
				log.Fatal(err)
			}
			log.Println("验证证书成功")
		}
		fmt.Println("license cmd")
	},
}

var serverCmd = &cobra.Command{
	Use:   "server",
	Short: "Run license server",
	Long:  "",
	Run: func(cmd *cobra.Command, args []string) {
		RunServer()
	},
}

func Execute() error {
	return rootCmd.Execute()
}

func main() {
	codeCmd.PersistentFlags().StringVarP(&productid, "productid", "", "pdf-guru-anki", "product id")
	codeCmd.PersistentFlags().StringVarP(&product_version, "product_version", "v", "", "product version")
	codeCmd.PersistentFlags().IntVarP(&device_count, "device_count", "n", 1, "device count")
	codeCmd.PersistentFlags().Float32VarP(&duration, "duration", "d", 24, "duration")
	codeCmd.PersistentFlags().IntVarP(&function_mode, "function_mode", "f", 1, "function mode")
	codeCmd.PersistentFlags().BoolVarP(&can_upgrade, "can_upgrade", "u", false, "can upgrade")
	codeCmd.PersistentFlags().BoolVarP(&can_unregister, "can_unregister", "r", false, "can unregister")
	codeCmd.PersistentFlags().StringVarP(&code, "code", "c", "", "code")
	codeCmd.PersistentFlags().StringVarP(&pubkey, "pubkey", "", "pubkey", "pubkey")
	codeCmd.PersistentFlags().StringVarP(&prikey, "prikey", "", "prikey", "prikey")
	codeCmd.PersistentFlags().StringVarP(&method, "method", "m", "create", "method")
	codeCmd.PersistentFlags().StringVarP(&output, "output", "o", "codes.json", "output file")
	codeCmd.PersistentFlags().IntVarP(&batch_size, "batch_size", "b", 10, "batch size")
	rootCmd.AddCommand(codeCmd)

	licenseCmd.PersistentFlags().StringVarP(&pubkey, "pubkey", "", "pubkey", "public key")
	licenseCmd.PersistentFlags().StringVarP(&prikey, "prikey", "", "prikey", "private key")
	licenseCmd.PersistentFlags().StringVarP(&code, "code", "", "", "activation code")
	licenseCmd.PersistentFlags().StringVarP(&machine_id, "machineid", "", "", "machine id")
	licenseCmd.PersistentFlags().StringVarP(&method, "method", "m", "verify", "method")
	licenseCmd.PersistentFlags().StringVarP(&input, "input", "i", "LICENSE.json", "input file")
	rootCmd.AddCommand(licenseCmd)

	serverCmd.PersistentFlags().StringVarP(&pubkey, "pubkey", "", "pubkey", "public key")
	serverCmd.PersistentFlags().StringVarP(&prikey, "prikey", "", "prikey", "private key")
	rootCmd.AddCommand(serverCmd)

	if err := rootCmd.Execute(); err != nil {
		log.Fatal(err)
	}
}
