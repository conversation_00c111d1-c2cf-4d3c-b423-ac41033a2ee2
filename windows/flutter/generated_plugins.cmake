#
# Generated file, do not edit.
#

list(APPEND FLUTTER_PLUGIN_LIST
  app_links
  audioplayers_windows
  desktop_drop
  file_selector_windows
  flutter_inappwebview_windows
  flutter_js
  flutter_localization
  flutter_machineid
  flutter_udid
  hotkey_manager_windows
  irondash_engine_context
  isar_flutter_libs
  local_notifier
  media_kit_libs_windows_video
  media_kit_video
  pasteboard
  permission_handler_windows
  screen_brightness_windows
  screen_capturer_windows
  share_plus
  super_native_extensions
  url_launcher_windows
  volume_controller
  window_size
)

list(APPEND FLUTTER_FFI_PLUGIN_LIST
  rinf
)

set(PLUGIN_BUNDLED_LIBRARIES)

foreach(plugin ${FLUTTER_PLUGIN_LIST})
  add_subdirectory(flutter/ephemeral/.plugin_symlinks/${plugin}/windows plugins/${plugin})
  target_link_libraries(${BINARY_NAME} PRIVATE ${plugin}_plugin)
  list(APPEND PLUGIN_BUNDLED_LIBRARIES $<TARGET_FILE:${plugin}_plugin>)
  list(APPEND PLUGIN_BUNDLED_LIBRARIES ${${plugin}_bundled_libraries})
endforeach(plugin)

foreach(ffi_plugin ${FLUTTER_FFI_PLUGIN_LIST})
  add_subdirectory(flutter/ephemeral/.plugin_symlinks/${ffi_plugin}/windows plugins/${ffi_plugin})
  list(APPEND PLUGIN_BUNDLED_LIBRARIES ${${ffi_plugin}_bundled_libraries})
endforeach(ffi_plugin)
