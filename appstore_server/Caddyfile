# Caddyfile for AppStore Server
# 配置反向代理，将请求路由到 production 和 sandbox 环境

# 生产环境 API
appstore.kevin2li.top {
    # 启用日志
    log {
        output file /var/log/caddy/api.appstore.log
        format json
    }

    # 启用真实 HTTPS 证书
    tls {
        protocols tls1.2 tls1.3
    }

    # 请求头处理
    header {
        # 允许跨域请求
        Access-Control-Allow-Origin *
        Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Access-Control-Allow-Headers "Content-Type, Authorization"
        # 安全相关头
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        X-XSS-Protection "1; mode=block"
        X-Content-Type-Options "nosniff"
        X-Frame-Options "DENY"
        # 隐藏服务器类型
        -Server
    }

    # 处理 OPTIONS 请求
    @options {
        method OPTIONS
    }
    handle @options {
        respond 204
    }
    
    # 反向代理 - 生产环境
    handle {
        reverse_proxy appstore_server:8080 {
            transport http {
                versions h1 h2c
            }
            header_up Host {host}
            header_up X-Real-IP {remote}
            header_up X-Forwarded-For {remote}
            header_up X-Forwarded-Proto {scheme}
        }
    }
}

# 沙盒环境 API
sandbox.appstore.kevin2li.top {
    # 启用日志
    log {
        output file /var/log/caddy/sandbox.api.appstore.log
        format json
    }

    # 启用真实 HTTPS 证书
    tls {
        protocols tls1.2 tls1.3
    }

    # 请求头处理
    header {
        # 允许跨域请求
        Access-Control-Allow-Origin *
        Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Access-Control-Allow-Headers "Content-Type, Authorization"
        # 安全相关头
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        X-XSS-Protection "1; mode=block"
        X-Content-Type-Options "nosniff"
        X-Frame-Options "DENY"
        # 隐藏服务器类型
        -Server
    }

    # 处理 OPTIONS 请求
    @options {
        method OPTIONS
    }
    handle @options {
        respond 204
    }
    
    # 反向代理 - 沙盒环境
    handle {
        reverse_proxy appstore_server_sandbox:8080 {
            transport http {
                versions h1 h2c
            }
            header_up Host {host}
            header_up X-Real-IP {remote}
            header_up X-Forwarded-For {remote}
            header_up X-Forwarded-Proto {scheme}
        }
    }
} 