version: '3.8'

services:
  mongodb:
    image: mongo:latest
    container_name: iap-mongodb
    restart: always
    environment:
      - MONGO_INITDB_ROOT_USERNAME=kevin2li
      - MONGO_INITDB_ROOT_PASSWORD=mongodb1125LK
      - TZ=Asia/Shanghai
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27027:27017"
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 20s

  iap-prod-backend:
    build: .
    image: iap-prod-backend:latest
    container_name: iap-prod-backend
    ports:
      - "5000:5000" # 生产环境对外暴露端口 5000
    env_file:
      - ./.env.production # 使用生产环境的配置文件
    environment:
      - MONGO_HOST=mongodb
      - MONGO_INITDB_ROOT_USERNAME=kevin2li
      - MONGO_INITDB_ROOT_PASSWORD=mongodb1125LK
      - TZ=Asia/Shanghai
    volumes:
      - ./logs_production:/app/logs # 生产环境日志持久化
    restart: always
    depends_on:
      - mongodb
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 20s
    user: root
    command: bash -c "mkdir -p /app/logs && chmod -R 777 /app/logs && exec gunicorn main:app --workers 1 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:5000"

  iap-sandbox-backend:
    build: .
    image: iap-sandbox-backend:latest
    container_name: iap-sandbox-backend
    ports:
      - "5001:5000" # 沙盒环境对外暴露端口 5001 (容器内部仍是 5000)
    env_file:
      - ./.env.sandbox # 使用沙盒环境的配置文件
    environment:
      - MONGO_HOST=mongodb
      - MONGO_INITDB_ROOT_USERNAME=kevin2li
      - MONGO_INITDB_ROOT_PASSWORD=mongodb1125LK
      - TZ=Asia/Shanghai
    volumes:
      - ./logs_sandbox:/app/logs # 沙盒环境日志持久化
    restart: always
    depends_on:
      - mongodb
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 20s
    user: root
    command: bash -c "mkdir -p /app/logs && chmod -R 777 /app/logs && exec gunicorn main:app --workers 1 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:5000"

volumes:
  mongodb_data:
    driver: local